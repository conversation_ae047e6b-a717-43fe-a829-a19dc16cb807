#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复CRM-CGI项目中extservice controller的apidoc注释 (仅处理新提交的代码)
功能：
1. 检查并添加缺失的@apiHeader content-type注释
2. 修复extservice项目中缺少/ext前缀的@api路径
3. 基于违规接口列表文件跳过已记录的违规接口
4. 专门用于处理新提交的代码，确保新代码符合规范

运行脚本：python3 ./script/fix_apidoc_annotations_new_commits.py

作者: hongdong.xie
日期: 2025-07-14 10:25:39
"""

import os
import re
import sys
import json
import subprocess
from typing import List, Tuple, Optional, Dict, Set


class GitHelper:
    """Git操作辅助类"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
    
    def get_changed_files(self, base_branch: str = "master") -> List[str]:
        """
        获取相对于基础分支的变更文件列表
        
        Args:
            base_branch: 基础分支名称
            
        Returns:
            变更文件的相对路径列表
        """
        try:
            # 获取相对于基础分支的变更文件
            cmd = ["git", "diff", "--name-only", f"{base_branch}...HEAD"]
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"⚠️  获取变更文件失败: {result.stderr}")
                return []
            
            changed_files = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    # 转换为绝对路径
                    abs_path = os.path.join(self.project_root, line.strip())
                    if os.path.exists(abs_path) and line.strip().endswith('.java'):
                        # 只处理extservice controller文件
                        if 'crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller' in line:
                            changed_files.append(abs_path)
            
            return changed_files
            
        except Exception as e:
            print(f"⚠️  获取变更文件异常: {e}")
            return []
    
    def get_unstaged_files(self) -> List[str]:
        """
        获取未暂存的变更文件列表
        
        Returns:
            未暂存文件的相对路径列表
        """
        try:
            # 获取未暂存的变更文件
            cmd = ["git", "diff", "--name-only"]
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"⚠️  获取未暂存文件失败: {result.stderr}")
                return []
            
            unstaged_files = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    # 转换为绝对路径
                    abs_path = os.path.join(self.project_root, line.strip())
                    if os.path.exists(abs_path) and line.strip().endswith('.java'):
                        # 只处理extservice controller文件
                        if 'crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller' in line:
                            unstaged_files.append(abs_path)
            
            return unstaged_files
            
        except Exception as e:
            print(f"⚠️  获取未暂存文件异常: {e}")
            return []


class ViolationListLoader:
    """违规接口列表加载器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.violation_list_file = os.path.join(project_root, "script", "violation_interfaces.json")
    
    def load_violation_interfaces(self) -> Set[str]:
        """
        从文件加载违规接口列表
        
        Returns:
            违规接口标识符集合
        """
        if not os.path.exists(self.violation_list_file):
            print(f"⚠️  违规接口列表文件不存在: {self.violation_list_file}")
            print(f"💡 请先运行 python3 script/generate_violation_list.py 生成违规接口列表")
            return set()
        
        try:
            with open(self.violation_list_file, 'r', encoding='utf-8') as f:
                violation_data = json.load(f)
            
            violation_interfaces = set()
            for file_violation in violation_data.get('violations', []):
                for interface_violation in file_violation.get('violations', []):
                    interface_id = interface_violation.get('interface_id')
                    if interface_id:
                        violation_interfaces.add(interface_id)
            
            return violation_interfaces
            
        except Exception as e:
            print(f"❌ 加载违规接口列表失败: {e}")
            return set()


class ApiDocFixer:
    def __init__(self, project_root: str, target_files: List[str] = None):
        self.project_root = project_root
        self.content_type_header = "@apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式"
        self.target_files = target_files or []
        
        # 从文件加载违规接口列表
        print("🔍 正在加载违规接口列表...")
        loader = ViolationListLoader(project_root)
        self.violation_interfaces = loader.load_violation_interfaces()
        print(f"📋 已加载 {len(self.violation_interfaces)} 个违规接口，将跳过修复")
        
    def find_controller_files(self) -> List[str]:
        """
        查找需要处理的Controller文件
            
        Returns:
            Controller文件路径列表
        """
        if self.target_files:
            # 如果指定了目标文件，则只处理这些文件
            return [f for f in self.target_files if f.endswith('.java')]
        
        # 否则处理所有Controller文件
        controller_path = os.path.join(
            self.project_root, 
            "crm-cgi-extservice",
            "src", "main", "java", "com", "howbuy", "crm", "cgi", "extservice", "controller"
        )
        
        java_files = []
        if os.path.exists(controller_path):
            for root, dirs, files in os.walk(controller_path):
                for file in files:
                    if file.endswith('.java'):
                        java_files.append(os.path.join(root, file))
        
        return java_files
    
    def read_file_content(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return ""
    
    def write_file_content(self, file_path: str, content: str) -> bool:
        """写入文件内容"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"写入文件失败 {file_path}: {e}")
            return False
    
    def find_apidoc_blocks(self, content: str) -> List[Tuple[int, int, str]]:
        """
        查找所有的apidoc注释块
        
        Returns:
            List of (start_pos, end_pos, block_content)
        """
        blocks = []
        # 匹配 /** ... */ 注释块，其中包含 @api
        pattern = r'/\*\*\s*\n(.*?)\*/'
        
        for match in re.finditer(pattern, content, re.DOTALL):
            block_content = match.group(1)
            if '@api ' in block_content:
                blocks.append((match.start(), match.end(), block_content))
        
        return blocks
    
    def extract_api_info_from_block(self, block_content: str) -> Dict:
        """
        从apidoc注释块中提取API信息
        
        Args:
            block_content: apidoc注释块内容
            
        Returns:
            API信息字典
        """
        lines = block_content.split('\n')
        api_info = {
            'method': '',
            'path': '',
            'name': ''
        }
        
        for line in lines:
            line = line.strip()
            
            # 提取@api信息
            api_match = re.search(r'@api\s+\{([^}]+)\}\s+(/[^\s\n]+)(?:\s+(.+))?', line)
            if api_match:
                api_info['method'] = api_match.group(1)
                api_info['path'] = api_match.group(2)
                if api_match.group(3):
                    api_info['name'] = api_match.group(3).strip()
                break
        
        return api_info
    
    def should_skip_interface(self, file_path: str, api_info: Dict) -> bool:
        """
        判断是否应该跳过该接口的修复
        
        Args:
            file_path: 文件路径
            api_info: API信息
            
        Returns:
            是否跳过
        """
        if not api_info['path'] or not api_info['method']:
            return False
            
        relative_path = os.path.relpath(file_path, self.project_root)
        interface_id = f"{relative_path}:{api_info['path']}:{api_info['method']}"
        
        return interface_id in self.violation_interfaces
    
    def has_content_type_header(self, block_content: str) -> bool:
        """检查是否已有content-type header注释"""
        return '@apiHeader' in block_content and 'content-type' in block_content.lower()
    
    def fix_api_path(self, block_content: str) -> str:
        """
        修复@api路径，添加/ext前缀
        
        Args:
            block_content: apidoc注释块内容
            
        Returns:
            修复后的注释块内容
        """
        # 匹配 @api {METHOD} /path 格式
        api_pattern = r'(@api\s+\{[^}]+\}\s+)(/[^\s\n]+)'
        
        def replace_api_path(match):
            api_prefix = match.group(1)
            path = match.group(2)
            
            # 检查路径是否已经有/ext前缀
            if not path.startswith("/ext"):
                # 如果路径以 / 开头，直接添加 /ext
                if path.startswith("/"):
                    new_path = f"/ext{path}"
                else:
                    new_path = f"/ext/{path}"
            else:
                # 已经有正确前缀，不需要修改
                new_path = path
            
            return api_prefix + new_path
        
        return re.sub(api_pattern, replace_api_path, block_content)
    
    def add_content_type_header(self, block_content: str) -> str:
        """
        在apidoc注释块中添加content-type header
        
        Args:
            block_content: 原始注释块内容
            
        Returns:
            添加header后的注释块内容
        """
        lines = block_content.split('\n')
        new_lines = []
        header_added = False
        
        for i, line in enumerate(lines):
            new_lines.append(line)
            
            # 在第一个@apiParam之前或@apiDescription之后添加header
            if not header_added and (
                line.strip().startswith('* @apiParam') or 
                (line.strip().startswith('* @apiDescription') and 
                 i + 1 < len(lines) and not lines[i + 1].strip().startswith('* @apiDescription'))
            ):
                # 如果是@apiParam，在其前面添加
                if line.strip().startswith('* @apiParam'):
                    new_lines.insert(-1, f"     * {self.content_type_header}")
                # 如果是@apiDescription，在其后面添加
                else:
                    new_lines.append(f"     * {self.content_type_header}")
                header_added = True
        
        # 如果没有找到@apiParam或@apiDescription，在@apiName后添加
        if not header_added:
            for i, line in enumerate(new_lines):
                if line.strip().startswith('* @apiName'):
                    new_lines.insert(i + 1, f"     * {self.content_type_header}")
                    break
        
        return '\n'.join(new_lines)
    
    def process_file(self, file_path: str) -> bool:
        """
        处理单个Java文件
        
        Args:
            file_path: Java文件路径
            
        Returns:
            是否有修改
        """
        content = self.read_file_content(file_path)
        if not content:
            return False
        
        original_content = content
        apidoc_blocks = self.find_apidoc_blocks(content)
        
        if not apidoc_blocks:
            return False
        
        # 统计跳过的接口数量
        skipped_count = 0
        
        # 从后往前处理，避免位置偏移问题
        for start_pos, end_pos, block_content in reversed(apidoc_blocks):
            # 提取API信息
            api_info = self.extract_api_info_from_block(block_content)
            
            # 检查是否应该跳过此接口
            if self.should_skip_interface(file_path, api_info):
                skipped_count += 1
                continue
            
            modified_block = block_content
            
            # 修复API路径
            modified_block = self.fix_api_path(modified_block)
            
            # 添加content-type header（如果缺失）
            if not self.has_content_type_header(modified_block):
                modified_block = self.add_content_type_header(modified_block)
            
            # 如果有修改，替换原内容
            if modified_block != block_content:
                new_block = f"/**\n{modified_block}*/"
                content = content[:start_pos] + new_block + content[end_pos:]
        
        # 打印跳过信息
        if skipped_count > 0:
            relative_path = os.path.relpath(file_path, self.project_root)
            print(f"⏭️  跳过 {skipped_count} 个违规接口: {relative_path}")
        
        # 如果内容有变化，写回文件
        if content != original_content:
            if self.write_file_content(file_path, content):
                print(f"✅ 已修复: {file_path}")
                return True
            else:
                print(f"❌ 写入失败: {file_path}")
                return False
        
        return False
    
    def process_files(self) -> Tuple[int, int, int]:
        """
        处理指定的文件列表
            
        Returns:
            (处理的文件数, 修改的文件数, 跳过的接口总数)
        """
        target_files = self.find_controller_files()
        
        if not target_files:
            print(f"⚠️  未找到需要处理的Controller文件")
            return 0, 0, 0
        
        print(f"📁 找到 {len(target_files)} 个需要处理的Controller文件")
        
        processed_count = 0
        modified_count = 0
        total_skipped = 0
        
        for file_path in target_files:
            processed_count += 1
            relative_path = os.path.relpath(file_path, self.project_root)
            print(f"🔧 处理文件 ({processed_count}/{len(target_files)}): {relative_path}")
            
            if self.process_file(file_path):
                modified_count += 1
        
        return processed_count, modified_count, total_skipped
    
    def run(self):
        """运行修复程序"""
        print("🚀 开始修复CRM-CGI extservice项目的apidoc注释 (新提交代码)...")
        print(f"📂 项目根目录: {self.project_root}")
        
        if self.target_files:
            print(f"🎯 目标文件: {len(self.target_files)} 个")
        
        # 检查是否有违规接口列表
        if not self.violation_interfaces:
            print("⚠️  未找到违规接口列表，将处理所有接口")
        
        # 处理文件
        total_processed, total_modified, total_skipped = self.process_files()
        
        print(f"\n📊 处理完成!")
        print(f"   总共处理文件: {total_processed}")
        print(f"   修改文件数量: {total_modified}")
        print(f"   未修改文件数量: {total_processed - total_modified}")
        print(f"   跳过违规接口数量: {len(self.violation_interfaces)}")
        
        if len(self.violation_interfaces) > 0:
            print(f"\n💡 说明: 基于违规接口列表文件，跳过了 {len(self.violation_interfaces)} 个已记录的违规接口")
        else:
            print(f"\n💡 说明: 未跳过任何接口，所有接口都进行了检查和修复")


def main():
    """主函数"""
    # 检查帮助参数
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("""
CRM-CGI ExtService ApiDoc注释修复脚本 (新提交代码)

功能：
1. 检查并添加缺失的@apiHeader content-type注释
2. 修复extservice项目中缺少/ext前缀的@api路径
3. 基于违规接口列表文件跳过已记录的违规接口
4. 专门用于处理新提交的代码，确保新代码符合规范

使用方法：
  python3 fix_apidoc_annotations_new_commits.py [选项] [项目根目录]
  
选项：
  --git-diff [base_branch]    处理相对于指定分支的变更文件 (默认: master)
  --git-unstaged            处理未暂存的变更文件
  --all                     处理所有文件 (默认行为)
  
参数：
  项目根目录    可选，CRM-CGI项目的根目录路径，默认为当前目录
  
示例：
  python3 fix_apidoc_annotations_new_commits.py --git-diff
  python3 fix_apidoc_annotations_new_commits.py --git-diff develop
  python3 fix_apidoc_annotations_new_commits.py --git-unstaged
  python3 fix_apidoc_annotations_new_commits.py --all
  python3 fix_apidoc_annotations_new_commits.py /path/to/crm-cgi
  
依赖文件：
  script/violation_interfaces.json - 违规接口列表文件
  
注意：
  如果违规接口列表文件不存在，请先运行：
  python3 script/generate_violation_list.py
  
作者：hongdong.xie
日期：2025-07-14 10:25:39
        """)
        sys.exit(0)
    
    # 解析命令行参数
    mode = "all"
    base_branch = "master"
    project_root = os.getcwd()
    
    args = sys.argv[1:]
    i = 0
    while i < len(args):
        arg = args[i]
        if arg == "--git-diff":
            mode = "git-diff"
            # 检查是否有分支参数
            if i + 1 < len(args) and not args[i + 1].startswith('--') and not args[i + 1].startswith('/'):
                base_branch = args[i + 1]
                i += 1
        elif arg == "--git-unstaged":
            mode = "git-unstaged"
        elif arg == "--all":
            mode = "all"
        elif not arg.startswith('--'):
            # 假设是项目根目录
            project_root = arg
        i += 1
    
    if not os.path.exists(project_root):
        print(f"❌ 项目目录不存在: {project_root}")
        sys.exit(1)
    
    # 检查是否是CRM-CGI项目
    if not os.path.exists(os.path.join(project_root, "crm-cgi-extservice")):
        print(f"❌ 指定目录不是CRM-CGI项目根目录或缺少extservice模块: {project_root}")
        sys.exit(1)
    
    # 获取目标文件列表
    target_files = []
    
    if mode == "git-diff":
        print(f"📋 模式: 处理相对于 {base_branch} 分支的变更文件")
        git_helper = GitHelper(project_root)
        target_files = git_helper.get_changed_files(base_branch)
        if not target_files:
            print(f"📝 没有发现相对于 {base_branch} 分支的Controller文件变更")
            sys.exit(0)
    elif mode == "git-unstaged":
        print(f"📋 模式: 处理未暂存的变更文件")
        git_helper = GitHelper(project_root)
        target_files = git_helper.get_unstaged_files()
        if not target_files:
            print(f"📝 没有发现未暂存的Controller文件变更")
            sys.exit(0)
    else:
        print(f"📋 模式: 处理所有文件")
        target_files = None
    
    # 创建修复器并运行
    fixer = ApiDocFixer(project_root, target_files)
    fixer.run()


if __name__ == "__main__":
    main() 