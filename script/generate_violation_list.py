#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成CRM-CGI项目中extservice controller的违规接口列表
功能：
1. 检查当前代码中不符合要求的接口
2. 将违规接口记录到文件中，供后续检查和修复时使用
3. 生成的列表文件将作为固定的排除清单

运行脚本：python3 ./script/generate_violation_list.py

作者: hongdong.xie
日期: 2025-07-14 10:25:39
"""

import os
import re
import sys
import json
from typing import List, Tuple, Dict, Set
from datetime import datetime


class ViolationListGenerator:
    """违规接口列表生成器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.violation_list_file = os.path.join(project_root, "script", "violation_interfaces.json")
        
    def find_controller_files(self) -> List[str]:
        """
        查找extservice项目下的所有Controller文件
            
        Returns:
            Controller文件路径列表
        """
        controller_path = os.path.join(
            self.project_root, 
            "crm-cgi-extservice",
            "src", "main", "java", "com", "howbuy", "crm", "cgi", "extservice", "controller"
        )
        
        java_files = []
        if os.path.exists(controller_path):
            for root, dirs, files in os.walk(controller_path):
                for file in files:
                    if file.endswith('.java'):
                        java_files.append(os.path.join(root, file))
        
        return java_files
    
    def read_file_content(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            return ""
    
    def find_apidoc_blocks(self, content: str) -> List[Tuple[int, int, str, int]]:
        """
        查找所有的apidoc注释块
        
        Returns:
            List of (start_pos, end_pos, block_content, start_line)
        """
        blocks = []
        
        # 匹配 /** ... */ 注释块，其中包含 @api
        pattern = r'/\*\*\s*\n(.*?)\*/'
        
        for match in re.finditer(pattern, content, re.DOTALL):
            block_content = match.group(1)
            if '@api ' in block_content:
                # 计算起始行号
                start_pos = match.start()
                start_line = content[:start_pos].count('\n') + 1
                blocks.append((match.start(), match.end(), block_content, start_line))
        
        return blocks
    
    def extract_api_info_from_block(self, block_content: str, start_line: int) -> Dict:
        """
        从apidoc注释块中提取API信息
        
        Args:
            block_content: apidoc注释块内容
            start_line: 注释块起始行号
            
        Returns:
            API信息字典
        """
        lines = block_content.split('\n')
        api_info = {
            'method': '',
            'path': '',
            'name': '',
            'line_number': start_line,
            'has_content_type_header': False,
            'api_line_number': start_line,
            'violations': []
        }
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 提取@api信息
            api_match = re.search(r'@api\s+\{([^}]+)\}\s+(/[^\s\n]+)(?:\s+(.+))?', line)
            if api_match:
                api_info['method'] = api_match.group(1)
                api_info['path'] = api_match.group(2)
                if api_match.group(3):
                    api_info['name'] = api_match.group(3).strip()
                api_info['api_line_number'] = start_line + i
            
            # 检查是否有content-type header
            if '@apiHeader' in line and 'content-type' in line.lower():
                api_info['has_content_type_header'] = True
        
        return api_info
    
    def check_violations(self, api_info: Dict) -> List[Dict]:
        """
        检查API违规情况
        
        Args:
            api_info: API信息
            
        Returns:
            违规列表
        """
        violations = []
        
        # 检查是否缺少/ext前缀
        if not api_info['path'].startswith('/ext'):
            violations.append({
                'type': 'missing_ext_prefix',
                'message': f"API路径缺少/ext前缀: {api_info['path']}",
                'line': api_info['api_line_number']
            })
        
        # 检查是否缺少content-type header
        if not api_info['has_content_type_header']:
            violations.append({
                'type': 'missing_content_type_header',
                'message': "缺少content-type header",
                'line': api_info['line_number']
            })
        
        return violations
    
    def generate_violation_list(self) -> Dict:
        """
        生成违规接口列表
        
        Returns:
            违规接口数据
        """
        violation_data = {
            'generated_at': datetime.now().isoformat(),
            'project_root': self.project_root,
            'total_files': 0,
            'violation_files': 0,
            'total_violations': 0,
            'violations': []
        }
        
        controller_files = self.find_controller_files()
        violation_data['total_files'] = len(controller_files)
        
        print(f"🔍 正在检查 {len(controller_files)} 个Controller文件...")
        
        for file_path in controller_files:
            content = self.read_file_content(file_path)
            if not content:
                continue
                
            relative_path = os.path.relpath(file_path, self.project_root)
            file_violations = []
            
            # 查找所有apidoc注释块
            apidoc_blocks = self.find_apidoc_blocks(content)
            
            for start_pos, end_pos, block_content, start_line in apidoc_blocks:
                api_info = self.extract_api_info_from_block(block_content, start_line)
                
                if not api_info['path']:
                    continue
                
                # 检查违规情况
                violations = self.check_violations(api_info)
                
                if violations:
                    file_violations.append({
                        'api_method': api_info['method'],
                        'api_path': api_info['path'],
                        'api_name': api_info['name'],
                        'api_line': api_info['api_line_number'],
                        'block_start_line': start_line,
                        'violations': violations,
                        'interface_id': f"{relative_path}:{api_info['path']}:{api_info['method']}"
                    })
            
            if file_violations:
                violation_data['violations'].append({
                    'file_path': relative_path,
                    'violations': file_violations
                })
                violation_data['violation_files'] += 1
                violation_data['total_violations'] += len(file_violations)
        
        return violation_data
    
    def save_violation_list(self, violation_data: Dict) -> bool:
        """
        保存违规接口列表到文件
        
        Args:
            violation_data: 违规接口数据
            
        Returns:
            是否保存成功
        """
        try:
            # 确保script目录存在
            script_dir = os.path.dirname(self.violation_list_file)
            if not os.path.exists(script_dir):
                os.makedirs(script_dir)
            
            with open(self.violation_list_file, 'w', encoding='utf-8') as f:
                json.dump(violation_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"❌ 保存违规接口列表失败: {e}")
            return False
    
    def print_summary(self, violation_data: Dict):
        """打印违规接口摘要"""
        print(f"\n📊 违规接口检查完成!")
        print(f"   总共检查文件: {violation_data['total_files']}")
        print(f"   有违规的文件: {violation_data['violation_files']}")
        print(f"   违规接口总数: {violation_data['total_violations']}")
        print(f"   符合规范的文件: {violation_data['total_files'] - violation_data['violation_files']}")
        
        if violation_data['total_violations'] > 0:
            print(f"\n📝 违规接口列表已保存到: {self.violation_list_file}")
            print(f"💡 后续的检查和修复脚本将基于此列表跳过这些违规接口")
            
            # 统计违规类型
            missing_ext_count = 0
            missing_header_count = 0
            
            for file_violation in violation_data['violations']:
                for interface_violation in file_violation['violations']:
                    for violation in interface_violation['violations']:
                        if violation['type'] == 'missing_ext_prefix':
                            missing_ext_count += 1
                        elif violation['type'] == 'missing_content_type_header':
                            missing_header_count += 1
            
            print(f"\n📈 违规类型统计:")
            print(f"   缺少/ext前缀的接口: {missing_ext_count}")
            print(f"   缺少content-type header的接口: {missing_header_count}")
        else:
            print(f"\n🎉 恭喜！所有接口都符合规范！")
    
    def run(self):
        """运行违规接口列表生成"""
        print("🚀 开始生成CRM-CGI extservice项目的违规接口列表...")
        print(f"📂 项目根目录: {self.project_root}")
        
        # 生成违规接口列表
        violation_data = self.generate_violation_list()
        
        # 保存到文件
        if self.save_violation_list(violation_data):
            print(f"✅ 违规接口列表已保存")
        else:
            print(f"❌ 保存违规接口列表失败")
            return False
        
        # 打印摘要
        self.print_summary(violation_data)
        
        return True


def main():
    """主函数"""
    # 检查帮助参数
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("""
CRM-CGI ExtService 违规接口列表生成脚本

功能：
1. 检查当前代码中不符合要求的接口
2. 将违规接口记录到文件中，供后续检查和修复时使用
3. 生成的列表文件将作为固定的排除清单

使用方法：
  python3 generate_violation_list.py [项目根目录]
  
参数：
  项目根目录    可选，CRM-CGI项目的根目录路径，默认为当前目录
  
示例：
  python3 generate_violation_list.py
  python3 generate_violation_list.py /path/to/crm-cgi
  
输出文件：
  script/violation_interfaces.json - 违规接口列表文件
  
作者：hongdong.xie
日期：2025-07-14 10:25:39
        """)
        sys.exit(0)
    
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        # 默认使用当前目录
        project_root = os.getcwd()
    
    if not os.path.exists(project_root):
        print(f"❌ 项目目录不存在: {project_root}")
        sys.exit(1)
    
    # 检查是否是CRM-CGI项目
    if not os.path.exists(os.path.join(project_root, "crm-cgi-extservice")):
        print(f"❌ 指定目录不是CRM-CGI项目根目录或缺少extservice模块: {project_root}")
        sys.exit(1)
    
    # 创建生成器并运行
    generator = ViolationListGenerator(project_root)
    success = generator.run()
    
    # 根据结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 