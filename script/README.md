# CRM-CGI ApiDoc 注释修复脚本

## 概述

本目录包含用于修复CRM-CGI项目中extservice controller的apidoc注释的脚本工具。

## 脚本列表

### 1. `generate_violation_list.py` - 违规接口列表生成脚本

**功能：**
- 检查当前代码中不符合要求的接口
- 将违规接口记录到文件中，供后续检查和修复时使用
- 生成的列表文件将作为固定的排除清单

**使用方法：**
```bash
python3 script/generate_violation_list.py [项目根目录]
```

**输出文件：**
- `script/violation_interfaces.json` - 违规接口列表文件

### 2. `check_apidoc_compliance.py` - 检查脚本

**功能：**
- 检查@api接口路径是否以"/ext"开头
- 检查是否包含必需的content-type header
- 只检查不修改，打印不符合规范的类名和接口名
- 支持基于违规接口列表文件进行检查

**使用方法：**
```bash
# 完整检查
python3 script/check_apidoc_compliance.py [项目根目录]

# 基于违规接口列表检查（跳过已知违规接口）
python3 script/check_apidoc_compliance.py --use-violation-list [项目根目录]
```

### 3. `fix_apidoc_annotations.py` - 修复脚本

**功能：**
- 检查并添加缺失的@apiHeader content-type注释
- 修复extservice项目中缺少/ext前缀的@api路径
- **基于违规接口列表文件跳过已记录的违规接口**

**使用方法：**
```bash
python3 script/fix_apidoc_annotations.py [项目根目录]
```

**依赖文件：**
- `script/violation_interfaces.json` - 违规接口列表文件

### 4. `fix_apidoc_annotations_new_commits.py` - 新提交代码修复脚本

**功能：**
- 专门用于处理新提交的代码，确保新代码符合规范
- 检查并添加缺失的@apiHeader content-type注释
- 修复extservice项目中缺少/ext前缀的@api路径
- **基于违规接口列表文件跳过已记录的违规接口**

**使用方法：**
```bash
# 处理相对于master分支的变更文件
python3 script/fix_apidoc_annotations_new_commits.py --git-diff

# 处理相对于指定分支的变更文件
python3 script/fix_apidoc_annotations_new_commits.py --git-diff develop

# 处理未暂存的变更文件
python3 script/fix_apidoc_annotations_new_commits.py --git-unstaged

# 处理所有文件
python3 script/fix_apidoc_annotations_new_commits.py --all
```

**依赖文件：**
- `script/violation_interfaces.json` - 违规接口列表文件

## 工作流程

### 初始化设置

1. **首次使用时，生成违规接口列表：**
   ```bash
   cd /path/to/crm-cgi
   python3 script/generate_violation_list.py
   ```

   这会生成 `script/violation_interfaces.json` 文件，记录当前所有违规接口。

### 日常使用

2. **检查新代码是否符合规范：**
   ```bash
   # 检查相对于master分支的变更
   python3 script/check_apidoc_compliance.py --use-violation-list
   ```

3. **修复新提交的代码：**
   ```bash
   # 修复相对于master分支的变更文件
   python3 script/fix_apidoc_annotations_new_commits.py --git-diff
   ```

4. **批量修复所有文件：**
   ```bash
   # 修复所有文件（跳过已知违规接口）
   python3 script/fix_apidoc_annotations.py
   ```

## 重要说明

### 违规接口列表机制

本工具采用**固定违规接口列表**的方式来处理历史违规接口：

1. **生成阶段**：运行 `generate_violation_list.py` 生成当前所有违规接口的列表
2. **使用阶段**：所有修复和检查脚本都基于这个固定列表跳过已知违规接口
3. **优势**：
   - 确保历史违规接口不会被意外修改
   - 可以在任何分支上使用相同的排除规则
   - 提供一致的检查和修复行为

### 检查规则

**API路径规则：**
- API路径必须以`/ext`开头
- 例如：`/ext/hkaccount/login`

**Content-Type Header规则：**
- 必须包含content-type header
- 格式：`@apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式`

### 使用建议

1. **项目初始化**：
   ```bash
   # 生成违规接口列表（仅需运行一次）
   python3 script/generate_violation_list.py
   ```

2. **日常开发**：
   ```bash
   # 检查新代码
   python3 script/check_apidoc_compliance.py --use-violation-list
   
   # 修复新代码
   python3 script/fix_apidoc_annotations_new_commits.py --git-diff
   ```

3. **代码审查**：
   ```bash
   # 检查特定分支的变更
   python3 script/fix_apidoc_annotations_new_commits.py --git-diff feature-branch
   ```

4. **批量处理**：
   ```bash
   # 修复所有文件（跳过已知违规）
   python3 script/fix_apidoc_annotations.py
   ```

## 文件说明

### 输入文件
- `script/violation_interfaces.json` - 违规接口列表文件（由 `generate_violation_list.py` 生成）

### 输出文件
- 修复后的Java源文件
- 控制台输出的检查和修复报告

## 示例

### 完整工作流程示例

```bash
# 1. 进入项目目录
cd /path/to/crm-cgi

# 2. 生成违规接口列表（首次使用）
python3 script/generate_violation_list.py

# 3. 检查当前代码状态
python3 script/check_apidoc_compliance.py --use-violation-list

# 4. 修复新提交的代码
python3 script/fix_apidoc_annotations_new_commits.py --git-diff

# 5. 再次检查确认修复效果
python3 script/check_apidoc_compliance.py --use-violation-list
```

## 输出说明

脚本运行时会显示：
- 🔍 检查/加载进度
- 📋 违规接口统计信息
- ⏭️ 跳过的违规接口
- ✅ 成功修复的文件
- 📊 处理统计信息

## 注意事项

1. **违规接口列表文件**：确保 `script/violation_interfaces.json` 文件存在且是最新的
2. **Git状态**：使用git相关功能时，确保在git仓库中运行
3. **备份**：建议在批量修复前备份代码
4. **测试**：修复后建议运行相关测试确保功能正常

## 作者

hongdong.xie

## 更新日期

2025-07-14 