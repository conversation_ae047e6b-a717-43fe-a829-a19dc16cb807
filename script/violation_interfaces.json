{"generated_at": "2025-07-14T10:26:55.609852", "project_root": "/Users/<USER>/work/workspace/CRM/crm-cgi", "total_files": 44, "violation_files": 17, "total_violations": 53, "violations": [{"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/TestController.java", "violations": [{"api_method": "GET", "api_path": "/ext/test/deleteCatch", "api_name": "deleteCatch()", "api_line": 48, "block_start_line": 48, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 48}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/TestController.java:/ext/test/deleteCatch:GET"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/dtmsproduct/ParamMobileAreaController.java", "violations": [{"api_method": "POST", "api_path": "/inner/dtmsproduct/mobilearea/query", "api_name": "查询手机地区码", "api_line": 45, "block_start_line": 45, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /inner/dtmsproduct/mobilearea/query", "line": 45}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/dtmsproduct/ParamMobileAreaController.java:/inner/dtmsproduct/mobilearea/query:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitRecommendController.java", "violations": [{"api_method": "POST", "api_path": "/ext/portrait/recommend/list", "api_name": "首页为您推荐查询", "api_line": 28, "block_start_line": 28, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 28}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitRecommendController.java:/ext/portrait/recommend/list:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitQaController.java", "violations": [{"api_method": "POST", "api_path": "/ext/portrait/qa/directory", "api_name": "getDirectory()", "api_line": 41, "block_start_line": 41, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 41}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitQaController.java:/ext/portrait/qa/directory:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/qa/reply", "api_name": "reply()", "api_line": 81, "block_start_line": 81, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 81}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitQaController.java:/ext/portrait/qa/reply:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitEntranceController.java", "violations": [{"api_method": "POST", "api_path": "/ext/portrait/entrance/getconscodebyuserid", "api_name": "getConsCodeByUserId()", "api_line": 126, "block_start_line": 126, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 126}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitEntranceController.java:/ext/portrait/entrance/getconscodebyuserid:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitReportController.java", "violations": [{"api_method": "POST", "api_path": "/ext/portrait/report/balance", "api_name": "getBalanceReport()", "api_line": 43, "block_start_line": 43, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 43}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitReportController.java:/ext/portrait/report/balance:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/report/product", "api_name": "getProductReport()", "api_line": 114, "block_start_line": 114, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 114}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitReportController.java:/ext/portrait/report/product:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/report/update/redpoint", "api_name": "updateRedpoint()", "api_line": 186, "block_start_line": 186, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 186}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitReportController.java:/ext/portrait/report/update/redpoint:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/report/checksendpermission", "api_name": "checkSendPermission()", "api_line": 219, "block_start_line": 219, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 219}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitReportController.java:/ext/portrait/report/checksendpermission:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitMaterialController.java", "violations": [{"api_method": "POST", "api_path": "/ext/portrait/material/history/delete", "api_name": "deleteHistory()", "api_line": 168, "block_start_line": 168, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 168}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitMaterialController.java:/ext/portrait/material/history/delete:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/material/history/query", "api_name": "queryHistory()", "api_line": 203, "block_start_line": 203, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 203}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitMaterialController.java:/ext/portrait/material/history/query:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/material/history/add", "api_name": "addHistory()", "api_line": 254, "block_start_line": 254, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 254}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitMaterialController.java:/ext/portrait/material/history/add:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/material/pool", "api_name": "getMaterialPool()", "api_line": 293, "block_start_line": 293, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 293}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitMaterialController.java:/ext/portrait/material/pool:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitWorkbenchController.java", "violations": [{"api_method": "POST", "api_path": "/ext/portrait/workbench/home", "api_name": "getWorkbenchHome()", "api_line": 35, "block_start_line": 35, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 35}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitWorkbenchController.java:/ext/portrait/workbench/home:POST"}, {"api_method": "POST", "api_path": "/ext/portrait/workbench/task/ignore", "api_name": "ignoreTask()", "api_line": 127, "block_start_line": 127, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 127}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/portrait/PortraitWorkbenchController.java:/ext/portrait/workbench/task/ignore:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/dtmsorder/DigitalSignController.java", "violations": [{"api_method": "POST", "api_path": "/dtmsorder/digitalsign/querylist", "api_name": "查询海外产品电子签约列表接口", "api_line": 42, "block_start_line": 40, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /dtmsorder/digitalsign/querylist", "line": 42}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/dtmsorder/DigitalSignController.java:/dtmsorder/digitalsign/querylist:POST"}, {"api_method": "POST", "api_path": "/dtmsorder/digitalsign/querydetail", "api_name": "查询海外产品电子签约详情接口", "api_line": 88, "block_start_line": 88, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /dtmsorder/digitalsign/querydetail", "line": 88}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/dtmsorder/DigitalSignController.java:/dtmsorder/digitalsign/querydetail:POST"}, {"api_method": "POST", "api_path": "/dtmsorder/digitalsign/sign", "api_name": "海外产品电子签约接口", "api_line": 157, "block_start_line": 157, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /dtmsorder/digitalsign/sign", "line": 157}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/dtmsorder/DigitalSignController.java:/dtmsorder/digitalsign/sign:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/PasswordController.java", "violations": [{"api_method": "POST", "api_path": "/hkaccount/password/changeloginpassword", "api_name": "修改登录密码", "api_line": 27, "block_start_line": 27, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/password/changeloginpassword", "line": 27}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/PasswordController.java:/hkaccount/password/changeloginpassword:POST"}, {"api_method": "POST", "api_path": "/hkaccount/password/changetradepassword", "api_name": "修改交易密码", "api_line": 54, "block_start_line": 54, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/password/changetradepassword", "line": 54}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/PasswordController.java:/hkaccount/password/changetradepassword:POST"}, {"api_method": "POST", "api_path": "/hkaccount/password/resetloginpassword", "api_name": "重置登录密码", "api_line": 81, "block_start_line": 81, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/password/resetloginpassword", "line": 81}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/PasswordController.java:/hkaccount/password/resetloginpassword:POST"}, {"api_method": "POST", "api_path": "/hkaccount/password/resettradepassword", "api_name": "重置交易密码", "api_line": 110, "block_start_line": 110, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/password/resettradepassword", "line": 110}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/PasswordController.java:/hkaccount/password/resettradepassword:POST"}, {"api_method": "POST", "api_path": "/hkaccount/password/setloginpassword", "api_name": "设置登录密码", "api_line": 138, "block_start_line": 138, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/password/setloginpassword", "line": 138}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/PasswordController.java:/hkaccount/password/setloginpassword:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCommonController.java", "violations": [{"api_method": "POST", "api_path": "/ext/hkaccount/common/getconstantparamsbytype", "api_name": "getconstantparamsbytype()", "api_line": 77, "block_start_line": 77, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 77}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCommonController.java:/ext/hkaccount/common/getconstantparamsbytype:POST"}, {"api_method": "POST", "api_path": "/ext/hkaccount/common/getcountry", "api_name": "getCountry()", "api_line": 469, "block_start_line": 469, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 469}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCommonController.java:/ext/hkaccount/common/getcountry:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/BusinessController.java", "violations": [{"api_method": "POST", "api_path": "/account/business/getmobilearealist", "api_name": "查询手机地区接口", "api_line": 29, "block_start_line": 29, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /account/business/getmobilearealist", "line": 29}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/BusinessController.java:/account/business/getmobilearealist:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java", "violations": [{"api_method": "POST", "api_path": "/ext/hkaccount/cust/app/getpersonalcenterinfo", "api_name": "getAppPersonalCenterInfo()", "api_line": 84, "block_start_line": 84, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 84}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/ext/hkaccount/cust/app/getpersonalcenterinfo:POST"}, {"api_method": "POST", "api_path": "/hkaccount/cust/getcustrealnameinfo", "api_name": "查询客户实名信息", "api_line": 176, "block_start_line": 176, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/cust/getcustrealnameinfo", "line": 176}, {"type": "missing_content_type_header", "message": "缺少content-type header", "line": 176}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/hkaccount/cust/getcustrealnameinfo:POST"}, {"api_method": "POST", "api_path": "/hkaccount/cust/getbankcardlist", "api_name": "查询银行卡列表", "api_line": 217, "block_start_line": 217, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/cust/getbankcardlist", "line": 217}, {"type": "missing_content_type_header", "message": "缺少content-type header", "line": 217}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/hkaccount/cust/getbankcardlist:POST"}, {"api_method": "POST", "api_path": "/ext/hkaccount/cust/getopencustfilelist", "api_name": "获取开户文件列表数据 (个人中心-开户文件查询)", "api_line": 248, "block_start_line": 248, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 248}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/ext/hkaccount/cust/getopencustfilelist:POST"}, {"api_method": "POST", "api_path": "/hkaccount/cust/getbankcardplaintext", "api_name": "查询银行卡明文", "api_line": 274, "block_start_line": 274, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/cust/getbankcardplaintext", "line": 274}, {"type": "missing_content_type_header", "message": "缺少content-type header", "line": 274}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/hkaccount/cust/getbankcardplaintext:POST"}, {"api_method": "POST", "api_path": "/hkaccount/cust/getcustinfo", "api_name": "查询客户信息", "api_line": 301, "block_start_line": 301, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/cust/getcustinfo", "line": 301}, {"type": "missing_content_type_header", "message": "缺少content-type header", "line": 301}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/hkaccount/cust/getcustinfo:POST"}, {"api_method": "POST", "api_path": "/hkaccount/cust/getcustinfonotlogin", "api_name": "查询客户信息（未登录）", "api_line": 346, "block_start_line": 346, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/cust/getcustinfonotlogin", "line": 346}, {"type": "missing_content_type_header", "message": "缺少content-type header", "line": 346}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkCustController.java:/hkaccount/cust/getcustinfonotlogin:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java", "violations": [{"api_method": "POST", "api_path": "/hkaccount/verifycode/getmobileverifymsgverifycode", "api_name": "获取手机验证短信验证码接口", "api_line": 295, "block_start_line": 295, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getmobileverifymsgverifycode", "line": 295}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getmobileverifymsgverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getactivatetxaccountmsgverifycode", "api_name": "获取交易账户激活短信验证码接口", "api_line": 322, "block_start_line": 322, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getactivatetxaccountmsgverifycode", "line": 322}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getactivatetxaccountmsgverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getactivatetxaccountemailverifycode", "api_name": "获取交易账户激活邮箱验证码接口", "api_line": 350, "block_start_line": 350, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getactivatetxaccountemailverifycode", "line": 350}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getactivatetxaccountemailverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getactivateaccountmsgverifycode", "api_name": "获取登录账户激活短信验证码接口", "api_line": 378, "block_start_line": 378, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getactivateaccountmsgverifycode", "line": 378}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getactivateaccountmsgverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getactivateaccountemailverifycode", "api_name": "获取登录账户激活邮箱验证码接口", "api_line": 406, "block_start_line": 406, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getactivateaccountemailverifycode", "line": 406}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getactivateaccountemailverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getresetloginpasswordmobileverifycode", "api_name": "获取重置登录密码手机号验证码", "api_line": 433, "block_start_line": 433, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getresetloginpasswordmobileverifycode", "line": 433}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getresetloginpasswordmobileverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getresetloginpasswordemailverifycode", "api_name": "获取重置登录密码邮箱验证码", "api_line": 459, "block_start_line": 459, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getresetloginpasswordemailverifycode", "line": 459}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getresetloginpasswordemailverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getresettxpasswordmobileverifycode", "api_name": "获取重置交易密码手机号验证码", "api_line": 485, "block_start_line": 485, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getresettxpasswordmobileverifycode", "line": 485}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getresettxpasswordmobileverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/verifycode/getresettxpasswordemailverifycode", "api_name": "获取重置交易密码邮箱验证码", "api_line": 511, "block_start_line": 511, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/verifycode/getresettxpasswordemailverifycode", "line": 511}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/VerifyCodeController.java:/hkaccount/verifycode/getresettxpasswordemailverifycode:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkOpenAcctController.java", "violations": [{"api_method": "POST", "api_path": "/hkaccount/openacct/saveopenbankinfo", "api_name": "银行卡信息暂存接口", "api_line": 802, "block_start_line": 802, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/openacct/saveopenbankinfo", "line": 802}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkOpenAcctController.java:/hkaccount/openacct/saveopenbankinfo:POST"}, {"api_method": "POST", "api_path": "/ext/hkaccount/openacct/pdf/previewbystream", "api_name": "previewNewPdfByBizCode()", "api_line": 1538, "block_start_line": 1538, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 1538}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/HkOpenAcctController.java:/ext/hkaccount/openacct/pdf/previewbystream:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java", "violations": [{"api_method": "POST", "api_path": "/hkaccount/login/loginbymobileandverifycode", "api_name": "loginByMobileAndVerifyCode()", "api_line": 56, "block_start_line": 56, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/loginbymobileandverifycode", "line": 56}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/loginbymobileandverifycode:POST"}, {"api_method": "POST", "api_path": "/hkaccount/login/loginby<PERSON>oridno", "api_name": "密码登录接口", "api_line": 121, "block_start_line": 121, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/loginbymobileoridno", "line": 121}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/loginbymobileoridno:POST"}, {"api_method": "POST", "api_path": "/hkaccount/login/verifymobile", "api_name": "手机验证接口", "api_line": 244, "block_start_line": 244, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/verifymobile", "line": 244}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/verifymobile:POST"}, {"api_method": "POST", "api_path": "/hkaccount/login/activatetxaccount", "api_name": "交易账户激活接口", "api_line": 272, "block_start_line": 272, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/activatetxaccount", "line": 272}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/activatetxaccount:POST"}, {"api_method": "POST", "api_path": "/hkaccount/login/activateloginaccount", "api_name": "登录账号激活接口", "api_line": 303, "block_start_line": 303, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/activateloginaccount", "line": 303}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/activateloginaccount:POST"}, {"api_method": "POST", "api_path": "/hkaccount/login/loginout", "api_name": "退出登录", "api_line": 334, "block_start_line": 334, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/loginout", "line": 334}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/loginout:POST"}, {"api_method": "POST", "api_path": "/hkaccount/login/getloginidtypelist", "api_name": "获取登录证件类型列表", "api_line": 357, "block_start_line": 357, "violations": [{"type": "missing_ext_prefix", "message": "API路径缺少/ext前缀: /hkaccount/login/getloginidtypelist", "line": 357}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkaccount/LoginController.java:/hkaccount/login/getloginidtypelist:POST"}]}, {"file_path": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkfund/RedeemFundController.java", "violations": [{"api_method": "POST", "api_path": "/ext/hkfund/redeem/queryredeempageinfo", "api_name": "queryRedeemPageInfo()", "api_line": 72, "block_start_line": 72, "violations": [{"type": "missing_content_type_header", "message": "缺少content-type header", "line": 72}], "interface_id": "crm-cgi-extservice/src/main/java/com/howbuy/crm/cgi/extservice/controller/hkfund/RedeemFundController.java:/ext/hkfund/redeem/queryredeempageinfo:POST"}]}]}