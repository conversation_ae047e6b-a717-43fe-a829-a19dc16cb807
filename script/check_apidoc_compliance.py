#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查CRM-CGI项目中extservice controller的apidoc规范合规性
功能：
1. 检查@api接口路径是否以"/ext"开头
2. 检查是否包含必需的content-type header
3. 只检查不修改，打印不符合规范的类名和接口名
4. 支持基于违规接口列表文件进行检查

运行脚本：python3 ./script/check_apidoc_compliance.py

作者: hongdong.xie
日期: 2025-07-14 10:25:39
"""

import os
import re
import sys
import json
from typing import List, Tuple, Dict, Set


class ViolationListLoader:
    """违规接口列表加载器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.violation_list_file = os.path.join(project_root, "script", "violation_interfaces.json")
    
    def load_violation_interfaces(self) -> Set[str]:
        """
        从文件加载违规接口列表
        
        Returns:
            违规接口标识符集合
        """
        if not os.path.exists(self.violation_list_file):
            return set()
        
        try:
            with open(self.violation_list_file, 'r', encoding='utf-8') as f:
                violation_data = json.load(f)
            
            violation_interfaces = set()
            for file_violation in violation_data.get('violations', []):
                for interface_violation in file_violation.get('violations', []):
                    interface_id = interface_violation.get('interface_id')
                    if interface_id:
                        violation_interfaces.add(interface_id)
            
            return violation_interfaces
            
        except Exception as e:
            print(f"❌ 加载违规接口列表失败: {e}")
            return set()


class ApiDocComplianceChecker:
    def __init__(self, project_root: str, use_violation_list: bool = False):
        self.project_root = project_root
        self.content_type_header = "@apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式"
        self.use_violation_list = use_violation_list
        
        # 如果使用违规接口列表，则加载它
        if use_violation_list:
            loader = ViolationListLoader(project_root)
            self.known_violations = loader.load_violation_interfaces()
            if self.known_violations:
                print(f"📋 已加载 {len(self.known_violations)} 个已知违规接口")
            else:
                print(f"⚠️  未找到违规接口列表文件，将进行完整检查")
                self.use_violation_list = False
        else:
            self.known_violations = set()
        
    def find_controller_files(self) -> List[str]:
        """
        查找extservice项目下的所有Controller文件
            
        Returns:
            Controller文件路径列表
        """
        controller_path = os.path.join(
            self.project_root, 
            "crm-cgi-extservice",
            "src", "main", "java", "com", "howbuy", "crm", "cgi", "extservice", "controller"
        )
        
        java_files = []
        if os.path.exists(controller_path):
            for root, dirs, files in os.walk(controller_path):
                for file in files:
                    if file.endswith('.java'):
                        java_files.append(os.path.join(root, file))
        
        return java_files
    
    def read_file_content(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            return ""
    
    def extract_class_name(self, content: str, file_path: str) -> str:
        """从文件内容中提取类名"""
        # 匹配 public class ClassName 或 class ClassName
        class_pattern = r'(?:public\s+)?class\s+(\w+)'
        match = re.search(class_pattern, content)
        if match:
            return match.group(1)
        
        # 如果没有找到，尝试从文件名提取
        file_name = os.path.basename(file_path)
        if file_name.endswith('.java'):
            return file_name[:-5]  # 去掉.java后缀
        
        return ""
    
    def find_apidoc_blocks(self, content: str) -> List[Tuple[int, int, str, int]]:
        """
        查找所有的apidoc注释块
        
        Returns:
            List of (start_pos, end_pos, block_content, start_line)
        """
        blocks = []
        
        # 匹配 /** ... */ 注释块，其中包含 @api
        pattern = r'/\*\*\s*\n(.*?)\*/'
        
        for match in re.finditer(pattern, content, re.DOTALL):
            block_content = match.group(1)
            if '@api ' in block_content:
                # 计算起始行号
                start_pos = match.start()
                start_line = content[:start_pos].count('\n') + 1
                blocks.append((match.start(), match.end(), block_content, start_line))
        
        return blocks
    
    def extract_api_info_from_block(self, block_content: str, start_line: int) -> Dict:
        """
        从apidoc注释块中提取API信息
        
        Args:
            block_content: apidoc注释块内容
            start_line: 注释块起始行号
            
        Returns:
            API信息字典
        """
        lines = block_content.split('\n')
        api_info = {
            'method': '',
            'path': '',
            'name': '',
            'line_number': start_line,
            'has_content_type_header': False,
            'api_line_number': start_line
        }
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 提取@api信息
            api_match = re.search(r'@api\s+\{([^}]+)\}\s+(/[^\s\n]+)(?:\s+(.+))?', line)
            if api_match:
                api_info['method'] = api_match.group(1)
                api_info['path'] = api_match.group(2)
                if api_match.group(3):
                    api_info['name'] = api_match.group(3).strip()
                api_info['api_line_number'] = start_line + i
            
            # 检查是否有content-type header
            if '@apiHeader' in line and 'content-type' in line.lower():
                api_info['has_content_type_header'] = True
        
        return api_info
    
    def is_known_violation(self, file_path: str, api_info: Dict) -> bool:
        """
        检查是否是已知的违规接口
        
        Args:
            file_path: 文件路径
            api_info: API信息
            
        Returns:
            是否是已知违规接口
        """
        if not self.use_violation_list or not api_info['path'] or not api_info['method']:
            return False
            
        relative_path = os.path.relpath(file_path, self.project_root)
        interface_id = f"{relative_path}:{api_info['path']}:{api_info['method']}"
        
        return interface_id in self.known_violations
    
    def check_file(self, file_path: str) -> Dict:
        """
        检查单个Java文件的apidoc合规性
        
        Args:
            file_path: Java文件路径
            
        Returns:
            检查结果字典
        """
        content = self.read_file_content(file_path)
        if not content:
            return {}
        
        relative_path = os.path.relpath(file_path, self.project_root)
        class_name = self.extract_class_name(content, file_path)
        
        result = {
            'file_path': relative_path,
            'class_name': class_name,
            'violations': [],
            'known_violations': []
        }
        
        # 查找所有apidoc注释块
        apidoc_blocks = self.find_apidoc_blocks(content)
        
        for start_pos, end_pos, block_content, start_line in apidoc_blocks:
            api_info = self.extract_api_info_from_block(block_content, start_line)
            
            if not api_info['path']:
                continue
            
            # 检查是否是已知违规接口
            if self.is_known_violation(file_path, api_info):
                result['known_violations'].append({
                    'api_method': api_info['method'],
                    'api_path': api_info['path'],
                    'api_name': api_info['name'],
                    'api_line': api_info['api_line_number'],
                    'block_start_line': start_line
                })
                continue
            
            violations = []
            
            # 检查是否缺少/ext前缀
            if not api_info['path'].startswith('/ext'):
                violations.append({
                    'type': 'missing_ext_prefix',
                    'message': f"API路径缺少/ext前缀: {api_info['path']}",
                    'line': api_info['api_line_number']
                })
            
            # 检查是否缺少content-type header
            if not api_info['has_content_type_header']:
                violations.append({
                    'type': 'missing_content_type_header',
                    'message': "缺少content-type header",
                    'line': start_line
                })
            
            if violations:
                result['violations'].append({
                    'api_method': api_info['method'],
                    'api_path': api_info['path'],
                    'api_name': api_info['name'],
                    'api_line': api_info['api_line_number'],
                    'block_start_line': start_line,
                    'issues': violations
                })
        
        return result
    
    def check_all_files(self) -> List[Dict]:
        """
        检查所有Controller文件的apidoc合规性
            
        Returns:
            所有检查结果列表
        """
        mode_desc = "基于违规接口列表" if self.use_violation_list else "完整"
        print(f"🔍 开始{mode_desc}检查extservice项目的apidoc合规性...")
        print(f"📂 项目根目录: {self.project_root}")
        
        controller_files = self.find_controller_files()
        if not controller_files:
            print("⚠️  未找到 extservice 项目的Controller文件")
            return []
        
        print(f"📁 找到 {len(controller_files)} 个Controller文件")
        
        all_results = []
        violation_files = 0
        total_violations = 0
        total_known_violations = 0
        
        for i, file_path in enumerate(controller_files, 1):
            relative_path = os.path.relpath(file_path, self.project_root)
            print(f"🔧 检查文件 ({i}/{len(controller_files)}): {relative_path}")
            
            result = self.check_file(file_path)
            if result:
                if result.get('violations') or result.get('known_violations'):
                    all_results.append(result)
                    if result.get('violations'):
                        violation_files += 1
                        total_violations += len(result['violations'])
                    if result.get('known_violations'):
                        total_known_violations += len(result['known_violations'])
        
        print(f"\n📊 检查完成!")
        print(f"   总共检查文件: {len(controller_files)}")
        print(f"   有新违规的文件: {violation_files}")
        print(f"   新违规接口总数: {total_violations}")
        
        if self.use_violation_list:
            print(f"   已知违规接口总数: {total_known_violations}")
            print(f"   符合规范的文件: {len(controller_files) - violation_files}")
        else:
            print(f"   符合规范的文件: {len(controller_files) - len(all_results)}")
        
        return all_results
    
    def print_violations(self, results: List[Dict]):
        """打印所有违规项目"""
        new_violations = [r for r in results if r.get('violations')]
        known_violations = [r for r in results if r.get('known_violations')]
        
        if not new_violations and not known_violations:
            print("\n🎉 恭喜！所有apidoc都符合规范！")
            return
        
        if new_violations:
            print(f"\n⚠️  发现 {len(new_violations)} 个文件存在新的apidoc规范违规：")
            print("=" * 100)
            
            for result in new_violations:
                print(f"\n📄 文件: {result['file_path']}")
                print(f"🏷️  类名: {result['class_name']}")
                
                for violation in result['violations']:
                    print(f"\n  🔸 接口违规:")
                    print(f"    📍 第{violation['api_line']}行: @api {{{violation['api_method']}}} {violation['api_path']}")
                    if violation['api_name']:
                        print(f"    📝 接口名: {violation['api_name']}")
                    
                    print(f"    ❌ 问题列表:")
                    for issue in violation['issues']:
                        print(f"       • 第{issue['line']}行: {issue['message']}")
        
        if known_violations and self.use_violation_list:
            print(f"\n📋 发现 {len(known_violations)} 个文件包含已知违规接口（已跳过）：")
            print("-" * 60)
            
            for result in known_violations:
                print(f"\n📄 文件: {result['file_path']}")
                print(f"🏷️  类名: {result['class_name']}")
                print(f"   已知违规接口数: {len(result['known_violations'])}")
        
        print("\n" + "=" * 100)
        print("📋 apidoc规范说明:")
        print("  • API路径必须以/ext开头 (如: /ext/hkaccount/login)")
        print("  • 必须包含content-type header:")
        print(f"    {self.content_type_header}")
    
    def generate_summary_report(self, results: List[Dict]):
        """生成汇总报告"""
        new_violations = [r for r in results if r.get('violations')]
        
        if not new_violations:
            return
        
        print(f"\n📈 详细统计报告:")
        print("-" * 60)
        
        missing_ext_count = 0
        missing_header_count = 0
        
        for result in new_violations:
            for violation in result['violations']:
                for issue in violation['issues']:
                    if issue['type'] == 'missing_ext_prefix':
                        missing_ext_count += 1
                    elif issue['type'] == 'missing_content_type_header':
                        missing_header_count += 1
        
        print(f"缺少/ext前缀的接口: {missing_ext_count}")
        print(f"缺少content-type header的接口: {missing_header_count}")
        print(f"总新违规接口数: {missing_ext_count + missing_header_count}")
    
    def run(self):
        """运行检查程序"""
        results = self.check_all_files()
        self.print_violations(results)
        self.generate_summary_report(results)
        
        # 返回新违规文件数量作为退出码
        new_violations = [r for r in results if r.get('violations')]
        return len(new_violations)


def main():
    """主函数"""
    # 检查帮助参数
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("""
CRM-CGI ExtService ApiDoc合规性检查脚本

功能：
1. 检查@api接口路径是否以"/ext"开头
2. 检查是否包含必需的content-type header
3. 只检查不修改，打印不符合规范的类名和接口名
4. 支持基于违规接口列表文件进行检查

使用方法：
  python3 check_apidoc_compliance.py [选项] [项目根目录]
  
选项：
  --use-violation-list    基于违规接口列表文件进行检查，跳过已知违规接口
  
参数：
  项目根目录    可选，CRM-CGI项目的根目录路径，默认为当前目录
  
示例：
  python3 check_apidoc_compliance.py
  python3 check_apidoc_compliance.py --use-violation-list
  python3 check_apidoc_compliance.py /path/to/crm-cgi
  
检查规则：
  • API路径必须以/ext开头
  • 必须包含content-type header: @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
  
作者：hongdong.xie
日期：2025-07-14 10:25:39
        """)
        sys.exit(0)
    
    # 解析命令行参数
    use_violation_list = False
    project_root = os.getcwd()
    
    args = sys.argv[1:]
    for arg in args:
        if arg == "--use-violation-list":
            use_violation_list = True
        elif not arg.startswith('--'):
            project_root = arg
    
    if not os.path.exists(project_root):
        print(f"❌ 项目目录不存在: {project_root}")
        sys.exit(1)
    
    # 检查是否是CRM-CGI项目
    if not os.path.exists(os.path.join(project_root, "crm-cgi-extservice")):
        print(f"❌ 指定目录不是CRM-CGI项目根目录或缺少extservice模块: {project_root}")
        sys.exit(1)
    
    # 创建检查器并运行
    checker = ApiDocComplianceChecker(project_root, use_violation_list)
    violation_count = checker.run()
    
    # 如果有违规，以非零状态退出
    sys.exit(violation_count)


if __name__ == "__main__":
    main() 