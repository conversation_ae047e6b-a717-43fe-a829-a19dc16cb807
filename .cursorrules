# .cursorrules
# 规则文件
# 作者: hongdong.xie
# 日期: 2024-02-24 10:00:00
#项目背景
你是一名资深Java开发和架构师,技术栈是spring boot、spring cloud、maven、junit

# 项目基本信息
project_name: crm-cgi
project_description: 好买CRM系统
project_usage: 
  - 用户登录、会话管理、用户信息管理、用户权限管理、用户操作日志管理等
  - 给前端页面提供restful接口
  - 项目不连接数据库，主要处理从三方系统获取数据并针对前端页面显示进行数据封装或转换处理
  - 所有restful接口注释采用apidoc注释，例如
    - @api {GET} /ext/test/deleteCatch deleteCatch()
    - @apiVersion 1.0.0
    - @apiGroup TestController
    - @apiName 删除缓存
    - @apiParam (请求参数) {String} key
    - @apiParamExample 请求参数示例
    - @apiSuccess (响应结果) {String} code 返回码
    - @apiSuccess (响应结果) {String} description 返回描述
    - @apiSuccess (响应结果) {Object} data 返回内容
    - @apiSuccess (响应结果) {String} timestampServer
    - @apiSuccessExample 响应结果示例

# 环境依赖
dependencies:
  - JDK: 1.8

# 技术栈
tech_stack:
  framework:
    - Spring Boot: 2.3.7.RELEASE
    - Spring Cloud: Hoxton.SR9
    - Spring Cloud Alibaba: 2.2.6.RELEASE
    - Dubbo: 2.7.15
  database:
    - MyBatis: 2.2.2
    - Druid: 1.2.8
    - PageHelper: 5.3.0
  cache:
    - Redis: 2.9.3
  middleware:
    - ZooKeeper: 3.4.13
    - RocketMQ
    - ActiveMQ
  utils:
    - FastJson: 1.2.80
    - Disruptor: 3.4.2
  logging:
    - Log4j: 2.15.0
    - SLF4J: 1.7.25
# 目标
目标:
    - 提升微服务稳定性
    - 优化接口性能
    - 实现需求场景的高可用性
    - 提升代码质量
    - 提升代码可读性
    - 提升代码可维护性
    - 提升代码可测试性
    - 新业务代码开发
    - 代码重构
    - 代码优化

# 代码风格
code_style:
  - 命名约定:
    - 变量: camelCase  # 变量名使用小驼峰
    - 方法: camelCase  # 方法名使用小驼峰
    - 类: PascalCase    # 类名使用大驼峰
    - 常量: UPPERCASE_WITH_UNDERSCORES  # 常量使用全大写+下划线分隔
  - 缩进: 4_spaces  # 使用 4 个空格进行缩进
  - 每行最大长度: 120  # 每行代码不得超过 120 个字符
  - 代码规范:
    - 使用阿里巴巴代码规范
    - 使用lombok注解
    - 使用dubbo注解
  - 导包顺序:
    - 静态导入优先: true
    - 包顺序:
        - java
        - javax
        - org
        - com
  - 注释要求:
    - Controller 层、Service 层、repository层和关键业务逻辑必须添加 Javadoc 注释
    - @RequestMapping/@GetMapping 等注解需说明 API 的用途及注意事项
    - 类和方法注释需说明其功能和使用场景
    - 所有参数注释需说明参数含义
    - 注释需清晰、简洁、准确，避免冗余和模糊
    - 注释需使用中文
    - 方法注释规范
      - @description
      - @param
      - @return
      - <AUTHOR> @date
    - 参数注释规范
      - 要在参数上方添加注释，不要再后面添加注释
# 目录结构
structure:
  - crm-cgi-common: 公共包，工具、枚举、异常等定义
  - crm-cgi-extservice: 外部业务接口暴露，个人用户session处理
  - crm-cgi-inservice: 对内使用好买域用户登录的业务接口暴露，crm域用户登录session处理
  - crm-cgi-manager: 通用业务处理，对第三方平台封装的层，对Service层通用能力的下沉，如缓存方案、中间件通用处理
  - crm-cgi-remote: 启动+配置

# 调用关系
call_relation:
  - crm-cgi-remote: 作为启动和配置模块，负责初始化其他模块的配置。
  - crm-cgi-manager: 作为中间层，封装对第三方平台的调用，提供给其他模块使用。
  - crm-cgi-extservice 和 crm-cgi-inservice: 直接处理用户登录和会话管理，依赖于 crm-cgi-manager 提供的服务。
  - crm-cgi-common: 提供公共工具和异常处理，供所有模块使用.

# 包目录结构
- 父项目 crm-cgi 包含多个子项目
  - crm-cgi-common 包含公共工具、枚举、异常等定义
    - 基础包名: com.howbuy.crm.cgi.common
    - 公共定义: com.howbuy.crm.cgi.common.base
    - 缓存处理: com.howbuy.crm.cgi.common.cacheservice
    - 常量定义: com.howbuy.crm.cgi.common.constants
    - 枚举类: com.howbuy.crm.cgi.common.enums
    - 工具类: com.howbuy.crm.cgi.common.utils
    - 异常类: com.howbuy.crm.cgi.common.exception
    - 日志处理: com.howbuy.crm.cgi.common.log
  - crm-cgi-extservice 包含直销个人用户登录的业务接口暴露
    - 基础包名: com.howbuy.crm.cgi.extservice
    - 公共包: com.howbuy.crm.cgi.extservice.common
    - 接口定义: com.howbuy.crm.cgi.extservice.controller
      - 入参: com.howbuy.crm.cgi.extservice.request
      - 出参: CgiResponse<XXXVO extends Body>
    - 接口入参定义: com.howbuy.crm.cgi.extservice.request
    - 接口出参定义: com.howbuy.crm.cgi.extservice.vo
    - 接口入参命名规范: 接口名 + Request
    - 接口出参命名规范: 接口名 + VO
  - crm-cgi-inservice 包含使用好买域用户登录的业务接口暴露
    - 基础包名: com.howbuy.crm.cgi.inservice
    - 公共包: com.howbuy.crm.cgi.inservice.common
    - 接口定义: com.howbuy.crm.cgi.inservice.controller
      - 入参: com.howbuy.crm.cgi.inservice.request
      - 出参: CgiResponse<XXXVO extends Body>
    - 接口入参定义: com.howbuy.crm.cgi.inservice.request
    - 接口出参定义: com.howbuy.crm.cgi.inservice.vo
    - 接口入参命名规范: 接口名 + Request
    - 接口出参命名规范: 接口名 + VO
  - crm-cgi-manager 包含通用业务处理
    - 基础包名: com.howbuy.crm.cgi.manager
    - 对象转换: com.howbuy.crm.cgi.manager.convert
    - 外部系统枚举: com.howbuy.crm.cgi.manager.enums
    - 外部系统接口调用封装: com.howbuy.crm.cgi.manager.outerservice
    - 外部系统返回结果封装: com.howbuy.crm.cgi.manager.domain
    - 外部系统接口命名规范: 外部接口名 + OuterService
    - 外部系统接口入参命名规范: 无
    - 外部系统接口出参命名规范: 外部系统出参(不包含Response) + DTO
  - crm-cgi-remote 包含启动和配置
    - 基础包名: com.howbuy.crm.cgi.remote
    - 启动类: com.howbuy.crm.cgi.remote.CrmCgiApplication
    - 配置类: com.howbuy.crm.cgi.remote.config
    - 切面: com.howbuy.crm.cgi.remote.aspect
    - 全局异常处理: com.howbuy.crm.cgi.remote.exception
    - 拦截器: com.howbuy.crm.cgi.remote.interceptor


  