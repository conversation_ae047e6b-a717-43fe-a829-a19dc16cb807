---
description: 外部dubbo接口调用封装
globs: 
alwaysApply: false
---
# .cursorrules
# 外部Dubbo接口调用封装规则
# 作者: hongdong.xie
# 日期: 2024-06-05 15:55:34

# 通用规范
项目名称: crm-cgi
模块名称: crm-cgi-manager
包路径: com.howbuy.crm.cgi.manager.outerservice
外部接口封装: crm-cgi-manager项目中对外部dubbo接口的封装类
描述: 
  该规则定义了外部Dubbo接口调用封装类的编写规范。
  在项目中，所有对外部系统的Dubbo接口调用都应该通过封装类来实现，
  以便于统一管理接口调用、异常处理和日志记录。

# 架构设计
架构设计:
  - 所有外部接口封装类都应位于com.howbuy.crm.cgi.manager.outerservice及其子包下
  - 按照外部系统类型或业务领域进行分包管理
  - 对同一外部系统的不同业务接口，应该按照业务功能分成不同的OuterService类
  - 所有外部系统返回的数据应转换为内部DTO对象，避免外部系统DTO对象在系统内部传递
  - 所有外部系统异常应统一处理并转换为内部异常

# 命名规范
命名规范:
  - 类名: 业务领域+OuterService
  - 方法名: 动词+名词，如getXxx、queryXxx、saveXxx等
  - 入参: 原始入参或自定义入参
  - 出参: 统一返回内部DTO对象，命名规则为业务名称+DTO
  - Dubbo接口引用变量: 使用原始接口名，如xxxFacade
  - 常量: 全大写+下划线分隔，如MAX_RETRY_TIMES

# 代码结构
代码结构:
  - 类注释: 包含作者、描述、创建日期等信息
  - 类注解: @Service
  - 日志注解: @Slf4j
  - Dubbo引用: @DubboReference(registry = "xxx-service", check = false)
  - 方法注释: 包含方法描述、参数说明、返回值说明等
  - 异常处理: 捕获外部系统异常并转换为内部异常

# 实现规范
实现规范:
  - 所有方法必须记录调用日志，包括入参和出参
  - 所有方法必须处理异常，并转换为内部自定义异常
  - 关键业务流程应添加详细的注释
  - 方法参数必须校验非空
  - 外部接口调用结果必须判断是否成功
  - 数据转换时需处理字段映射关系
  - 缓存使用应遵循一致性原则
  - 日志格式应统一为: 类名_方法名_接口名_request/response: {}

# 异常处理
异常处理:
  - 外部接口返回失败时，应抛出BusinessException异常
  - 参数校验失败时，应抛出BusinessException异常
  - 系统内部异常时，应抛出BusinessException异常
  - 异常信息应包含足够的上下文信息，便于排查问题

# 日志记录
日志记录:
  - 方法入口记录入参: log.info("类名_方法名_request:{}", JSON.toJSONString(request))
  - 方法出口记录出参: log.info("类名_方法名_response:{}", JSON.toJSONString(response))
  - 关键业务流程记录: log.info("类名_方法名_业务节点:{}", 业务信息)
  - 异常记录: log.error("类名_方法名_异常信息", e)

# 性能优化
性能优化:
  - 合理使用缓存减少外部接口调用
  - 批量查询优于单条查询
  - 避免循环中调用外部接口
  - 超时时间设置合理，避免长时间阻塞
  - 结果集过大时应分页处理

# 安全规范
安全规范:
  - 敏感信息不应明文日志记录
  - 密码等敏感信息应脱敏处理
  - 外部接口调用应添加必要的权限校验
  - 外部系统返回的敏感数据应进行脱敏处理
  - 防止SQL注入和XSS攻击

# 单元测试
单元测试:
  - 每个OuterService类应有对应的单元测试类
  - 外部接口调用应提供Mock实现
  - 测试用例应覆盖正常流程和异常流程
  - 对缓存逻辑应进行单独测试

# 代码示例
代码示例:
```java
/**
 * <AUTHOR>
 * @description: 香港账户公共信息服务
 * @date 2023/12/05 17:47
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkAccCommonOuterService {

    @DubboReference(registry = "hk-acc-online-service", check = false)
    private QueryHkEmailSuffixFacade queryHkEmailSuffixFacade;

    /**
     * @description: 获取邮箱后缀列表
     * @return EmailSuffixListDTO 邮箱后缀列表
     * @author: xxx
     * @date: 2023/12/04 14:22
     * @since JDK 1.8
     */
    public EmailSuffixListDTO getEmailSuffix() {
        EmailSuffixListDTO emailSuffixListDTO = new EmailSuffixListDTO();
        QueryHkEmailSuffixRequest request = new QueryHkEmailSuffixRequest();
        log.info("HkAccCommonOuterService_getEmailSuffix_request:{}", JSON.toJSONString(request));
        QueryHkEmailSuffixResponse response = queryHkEmailSuffixFacade.execute(request);
        log.info("HkAccCommonOuterService_getEmailSuffix_response:{}", JSON.toJSONString(response));
        if (response.isSuccess()) {
            List<String> emailSuffixList = response.getEmailSuffixList();
            emailSuffixListDTO.setEmailSuffixList(emailSuffixList);
            return emailSuffixListDTO;
        }
        return emailSuffixListDTO;
    }
} 