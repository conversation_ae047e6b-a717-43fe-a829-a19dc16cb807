---
description: 根据设计文档接口定义生成接口代码
globs: 
alwaysApply: false
---
# CRM-CGI项目API生成规则
# 作者: hongdong.xie
# 日期: 2024-02-24 10:00:00

#==================== 编码与命名规范 ====================

# 接口定义规范
interface_rules:
  - 路径：小写字母，多词用/分隔，Controller中不添加/ext或/inner前缀
  - apidoc中的路径：如果生成到innerservice中，路径前加/inner，如果生成到extservice中，路径前加/ext
  - 方法：使用@PostMapping，所有接口默认POST方法
  - 参数：使用@RequestBody注解
  - 返回：统一使用com.howbuy.crm.cgi.common.base.CgiResponse<VO>格式，通过CgiResponse.ok(vo)返回
  - 校验：使用@MyValidation(validatorType=类型, fieldName=中文名, isRequired=true/false)
  - @MyValidation注解类型：使用com.howbuy.commons.validator.MyValidation
  - List、Map等集合对象类型参数，即使需求文档要求必填，也不要加@MyValidation，必填验证在实现类通过代码实现
  - ValidatorTypeEnum类型：使用com.howbuy.commons.validator.util.ValidatorTypeEnum

# 接口修改/新增规则
interface_modify_rules:
  - 判断依据：
    - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
    - 设计文档中接口名后标注"——新增"的，表示新增接口
    - 设计文档中接口名后无标注的，表示新增接口
  - 修改原则：
    - 修改接口时，保持原有的路径和方法名不变
    - 修改接口时，需要兼容原有的功能
    - 修改接口时，新增字段采用追加方式，不要删除或修改原有字段
    - 修改接口时，保持原有的注释格式，仅更新内容
  - 新增原则：
    - 新增接口时，需要在对应的Controller中新增方法
    - 新增接口时，需要创建新的Request和VO类
    - 新增接口时，需要按规范添加完整的注释

# VO和Request对象复用规则
vo_request_rules:
  - 基本原则：
    - 即使不同接口的入参或出参结构完全一样，也不要复用VO和Request类
    - 每个接口都应该有自己独立的Request和VO类
    - 避免多个接口共用同一个Request或VO类
  - 命名规范：
    - Request类命名：接口名+Request
    - VO类命名：接口名+VO
    - 内部VO类命名：业务名+VO
  - 目的说明：
    - 避免后期维护时因共用类导致的连带影响
    - 保证每个接口的参数独立演进
    - 提高代码的可维护性和可扩展性

# 类和方法命名规范
naming_rules:
  - Controller: 业务名Controller (例：QuestionnaireController)
  - Request对象: 接口名+Request (例：NeedQuestionnaireRequest)
  - 响应VO: 接口名+VO (例：NeedQuestionnaireVO)
  - 内部VO: 业务名+VO (例：QuestionnaireItemVO)
  - 所有类和方法必须提供完整注释

# 对象规范
object_rules:
  - Request类：
    - 使用@Setter/@Getter (不用@Data)
    - 必填字段使用@MyValidation(isRequired=true)
    - 所有字段使用String类型并添加中文注释
  - VO类：
    - 使用@Setter/@Getter (不用@Data)
    - 直接用于CgiResponse的VO继承com.howbuy.crm.cgi.common.base.Body类
    - 内部VO不需要继承Body
    - 所有字段使用String类型并添加注释
  - 复杂对象：
    - 列表使用List<专用VO>类型
    - 字段命名应体现列表性质(例：questionnaireItems)
    - 嵌套对象创建专用VO类

#==================== 注释规范 ====================

# 类注释规范
class_comment:
  ```
  /**
   * @description: 类的功能描述
   * @author: 作者名称
   * @date: yyyy-MM-dd HH:mm:ss
   */
  ```

# 方法注释规范
method_comment:
  ```
  /**
   * @description: 方法功能描述
   * @param 参数名: 参数说明
   * @author: 作者名称
   * @date: yyyy-MM-dd HH:mm:ss
   * @return: 返回值说明
   */
  ```

# APIDOC规范
apidoc_rules:
  - 基本格式：
    ```
    /**
     * @api {POST} /路径 方法名()
     * @apiVersion 1.0.0
     * @apiGroup 控制器名
     * @apiName 方法名()
     * @apiDescription 接口功能描述
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {类型} 参数名 参数说明 (必填参数无方括号，可选参数加方括号)
     * @apiParamExample 请求体示例
     * {"参数":"值"}
     * @apiSuccess (响应结果) {类型} 字段名 说明
     * @apiSuccessExample 响应结果示例
     * {"code":"0000","data":{},"description":"成功","timestampServer":"1677196800000"}
     */
    ```
  - 注意事项：
    - 必填参数格式：{String} paramName 说明
    - 可选参数格式：{String} [paramName] 说明
    - 数组类型使用{Array}，对象类型使用{Object}
    - 嵌套属性使用点号(例：data.items.option)
    - 参数说明包含可选值说明(例：Y-是，N-否)
    - 所有注释使用中文，示例数据符合实际业务场景

#==================== 示例 ====================

# 接口示例
example_interface:
  ```java
  /**
   * @api {POST} /questionnaire/need needQuestionnaire()
   * @apiVersion 1.0.0
   * @apiGroup QuestionnaireController
   * @apiName needQuestionnaire()
   * @apiDescription 查询投顾是否需要做问卷接口
   * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
   * @apiParam (请求体) {String} partnerCode 合作方代码
   * @apiParam (请求体) {String} productCode 产品代码
   * @apiParam (请求体) {String} [disCode] 分销代码
   * @apiParamExample 请求体示例
   * {"productCode":"o","partnerCode":"Tzo9","disCode":"z"}
   * @apiSuccess (响应结果) {String} code 返回码
   * @apiSuccess (响应结果) {String} description 返回描述
   * @apiSuccess (响应结果) {Object} data 返回内容
   * @apiSuccess (响应结果) {String} data.needRiskFlag 是否需要做风险测评标识，Y-需要，N-不需要
   * @apiSuccess (响应结果) {String} data.custRiskStatus 客户风险测评状态，0-未测评，1-已测评
   * @apiSuccess (响应结果) {String} timestampServer
   * @apiSuccessExample 响应结果示例
   * {"code":"0000","data":{"needRiskFlag":"Y","custRiskStatus":"0"},"description":"成功","timestampServer":"1677196800000"}
   */
  @PostMapping("/need")
  public CgiResponse<NeedQuestionnaireVO> needQuestionnaire(@RequestBody NeedQuestionnaireRequest request) {
      // 业务逻辑处理
      NeedQuestionnaireVO vo = new NeedQuestionnaireVO();
      // 填充vo的属性
      return CgiResponse.ok(vo);
  }
  ```

# 请求对象示例
request_example:
  ```java
  /**
   * @description: 查询投顾是否需要做问卷请求参数
   * @author: hongdong.xie
   * @date: 2024-02-24 10:00:00
   */
  @Setter
  @Getter
  public class NeedQuestionnaireRequest {
      /**
       * 合作方代码
       */
      @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "合作方代码", isRequired = true)
      private String partnerCode;
      
      /**
       * 产品代码
       */
      @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品代码", isRequired = true)
      private String productCode;
      
      /**
       * 分销代码
       */
      private String disCode;
  }
  ```

# VO对象示例
vo_example:
  ```java
  /**
   * @description: 查询投顾是否需要做问卷响应对象
   * @author: hongdong.xie
   * @date: 2024-02-24 10:00:00
   */
  @Setter
  @Getter
  public class NeedQuestionnaireVO extends Body {
      /**
       * 是否需要做风险测评标识，Y-需要，N-不需要
       */
      private String needRiskFlag;
      
      /**
       * 客户风险测评状态，0-未测评，1-已测评
       */
      private String custRiskStatus;
  }
  ```

# 复杂VO对象示例
complex_vo_example:
  ```java
  // 主VO类 - 直接用于CgiResponse
  @Setter
  @Getter
  public class GetQuestionnaireDetailsVO extends Body {
      /**
       * 合作方代码
       */
      private String partnerCode;
      
      /**
       * 投顾风险问卷题目列表
       */
      private List<QuestionnaireItemVO> questionnaireItems;
  }
  
  // 内部VO类 - 不需要继承Body
  @Setter
  @Getter
  public class QuestionnaireItemVO {
      /**
       * 题目编号
       */
      private String titleNum;
      
      /**
       * 问题
       */
      private String question;
      
      /**
       * 选项列表
       */
      private List<QuestionnaireOptionVO> options;
  }
  ``` 