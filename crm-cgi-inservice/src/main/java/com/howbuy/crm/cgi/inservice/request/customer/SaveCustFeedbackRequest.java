package com.howbuy.crm.cgi.inservice.request.customer;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * @description: 保存客户反馈请求
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class SaveCustFeedbackRequest {
    
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
    
    /**
     * 陪访人列表
     */
    private List<AccompanyingInfo> accompanyingList;
    
    /**
     * 客户参与角色
     */
    private String attendRole;
    
    /**
     * 对产品或服务的具体反馈
     */
    private String productServiceFeedback;
    
    /**
     * 对于IPS报告反馈
     */
    private String ipsFeedback;
    
    /**
     * 人民币金额
     */
    private String addAmtRmb;
    
    /**
     * 外币金额
     */
    private String addAmtForeign;
    
    /**
     * 近期关注的资产类别或具体产品
     */
    private String focusAsset;
    
    /**
     * 评估客户需求
     */
    private String estimateNeedBusiness;
    
    /**
     * 下一步工作计划
     */
    private String nextPlan;
    
    @Getter
    @Setter
    public static class AccompanyingInfo {
        /**
         * 陪访人类型
         */
        private String accompanyingType;
        
        /**
         * 陪访人Id
         */
        private String accompanyingUserId;
    }
} 