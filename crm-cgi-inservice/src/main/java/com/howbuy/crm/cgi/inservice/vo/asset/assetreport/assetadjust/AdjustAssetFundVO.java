/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/28 16:11
 * @since JDK 1.8
 */
@Getter
@Setter
public class AdjustAssetFundVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调整类型 MID("1", "中台持仓产品"),
     * CRM_EXTERNAL("2", "crm外部资产持仓"),
     * ADD("3", "在添加产品列表中另外添加的产品");
     */
    private String adjustType;

    /**
     * 持仓金额
     */
    private String balanceAmt;

    /**
     * 策略类型
     */
    private String assetType;

    /**
     * 建议调整金额
     */
    private String diffAmt;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;


}