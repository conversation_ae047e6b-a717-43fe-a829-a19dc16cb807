/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.BalanceHoldFundDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/5 14:55
 * @since JDK 1.8
 */
@Getter
@Setter
public class FofFundVO implements Serializable {


    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 是否公募组合 0: 否 1:是
     */
    private String isCombination;

    /**
     * 策略类型
     */
    private String assetType;

    /**
     * 基金类型 0: 公募 1:私募 2:其他
     */
    private String fundType;

    /**
     * 是否海外 0: 否 1:是	Y
     */
    private String isOverseas;

    /**
     * 持仓金额数据
     */
    private BigDecimal balanceAmt;

    /**
     * adjustAmt
     */
    private BigDecimal adjustAmt;

    private String hasStrategyProfile;


    public static List<FofFundVO> copyByDTO(List<BalanceHoldFundDTO> funds) {
          List<FofFundVO> fofFundVOS = new java.util.ArrayList<>();
        if (CollectionUtils.isNotEmpty(funds)) {
            for (BalanceHoldFundDTO balanceHoldFundDTO : funds) {
                FofFundVO fofFundVO = new FofFundVO();
                fofFundVO.setFundName(balanceHoldFundDTO.getFundName());
                fofFundVO.setFundCode(balanceHoldFundDTO.getFundCode());
                fofFundVO.setIsCombination(balanceHoldFundDTO.getIsCombination());
                fofFundVO.setAssetType(balanceHoldFundDTO.getAssetType());
                fofFundVO.setFundType(balanceHoldFundDTO.getFundType());
                fofFundVO.setIsOverseas(balanceHoldFundDTO.getIsOverSea());
                fofFundVO.setBalanceAmt(balanceHoldFundDTO.getBalanceAmt());
                fofFundVO.setAdjustAmt(balanceHoldFundDTO.getAdjustAmt());
                fofFundVO.setHasStrategyProfile(balanceHoldFundDTO.getHasStrategyProfile());
                fofFundVOS.add(fofFundVO);
            }
        }
        return fofFundVOS;
    }
}