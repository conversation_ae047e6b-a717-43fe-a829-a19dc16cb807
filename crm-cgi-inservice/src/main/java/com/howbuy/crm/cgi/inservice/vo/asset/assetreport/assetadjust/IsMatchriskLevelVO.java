/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/23 11:18
 * @since JDK 1.8
 */
@Data
public class IsMatchriskLevelVO extends Body implements Serializable {

    /**
     * 是否可自动调整 0: 否 1:是
     */
    private String isCanAdjust;

    /**
     * 客户风险等级
     */
    private String riskLevelDesc;

}