/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.request.asset.assetreport.assetadjust;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/21 09:11
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class QueryFoFRequest extends BodyRequest {
    /**
     * 资配报告ID
     */
    private String assetId;

    /**
     * 投顾客户号
     */
    private String conscustNo;

    /**
     * 套餐内产品个数 1: 1只 2:2只 3:3只
     */
    private String fundSize;

    /**
     * 买入总金额选项 1: 0～300万 2: 300~500万  3: 500~1000万 4: 1000万以上
     */
    private String buyAmtType;

    /**
     * 策略比例选项 1: 上下偏离5%以内 2: 上下偏离10%以内 3: 上下偏离15%以内 默认: 选中 2
     */
    private String strategyRatioType;

    /**
     * 进取型资产偏离比例选项 	1：上下偏离5%以内 2：上下偏离10%以内 3：上下偏离15%以内
     */
    private String aggressiveType;

}