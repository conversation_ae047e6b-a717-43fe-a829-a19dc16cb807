/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.dtmsorder;

import com.howbuy.crm.cgi.common.base.Body;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/9 16:03
 * @since JDK 1.8
 */
public class BuyDealOrderPdfVO extends Body {


    private static final long serialVersionUID = -7382618222585510034L;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户名称
     */
    private String custName;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品份额
     */
    private String productVol;

    /**
     *  分红方式 0-红利再投 1-现金分红 2-N/A不适用
     */
    private String fundDivMode;

    /**
     * 交易日期，YYYY-MM-DD
     */
    private String tradeDt;


    /**
     * 币种
     */
    private String currency;


    /**
     * 币种描述
     */
    private String currencyDesc;


    /**
     * 认购金额
     */
    private String buyAmt;

    /**
     * 手续费
     */
    private String fee;


    /**
     * 支付方式  1-电汇、2-支票、3-海外储蓄罐  9 其他
     */
    private String payMethod;

    /**
     * 支付方式描述
     */
    private String payMethodDesc;

    /**
     * 其他支付方式描述
     */
    private String otherPayMethodDesc;

    /**
     * 展期
     */
    private String extendPeriod;


    /**
     * 申请日期
     */
    private String appDt;


    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductVol() {
        return productVol;
    }

    public void setProductVol(String productVol) {
        this.productVol = productVol;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyDesc() {
        return currencyDesc;
    }

    public void setCurrencyDesc(String currencyDesc) {
        this.currencyDesc = currencyDesc;
    }

    public String getBuyAmt() {
        return buyAmt;
    }

    public void setBuyAmt(String buyAmt) {
        this.buyAmt = buyAmt;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayMethodDesc() {
        return payMethodDesc;
    }

    public void setPayMethodDesc(String payMethodDesc) {
        this.payMethodDesc = payMethodDesc;
    }

    public String getOtherPayMethodDesc() {
        return otherPayMethodDesc;
    }

    public void setOtherPayMethodDesc(String otherPayMethodDesc) {
        this.otherPayMethodDesc = otherPayMethodDesc;
    }

    public String getExtendPeriod() {
        return extendPeriod;
    }

    public void setExtendPeriod(String extendPeriod) {
        this.extendPeriod = extendPeriod;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }
}