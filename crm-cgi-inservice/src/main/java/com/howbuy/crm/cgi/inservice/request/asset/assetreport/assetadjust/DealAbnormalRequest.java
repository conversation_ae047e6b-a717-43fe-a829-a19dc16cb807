/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.request.asset.assetreport.assetadjust;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/1/20 10:31
 * @since JDK 1.8
 */
@Data
public class DealAbnormalRequest implements Serializable {

    /**
     * 资产配置报告ID
     */
    private String assetId;

    /**
     * 异常回测基金数据列表
     */
    private List<AbnormalFundRequest> backTestFundList;

}