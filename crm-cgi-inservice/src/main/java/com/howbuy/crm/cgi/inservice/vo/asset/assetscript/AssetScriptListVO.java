/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetscript;

import com.howbuy.crm.cgi.inservice.vo.asset.assetreport.homepage.PageVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/27 23:20
 * @since JDK 1.8
 */
@Data
public class AssetScriptListVO extends PageVO {

    /**
     * 话术列表
     */
    private List<AssetScriptVO> rows;

}