/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.IndexDTO;
import com.howbuy.crm.cgi.inservice.vo.asset.assetreport.IndexVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/1/20 10:53
 * @since JDK 1.8
 */
@Data
public class BenckMarkVO implements Serializable {

    /**
     * 指数名称
     */
    private String indexName;

    /**
     * 指数代码
     */
    private String indexCode;

    /**
     * 市场指数:1 好买指数：2  公募基金:3 私募基金:4  旗舰基金:5
     */
    private String indexType;

    public static List<BenckMarkVO> copyIndexListDto(List<IndexDTO> indexDTOList){
        List<BenckMarkVO> indexVOList = new ArrayList<>();
        indexDTOList.forEach(indexDTO -> {
            BenckMarkVO indexVO = new BenckMarkVO();
            indexVO.setIndexCode(indexDTO.getIndexCode());
            indexVO.setIndexName(indexDTO.getIndexName());
            indexVO.setIndexType(indexDTO.getIndexType());
            indexVOList.add(indexVO);
        });
        return indexVOList;
    }

}