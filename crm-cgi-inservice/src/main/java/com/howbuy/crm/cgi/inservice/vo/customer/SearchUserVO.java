package com.howbuy.crm.cgi.inservice.vo.customer;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 用户搜索响应
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class SearchUserVO extends Body {

    private List<UserItem> userList;

    @Getter
    @Setter
    public static class UserItem {

        /**
         * 用户ID
         */
        private String userId;

        /**
         * 用户名
         */
        private String userName;
    }
} 