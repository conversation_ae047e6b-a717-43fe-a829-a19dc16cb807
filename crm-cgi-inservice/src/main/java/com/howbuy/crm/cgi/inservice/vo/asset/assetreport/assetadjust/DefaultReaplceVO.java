/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.DefaultReaplceDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/2/18 13:07
 * @since JDK 1.8
 */
@Data
public class DefaultReaplceVO implements Serializable {

    /**
     * 默认替换方式 1: 旗舰基金 2: 指数
     */
    private String type;

    /**
     * 默认替换的指数/基金代码
     */
    private String code;

    /**
     * 默认替换的基金/指数 名称
     */
    private String name;

    public static DefaultReaplceVO copyByDto(DefaultReaplceDTO defaultRepalceObj) {
        DefaultReaplceVO defaultReaplceVO = new DefaultReaplceVO();
        if (defaultRepalceObj != null) {
            defaultReaplceVO.setType(defaultRepalceObj.getType());
            defaultReaplceVO.setCode(defaultRepalceObj.getCode());
            defaultReaplceVO.setName(defaultRepalceObj.getName());
            return defaultReaplceVO;
        }
        return null;
    }
}