/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.BalanceFundDTO;
import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.BalanceHoldFundDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/5 14:45
 * @since JDK 1.8
 */
@Getter
@Setter
public class BalanceHoldFundVO implements Serializable {

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 用于展示的产品代码
     */
    private String showProductCode;

    /**
     * 是否公募组合 0: 否 1:是
     */
    private String isCombination;

    /**
     * 策略类型
     */
    private String assetType;

    /**
     * 基金类型 0: 公募 1:私募 2:其他
     */
    private String fundType;

    /**
     * 是否海外 0: 否 1:是	Y
     */
    private String isOverseas;

    /**
     * 持仓金额数据 单位万元
     */
    private BigDecimal balanceAmt;

    /**
     * 实际持仓金额 单位 元
     */
    private BigDecimal realBalanceAmt;

    /**
     * adjustAmt
     */
    private BigDecimal adjustAmt;

    /**
     * 是否黄金
     */
    private String isGold;

    /**
     * 是否外部资产 0: 否 1:是
     */
    private String isInnerAsset;

    /**
     * 是否存在策略分布 0-否 1-是
     */
    private String hasStrategyProfile;

    public static List<BalanceHoldFundVO> copyByDto(List<BalanceHoldFundDTO> balanceFundDTOList) {
        List<BalanceHoldFundVO> balanceFundVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(balanceFundDTOList)) {
            balanceFundDTOList.forEach(balanceFundDTO -> {
                BalanceHoldFundVO balanceFundVO = new BalanceHoldFundVO();
                balanceFundVO.setFundName(balanceFundDTO.getFundName());
                balanceFundVO.setFundCode(balanceFundDTO.getFundCode());
                balanceFundVO.setShowProductCode(balanceFundDTO.getShowProductCode());
                balanceFundVO.setIsCombination(balanceFundDTO.getIsCombination());
                balanceFundVO.setAssetType(balanceFundDTO.getAssetType());
                balanceFundVO.setFundType(balanceFundDTO.getFundType());
                balanceFundVO.setRealBalanceAmt(balanceFundDTO.getRealBalanceAmt());
                balanceFundVO.setIsOverseas(balanceFundDTO.getIsOverSea());
                balanceFundVO.setBalanceAmt(balanceFundDTO.getBalanceAmt());
                balanceFundVO.setIsInnerAsset(balanceFundDTO.getIsInnerAsset());
                balanceFundVO.setAdjustAmt(balanceFundDTO.getAdjustAmt());
                balanceFundVO.setIsGold(balanceFundDTO.getIsGold());
                balanceFundVO.setHasStrategyProfile(balanceFundDTO.getHasStrategyProfile());
                balanceFundVOS.add(balanceFundVO);
            });
        }
        return balanceFundVOS;
    }
}