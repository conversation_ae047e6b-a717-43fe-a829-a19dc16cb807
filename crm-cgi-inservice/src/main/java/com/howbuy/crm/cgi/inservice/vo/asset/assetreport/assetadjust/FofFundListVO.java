/**
 *Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.FofRecommendDTO;
import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.inservice.vo.asset.health.HealthDataVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/23 11:25
 * @since JDK 1.8
 */
@Setter
@Getter
@ToString
public class FofFundListVO extends Body implements Serializable {

    /**
     * fof套餐列表数据
     */
    private List<AutoFofFundVO> fofFundLists;

    public static List<AutoFofFundVO> copyByDTO(List<FofRecommendDTO> fofRecommendDTOList) {
        List<AutoFofFundVO> autoFofFundVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fofRecommendDTOList)) {
            for (FofRecommendDTO fofRecommendDTO : fofRecommendDTOList) {
                AutoFofFundVO autoFofFundVO = new AutoFofFundVO();
                autoFofFundVO.setFofId(fofRecommendDTO.getFofId());
                autoFofFundVO.setShowFofId(fofRecommendDTO.getShowFofId());
                autoFofFundVO.setIsSelected(fofRecommendDTO.getIsSelected());
                autoFofFundVO.setInvestAmt(fofRecommendDTO.getInvestableAmt());
                autoFofFundVO.setRemainInvestAbleAmt(fofRecommendDTO.getRemainInvestAbleAmt());
                autoFofFundVO.setAdjustRatioDiffList(AdjustRatioDiffVO.copyByDTO(fofRecommendDTO.getAdjustRatioDiffList()));
                autoFofFundVO.setHealthData(HealthDataVO.copyByDTO(fofRecommendDTO.getHealthDataDTO()));
                autoFofFundVO.setFunds(FofFundVO.copyByDTO(fofRecommendDTO.getFunds()));
                autoFofFundVOS.add(autoFofFundVO);
            }
        }
        return autoFofFundVOS;
    }
}