/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetbalance.AutoFofRefreshDataDTO;
import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.inservice.vo.asset.health.HealthDataVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/20 16:39
 * @since JDK 1.8
 */
@Data
public class AutoFofRefreshDataVO extends Body implements Serializable {


    /**
     * fof套餐id数据
     */
    private String fofId;

    /**
     * 用于展示的fofId数据
     */
    private String showFofId;

    /**
     * 是否选中 0:否 1:是
     */
    private String isSelected;

    /**
     * 可投资金额
     */
    private BigDecimal investAmt;

    /**
     * 剩余可投资金额
     */
    private BigDecimal remainInvestAbleAmt;

    /**
     * 调仓后比例差距
     */
    private List<AdjustRatioDiffVO> adjustRatioDiffList;

    /**
     * 健康度数据
     */
    private HealthDataVO healthData;

    public static AutoFofRefreshDataVO copyByDto(AutoFofRefreshDataDTO data) {
        AutoFofRefreshDataVO autoFofRefreshDataVO = new AutoFofRefreshDataVO();
        if (data != null) {
            autoFofRefreshDataVO.setInvestAmt(data.getInvestAmt());
            autoFofRefreshDataVO.setRemainInvestAbleAmt(data.getRemainInvestAbleAmt());
            autoFofRefreshDataVO.setAdjustRatioDiffList(AdjustRatioDiffVO.copyByDTO(data.getAdjustRatioDiffList()));
            autoFofRefreshDataVO.setHealthData(HealthDataVO.copyByDTO(data.getHealthData()));
        }
        return autoFofRefreshDataVO;
    }
}