/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.controller.asset.assetScript;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.inservice.request.asset.assetscript.*;
import com.howbuy.crm.cgi.inservice.service.asset.AssetScriptService;
import com.howbuy.crm.cgi.inservice.vo.asset.assetscript.AssetScriptListVO;
import com.howbuy.crm.cgi.inservice.vo.asset.assetscript.ListMenuTreeVO;
import com.howbuy.crm.cgi.inservice.vo.wechat.CheckFileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/24 14:56
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/asset/script")
public class AssetScriptController {

    @Autowired
    private AssetScriptService assetScriptService;

    /**
     * @api {POST} /inner/asset/script/import importScriptFile()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName importScriptFile()
     * @apiDescription 导入话术数据
     * @apiParam (请求参数) {Object} multipartFile 导入文件
     * @apiParamExample 请求参数示例
     * multipartFile=null
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"WJ6PAcwB6","description":"i8Jo5SNMN6","timestampServer":"O"}
     */
    @PostMapping("/import")
    @ResponseBody
    public CgiResponse<CheckFileVO> importScriptFile(ScriptTemplateRequest scriptTemplateRequest) {
        CheckFileVO checkFileVO = assetScriptService.importScriptFile(scriptTemplateRequest);
        return CgiResponse.ok(checkFileVO);
    }

    /**
     * @api {POST} /inner/asset/script/confirmimport confirmImportFile()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName confirmImportFile()
     * @apiDescription 确认导入接口
     * @apiParam (请求体) {Number} id
     * @apiParam (请求体) {Number} treeId
     * @apiParam (请求体) {String} title
     * @apiParam (请求体) {String} content
     * @apiParam (请求体) {String} source
     * @apiParam (请求体) {String} offStatus
     * @apiParam (请求体) {String} recStatus
     * @apiParamExample 请求体示例
     * {"treeId":7979,"offStatus":"i","recStatus":"Fv7","id":8218,"source":"0r6","title":"BC","content":"TFB"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"jpxF","description":"o","timestampServer":"J"}
     */
    @PostMapping("/confirmimport")
    @ResponseBody
    public CgiResponse<Body> confirmImportFile(@RequestBody CmAssetScriptRequest request) {
        assetScriptService.confirmImportFile();
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/listcmassetscript queryAssetListVO()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName queryAssetListVO()
     * @apiDescription 查询话术列表数据
     * @apiParam (请求体) {Number} id
     * @apiParam (请求体) {Number} treeId
     * @apiParam (请求体) {String} title
     * @apiParam (请求体) {String} content
     * @apiParam (请求体) {String} source
     * @apiParam (请求体) {String} recStatus
     * @apiParamExample 请求体示例
     * {"treeId":9023,"recStatus":"Q8g2j","id":3974,"source":"s0i5fW","title":"68xy","content":"t2SPjlMj"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.rows 话术列表
     * @apiSuccess (响应结果) {Number} data.rows.id 主键id
     * @apiSuccess (响应结果) {Number} data.rows.treeId 目录树节点id
     * @apiSuccess (响应结果) {String} data.rows.title 标题
     * @apiSuccess (响应结果) {String} data.rows.content 话术内容
     * @apiSuccess (响应结果) {String} data.rows.source 来源 0: 自己配置 1:微伴红宝书
     * @apiSuccess (响应结果) {String} data.rows.updateUser 更新人
     * @apiSuccess (响应结果) {Number} data.rows.updateTime 更新时间
     * @apiSuccess (响应结果) {Number} data.rows.createTime 创建时间
     * @apiSuccess (响应结果) {Number} data.rows.sort 顺序
     * @apiSuccess (响应结果) {Number} data.rows.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.rows.size 返回数据量
     * @apiSuccess (响应结果) {Number} data.rows.total 总数
     * @apiSuccess (响应结果) {Number} data.page 当前第几页
     * @apiSuccess (响应结果) {Number} data.size 返回数据量
     * @apiSuccess (响应结果) {Number} data.total 总数
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ZV8Jrh986d","data":{"total":6023,"size":7806,"page":6251,"rows":[{"treeId":2781,"total":3143,"size":8181,"createTime":4098006962401,"updateUser":"c0","updateTime":2258647299169,"id":4778,"source":"9U3TI48oi0","sort":9846,"page":5529,"title":"O1B","content":"ydHXYDxPpp"}]},"description":"5q","timestampServer":"BGdyiq"}
     */
    @PostMapping("/listcmassetscript")
    @ResponseBody
    public CgiResponse<AssetScriptListVO> queryAssetListVO(@RequestBody CmAssetScriptRequest request) {
        return CgiResponse.ok(assetScriptService.listAssetScript(request));
    }

    /**
     * @api {POST} /inner/asset/script/updatestatus updateStatus()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName updateStatus()
     * @apiDescription 更新状态
     * @apiParam (请求体) {Number} id
     * @apiParam (请求体) {Number} treeId
     * @apiParam (请求体) {String} title
     * @apiParam (请求体) {String} content
     * @apiParam (请求体) {String} source
     * @apiParam (请求体) {String} offStatus
     * @apiParam (请求体) {String} recStatus
     * @apiParamExample 请求体示例
     * {"treeId":510,"offStatus":"nBAsYhq7ao","recStatus":"j8j","id":1808,"source":"W","title":"Of","content":"a"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"u","description":"QpqN","timestampServer":"4JhAN21"}
     */
    @PostMapping("/updatestatus")
    @ResponseBody
    public CgiResponse<Body> updateStatus(@RequestBody CmAssetScriptRequest request) {
        assetScriptService.updateStatus(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/batchupdatelist batchUpdateList()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName batchUpdateList()
     * @apiDescription 批量更新状态
     * @apiParam (请求体) {Array} ids 需要更新的列表数据id
     * @apiParam (请求体) {String} updateType 更新状态
     * @apiParamExample 请求体示例
     * {"ids":[1413],"updateType":"mnR"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"2z9xgu","description":"Ypbq1d","timestampServer":"7s4m"}
     */
    @PostMapping("/batchupdatelist")
    @ResponseBody
    public CgiResponse<Body> batchUpdateList(@RequestBody BatchUpdateRequest request) {
        assetScriptService.batchUpdateStatus(request);
        return CgiResponse.ok(new Body());
    }



    /**
     * @api {POST} /inner/asset/script/savemenutree saveMenuTree()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName saveMenuTree()
     * @apiDescription 保存节点数据
     * @apiParam (请求体) {Array} treeNodes
     * @apiParam (请求体) {Number} treeNodes.id
     * @apiParam (请求体) {String} treeNodes.label
     * @apiParam (请求体) {Array} treeNodes.children
     * @apiParam (请求体) {Number} treeNodes.children.id
     * @apiParam (请求体) {String} treeNodes.children.label
     * @apiParam (请求体) {Array} treeNodes.children.children
     * @apiParamExample 请求体示例
     * {"treeNodes":[{"children":[{"children":[],"id":8848,"label":"7zisUXW"}],"id":4728,"label":"r"}]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"kXi","description":"Un3qnBdR","timestampServer":"ulS"}
     */
    @PostMapping("/savemenutree")
    @ResponseBody
    public CgiResponse<Body> saveMenuTree(@RequestBody ScriptTreeNodeRequest treedata) {
        assetScriptService.saveMenuTree(treedata);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/savefirstmenutree saveFirstMenuTree()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName saveFirstMenuTree()
     * @apiParam (请求体) {Array} treeNodes
     * @apiParam (请求体) {Number} treeNodes.id
     * @apiParam (请求体) {String} treeNodes.label
     * @apiParam (请求体) {Number} treeNodes.sortOrder
     * @apiParam (请求体) {Array} treeNodes.children
     * @apiParam (请求体) {Number} treeNodes.children.id
     * @apiParam (请求体) {String} treeNodes.children.label
     * @apiParam (请求体) {Number} treeNodes.children.sortOrder
     * @apiParam (请求体) {Array} treeNodes.children.children
     * @apiParamExample 请求体示例
     * {"treeNodes":[{"children":[{"children":[],"sortOrder":663,"id":4439,"label":"0OfqZHA4S"}],"sortOrder":3589,"id":4368,"label":"G6d"}]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"JeXV","description":"ZKMMp","timestampServer":"j9gwRbUU"}
     */
    @PostMapping("/savefirstmenutree")
    @ResponseBody
    public CgiResponse<Body> saveFirstMenuTree(@RequestBody ScriptTreeNodeRequest treedata) {
        assetScriptService.saveFirstMenuTree(treedata);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/batchmoveup batchMoveUp()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName batchMoveUp()
     * @apiDescription 上移接口
     * @apiParam (请求体) {Array} ids 需要被移动的id
     * @apiParam (请求体) {Number} treeId
     * @apiParamExample 请求体示例
     * {"treeId":6115,"ids":[8688]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Ogunnp","description":"Sx","timestampServer":"ZuEW8s2DU"}
     */
    @PostMapping("/batchmoveup")
    @ResponseBody
    public CgiResponse<Body> batchMoveUp(@RequestBody MoveScriptRequest request) {
        assetScriptService.batchMoveUp(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/batchmovedown batchMoveDown()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName batchMoveDown()
     * @apiDescription 下移接口
     * @apiParam (请求体) {Array} ids 需要被移动的id
     * @apiParam (请求体) {Number} treeId
     * @apiParamExample 请求体示例
     * {"treeId":4453,"ids":[1701]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"DS7axdJx","description":"2bgpHyy","timestampServer":"uF"}
     */
    @PostMapping("/batchmovedown")
    @ResponseBody
    public CgiResponse<Body> batchMoveDown(@RequestBody MoveScriptRequest request) {
        assetScriptService.batchMoveDown(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/saveassetscript saveAssetScript()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName saveAssetScript()
     * @apiDescription 保存话术数据
     * @apiParam (请求体) {Number} id
     * @apiParam (请求体) {Number} treeId
     * @apiParam (请求体) {String} title
     * @apiParam (请求体) {String} content
     * @apiParam (请求体) {String} source
     * @apiParam (请求体) {String} recStatus
     * @apiParamExample 请求体示例
     * {"treeId":8167,"recStatus":"M7voZvBPZw","id":981,"source":"M1","title":"xP","content":"cmz0cBx9x5"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"pv8wdR5LgX","description":"f9Iwq","timestampServer":"V"}
     */
    @PostMapping("/saveassetscript")
    @ResponseBody
    public CgiResponse<Body> saveAssetScript(@RequestBody CmAssetScriptRequest request) {
        assetScriptService.saveCmAssetScript(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/syncwbdata syncWbMenuAndScripts()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName syncWbMenuAndScripts()
     * @apiDescription 同步ips红宝书数据接口
     * @apiParam (请求体) {Number} id
     * @apiParam (请求体) {Number} treeId
     * @apiParam (请求体) {String} title
     * @apiParam (请求体) {String} content
     * @apiParam (请求体) {String} source
     * @apiParam (请求体) {String} offStatus
     * @apiParam (请求体) {String} recStatus
     * @apiParamExample 请求体示例
     * {"treeId":1575,"offStatus":"QHMkY","recStatus":"t74JYg72f5","id":377,"source":"b4s8","title":"aQDV","content":"DoEcDPx"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ZylliVHee0","description":"SfztB","timestampServer":"h7qfc"}
     */
    @PostMapping("/syncwbdata")
    @ResponseBody
    public CgiResponse<Body> syncWbMenuAndScripts(@RequestBody CmAssetScriptRequest request) {
        assetScriptService.syncWbMenuAndScripts(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /inner/asset/script/querymenutree queryMenuTree()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName queryMenuTree()
     * @apiDescription 查询目录树数据
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.treeNodes 节点树数据
     * @apiSuccess (响应结果) {Number} data.treeNodes.id
     * @apiSuccess (响应结果) {String} data.treeNodes.label
     * @apiSuccess (响应结果) {Array} data.treeNodes.children
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"DpnA2T","data":{"treeNodes":[{"children":[],"id":6932,"label":"XecD4K"}]},"description":"izas","timestampServer":"skT4zVO3"}
     */
    @PostMapping("/querymenutree")
    @ResponseBody
    public CgiResponse<ListMenuTreeVO> queryMenuTree() {
        return CgiResponse.ok(assetScriptService.queryMenuTree());
    }

    /**
     * @api {POST} /inner/asset/script/querynewmenutree queryNewMenuTree()
     * @apiVersion 1.0.0
     * @apiGroup AssetScriptController
     * @apiName queryNewMenuTree()
     * @apiDescription 查询目录树数据
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.treeNodes 节点树数据
     * @apiSuccess (响应结果) {Number} data.treeNodes.id
     * @apiSuccess (响应结果) {String} data.treeNodes.label
     * @apiSuccess (响应结果) {Array} data.treeNodes.children
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"hvfBz5d","data":{"treeNodes":[{"children":[],"id":2557,"label":"6ShN"}]},"description":"QPBV4ch","timestampServer":"0Gxug3XiLl"}
     */
    @PostMapping("/querynewmenutree")
    @ResponseBody
    public CgiResponse<ListMenuTreeVO> queryNewMenuTree() {
        return CgiResponse.ok(assetScriptService.queryNewMenuTree());
    }


}