/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.AssetAdjustFundInfoListDTO;
import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/4/1 11:10
 * @since JDK 1.8
 */
@Setter
@Getter
public class AdjustFundInfoListVO extends Body implements Serializable {

    /**
     * 调整数据清单对象列表
     */
    private List<AdjustFundInfoVO> adjustFundInfoList;


    public static AdjustFundInfoListVO copyByDto(AssetAdjustFundInfoListDTO data) {
        AdjustFundInfoListVO adjustFundInfoVO = new AdjustFundInfoListVO();
        List<AdjustFundInfoVO> adjustFundInfoList = new ArrayList<>();
        if (data != null && CollectionUtils.isNotEmpty(data.getAdjustFundInfoList())) {
            data.getAdjustFundInfoList().forEach(item -> {
                AdjustFundInfoVO adjustFundInfoVO1 = AdjustFundInfoVO.copyByDTO(item);
                adjustFundInfoList.add(adjustFundInfoVO1);
            });
            adjustFundInfoVO.setAdjustFundInfoList(adjustFundInfoList);
        }

        return adjustFundInfoVO;
    }
}