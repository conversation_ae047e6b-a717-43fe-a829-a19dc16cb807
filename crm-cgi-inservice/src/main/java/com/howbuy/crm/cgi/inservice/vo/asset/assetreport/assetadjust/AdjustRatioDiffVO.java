/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetbalance.AdjustRatioDiffDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/5 15:10
 * @since JDK 1.8
 */
@Setter
@Getter
public class AdjustRatioDiffVO implements Serializable {

    /**
     * 策略类型
     */
    private String assetType;

    /**
     * 国内变化差值比例
     */
    private String gnRatioDiff;

    /**
     * 国外变化差值比例
     */
    private String hwRatioDiff;


    public static List<AdjustRatioDiffVO> copyByDTO(List<AdjustRatioDiffDTO> adjustRatioDiffList) {
        List<AdjustRatioDiffVO> adjustRatioDiffVOS = new ArrayList<>();
        if (adjustRatioDiffList != null) {
            for (AdjustRatioDiffDTO adjustRatioDiffDTO : adjustRatioDiffList) {
                AdjustRatioDiffVO adjustRatioDiffVO = new AdjustRatioDiffVO();
                adjustRatioDiffVO.setAssetType(adjustRatioDiffDTO.getAssetType());
                adjustRatioDiffVO.setGnRatioDiff(adjustRatioDiffDTO.getGnDiffRatio());
                adjustRatioDiffVO.setHwRatioDiff(adjustRatioDiffDTO.getHwDiffRatio());
                adjustRatioDiffVOS.add(adjustRatioDiffVO);
            }
        }
        return adjustRatioDiffVOS;
    }
}