package com.howbuy.crm.cgi.inservice.request.customer;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 修改主管请求
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class UpdateManageRequest {
    
    /**
     * 拜访纪要ID
     */
    private List<String> visitMinutesIds;
    
    /**
     * 是否清空原有人员
     */
    private Boolean isClear;
    
    /**
     * 新用户ID，不清空时必填
     */
    private String newUserId;
} 