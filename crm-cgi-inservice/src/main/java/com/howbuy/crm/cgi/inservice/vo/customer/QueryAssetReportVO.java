package com.howbuy.crm.cgi.inservice.vo.customer;

import com.howbuy.crm.cgi.inservice.vo.asset.assetreport.homepage.PageVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 查询IPS报告列表响应对象
 * @author: jin.wang03
 * @date: 2025-04-09 10:00:00
 */
@Setter
@Getter
public class QueryAssetReportVO extends PageVO {

    /**
     * 报告列表
     */
    private List<AssetReportItemVO> rows;
}

