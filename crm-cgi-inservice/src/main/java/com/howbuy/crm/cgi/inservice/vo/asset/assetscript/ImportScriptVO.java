/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetscript;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/24 16:34
 * @since JDK 1.8
 */
@Data
public class ImportScriptVO implements Serializable {
    @ExcelProperty(value = "一级分组（必填）", index = 0)
    private String firstNode;

    @ExcelProperty(value = "二级分组（选填）", index = 1)
    private String secondNode;

    @ExcelProperty(value = "三级分组（选填）", index = 2)
    private String thirdNode;

    @ExcelProperty(value = "标题(100个字以内)", index = 3)
    private String title;

    @ExcelProperty(value = "话术内容(1300个字以内)", index = 4)
    private String content;
}