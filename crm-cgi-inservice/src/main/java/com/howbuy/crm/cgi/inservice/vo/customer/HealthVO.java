package com.howbuy.crm.cgi.inservice.vo.customer;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 客户健康度响应
 * @author: jian<PERSON><PERSON>.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class HealthVO extends Body {
    
    /**
     * 客户当天存量
     */
    private String marketVal;
    
    /**
     * 综合健康度
     */
    private String healthAvgStar;
} 