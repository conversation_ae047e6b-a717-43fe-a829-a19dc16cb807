package com.howbuy.crm.cgi.inservice.request.customer;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 保存拜访纪要反馈请求
 * @author: jianji<PERSON>.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class SaveVisitFeedbackRequest {
    
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
    
    /**
     * 陪访人数据ID
     */
    private String accompanyingId;
    
    /**
     * 反馈类型 1:陪访人反馈 2:主管反馈
     */
    private String feedbackType;
    
    /**
     * 陪访概要
     */
    private String summary;
    
    /**
     * 工作建议
     */
    private String suggestion;
} 