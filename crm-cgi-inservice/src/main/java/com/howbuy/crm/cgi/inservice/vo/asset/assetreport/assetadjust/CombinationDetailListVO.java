/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetbalance.CombinationDetailListDTO;
import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询持仓公募组合明细数据响应
 * @date 2025/3/6 17:30
 * @since JDK 1.8
 */
@Setter
@Getter
public class CombinationDetailListVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组合基金名称
     */
    private String fundName;

    /**
     * 组合基金成分列表
     */
    private List<CombinationFundVO> combinationFundList;

    /**
     * 组合基金成分数据对象
     */
    @Setter
    @Getter
    public static class CombinationFundVO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 成分基金名称
         */
        private String combiationFundName;

        /**
         * 成分基金代码
         */
        private String combiationFundCode;

        /**
         * 策略类型
         */
        private String assetType;

        /**
         * 是否含海外资产 0:否 1:是
         */
        private String isOverseas;

        /**
         * 持仓市值(万元)
         */
        private BigDecimal balanceAmt;
    }

    public static CombinationDetailListVO copyByDTO(CombinationDetailListDTO combinationDetailListDTO) {
        CombinationDetailListVO combinationDetailListVO = new CombinationDetailListVO();

        combinationDetailListVO.setFundName(combinationDetailListDTO.getFundName());
        combinationDetailListVO.setCombinationFundList(copyToListVo(combinationDetailListDTO.getCombinationFundList()));

        return combinationDetailListVO;
    }

    private static List<CombinationFundVO> copyToListVo(List<CombinationDetailListDTO.CombinationFundDTO> combinationFundList) {
        List<CombinationFundVO> combinationFundVOList = new ArrayList<>();
        for (CombinationDetailListDTO.CombinationFundDTO combinationFundDTO : combinationFundList) {
            CombinationFundVO combinationFundVO = new CombinationFundVO();
            combinationFundVO.setCombiationFundName(combinationFundDTO.getCombiationFundName());
            combinationFundVO.setCombiationFundCode(combinationFundDTO.getCombiationFundCode());
            combinationFundVO.setAssetType(combinationFundDTO.getAssetType());
            combinationFundVO.setIsOverseas(combinationFundDTO.getIsOverSea());
            combinationFundVO.setBalanceAmt(combinationFundDTO.getBalanceAmt());
            combinationFundVOList.add(combinationFundVO);
        }
        return combinationFundVOList;
    }
} 