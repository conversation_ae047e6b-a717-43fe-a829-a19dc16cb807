/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.request.asset.assetscript;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/24 14:58
 * @since JDK 1.8
 */
@Data
public class ScriptTemplateRequest extends BodyRequest {
    /**
     * 导入文件
     */
    private MultipartFile multipartFile;
}