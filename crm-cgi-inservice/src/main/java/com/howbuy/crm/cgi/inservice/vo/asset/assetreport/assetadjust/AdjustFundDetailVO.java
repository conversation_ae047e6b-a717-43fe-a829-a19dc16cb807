/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/4/1 15:37
 * @since JDK 1.8
 */
@Get<PERSON>
@Setter
@ToString
public class AdjustFundDetailVO extends Body {

    /**
     * 调整类型
     */
    private String adjustType;

    /**
     * 持仓金额
     */
    private BigDecimal balanceAmt;

    /**
     * 建议调整金额
     */
    private BigDecimal suggestAmt;

    /**
     * 调整的差值金额
     */
    private BigDecimal diffAmt;

    /**
     * 策略类型
     */
    private String assetType;

    /**
     * 基金类型
     */
    private String fundType;

    /**
     * 基金代码
     */
    private String productCode;

    /**
     * 基金名称
     */
    private String productName;


}