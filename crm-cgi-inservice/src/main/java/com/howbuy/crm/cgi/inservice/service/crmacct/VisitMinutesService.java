package com.howbuy.crm.cgi.inservice.service.crmacct;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.commvisit.FeedbackTypeEnum;
import com.howbuy.crm.auth.dto.CmOperateDTO;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.CommonResultEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.inservice.interceptor.UserThreadLocal;
import com.howbuy.crm.cgi.inservice.request.customer.*;
import com.howbuy.crm.cgi.inservice.vo.common.CmOperateVO;
import com.howbuy.crm.cgi.inservice.vo.customer.*;
import com.howbuy.crm.cgi.inservice.vo.common.ExportToFileVO;
import com.howbuy.crm.cgi.manager.domain.asset.assetreport.homepage.AssetIdDTO;
import com.howbuy.crm.cgi.manager.domain.asset.assetreport.homepage.AssetInfoForVisitDTO;
import com.howbuy.crm.cgi.manager.domain.crmaccount.*;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.asset.assetreport.homepage.AssetPageHomeOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.CmVisitMinutesOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.CommunicateOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 拜访纪要服务
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Slf4j
@Service
public class VisitMinutesService {

    @Autowired
    private CmVisitMinutesOuterService cmVisitMinutesOuterService;
    @Autowired
    private AssetPageHomeOuterService assetPageHomeOuterService;
    @Autowired
    private CommunicateOuterService communicateOuterService;
    @Autowired
    private AuthService authService;
    /**
     * 沟通记录查看编辑权限需要的菜单及操作编码
     */
    private static final String CONSCUST_MENU_CODE = "020107";
    private static final String VISIT_MINUTES_DETAIL_OPERATE_CODE = "401";

    /**
     * @description 查询客户拜访纪要列表
     * @param request 查询请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.VisitMinutesListVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<VisitMinutesListVO> visitMinutesList(VisitMinutesListRequest request) {
        log.info("VisitMinutesService_visitMinutesList_request:{}", JSON.toJSONString(request));

        // 参数校验
        if (request == null) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "请求参数不能为空", null);
        }

        try {
            // 构建查询DTO
            VisitMinutesQueryDTO queryDTO = new VisitMinutesQueryDTO();
            queryDTO.setOrgCode(request.getOrgCode());
            queryDTO.setConsCode(request.getConsCode());
            queryDTO.setVisitDateStart(request.getVisitDateStart());
            queryDTO.setVisitDateEnd(request.getVisitDateEnd());
            queryDTO.setCreateDateStart(request.getCreateDateStart());
            queryDTO.setCreateDateEnd(request.getCreateDateEnd());
            queryDTO.setVisitPurpose(request.getVisitPurpose());
            queryDTO.setCustName(request.getCustName());
            queryDTO.setConsCustNo(request.getConsCustNo());
            queryDTO.setAccompanyingUser(request.getAccompanyingUser());
            queryDTO.setManagerId(request.getManagerId());
            queryDTO.setFeedbackStatus(request.getFeedbackStatus());
            queryDTO.setPageNo(request.getPage());
            queryDTO.setPageSize(request.getSize());
            queryDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());
            
            // 调用外部服务获取拜访纪要列表
            VisitMinutesListDTO listDTO = cmVisitMinutesOuterService.getVisitMinutesList(queryDTO);

            // 转换返回结果
            VisitMinutesListVO result = new VisitMinutesListVO();
            result.setTotal(listDTO.getTotal());
            
            if (CollectionUtils.isNotEmpty(listDTO.getList())) {
                List<VisitMinutesListVO.VisitMinutesItem> voList = new ArrayList<>();
                for (VisitMinutesListDTO.VisitMinutesItemDTO dto : listDTO.getList()) {
                    VisitMinutesListVO.VisitMinutesItem vo = new VisitMinutesListVO.VisitMinutesItem();
                    vo.setId(dto.getId());
                    vo.setCreateTime(dto.getCreateTime());
                    vo.setVisitDt(dto.getVisitDt());
                    vo.setVisitPurpose(dto.getVisitPurpose());
                    vo.setConsCustNo(dto.getConsCustNo());
                    vo.setCustName(dto.getCustName());
                    vo.setCreatorName(dto.getCreatorName());
                    vo.setCenterName(dto.getCenterName());
                    vo.setAreaName(dto.getAreaName());
                    vo.setBranchName(dto.getBranchName());
                    vo.setVisitType(dto.getVisitType());
                    vo.setMarketVal(dto.getMarketVal());
                    vo.setHealthAvgStar(dto.getHealthAvgStar());
                    vo.setGiveInformation(dto.getGiveInformation());
                    vo.setAttendRole(dto.getAttendRole());
                    vo.setProductServiceFeedback(dto.getProductServiceFeedback());
                    vo.setIpsFeedback(dto.getIpsFeedback());
                    vo.setAddAmount(dto.getAddAmount());
                    vo.setFocusAsset(dto.getFocusAsset());
                    vo.setEstimateNeedBusiness(dto.getEstimateNeedBusiness());
                    vo.setNextPlan(dto.getNextPlan());
                    vo.setAccompanyingType(dto.getAccompanyingType());
                    vo.setAccompanyingUser(dto.getAccompanyingUser());
                    vo.setAccompanySummary(dto.getAccompanySummary());
                    vo.setAccompanySuggestion(dto.getAccompanySuggestion());
                    vo.setManagerName(dto.getManagerName());
                    vo.setManagerSummary(dto.getManagerSummary());
                    vo.setManagerSuggestion(dto.getManagerSuggestion());
                    vo.setCanAccompanyFeedback(dto.getCanAccompanyFeedback());
                    vo.setCanManagerFeedback(dto.getCanManagerFeedback());
                    voList.add(vo);
                }
                result.setList(voList);
            }
            
            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("获取拜访纪要列表异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "获取拜访纪要列表异常", null);
        }
    }

    /**
     * @description 查询客户拜访纪要反馈明细
     * @param request 查询请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.VisitFeedbackDetailVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<VisitFeedbackDetailVO> visitFeedbackDetail(VisitFeedbackDetailRequest request) {
        log.info("VisitMinutesService_visitFeedbackDetail_request:{}", JSON.toJSONString(request));
        
        // 参数校验
        if (request == null || StringUtils.isBlank(request.getVisitMinutesId())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "拜访纪要ID不能为空", null);
        }

        try {
            // 构建查询DTO
            VisitFeedbackQueryDTO queryDTO = new VisitFeedbackQueryDTO();
            queryDTO.setVisitMinutesId(request.getVisitMinutesId());
            queryDTO.setFeedbackType(request.getFeedbackType());
            queryDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());

            // 调用外部服务获取拜访纪要反馈明细
            VisitFeedbackDetailDTO detailDTO = cmVisitMinutesOuterService.getVisitFeedbackDetail(queryDTO);

            // 转换返回结果
            VisitFeedbackDetailVO result = new VisitFeedbackDetailVO();
            result.setCustName(detailDTO.getCustName());
            result.setConsCustNo(detailDTO.getConsCustNo());
            result.setVisitDt(detailDTO.getVisitDt());
            result.setVisitType(detailDTO.getVisitType());
            result.setMarketVal(detailDTO.getMarketVal());
            result.setHealthAvgStar(detailDTO.getHealthAvgStar());
            result.setVisitPurpose(detailDTO.getVisitPurpose());
            result.setVisitPurposeOther(detailDTO.getVisitPurposeOther());
            result.setIpsReport(convertIpsReport(detailDTO.getReportId()));
            result.setGiveInformation(detailDTO.getGiveInformation());
            result.setAccompanyingUser(detailDTO.getAccompanyingUser());
            result.setAttendRole(detailDTO.getAttendRole());
            result.setProductServiceFeedback(detailDTO.getProductServiceFeedback());
            result.setIpsFeedback(detailDTO.getIpsFeedback());
            result.setAddAmountRmb(detailDTO.getAddAmountRmb());
            result.setAddAmountForeign(detailDTO.getAddAmountForeign());
            result.setFocusAsset(detailDTO.getFocusAsset());
            result.setEstimateNeedBusiness(detailDTO.getEstimateNeedBusiness());
            result.setNextPlan(detailDTO.getNextPlan());
            result.setUserName(detailDTO.getUserName());
            result.setAccompanyingId(detailDTO.getAccompanyingId());
            result.setManagerName(detailDTO.getManagerName());
            result.setSummary(detailDTO.getSummary());
            result.setSuggestion(detailDTO.getSuggestion());
            result.setCanNotEditSummary(detailDTO.getCanNotEditSummary());
            
            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("获取拜访纪要反馈明细异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "获取拜访纪要反馈明细异常", null);
        }
    }

    /**
     * @description 查询客户拜访纪要明细
     * @param request 查询请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.VisitMinutesDetailVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<VisitMinutesDetailVO> visitMinutesDetail(VisitMinutesDetailRequest request) {
        log.info("VisitMinutesService_visitMinutesDetail_request:{}", JSON.toJSONString(request));

        // 参数校验
        if (request == null || StringUtils.isBlank(request.getVisitMinutesId())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "拜访纪要ID不能为空", null);
        }

        try {
            // 构建查询DTO
            VisitMinutesDetailQueryDTO queryDTO = new VisitMinutesDetailQueryDTO();
            queryDTO.setVisitMinutesId(request.getVisitMinutesId());

            // 调用外部服务获取拜访纪要明细
            VisitMinutesDetailDTO detailDTO = cmVisitMinutesOuterService.getVisitMinutesDetail(queryDTO);

            // 转换返回结果
            VisitMinutesDetailVO result = new VisitMinutesDetailVO();
            result.setCustName(detailDTO.getCustName());
            result.setConsCustNo(detailDTO.getConsCustNo());
            result.setVisitDt(detailDTO.getVisitDt());
            result.setVisitType(detailDTO.getVisitType());
            result.setMarketVal(detailDTO.getMarketVal());
            result.setHealthAvgStar(detailDTO.getHealthAvgStar());
            result.setVisitPurpose(detailDTO.getVisitPurposeList());
            result.setVisitPurposeOther(detailDTO.getVisitPurposeOther());
            // 转换IPS报告
            result.setIpsReport(convertIpsReport(detailDTO.getReportId()));
            result.setGiveInformation(detailDTO.getGiveInformation());
            result.setAccompanyingUser(detailDTO.getAccompanyingUser());
            result.setAttendRole(detailDTO.getAttendRole());
            result.setProductServiceFeedback(detailDTO.getProductServiceFeedback());
            result.setIpsFeedback(detailDTO.getIpsFeedback());
            result.setRmb(detailDTO.getAddAmountRmb());
            result.setForeign(detailDTO.getAddAmountForeign());
            result.setFocusAsset(detailDTO.getFocusAsset());
            result.setEstimateNeedBusiness(detailDTO.getEstimateNeedBusiness());
            result.setNextPlan(detailDTO.getNextPlan());
            result.setAccompanyingList(convertAccompanyingList(detailDTO.getAccompanyingList()));
            // 是否可编辑
            result.setCanEditData(isCanEdit(detailDTO.getCanEdit()));
            if(result.getCanEditData() && StringUtils.isBlank(detailDTO.getAccompanyingUser())) {
                //陪访人需要填时需要的数据
                VisitInitDTO dto = communicateOuterService.getVisitInitData(UserThreadLocal.getCurrentUserId());
                convertToDetailVO(dto, result);
            }

            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("获取拜访纪要明细异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "获取拜访纪要明细异常", null);
        }
    }

    /**
     * @description 在判断了业务权限后再判断操作权限
     * @param canEditData 判断了的业务权限
     * @return boolean
     * @author: jianjian.yang
     * @date: 2025/4/27 17:46
     * @since JDK 1.8
     */
    private boolean isCanEdit(boolean canEditData) {
        if(!canEditData){
            return false;
        }
        List<CmOperateVO> cmOperateVOS = authService.getMenuOperateAuthList(CONSCUST_MENU_CODE, UserThreadLocal.getCurrentUserId());
        if(CollectionUtils.isNotEmpty(cmOperateVOS)){
            return cmOperateVOS.stream().anyMatch(cmOperateVO -> VISIT_MINUTES_DETAIL_OPERATE_CODE.equals(cmOperateVO.getOperateCode())
                    && YesNoEnum.YES.getCode().equals(cmOperateVO.getDisplay()));
        }
        return false;
    }

    /**
     * @description 转换IPS报告
     * @param reportId IPS报告ID
     * @return com.howbuy.crm.cgi.inservice.vo.customer.VisitMinutesDetailVO.IpsReport
     * @author: jianjian.yang
     * @date: 2025/4/17 13:34
     * @since JDK 1.8
     */ 
    private VisitMinutesDetailVO.IpsReport convertIpsReport(String reportId) {
        if(StringUtils.isBlank(reportId)){
            return null;
        }
        AssetIdDTO assetIdDTO = new AssetIdDTO();
        assetIdDTO.setAssetId(reportId);
        CrmResult<AssetInfoForVisitDTO> crmResult = assetPageHomeOuterService.getAssetInfoForVisit(assetIdDTO);
        VisitMinutesDetailVO.IpsReport ipsReport = new VisitMinutesDetailVO.IpsReport();
        ipsReport.setReportId(reportId);
        if (crmResult != null) {
            AssetInfoForVisitDTO assetInfo = crmResult.getData();
            ipsReport.setReportTitle(assetInfo.getReportTitle());
        }
        return ipsReport;
    }
    
    /** 
     * @description 转换陪访人/主管反馈列表
     * @param dtoList 陪访人/主管反馈列表VO
     * @return java.util.List<com.howbuy.crm.cgi.inservice.vo.customer.VisitMinutesDetailVO.AccompanyingInfo>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */
    private List<VisitMinutesDetailVO.AccompanyingInfo> convertAccompanyingList(List<AccompanyingDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        return dtoList.stream().map(this::convertAccompanying).collect(Collectors.toList());
    }

    /**
     * @description 转换陪访人/主管反馈
     * @param dto 陪访人/主管反馈DTO
     * @return com.howbuy.crm.cgi.inservice.vo.customer.VisitMinutesDetailVO.AccompanyingInfo
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */
    private VisitMinutesDetailVO.AccompanyingInfo convertAccompanying(AccompanyingDTO dto) {
        VisitMinutesDetailVO.AccompanyingInfo result = new VisitMinutesDetailVO.AccompanyingInfo();
        result.setUserName(dto.getUserName());
        result.setManagerName(dto.getManagerName());
        result.setSummary(dto.getSummary());
        result.setSuggestion(dto.getSuggestion());
        return result;
    }
    /**
     * 将DTO对象转换为VO对象
     *
     * @param dto DTO对象
     * @param vo
     * @return VO对象
     */
    private void convertToDetailVO(VisitInitDTO dto, VisitMinutesDetailVO vo) {
        vo.setProjectManagerUserList(convertUserList(dto.getProjectManagerUserList()));
        vo.setManageUserList(convertUserList(dto.getManageUserList()));
        vo.setSupervisorUserList(convertUserList(dto.getSupervisorUserList()));
        vo.setOtherUserList(convertUserList(dto.getOtherUserList()));
    }

    /**
     * 转换用户列表
     *
     * @param dtoList DTO用户列表
     * @return VO用户列表
     */
    private List<UserInfoVO> convertUserList(List<UserInfoDTO> dtoList) {
        List<UserInfoVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return result;
        }

        for (UserInfoDTO dto : dtoList) {
            UserInfoVO vo = new UserInfoVO();
            vo.setCode(dto.getCode());
            vo.setName(dto.getName());
            result.add(vo);
        }
        return result;
    }

    /**
     * @description 保存陪访人/主管反馈
     * @param request 保存请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.SaveVisitFeedbackVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<SaveVisitFeedbackVO> saveFeedback(SaveVisitFeedbackRequest request) {
        log.info("VisitMinutesService_saveFeedback_request:{}", JSON.toJSONString(request));

        // 参数校验
        if (request == null || StringUtils.isBlank(request.getVisitMinutesId())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "拜访纪要ID不能为空", null);
        }
        if (StringUtils.isBlank(request.getFeedbackType())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "反馈类型不能为空", null);
        }
        if (StringUtils.isBlank(request.getSuggestion())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "工作建议不能为空", null);
        }
        if (FeedbackTypeEnum.ACCOMPANYING.getCode().equals(request.getFeedbackType()) && StringUtils.isBlank(request.getAccompanyingId())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "陪访人反馈时陪访人数据ID不能为空", null);
        }

        try {
            // 构建保存DTO
            SaveVisitFeedbackDTO saveDTO = new SaveVisitFeedbackDTO();
            saveDTO.setVisitMinutesId(request.getVisitMinutesId());
            saveDTO.setAccompanyingId(request.getAccompanyingId());
            saveDTO.setFeedbackType(request.getFeedbackType());
            saveDTO.setSummary(request.getSummary());
            saveDTO.setSuggestion(request.getSuggestion());
            saveDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());

            // 调用外部服务保存反馈
            String verifyMsg = cmVisitMinutesOuterService.saveFeedback(saveDTO);

            // 构造返回结果
            SaveVisitFeedbackVO result = new SaveVisitFeedbackVO();
            result.setVerifyMsg(verifyMsg);

            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("保存拜访纪要反馈异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "保存拜访纪要反馈异常", null);
        }
    }

    /**
     * @description 导出客户拜访纪要列表
     * @param request 导出请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.common.ExportToFileVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<ExportToFileVO> exportVisitMinutes(ExportVisitMinutesRequest request) {
        log.info("VisitMinutesService_exportVisitMinutes_request:{}", JSON.toJSONString(request));

        // 参数校验
        if (request == null) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "请求参数不能为空", null);
        }

        try {
            // 构建导出DTO
            ExportVisitMinutesDTO exportDTO = new ExportVisitMinutesDTO();
            exportDTO.setOrgCode(request.getOrgCode());
            exportDTO.setConsCode(request.getConsCode());
            exportDTO.setVisitDateStart(request.getVisitDateStart());
            exportDTO.setVisitDateEnd(request.getVisitDateEnd());
            exportDTO.setCreateDateStart(request.getCreateDateStart());
            exportDTO.setCreateDateEnd(request.getCreateDateEnd());
            exportDTO.setVisitPurpose(request.getVisitPurpose());
            exportDTO.setCustName(request.getCustName());
            exportDTO.setConsCustNo(request.getConsCustNo());
            exportDTO.setAccompanyingUser(request.getAccompanyingUser());
            exportDTO.setManagerId(request.getManagerId());
            exportDTO.setFeedbackStatus(request.getFeedbackStatus());
            exportDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());

            // 调用防腐层接口导出数据
            ExportToFileDTO exportToFileDTO = cmVisitMinutesOuterService.exportVisitMinutes(exportDTO);
            log.info("VisitMinutesService_exportVisitMinutes_response:{}", JSON.toJSONString(exportToFileDTO));

            // 转换返回结果
            ExportToFileVO result = new ExportToFileVO();
            result.setFileByte(exportToFileDTO.getFileByte());
            result.setName(exportToFileDTO.getName());
            result.setType(exportToFileDTO.getType());
            result.setErrorMsg(exportToFileDTO.getErrorMsg());
            
            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("导出客户拜访纪要列表异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "导出客户拜访纪要列表异常", null);
        }
    }


    /**
     * @description 保存客户反馈
     * @param request 保存请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.SaveCustFeedbackVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<SaveCustFeedbackVO> saveCustFeedback(SaveCustFeedbackRequest request) {
        log.info("VisitMinutesService_saveCustFeedback_request:{}", JSON.toJSONString(request));
        
        // 参数校验
        if (request == null || StringUtils.isBlank(request.getVisitMinutesId())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "拜访纪要ID不能为空", null);
        }

        try {
            // 构建保存DTO
            SaveCustFeedbackDTO saveDTO = new SaveCustFeedbackDTO();
            saveDTO.setVisitMinutesId(request.getVisitMinutesId());
            saveDTO.setAccompanyingList(convertAccompanyingDtoList(request.getAccompanyingList()));
            saveDTO.setAttendRole(request.getAttendRole());
            saveDTO.setProductServiceFeedback(request.getProductServiceFeedback());
            saveDTO.setIpsFeedback(request.getIpsFeedback());
            saveDTO.setAddAmtRmb(request.getAddAmtRmb());
            saveDTO.setAddAmtForeign(request.getAddAmtForeign());
            saveDTO.setFocusAsset(request.getFocusAsset());
            saveDTO.setEstimateNeedBusiness(request.getEstimateNeedBusiness());
            saveDTO.setNextPlan(request.getNextPlan());
            saveDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());
            
            // 调用外部服务保存反馈
            String verifyMsg = cmVisitMinutesOuterService.saveCustFeedback(saveDTO);

            // 构造返回结果
            SaveCustFeedbackVO result = new SaveCustFeedbackVO();
            result.setVerifyMsg(verifyMsg);
            
            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("保存客户反馈异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "保存客户反馈异常", null);
        }
    }

    /** 
     * @description 转换客户反馈
     * @param dtoList 客户反馈DTO列表
     * @return java.util.List<com.howbuy.crm.cgi.inservice.vo.customer.SaveCustFeedbackDTO.AccompanyingInfo>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */
    private List<SaveCustFeedbackDTO.AccompanyingInfo> convertAccompanyingDtoList(List<SaveCustFeedbackRequest.AccompanyingInfo> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        List<SaveCustFeedbackDTO.AccompanyingInfo> resultList = new ArrayList<>();
        for (SaveCustFeedbackRequest.AccompanyingInfo dto : dtoList) {
            SaveCustFeedbackDTO.AccompanyingInfo result = new SaveCustFeedbackDTO.AccompanyingInfo();
            result.setAccompanyingType(dto.getAccompanyingType());
            result.setAccompanyingUserId(dto.getAccompanyingUserId());
            resultList.add(result);
        }
        return resultList;
    }

    /**
     * @description 修改主管
     * @param request 修改请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.UpdateManageVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<UpdateManageVO> updateManage(UpdateManageRequest request) {
        log.info("VisitMinutesService_updateManage_request:{}", JSON.toJSONString(request));
        
        // 参数校验
        if (request == null || CollectionUtils.isEmpty(request.getVisitMinutesIds())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "拜访纪要ID不能为空", null);
        }
        if (request.getIsClear() == null) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "是否清空不能为空", null);
        }
        if (!request.getIsClear() && StringUtils.isBlank(request.getNewUserId())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "不清空时新用户ID不能为空", null);
        }

        try {
            // 构建更新DTO
            UpdateManageDTO updateDTO = new UpdateManageDTO();
            updateDTO.setVisitMinutesIds(request.getVisitMinutesIds());
            updateDTO.setIsClear(request.getIsClear());
            updateDTO.setNewUserId(request.getNewUserId());
            updateDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());
            
            // 调用外部服务更新主管
            String verifyMsg = cmVisitMinutesOuterService.updateManage(updateDTO);

            // 构造返回结果
            UpdateManageVO result = new UpdateManageVO();
            result.setVerifyMsg(verifyMsg);
            
            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("修改主管异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "修改主管异常", null);
        }
    }

    /**
     * @description 用户搜索
     * @param request 搜索请求参数
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<com.howbuy.crm.cgi.inservice.vo.customer.SearchUserVO>
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public CgiResponse<SearchUserVO> searchUser(SearchUserRequest request) {
        log.info("CommunicateService_searchUser_request:{}", JSON.toJSONString(request));

        // 参数校验
        if (request == null || StringUtils.isBlank(request.getSearchParam())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "搜索参数不能为空", null);
        }
        if (StringUtils.isBlank(request.getSearchType())) {
            return CgiResponse.error(CommonResultEnum.PARAM_ERROR.getCode(), "搜索类型不能为空", null);
        }

        try {
            // 构建查询DTO
            SearchUserParamDTO searchUserParamDTO = new SearchUserParamDTO();
            searchUserParamDTO.setSearchParam(request.getSearchParam());
            searchUserParamDTO.setSearchType(request.getSearchType());
            searchUserParamDTO.setCurrentUserId(UserThreadLocal.getCurrentUserId());

            // 调用外部服务搜索用户
            SearchUserDTO resultDTO = cmVisitMinutesOuterService.searchUser(searchUserParamDTO);

            // 转换返回结果
            SearchUserVO result = new SearchUserVO();
            if (resultDTO != null && CollectionUtils.isNotEmpty(resultDTO.getUserList())) {
                List<SearchUserVO.UserItem> userList = Lists.newArrayList();
                for (SearchUserDTO.UserItem item : resultDTO.getUserList()) {
                    SearchUserVO.UserItem userItem = new SearchUserVO.UserItem();
                    userItem.setUserId(item.getUserId());
                    userItem.setUserName(item.getUserName());
                    userList.add(userItem);
                }
                result.setUserList(userList);
            }

            return CgiResponse.ok(result);
        } catch (Exception e) {
            log.error("用户搜索异常，request:{}", JSON.toJSONString(request), e);
            return CgiResponse.error(CommonResultEnum.FAIL.getCode(), "用户搜索异常", null);
        }
    }
}