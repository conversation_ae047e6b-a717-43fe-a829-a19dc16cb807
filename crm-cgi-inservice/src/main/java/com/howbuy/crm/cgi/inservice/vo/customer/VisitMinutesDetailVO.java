package com.howbuy.crm.cgi.inservice.vo.customer;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * @description: 拜访纪要明细响应
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Getter
@Setter
public class VisitMinutesDetailVO extends Body {
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 拜访日期，格式YYYYMMDD
     */
    private String visitDt;
    
    /**
     * 沟通方式
     */
    private String visitType;
    
    /**
     * 客户存量
     */
    private String marketVal;
    
    /**
     * 客户综合健康度
     */
    private String healthAvgStar;
    
    /**
     * 拜访目的列表
     */
    private List<String> visitPurpose;
    
    /**
     * 拜访目的其他说明
     */
    private String visitPurposeOther;
    
    /**
     * IPS报告信息
     */
    private IpsReport ipsReport;
    
    /**
     * 提供资料
     */
    private String giveInformation;
    
    /**
     * 陪访人姓名，多个逗号分隔
     */
    private String accompanyingUser;
    
    /**
     * 参与人员及角色
     */
    private String attendRole;
    
    /**
     * 产品服务反馈
     */
    private String productServiceFeedback;
    
    /**
     * IPS报告反馈
     */
    private String ipsFeedback;
    
    /**
     * 人民币金额
     */
    private String rmb;
    
    /**
     * 外币金额
     */
    private String foreign;
    
    /**
     * 关注资产
     */
    private String focusAsset;
    
    /**
     * 客户需求
     */
    private String estimateNeedBusiness;
    
    /**
     * 工作计划
     */
    private String nextPlan;
    
    /**
     * 陪访人列表
     */
    private List<AccompanyingInfo> accompanyingList;
    
    /**
     * 是否可编辑
     */
    private Boolean canEditData;

    /**
     * 项目经理列表
     */
    private List<UserInfoVO> projectManagerUserList;
    /**
     * 主管列表
     */
    private List<UserInfoVO> manageUserList;

    /**
     * 总部业资列表
     */
    private List<UserInfoVO> supervisorUserList;

    /**
     * 其他列表
     */
    private List<UserInfoVO> otherUserList;
    
    @Getter
    @Setter
    public static class VisitPurpose {
        /**
         * 拜访目的编码
         */
        private String code;
    }
    
    @Getter
    @Setter
    public static class IpsReport {
        /**
         * 报告ID
         */
        private String reportId;
        
        /**
         * 报告标题
         */
        private String reportTitle;
    }
    
    @Getter
    @Setter
    public static class AccompanyingInfo {
        /**
         * 陪访人姓名
         */
        private String userName;
        
        /**
         * 主管姓名
         */
        private String managerName;
        
        /**
         * 主管概要
         */
        private String summary;
        
        /**
         * 主管建议
         */
        private String suggestion;
    }
} 