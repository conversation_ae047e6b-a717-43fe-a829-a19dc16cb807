package com.howbuy.crm.cgi.inservice.vo.asset.assetscript;

import com.howbuy.crm.cgi.inservice.vo.asset.assetreport.homepage.PageVO;
import lombok.Data;

import java.util.Date;

@Data
public class AssetScriptVO extends PageVO {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 目录树节点id
     */
    private Integer treeId;

    /**
     * 节点树名称
     */
    private String treeLabel;

    /**
     * 标题
     */
    private String title;

    /**
     * 话术内容
     */
    private String content;

    /**
     * 来源 0: 自己配置 1:微伴红宝书
     */
    private String source;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 有效状态
     */
    private String recStatus;

    /**
     * 上下线状态
     */
    private String offStatus;

    /**
     * 是否可以上移
     */
    private String canMoveUp;

    /**
     * 是否可以下移
     */
    private String canMoveDown;



}
