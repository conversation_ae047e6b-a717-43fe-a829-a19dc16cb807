package com.howbuy.crm.cgi.inservice.vo.asset.assetscript;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.howbuy.crm.cgi.inservice.common.enums.FileCheckEnum;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class UploadListener extends AnalysisEventListener<ImportScriptVO> {
        private final List<ImportScriptVO> dataModels = new ArrayList<>();
        private final List<String> errors = new ArrayList<>();


        @Override
        public void invoke(ImportScriptVO data, AnalysisContext context) {
            // 校验数据是否有空值
            if (StringUtils.isEmpty(data.getFirstNode())) {
                errors.add(FileCheckEnum.IS_NULL.getCode());
            }
            if (StringUtils.isEmpty(data.getContent())) {
                errors.add(FileCheckEnum.IS_NULL.getCode());
            }
            if (StringUtils.isEmpty(data.getTitle())) {
                errors.add(FileCheckEnum.IS_NULL.getCode());
            }
            // 如果没有错误，添加到数据列表
            if (errors.isEmpty()) {
                dataModels.add(data);
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            try {
                boolean flag = true;
                Field[] declaredFields = ImportScriptVO.class.getDeclaredFields();
                if (declaredFields.length == headMap.size()) {
                    for (Field field : declaredFields) {
                        ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                        int index = annotation.index();
                        if ((null == headMap.get(index) || (!headMap.get(index).equals(annotation.value()[0])))) {
                            flag = false;
                            break;
                        }
                    }
                } else {
                    throw new RuntimeException("上传失败，请使用模板后再导入~");
                }
                if (!flag) {
                    throw new RuntimeException("上传失败，请使用模板后再导入~");
                }
            } catch (RuntimeException e) {
                throw new RuntimeException("上传失败，请使用模板后再导入~");
            }
        }

        public List<ImportScriptVO> getDataModels() {
            return dataModels;
        }

        public List<String> getErrors() {
            return errors;
        }
    }