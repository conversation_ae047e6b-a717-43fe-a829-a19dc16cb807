/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.dtmsorder;

import com.howbuy.crm.cgi.common.base.Body;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/9 16:03
 * @since JDK 1.8
 */
public class SellDealOrderPdfVO extends Body {


    private static final long serialVersionUID = 3772269013390733703L;


    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户名称
     */
    private String custName;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品份额
     */
    private String productVol;

    /**
     * 赎回方式
     */
    private String redeemType;

    /**
     * 赎回方向
     */
    private List<String> redeemDirectionList;
    /**
     * 银行展示名称
     */
    private String bankDisName;


    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 账户币种
     */
    private List<AccountCurrency> accountCurrency;
    
    /**
     * 赎回金额
     */
    private String redeemAmt;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 赎回份额
     */
    private String redeemVol;

    /**
     * 交易日期
     */
    private String tradeDt;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 账户币种实体类
     */
    public static class AccountCurrency implements Serializable {
        private static final long serialVersionUID = 5180299994606244482L;

        /**
         * 币种
         */
        private String currency;

        /**
         * 币种描述
         */
        private String currencyDesc;

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getCurrencyDesc() {
            return currencyDesc;
        }

        public void setCurrencyDesc(String currencyDesc) {
            this.currencyDesc = currencyDesc;
        }
    }


    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductVol() {
        return productVol;
    }

    public void setProductVol(String productVol) {
        this.productVol = productVol;
    }

    public String getRedeemAmt() {
        return redeemAmt;
    }

    public void setRedeemAmt(String redeemAmt) {
        this.redeemAmt = redeemAmt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyDesc() {
        return currencyDesc;
    }

    public void setCurrencyDesc(String currencyDesc) {
        this.currencyDesc = currencyDesc;
    }

    public String getRedeemVol() {
        return redeemVol;
    }

    public void setRedeemVol(String redeemVol) {
        this.redeemVol = redeemVol;
    }

    public String getRedeemType() {
        return redeemType;
    }

    public void setRedeemType(String redeemType) {
        this.redeemType = redeemType;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getBankDisName() {
        return bankDisName;
    }

    public void setBankDisName(String bankDisName) {
        this.bankDisName = bankDisName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public List<AccountCurrency> getAccountCurrency() {
        return accountCurrency;
    }

    public void setAccountCurrency(List<AccountCurrency> accountCurrency) {
        this.accountCurrency = accountCurrency;
    }

    public List<String> getRedeemDirectionList() {
        return redeemDirectionList;
    }

    public void setRedeemDirectionList(List<String> redeemDirectionList) {
        this.redeemDirectionList = redeemDirectionList;
    }
}