/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.request.asset.assetreport.assetbalance;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/23 11:20
 * @since JDK 1.8
 */
@Data
public class QueryIsMatchRiskLevelRequeset extends BodyRequest {

    /**
     * 投顾客户号
     */
    private String conscustNo;

    /**
     * 资配报告Id
     */
    private String assetId;
}