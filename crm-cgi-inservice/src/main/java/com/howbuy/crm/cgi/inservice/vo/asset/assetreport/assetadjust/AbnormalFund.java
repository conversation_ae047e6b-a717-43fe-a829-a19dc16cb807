/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.AbnormalFundDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/1/20 10:50
 * @since JDK 1.8
 */
@Data
public class AbnormalFund {

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 替换的基金/指数代码
     */
    private String replaceCode;

    /**
     * 默认替换数据的对象
     */
    private DefaultReaplceVO defaultRepalceObj;

    public static List<AbnormalFund> copyByDto(List<AbnormalFundDTO> fundDTOList) {
        List<AbnormalFund> fundVOList = new ArrayList<>();
        fundDTOList.forEach(fundDTO -> {
            AbnormalFund fundVO = new AbnormalFund();
            fundVO.setFundCode(fundDTO.getFundCode());
            fundVO.setFundName(fundDTO.getFundName());
            fundVO.setReplaceCode(fundDTO.getReplaceCode());
            fundVO.setDefaultRepalceObj(DefaultReaplceVO.copyByDto(fundDTO.getDefaultRepalceObj()));
            fundVOList.add(fundVO);
        });

        return fundVOList;
    }


}