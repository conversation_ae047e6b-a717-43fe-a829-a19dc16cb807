package com.howbuy.crm.cgi.inservice.request.customer;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.inservice.request.asset.assetreport.homepage.PageRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 查询IPS报告列表请求参数
 * @author: jin.wang03
 * @date: 2025-04-09 10:00:00
 */
@Setter
@Getter
public class QueryAssetReportRequest extends PageRequest {
    
    /**
     * 客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户号", isRequired = true)
    private String consCustNo;

    /**
     * 投顾编码
     */
    private String consCode;

} 