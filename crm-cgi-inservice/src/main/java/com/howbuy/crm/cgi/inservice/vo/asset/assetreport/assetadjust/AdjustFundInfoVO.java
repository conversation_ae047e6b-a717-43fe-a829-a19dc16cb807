/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.asset.assetreport.assetadjust;

import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.AdjustFundInfoDTO;
import com.howbuy.crm.asset.client.domain.assetreport.assetadjust.AssetAdjustFundInfoListDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/3/10 10:26
 * @since JDK 1.8
 */
@Setter
@Getter
public class AdjustFundInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调整类型 1:增持 2:减持
     */
    private String type;

    /**
     * 基金数据列表
     */
    private List<FundVO> fundList;

    public static AdjustFundInfoVO copyByDTO(AdjustFundInfoDTO item) {
        AdjustFundInfoVO adjustFundInfoVO = new AdjustFundInfoVO();
        if (item != null) {

            adjustFundInfoVO.setType(item.getType());
            adjustFundInfoVO.setFundList(FundVO.copyByDTO(item.getFundList()));
        }
        return adjustFundInfoVO;
    }

    /**
     * 基金数据对象
     */
    @Setter
    @Getter
    public static class FundVO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 当前修改基金id
         */
        private String fundId;

        /**
         * 策略类型
         */
        private String assetType;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 套餐id数据
         */
        private Integer fofId;

        /**
         * 用于展示的fofid字段
         */
        private Integer showFofId;

        /**
         * 调整金额
         */
        private BigDecimal adjustAmt;

        public static List<FundVO> copyByDTO(List<AdjustFundInfoDTO.FundDTO> fundList) {
            List<FundVO> fundVOList = new ArrayList<>();
            if (fundList != null) {

                fundList.forEach(fundDTO -> {
                    FundVO fundVO = new FundVO();
                    fundVO.setFundId(fundDTO.getFundId());
                    fundVO.setAssetType(fundDTO.getAssetType());
                    fundVO.setFundCode(fundDTO.getFundCode());
                    fundVO.setFundName(fundDTO.getFundName());
                    fundVO.setFofId(fundDTO.getFofId());
                    fundVO.setShowFofId(fundDTO.getShowFofId());
                    fundVO.setAdjustAmt(fundDTO.getAdjustAmt());
                    fundVOList.add(fundVO);
                });
            }
            return fundVOList;
        }

    }

}