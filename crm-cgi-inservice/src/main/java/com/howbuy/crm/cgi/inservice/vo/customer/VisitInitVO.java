/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.vo.customer;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 客户沟通记录初始化响应对象
 * @author: jin.wang03
 * @date: 2025-04-08 19:27:00
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class VisitInitVO extends Body implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目经理列表
     */
    private List<UserInfoVO> projectManagerUserList;

    /**
     * 主管列表
     */
    private List<UserInfoVO> manageUserList;

    /**
     * 总部业资列表
     */
    private List<UserInfoVO> supervisorUserList;

    /**
     * 其他列表
     */
    private List<UserInfoVO> otherUserList;
} 