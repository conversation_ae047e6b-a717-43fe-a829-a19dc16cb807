/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.inservice.request.asset.assetreport.assetadjust;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/1/20 10:34
 * @since JDK 1.8
 */
@Data
public class AbnormalFundRequest implements Serializable {

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 替换的基金/指数代码
     */
    private String replaceCode;

    /**
     * 替换方式 1: 旗舰基金 2: 指数
     */
    private String replaceType;

    /**
     * 是否进行替换 0:不替换 1: 替换
     */
    private String isReplace;

    /**
     * 是否剔除 0: 不剔除，1:剔除
     */
    private String isDelete;

}