package com.howbuy.crm.cgi.inservice.request.customer;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 新增客户沟通记录请求
 * @author: jin.wang03
 * @date: 2025-04-08 19:27:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class AddCommunicateRequest implements Serializable {

    /**
     * 沟通记录信息
     */
    private CommunicateReq communicateReq;
    
    /**
     * 预约信息
     */
    private BookingReq bookingReq;
    
    /**
     * 拜访纪要信息
     */
    private VisitMinutesReq visitMinutesReq;

    /**
     * 操作人ID
     */
    private String operator;


    @Getter
    @Setter
    public static class AccompanyingReq {
        /**
         * 陪访人类型(1-项目经理 2-主管 3-总部业资 4-其他)
         */
        private String accompanyingType;

        /**
         * 陪访人用户ID
         */
        private String accompanyingUserId;
    }


    @Getter
    @Setter
    public static class BookingReq {
        /**
         * 预约沟通方式
         */
        private String nextvisittype;

        /**
         * 预约日期(格式YYYYMMDD)
         */
        private String nextdt;

        /**
         * 预约开始时间(格式HH:MM)
         */
        private String nextstarttime;

        /**
         * 预约结束时间(格式HH:MM)
         */
        private String nextendtime;

        /**
         * 预约内容
         */
        private String nextvisitcontent;
    }

    @Getter
    @Setter
    public static class CommunicateReq {
        /**
         * 客户号
         */
        private String consCustNo;

        /**
         * 拜访日期(格式YYYYMMDD)
         */
        private String visitDate;

        /**
         * 沟通方式(见面、线上会议、电话等)
         */
        private String visittype;

        /**
         * 沟通内容摘要
         */
        private String commcontent;
    }

    @Getter
    @Setter
    public static class VisitMinutesReq {

        /**
         * 客户存量
         */
        private String marketVal;

        /**
         * 客户目前综合健康度，1-5星
         */
        private String healthAvgStar;

        /**
         * 拜访目的(多选，逗号分隔，如"1,2,3")
         */
        private String visitPurpose;

        /**
         * 拜访目的其他说明
         */
        private String visitPurposeOther;

        /**
         * IPS报告ID
         */
        private String assetReportId;

        /**
         * 提供资料
         */
        private String giveInformation;

        /**
         * 客户参与人员及角色
         */
        private String attendRole;

        /**
         * 对产品或服务的具体反馈
         */
        private String productServiceFeedback;

        /**
         * 对于IPS报告反馈
         */
        private String ipsFeedback;

        /**
         * 近期可用于加仓的金额(人民币)
         */
        private String addAmountRmb;

        /**
         * 近期可用于加仓的金额(外币)
         */
        private String addAmountForeign;

        /**
         * 近期关注的资产类别或具体产品
         */
        private String focusAsset;

        /**
         * 评估客户需求
         */
        private String estimateNeedBusiness;

        /**
         * 下一步工作计划
         */
        private String nextPlan;

        /**
         * 陪访人列表
         */
        private List<AccompanyingReq> accompanyingList;
        
    }

}

