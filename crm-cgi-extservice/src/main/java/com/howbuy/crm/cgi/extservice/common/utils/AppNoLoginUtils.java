/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * @description: 海外APP免登录工具类,只支持DEV和测试环境,不支持产线
 * <AUTHOR>
 * @date 2024/5/13 19:54
 * @since JDK 1.8
 */
public class AppNoLoginUtils {
    private static final String NO_LOGIN = "noLogin";
    
    
    /**
     * @description: 海外APP免登录工具类,只支持DEV和测试环境,不支持产线
     * @param env	当前环境
     * @return boolean
     * @author: jinqing.rao
     * @date: 2024/5/13 19:58
     * @since JDK 1.8
     */
    public static boolean isAppNoLogin(String env) {
        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        String noLoginFlag = httpRequest.getHeader(NO_LOGIN);
        if(StringUtils.isBlank(noLoginFlag) || !YesNoEnum.YES.getCode().equals(noLoginFlag)){
            return false;
        }
        return Constants.DEV.equalsIgnoreCase(env) || Constants.TEST.equalsIgnoreCase(env);
    }
}
