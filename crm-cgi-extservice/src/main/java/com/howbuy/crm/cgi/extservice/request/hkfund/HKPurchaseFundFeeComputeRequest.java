package com.howbuy.crm.cgi.extservice.request.hkfund;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description:  购买基金费率计算请求
 * <AUTHOR>
 * @date 2024/4/9 15:22
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKPurchaseFundFeeComputeRequest extends BodyRequest {

    /**
     *香港客户号
     */
    private String hkCustNo;

    /**
     *基金代码
     */
    @NotBlank(message = "基金编码不能为空")
    private String fundCode;

    /**
     *申购金额
     */
    @NotBlank(message = "申购金额不能为空")
    private String appAmt;

    /**
     * 实缴金额
     */
    private String paidAmt;
}
