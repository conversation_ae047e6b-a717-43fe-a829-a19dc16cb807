/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ContractFileVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenFileVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (合同信息VO
 * <AUTHOR>
 * @date 2024/2/22 18:26
 * @since JDK 1.8
 */
@Data
public class ContractInfoVO extends AccountBaseVO implements Serializable {

    /**
     * ebrokerID
     */
    private String ebrokerID;

    /**
     * 邮箱验码
     */
    private String emailMask;

    /**
     * 是否满足所有条件 (1:满足, 0:不满足)
     */
    private String isSatisfy;

    /**
     * 文件类型 (1: 开户文件 2: 产品合同文件)
      */
    private String fileType;

    /**
     * 开户文件列表
     */
    private List<OpenFileVO> openFileList;

    /**
     * 产品合同文件列表
     */
    private List<ContractFileVO> contractFileList;

}