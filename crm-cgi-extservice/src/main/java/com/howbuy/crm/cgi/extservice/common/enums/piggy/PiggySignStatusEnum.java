package com.howbuy.crm.cgi.extservice.common.enums.piggy;

public enum PiggySignStatusEnum {
    // 海外储蓄罐协议状态 0-未签署、1-已签署、2-已终止，空值转为0-未签署
    UNSIGNED("0", "未签署"),
    SIGNED("1", "已签署"),
    TERMINATED("2", "已终止");

    private final String code;

    private final String desc;

    PiggySignStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
