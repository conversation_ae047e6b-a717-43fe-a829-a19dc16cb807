package com.howbuy.crm.cgi.extservice.service;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.cms.service.base.ICmsRecommendService;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.persistence.cms.recommended.CmsRecommColumn;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description: 银行logo缓存
 * @date 2023/5/23 14:07
 * @since JDK 1.8
 */
@Slf4j
@Service
public class BankLogoService {

    @DubboReference(registry = "howbuy-cms-server", check = false)
    private static ICmsRecommendService iCmsRecommendService;

    protected static CacheService cacheService = CacheServiceImpl.getInstance();

    private static final String bankLogoCacheKey = CacheKeyPrefix.LOCK_KEY_PREFIX + "bankLogo";


    /**
     * @description:(根据银行code获取银行logo)
     * @param bankCode
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2023/6/6 15:36
     * @since JDK 1.8
     */
    public static String getBankLogoUrlByBankCode(String bankCode){
        Map<String,String> bankLogoMap = new HashMap();
        cacheService.initializeKey(bankLogoCacheKey);
        if(cacheService.exists(bankLogoCacheKey)){
            bankLogoMap = cacheService.get(bankLogoCacheKey);
        }else{
            bankLogoMap = getBankLogoMap();
        }
        return bankLogoMap.get(bankCode);
    }

    /**
     * @description:(获取所有银行logo)
     * @param
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: shuai.zhang
     * @date: 2023/6/6 15:55
     * @since JDK 1.8
     */
    public static Map<String,String> getBankLogoMap(){
        Map<String,String> bankLogoMap = new HashMap();
        List<CmsRecommColumn> cmsRecommColumns = iCmsRecommendService.getCmsRecommColumnsByParamer("yhktb");
        if(CollectionUtils.isEmpty(cmsRecommColumns)) {
            return bankLogoMap;
        }
        for (CmsRecommColumn cmsRecommColumn : cmsRecommColumns) {
            List<CmsRecommColumn> cmsRecommColumnList = cmsRecommColumn.getCmsRecommColumnList();
            if (CollectionUtils.isNotEmpty(cmsRecommColumnList)) {
                for (CmsRecommColumn recommColumn : cmsRecommColumnList) {
                    bankLogoMap.put(recommColumn.getName(),recommColumn.getUrlImage());
                }
            }
        }
        cacheService.initializeKey(bankLogoCacheKey);
        cacheService.put(bankLogoCacheKey,bankLogoMap);
        return bankLogoMap;
    }
}
