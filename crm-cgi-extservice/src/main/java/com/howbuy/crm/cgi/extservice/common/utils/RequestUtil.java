/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 请求参数工具类
 * @date 2023/5/17 15:03
 * @since JDK 1.8
 */
@Slf4j
public class RequestUtil {
    private static final String REQUEST_PARAMS = "REQUEST_PARAMS";

    private RequestUtil() {
    }

    /**
     * @param
     * @return javax.servlet.http.HttpServletRequest
     * @description: 获取HttpServletRequest
     * @author: hongdong.xie
     * @date: 2023/5/17 15:22
     * @since JDK 1.8
     */
    public static HttpServletRequest getHttpRequest() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("RequestAttributes is null!");
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) attributes;
        return requestAttributes.getRequest();
    }

    /**
     * 获取请求参数：放在request.params中，且未加密
     *
     * @param name
     * @return
     */
    /**
     * @param name 参数名字
     * @return java.lang.String
     * @description: 获取请求参数：放在request.params中，且未加密
     * @author: hongdong.xie
     * @date: 2023/5/17 15:20
     * @since JDK 1.8
     */
    public static String getHttpParameter(String name) {
        return getHttpRequest().getParameter(name);
    }

    /**
     * @description:(获取请求属性)
     * @param name
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/1/6 17:21
     * @since JDK 1.8
     */
    public static String getHttpAttribute(String name) {
        return (String) getHttpRequest().getAttribute(name);
    }

    /**
     * @param name 参数名
     * @return java.lang.String
     * @description: 获取请求参数：首先查找解密后的参数，再查找request.params
     * @author: hongdong.xie
     * @date: 2023/5/17 15:21
     * @since JDK 1.8
     */
    public static String getParameter(String name) {
        JSONObject jsonObject = getRequestParams();
        String value = null;
        if (jsonObject != null && jsonObject.containsKey(name)) {
            value = jsonObject.getString(name);
        }

        if (StringUtil.isEmpty(value)) {
            value = getHttpParameter(name);
        }

        return value;
    }

    /**
     * @param params 参数
     * @return void
     * @description: 设置请求的参数，包含request.params与解密后的参数
     * @author: hongdong.xie
     * @date: 2023/5/17 15:21
     * @since JDK 1.8
     */
    public static void setRequestParams(JSONObject params) {
        getHttpRequest().setAttribute(REQUEST_PARAMS, params);
    }

    /**
     * @param
     * @return net.sf.json.JSONObject
     * @description: 获得请求的参数，包含request.params与解密后的参数
     * @author: hongdong.xie
     * @date: 2023/5/17 15:21
     * @since JDK 1.8
     */
    public static JSONObject getRequestParams() {
        return (JSONObject) getHttpRequest().getAttribute(REQUEST_PARAMS);
    }

    /**
     * @param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @description: 获取请求链接后拼接的参数
     * @author: hongdong.xie
     * @date: 2023/5/19 15:09
     * @since JDK 1.8
     */
    public static Map<String, String> getQueryStringMap() {
        Map<String, String> params = new HashMap<String, String>();
        HttpServletRequest request = getHttpRequest();
        if (request == null) {
            log.error("http request is null!");
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        String queryString = request.getQueryString();
        if (StringUtil.isEmpty(queryString)) {
            return params;
        }
        String[] arrSplit = queryString.split("&");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = strSplit.split("=");
            String key = null;
            String value = null;
            if (arrSplitEqual.length > 1) {
                key = arrSplitEqual[0];
                value = arrSplitEqual[1];
            }
            if (arrSplitEqual.length == 1 && !StringUtil.isEmpty(arrSplitEqual[0])) {
                key = arrSplitEqual[0];
                value = "";
            }
            try {
                value = URLDecoder.decode(value, Constants.CHARSET);
            } catch (UnsupportedEncodingException e) {
                log.error("network URLDecoder decode is error:", e);
            }
            params.put(key, value);

        }
        return params;
    }

    /**
     * @param fileName 字段名称
     * @return java.lang.String
     * @description: 获取请求头中的公共参数信息
     * @author: jinqing.rao
     * @date: 2024/2/29 14:34
     * @since JDK 1.8
     */
    public static String getPublicParams(String fileName) {
        String headerJson = getHttpRequest().getHeader(Constants.X_COMMON_PARAMS);
        log.info("getPublicParams>>>headerJson:{}", headerJson);
        JSONObject jsonObject = JSONObject.parseObject(headerJson);
        if (null == jsonObject) {
            return null;
        }
        return jsonObject.getString(fileName);
    }

    public static JSONObject getCommonHeaderParams() {
        String headerJson = getHttpRequest().getHeader(Constants.X_COMMON_PARAMS);
        log.info("getPublicParams>>>headerJson:{}", headerJson);
        JSONObject jsonObject = JSONObject.parseObject(headerJson);
        if (null == jsonObject) {
            return new JSONObject();
        }
        return jsonObject;
    }

    /**
     * @param
     * @return boolean
     * @description: 判断是否是海外App的请求
     * @author: jinqing.rao
     * @date: 2024/2/29 18:51
     * @since JDK 1.8
     */
    public static boolean isHkAppLogin() {
        String httpParameter = getHttpParameter(Constants.PRODUCT_ID);
        return StringUtils.isNotEmpty(httpParameter) && Constants.PRODUCT_ID_VALUE.equals(httpParameter);
    }

    /**
     * @param
     * @return boolean
     * @description: 海外App中H5登录
     * @author: jinqing.rao
     * @date: 2024/2/29 18:52
     * @since JDK 1.8
     */
    public static boolean isHkAppH5Login() {
        String productId = getHttpParameter(Constants.PRODUCT_ID);
        String h5Req = getHttpParameter(Constants.H5_REQ);
        boolean appFlag = StringUtils.isNotEmpty(productId) && Constants.PRODUCT_ID_VALUE.equals(productId);
        boolean h5Flag = StringUtils.isNotEmpty(h5Req) && Constants.H5_REQ_VALUE.equals(h5Req);
        return appFlag && h5Flag;
    }

    /**
     * @description: 获取请求的IP
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/3/25 10:34
     * @since JDK 1.8
     */
    public static String getCustIp() {
        return WebUtil.getCustIP(getHttpRequest());
    }
}