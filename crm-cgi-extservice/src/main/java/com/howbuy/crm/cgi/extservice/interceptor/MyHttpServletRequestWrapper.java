package com.howbuy.crm.cgi.extservice.interceptor;

import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;


public class MyHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private String body;

    public MyHttpServletRequestWrapper(HttpServletRequest httpServletRequest) throws IOException {
        super(httpServletRequest);
        if(ServletFileUpload.isMultipartContent(httpServletRequest)){
            return;
        }
        //把流数据读出来，在再次读时获取，这样可以读取多次
        StringBuilder stringBuilder = new StringBuilder();
        try(BufferedReader bufferedReader = httpServletRequest.getReader()) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
            this.body = stringBuilder.toString();
        }
    }

    public String getBody(){
       return this.body;
    }

    @Override
    public ServletInputStream getInputStream(){
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.body.getBytes(StandardCharsets.UTF_8));
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
            }

            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }
        };
    }

    /**
     * The default behavior of this method is to return getReader() on the
     * wrapped request object.
     */
    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    public void setBody(String body) {
        this.body = body;
    }
}

