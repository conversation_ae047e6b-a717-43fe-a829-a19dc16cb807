package com.howbuy.crm.cgi.extservice.request.account.hkapp;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 海外小程序App公告请求对象
 * <AUTHOR>
 * @date 2024/2/22 14:50
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAppAnnouncementRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -6393660396712192511L;

    /**
     * 位置
     */
    private String position;
}
