package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 全委账号列表查询请求参数
 * @author: jinqing.rao
 * @date: 2024/3/11 11:20
 * @since JDK 1.8
 */
@Setter
@Getter
public class FundTxCodeListRequest implements Serializable {

    private static final long serialVersionUID = 4928069969264925444L;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
} 