/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;

import com.howbuy.crm.cgi.extservice.common.enums.CustPasswdStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;

/**
 * @description: 账户状态验证
 * <AUTHOR>
 * @date 2024/4/24 15:39
 * @since JDK 1.8
 */
public class PiggySignCustTxAccountValidator implements PiggyValidator<PiggyBankVerificationVO> {

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;

    public PiggySignCustTxAccountValidator(HkCustInfoDTO hkCustInfo){
        this.hkCustInfo = hkCustInfo;
    }

    @Override
    public PiggyBankVerificationVO verification() {
        PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
        //交易账号是否激活
        if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfo.getCustTxPasswdType())) {
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.TRADE_ACCOUNT_NOT_ACTIVATED.getCode());
            return piggyBankVerificationVO;
        }
        return null;
    }
}
