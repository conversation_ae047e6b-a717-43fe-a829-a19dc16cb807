package com.howbuy.crm.cgi.extservice.validator.hkopen;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.common.constant.ExternalConstant;
import com.howbuy.crm.cgi.extservice.common.enums.HkIdTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctEmploymentTypeEnum;
import com.howbuy.crm.cgi.common.utils.HkOpenAcctIdTypeUtils;
import com.howbuy.crm.cgi.extservice.request.account.OpenInvestRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.HkOpenAcctProCityRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.bankinfo.HkOpenAcctBankRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.declare.HkOpenAcctDeclareRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.occupation.HkOpenAcctOccupationRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.personalInfo.HkOpenPersonalInfoRequest;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAcctAddressTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description: 海外小程序开户规则校验类
 * @date 2023/12/5 10:51
 * @since JDK 1.8
 */
public class HkOpenAcctValidator extends BasicDataTypeValidator {


    /**
     * @param obj 校验类
     * @return boolean
     * @description: 通过反射校验一个类中的字符串类型的参数是否全为空
     * @author: jinqing.rao
     * @date: 2023/12/5 11:00
     * @since JDK 1.8
     */
    public static boolean validateAllStringIsEmptyInClass(Object obj) throws IllegalAccessException {
        if (obj == null) {
            return true;
        }
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(obj);
            if (!isFieldEmpty(value)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param fieldValue 字段值
     * @return boolean
     * @description: 字符串类型是否是空
     * @author: jinqing.rao
     * @date: 2023/12/5 11:01
     * @since JDK 1.8
     */
    private static boolean isFieldEmpty(Object fieldValue) {
        if (null == fieldValue) {
            return true;
        }
        if (fieldValue instanceof String) {
            return StringUtils.isBlank((String) fieldValue);
        }
        return true;
    }

    /**
     * 1.大陆身份证:18位数（字母+数字）组成,
     * 2.香港身份证：8位数（字母+数字）组成，首位是字母，中间是数字，最后一位是字母或数字，例如：A1234560
     * 3.台湾身份证：10位数（字母+数字）组成，首位是字母，后面8位是数字
     * 4.中国护照:9位数（字母+数字）组成，首位是字母，后面8位是数字
     * 5.港澳通行证:11位数（字母+数字）组成，首位是字母，后面10位是数字
     * 6.18位数
     * 7.台胞证 8位数
     * 8.其他证件 不做校验
     *
     * @param idType 证件类型
     * @param idNo   证件编号
     * @description: 校验基础的证件类型
     * @author: jinqing.rao
     * @date: 2023/12/7 9:52
     * @since JDK 1.8
     */
    public static void validatorOpenIdBaseFormat(String idType, String idNo) {
        if (StringUtils.isAnyBlank(idType, idNo)) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "证件信息必传");
        }
        //身份证
        if (HkIdTypeEnum.MAINLAND_ID_CARD.getKey().equals(idType)) {
            // 使用正则表达式进行验证
            checkSwiftCode("^[A-Za-z0-9]{18}$", idNo, "您输入的证件号码不正确");
        }
        //香港身份证
        if (HkIdTypeEnum.HONG_KONG_ID_CARD.getKey().equals(idType)) {
            checkSwiftCode("^[a-zA-Z][0-9]{6}[a-zA-Z0-9]$", idNo, "您输入的证件号码不正确");
        }
        //台湾身份证
        if (HkIdTypeEnum.TAIWAN_ID_CARD.getKey().equals(idType)) {
            checkSwiftCode("^[a-zA-Z][0-9]{9}$", idNo, "您输入的证件号码不正确");
        }
        //中国护照
        if (HkIdTypeEnum.CHINA_PASSPORT.getKey().equals(idType)) {
            checkSwiftCode("^[a-zA-Z0-9]{9}$", idNo, "您输入的证件号码不正确");
        }
        // 港澳通行证
        if (HkIdTypeEnum.EXIT_ENTRY_PERMIT_FROM_HONG_KONG_AND_MACAO.getKey().equals(idType)) {
            checkSwiftCode("^[a-zA-Z][0-9]{10}$", idNo, "您输入的证件号码不正确");
        }
        //台胞证
        if (HkIdTypeEnum.MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS_PERMIT.getKey().equals(idType)) {
            checkSwiftCode("^[0-9]{8}$", idNo, "您输入的证件号码不正确");
        }
        //澳门身份证
        if (HkIdTypeEnum.MACAO_ID_CARD.getKey().equals(idType)) {
            checkSwiftCode("^[0-9]{8}$", idNo, "您输入的证件号码不正确");
        }
        //港澳台居民居住证
        if (HkIdTypeEnum.HONG_KONG_MACAO_TAIWAN_RESIDENCE_PERMIT.getKey().equals(idType)) {
            checkSwiftCode("^[0-9]{8}$", idNo, "您输入的证件号码不正确");
        }

    }

    /**
     * @param request 请求参数
     * @return void
     * @description: 香港小程序开户, 个人信息填写页校验
     * @author: jinqing.rao
     * @date: 2023/12/7 17:11
     * @since JDK 1.8
     */
    public static void validatorOpenPersonalInfoRequest(HkOpenPersonalInfoRequest request) {
        //账户中心来源,需要校验手机号和邮箱摘要必传
        if (YesNoEnum.YES.getCode().equals(request.getMobileResource())) {
            if (StringUtils.isBlank(request.getMobileMask())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "手机号摘要参数错误");
            }
        }
        //非账户中心来源,正则校验手机号
        if (YesNoEnum.NO.getCode().equals(request.getMobileResource())) {
            checkSwiftCode("1[3456789]\\d{9}", request.getMobile(), "手机号填写错误");
        }
        //非账户中心来源,正则校验邮箱
        if (YesNoEnum.NO.getCode().equals(request.getEmailResource())) {
            checkSwiftCode("^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-.]+\\.[a-zA-Z0-9]+$", request.getEmail(), "邮箱填写错误");
        }
        if (YesNoEnum.YES.getCode().equals(request.getEmailResource())) {
            if (StringUtils.isBlank(request.getEmailMask())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "邮箱摘要参数错误");
            }
        }
        //是否有曾用名,是的情况需要校验 曾用名的中文,英文 姓和名
        if (YesNoEnum.YES.getCode().equals(request.getFormerNameFlag())) {
            if (StringUtils.isAnyBlank(request.getCnFormerSurName(), request.getEnFormerSurName(), request.getCnFormerName(), request.getEnFormerName())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "曾用名信息参数错误");
            }
        }
    }

    private static void checkEmplCompanyInfo(HkOpenAcctProCityRequest birthplace) {
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(birthplace.getCountryCode())) {
            if (StringUtils.isAnyBlank(birthplace.getCountryCode(), birthplace.getCountryDesc(), birthplace.getDetailAddrCn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "就业公司国家信息/中文详细地址参数错误");
            }
            return;
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(birthplace.getCountryCode())) {
            //中国大陆判断省市区是否为空
            validatorStrings("就业公司省信息参数错误", birthplace.getProvCode(), birthplace.getProvDesc());
            //市信息
            validatorStrings("就业公司市信息参数错误", birthplace.getCityCode(), birthplace.getCityDesc());
            //现居区县信息
            validatorStrings("就业公司区县信息参数错误", birthplace.getCountyCode(), birthplace.getCountyDesc());
            //详细地址
            if (StringUtils.isBlank(birthplace.getDetailAddrCn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "就业公司详细地址参数错误");
            }
        } else {
            // 国外地区 省/州(英文)、城/镇(英文)、
            validatorStrings("就业公司州/镇信息参数错误", birthplace.getTownEn(), birthplace.getStateEn());
            //详细地址(英文)
            if (StringUtils.isBlank(birthplace.getDetailAddrEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "就业公司详细地址(英文)参数错误");
            }
        }
    }

    /**
     * @param birthplace 出生地地址信息
     * @return void
     * @description: 出生地地址信息校验
     * @author: jinqing.rao
     * @date: 2023/12/13 13:58
     * @since JDK 1.8
     */
    private static void checkBirthplaceInfo(HkOpenAcctProCityRequest birthplace) {
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(birthplace.getCountryCode())) {
            if (StringUtils.isAnyBlank(birthplace.getCountryCode(), birthplace.getCountryDesc(), birthplace.getCountryEnglishDesc(),
                    birthplace.getDetailAddrCn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "出生地国家信息/中文详细地址参数错误");
            }
            return;
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(birthplace.getCountryCode())) {
            //中国大陆判断省市区是否为空
            //省信息
//            if (StringUtils.isAnyBlank(birthplace.getProvCode(), birthplace.getProvDesc())) {
//                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),);
//            }
            validatorStrings("出生地省份信息参数错误", birthplace.getProvCode(), birthplace.getProvDesc());
            //市信息
//            if (StringUtils.isAnyBlank(birthplace.getCityCode(), birthplace.getCityDesc())) {
//                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "出生地城市信息参数错误");
//            }
            validatorStrings("出生地城市信息参数错误", birthplace.getCityCode(), birthplace.getCityDesc());
            //现居区县信息
//            if (StringUtils.isAnyBlank(birthplace.getCountyCode(), birthplace.getCountyCodeDesc())) {
//                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "出生地区县信息参数错误");
//            }
            validatorStrings("出生地区县信息参数错误", birthplace.getCountyCode(), birthplace.getCountyDesc());
            //详细地址
            if (StringUtils.isBlank(birthplace.getDetailAddrCn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "现居地详细地址参数错误");
            }
        } else {
            // 国外地区 省/州(英文)、城/镇(英文)、
            validatorStrings("出生地信息参数错误", birthplace.getTownEn(), birthplace.getStateEn());
            //详细地址(英文)
            if (StringUtils.isBlank(birthplace.getDetailAddrEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "出生地详细地址(英文)参数错误");
            }
        }
    }

    /**
     * 校验通讯地校验
     */
    private static void checkMailingInfo(HkOpenAcctProCityRequest mailingInfo) {
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(mailingInfo.getCountryCode())) {
            if (StringUtils.isAnyBlank(mailingInfo.getCountryCode(), mailingInfo.getCountryDesc(), mailingInfo.getCountryEnglishDesc(),
                    mailingInfo.getDetailAddrCn(), mailingInfo.getDetailAddrEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "通讯地省份信息参数错误");
            }
        } else if (HkOpenAcctIdTypeUtils.CN.equals(mailingInfo.getCountryCode())) {
            //中国大陆判断省市区是否为空
            //省信息
//            if (StringUtils.isAnyBlank(mailingInfo.getProvCode(), mailingInfo.getProvEnglishDesc(), mailingInfo.getProvDesc())) {
//                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "通讯地省份信息参数错误");
//            }
            validatorStrings("通讯地省份信息参数错误", mailingInfo.getProvCode(), mailingInfo.getProvDesc());
            //市信息
//            if (StringUtils.isAnyBlank(mailingInfo.getCityCode(), mailingInfo.getCityEnglishDesc(), mailingInfo.getCityDesc())) {
//                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "通讯地城市信息参数错误");
//            }
            validatorStrings("通讯地城市信息参数错误", mailingInfo.getCityCode(), mailingInfo.getCityEnglishDesc(), mailingInfo.getCityDesc());
            //现居区县信息
            if (StringUtils.isAnyBlank(mailingInfo.getCountyCode(), mailingInfo.getCountyEnglishDesc(), mailingInfo.getCountyDesc())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "通讯地区县信息参数错误");
            }
            validatorStrings("通讯地区县信息参数错误", mailingInfo.getCountyCode(), mailingInfo.getCountyEnglishDesc(), mailingInfo.getCountyDesc());
            //详细地址
            if (StringUtils.isBlank(mailingInfo.getDetailAddrCn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "现居地详细地址参数错误");
            }
        } else {
            // 国外地区 省/州(英文)、城/镇(英文)、
            if (StringUtils.isAnyBlank(mailingInfo.getTownEn(), mailingInfo.getStateEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "通讯地信息参数错误");
            }
            //详细地址(英文)
            if (StringUtils.isBlank(mailingInfo.getDetailAddrEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "通讯地详细地址(英文)参数错误");
            }
        }
    }

    /**
     * 校验现居地信息
     */
    public static void checkResidenceInfo(HkOpenAcctProCityRequest residenceInfo) {

        //中国特别行政区,国家(中/英),详细地址(中/英)
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(residenceInfo.getCountryCode())) {
            if (StringUtils.isAnyBlank(residenceInfo.getCountryCode(), residenceInfo.getCountryDesc(), residenceInfo.getCountryEnglishDesc(),
                    residenceInfo.getDetailAddrCn(), residenceInfo.getDetailAddrEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR);
            }
        } else if (HkOpenAcctIdTypeUtils.CN.equals(residenceInfo.getCountryCode())) {
            //中国大陆判断省市区是否为空
            //省信息
            validatorStrings("现居地省份信息参数错误", residenceInfo.getProvCode(), residenceInfo.getProvEnglishDesc(), residenceInfo.getProvDesc());
            //市信息
            validatorStrings("现居地城市信息参数错误", residenceInfo.getCityCode(), residenceInfo.getCityEnglishDesc(), residenceInfo.getCityDesc());
            //现居区县信息
            validatorStrings("现居地区县信息参数错误", residenceInfo.getCountyCode(), residenceInfo.getCountyEnglishDesc(), residenceInfo.getCountyDesc());
            //详细地址
            if (StringUtils.isBlank(residenceInfo.getDetailAddrCn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "现居地详细地址参数错误");
            }
        } else {
            // 国外地区 省/州(英文)、城/镇(英文)、
            if (StringUtils.isAnyBlank(residenceInfo.getTownEn(), residenceInfo.getStateEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "现居地信息参数错误");
            }
            //详细地址(英文)
            if (StringUtils.isBlank(residenceInfo.getDetailAddrEn())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "现居地详细地址(英文)参数错误");
            }
        }
    }

    /**
     * @param request 请求参数
     * @description: 校验就业信息
     * @author: jinqing.rao
     * @date: 2023/12/7 19:39
     * @since JDK 1.8
     */
    public static void validatorOpenOccupationRequest(HkOpenAcctOccupationRequest request) {
        //基本参数校验
        // validator(request);
        //职业信息校验
        if (!HkOpenAcctEmploymentTypeEnum.STUDENT.getCode().equals(request.getEmplStatus())) {
            //雇主/全职/兼职  校验:公司名称、公司地址、业务性质、职位/职称、年收入
            //退休/非在职         公司名称、公司地址、业务性质、职位/职称、年收入
            //主妇 勾选上份工作或配偶工作；单选，二者只能选其一  公司名称、公司地址、业务性质、职位/职称、年收入
            checkEmployerInfo(request);
        }
        //税务信息校验
        if (YesNoEnum.NO.getCode().equals(request.getHasTin())) {
            if (StringUtils.isBlank(request.getNoTinReason())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "年收入参数错误");
            }
        }
        if (YesNoEnum.YES.getCode().equals(request.getHasTin())) {
            if (StringUtils.isBlank(request.getTin())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "税务编号参数错误");
            }
            if (StringUtils.isBlank(request.getTinType())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "税务编号类型参数错误");
            }
        }
    }

    /**
     * @param request 请求参数信息
     * @description: 校验雇主/全职/兼职
     * @author: jinqing.rao
     * @date: 2023/12/7 19:42
     * @since JDK 1.8
     */
    private static void checkEmployerInfo(HkOpenAcctOccupationRequest request) {
        //公司名称
        if (StringUtils.isBlank(request.getEmplCompanyName())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "公司名称参数错误");
        }
//        //公司地址
//        if (StringUtils.isBlank(request.getEmplAddr())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "公司地址参数错误");
//        }
//        //业务性质
//        if (StringUtils.isBlank(request.getEmplNatureOfBusiness())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "业务性质参数错误");
//        }
//        //职位/职称
//        if (StringUtils.isBlank(request.getDesignation())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "职位/职称参数错误");
//        }
//        //年收入
//        if (StringUtils.isBlank(request.getEmplCountyCode())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "年收入参数错误");
//        }
//        //校验公司地址省市区
//        if (StringUtils.isBlank(request.getEmplProvCode())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "公司地址省份参数错误");
//        }
//        if (StringUtils.isBlank(request.getEmplCityCode())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "公司地址城市参数错误");
//        }
//        if (StringUtils.isBlank(request.getEmplCountyCode())) {
//            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "公司地址区县参数错误");
//        }
    }

    /**
     * @param request 请求参数
     * @description: 校验声明信息
     * @author: jinqing.rao
     * @date: 2023/12/7 20:19
     * @since JDK 1.8
     */
    public static void validatorOpenDeclareRequest(HkOpenAcctDeclareRequest request) {
        //声明1校验
        if (StringUtils.isBlank(request.getDeclareOne())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "声明1参数错误");
        }
        //声明1 是的情况,需要校验公司信息
        checkDeclareOne(request);
        //校验声明2
        checkDeclareInfo(request.getDeclareTwo(), "声明2参数错误", request.getLicensedNo(), "持证号码参数错误");
        //校验声明3
        checkDeclareInfo(request.getDeclareThree(), "声明3参数错误", request.getRelateName(), "相关人士姓名参数错误");
        //校验声明4
        checkDeclareInfo(request.getDeclareFour(), "声明4参数错误", request.getDeclareFourDesc(), "详细说明参数错误");
        //校验 开户暂存类型 必填，01-保存，02-退出
        if (StringUtils.isBlank(request.getOpenSaveType())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "开户暂存类型参数错误");
        }
    }

    private static void checkDeclareInfo(String declare, String paramsMsg, String bizCode, String errorMsg) {
        if (StringUtils.isBlank(declare)) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), paramsMsg);
        }
        //声明2 是的情况下 校验持证号码 雇主书面同意书URL列表
        if (YesNoEnum.YES.getCode().equals(declare)) {
            //校验持证号码
            if (StringUtils.isBlank(bizCode)) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), errorMsg);
            }
        }
    }

    private static void checkDeclareOne(HkOpenAcctDeclareRequest request) {
        if (YesNoEnum.YES.getCode().equals(request.getDeclareOne())) {
            //校验公司名称
            if (StringUtils.isBlank(request.getCompanyName())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "公司名称参数错误");
            }
            //校验交易所名称
            if (StringUtils.isBlank(request.getExchangeName())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "交易所名称参数错误");
            }
            //校验交易流水号
            if (StringUtils.isBlank(request.getTradeNo())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "交易流水号参数错误");
            }
        }
    }

    /**
     * @param request 请求参数
     * @description: 校验投资信息
     * @author: jinqing.rao
     * @date: 2023/12/8 11:01
     * @since JDK 1.8
     */
    public static void validatorOpenInvestRequest(OpenInvestRequest request) {
        //如果是专业投资者，需上传资产证明
        if (ExternalConstant.PRO.equals(request.getInvestorQualification())) {
            //资产证明列表
            if (CollectionUtils.isEmpty(request.getAssetCertUrlList())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "资产证明列表参数错误");
            }
        }
        //财富来源地是不能空
        if (CollectionUtils.isEmpty(request.getWealthSource())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "财富来源地参数错误");
        }
    }
    /**
     * @param request 请求参数信息
     * @description: 校验参数信息
     * @author: jinqing.rao
     * @date: 2023/12/8 11:26
     * @since JDK 1.8
     */
    public static void validatorOpenBankRequest(HkOpenAcctBankRequest request) {
        //检验校验银行账户号码格式是否正确，规则：16~19位数字，如不满足，toast提示：银行账户号码格式不正确
        checkSwiftCode("^[0-9]{7,}$", request.getBankAcct(), "银行账户号码格式不正确");
        //校验银行SWIFT编码格式是否正确 规则：8~11位数字或字母，如不满足，toast提示：SWIFT编码格式不正确
        checkSwiftCode("^[A-Za-z0-9]{8,11}$", request.getSwiftCode(), "SWIFT编码格式不正确");
        //校验银行SWIFT编码格式是否正确
        // 判断是否银行联名账户 ,是的情况需要校验关系证明文件
        if (YesNoEnum.YES.getCode().equals(request.getJointAccount())) {
            // 关系证明文件列表
            if (CollectionUtils.isEmpty(request.getJointAccountFileList())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "关系证明文件列表参数错误");
            }
        }
        //账户货币
        if (CollectionUtils.isEmpty(request.getCurrencyVOList())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "账户货币参数错误");
        }
        //代理银行
        if (StringUtils.isNotBlank(request.getBrokerSwiftCode())) {
            if (StringUtils.isBlank(request.getBrokerBankAcct())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "代理银行账号参数错误");
            }
            if (StringUtils.isBlank(request.getBrokerBankEnName())) {
                throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "代理银行英文名参数错误");
            }
            //检验校验银行账户号码格式是否正确，规则：16~19位数字，如不满足，toast提示：银行账户号码格式不正确
            checkSwiftCode("^[0-9]{7,}$", request.getBrokerBankAcct(), "代理银行账户号码格式不正确");
            //校验银行SWIFT编码格式是否正确 规则：8~11位数字或字母，如不满足，toast提示：SWIFT编码格式不正确
            checkSwiftCode("^[A-Za-z0-9]{8,11}$", request.getBrokerSwiftCode(), "代理银行SWIFT编码格式不正确");
        }
        //校验银行卡照片
        if (CollectionUtils.isEmpty(request.getBankAcctImageList())) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "银行卡照片参数错误");
        }
    }

    private static void checkSwiftCode(String pattern2, String param, String errorMsg) {
        if (!Pattern.matches(pattern2, param)) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), errorMsg);
        }
    }

    /**
     * @param request
     * @return void
     * @description: 根据不同的地址类型, 校验地址信息
     * @author: jinqing.rao
     * @date: 2023/12/19 18:06
     * @since JDK 1.8
     */
    public static void validatorOpenAddressInfoByType(HkOpenAcctProCityRequest request) {
        //开户个人信息页,现居地校验
        if (HkOpenAcctAddressTypeEnum.PERSONAL_INFO_RESIDENCE.getCode().equals(request.getAddressType())) {
            checkResidenceInfo(request);
        }
        // 开户个人信息页,通讯地址校验
        if (HkOpenAcctAddressTypeEnum.PERSONAL_INFO_MAILING.getCode().equals(request.getAddressType())) {
            checkMailingInfo(request);
        }
        // 开户个人信息页,出生地校验
        if (HkOpenAcctAddressTypeEnum.PERSONAL_INFO_BIRTH.getCode().equals(request.getAddressType())) {
            checkBirthplaceInfo(request);
        }

        // 就业公司地址校验
        if (HkOpenAcctAddressTypeEnum.COMPANY_ACCOUNT.getCode().equals(request.getAddressType())) {
            checkEmplCompanyInfo(request);
        }
    }

    public static void validateHkCustInfoDTO(String hkCusNo, HkCustInfoDTO hkCustInfoDTO, ExceptionCodeEnum exceptionCodeEnum) {
        if (!OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfoDTO.getReturnCode()) && !hkCusNo.equals(hkCustInfoDTO.getHkCustNo())) {
            throw new BusinessException(exceptionCodeEnum);
        }
    }
}
