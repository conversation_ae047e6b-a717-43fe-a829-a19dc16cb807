package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 工作台待办任务忽略操作请求参数
 * <AUTHOR>
 * @date 2024-03-19 15:00:00
 */
@Getter
@Setter
public class PortraitTaskIgnoreRequest extends Body {

    /**
     * 任务id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "任务id", isRequired = true)
    private String taskId;

    /**
     * 用户Id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "用户id", isRequired = true)
    private String userId;
} 