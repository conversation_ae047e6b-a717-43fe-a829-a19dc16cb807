/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/18 16:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkRevokeFundOrderRequest extends BodyRequest {
    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String dealNo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String txPassword;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;


    @NotBlank(message = "防重标识不能为空")
    private String hbSceneId;
}
