/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.fund;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundPrebookEnum;
import com.howbuy.crm.cgi.extservice.common.utils.NumberUtils;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKFundAgreementRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKRedeemFundSubmitRequest;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundPageInfoVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.AgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.DigitalSignSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkRedeemFundSubmitInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.RedeemFundInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.RedeemPageInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.RedeemPrebookInfoVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundFeeRateResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundLimitResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/10 14:28
 * @since JDK 1.8
 */
public class HKRedeemFundConvert {

    public static HKRedeemFundPageInfoVO toHkRedeemFundPageInfoVO(HkCustInfoDTO hkCustInfo, RedeemPageInfoDTO redeemPageInfoDTO, FundBasicInfoDTO fundBasicInfoDTO, List<FundLimitResponseDTO> fundLimitDTO, List<FundFeeRateResponseDTO> fundFeeRateDTO, List<HkBankCardInfoDTO> hkBankAcctLogoList) {
            HKRedeemFundPageInfoVO hkRedeemFundPageInfoVO = new HKRedeemFundPageInfoVO();
            //设置获取赎回页面的信息
            HKRedeemFundPageInfoVO.RedeemFundInfoVO redeemFundInfoVO = new HKRedeemFundPageInfoVO.RedeemFundInfoVO();
            //设置手机区号 手机掩码 邮箱掩码
            hkRedeemFundPageInfoVO.setMobileAreaCode(hkCustInfo.getMobileAreaCode());
            hkRedeemFundPageInfoVO.setMobileMask(hkCustInfo.getMobileMask());
            hkRedeemFundPageInfoVO.setEmailMask(hkCustInfo.getEmailMask());
            hkRedeemFundPageInfoVO.setMobileDigest(hkCustInfo.getMobileDigest());
            hkRedeemFundPageInfoVO.setEmailDigest(hkCustInfo.getEmailDigest());
            //赎回页面信息
            RedeemFundInfoDTO sellFundInfoVO = redeemPageInfoDTO.getSellFundInfoVO();
            redeemFundInfoVO.setFundShortName(fundBasicInfoDTO.getFundAbbr());
            redeemFundInfoVO.setFundEnName(fundBasicInfoDTO.getFundNameEn());
            redeemFundInfoVO.setVolPrecision(null == fundBasicInfoDTO.getVolPrecision() ? Integer.valueOf("2") : fundBasicInfoDTO.getVolPrecision());
            redeemFundInfoVO.setSupportPrebook(HKFundPrebookEnum.isSupportRedeemPrebook(fundBasicInfoDTO.getIsScheduledTrade()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            if(null != sellFundInfoVO){
                getRedeemFundInfoVO(redeemFundInfoVO, sellFundInfoVO, fundBasicInfoDTO, fundLimitDTO);
            }
            hkRedeemFundPageInfoVO.setRedeemFundInfoVO(redeemFundInfoVO);
            //获取赎回页面信息的预约信息
            HKRedeemFundPageInfoVO.RedeemPrebookInfoVO redeemPrebookInfoVO = new HKRedeemFundPageInfoVO.RedeemPrebookInfoVO();
            RedeemPrebookInfoVO prebookSellInfoVO = redeemPageInfoDTO.getPrebookSellInfoVO();
            if(null != prebookSellInfoVO){
                redeemPrebookInfoVO.setPrebookDealNo(prebookSellInfoVO.getPrebookDealNo());
                redeemPrebookInfoVO.setPrebookRedeemMethod(prebookSellInfoVO.getPrebookRedeemMethod());
                redeemPrebookInfoVO.setPrebookRedeemAmt(NumberUtils.bigDecimalToString(prebookSellInfoVO.getPrebookAppAmt(),2,RoundingMode.DOWN));
                redeemPrebookInfoVO.setPrebookRedeemVol(NumberUtils.bigDecimalToString(prebookSellInfoVO.getPrebookAppVol(),fundBasicInfoDTO.getVolPrecision(),RoundingMode.DOWN));
            }
            if(null != sellFundInfoVO){
                redeemPrebookInfoVO.setPrebookRedeemDate(DateUtils.formatDateStr(sellFundInfoVO.getAdvanceEndDt(), DateUtils.YYYY_MM_DD,DateUtils.YYYYMMDD));
                redeemPrebookInfoVO.setPrebookRedeemTime(DateUtils.formatDateStr(sellFundInfoVO.getAdvanceEndTm(), DateUtils.HH_MM,DateUtils.HHMMSS));
            }
            hkRedeemFundPageInfoVO.setRedeemPrebookInfoVO(redeemPrebookInfoVO);

            // 设置费率列表
            if(CollectionUtils.isNotEmpty(fundFeeRateDTO)){
                List<HKRedeemFundPageInfoVO.RedeemFeeRateInfoVO> rateInfoVOS = fundFeeRateDTO.stream().map(m -> {
                    HKRedeemFundPageInfoVO.RedeemFeeRateInfoVO rateInfoVO = new HKRedeemFundPageInfoVO.RedeemFeeRateInfoVO();
                    rateInfoVO.setMinFeeRateDays(NumberUtils.integerToStr(m.getMinFeeDays()));
                    rateInfoVO.setMaxFeeRateDays(NumberUtils.integerToStr(m.getMaxFeeDays()));
                    rateInfoVO.setFeeRate(NumberUtils.formatToTwoDecimalPlaces(m.getFeeRate()));
                    return rateInfoVO;
                }).sorted(ComparingMinFeeRateDays()).collect(Collectors.toList());
                // 根据基金费率天数下限升序,基金费率天数为空的放最后展示
                hkRedeemFundPageInfoVO.setRedeemFeeRateInfoVO(rateInfoVOS);
            }
            HKRedeemFundPageInfoVO.RedeemDirectionInfoVO directionInfoVO = new HKRedeemFundPageInfoVO.RedeemDirectionInfoVO();
            directionInfoVO.setSignCxg(redeemPageInfoDTO.getHasSignCxg());
            if(CollectionUtils.isNotEmpty(hkBankAcctLogoList)){
                List<HKRedeemFundPageInfoVO.RedeemDirectionInfoVO.RedeemDirectionBankInfoVO> bankInfoVOS = hkBankAcctLogoList.stream().map(m -> {
                    HKRedeemFundPageInfoVO.RedeemDirectionInfoVO.RedeemDirectionBankInfoVO bankInfoVO = new HKRedeemFundPageInfoVO.RedeemDirectionInfoVO.RedeemDirectionBankInfoVO();
                    bankInfoVO.setBankName(StringUtils.isBlank(m.getBankChineseName()) ? m.getBankName() : m.getBankChineseName());
                    bankInfoVO.setBankLogoUrl(m.getBankLogoUrl());
                    bankInfoVO.setCpAcctNo(m.getHkCpAcctNo());
                    bankInfoVO.setBankAcctMask(m.getBankAcctMask());
                    return bankInfoVO;
                }).collect(Collectors.toList());
                directionInfoVO.setRedeemDirectionBankInfoVO(bankInfoVOS);
            }
            hkRedeemFundPageInfoVO.setRedeemDirectionInfoVO(directionInfoVO);
            return hkRedeemFundPageInfoVO;
        }

        /**
         * @description: 根据基金费率天数下限升序,基金费率天数为空的放最后展示
         * @param
         * @return java.util.Comparator<com.howbuy.crm.cgi.extservice.vo.hkfund.HKRedeemFundPageInfoVO.RedeemFeeRateInfoVO>
         * @author: jinqing.rao
         * @date: 2024/6/11 15:48
         * @since JDK 1.8
         */
    private static Comparator<HKRedeemFundPageInfoVO.RedeemFeeRateInfoVO> ComparingMinFeeRateDays() {
        return Comparator.comparing(
                vo -> {
                    try {
                        // 尝试将minFeeRateDays转换为Integer进行比较
                        return Optional.ofNullable(vo.getMinFeeRateDays())
                                .filter(s -> !s.isEmpty())
                                .map(Integer::valueOf)
                                .orElse(null);
                    } catch (NumberFormatException e) {
                        // 如果转换失败（例如，minFeeRateDays包含非数字字符），当作null处理
                        return null;
                    }
                },
                Comparator.nullsLast(Comparator.naturalOrder())
        );
    }

    private static void getRedeemFundInfoVO(HKRedeemFundPageInfoVO.RedeemFundInfoVO redeemFundInfoVO, RedeemFundInfoDTO sellFundInfoVO, FundBasicInfoDTO fundBasicInfoDTO, List<FundLimitResponseDTO> fundLimitDTO) {
        redeemFundInfoVO.setOpenStartDate(DateUtils.formatDateStr(sellFundInfoVO.getOpenStartDt(),DateUtils.YYYY_MM_DD, DateUtils.YYYYMMDD));
        redeemFundInfoVO.setOpenEndDate(DateUtils.formatDateStr(sellFundInfoVO.getOpenEndDt(),DateUtils.YYYY_MM_DD, DateUtils.YYYYMMDD));
        //redeemFundInfoVO.setOpenEndTime(DateUtils.formatDateStr(sellFundInfoVO.getOpenEndTm(), DateUtils.HHMMSS,DateUtils.HH_MM_SS));
        redeemFundInfoVO.setTradeDate(DateUtils.formatDateStr(sellFundInfoVO.getTradeDt(),DateUtils.YYYY_MM_DD, DateUtils.YYYYMMDD));
        //获取系统的当前日期,仅做前端比较展示用
        redeemFundInfoVO.setCurrentDate(DateUtils.getCurrentDate(DateUtils.YYYY_MM_DD));
        redeemFundInfoVO.setCurrencyCode(fundBasicInfoDTO.getCurrency());
        redeemFundInfoVO.setCurrencyDesc(CurrencyEnum.getDescription(fundBasicInfoDTO.getCurrency()));
        redeemFundInfoVO.setInTransitOrder(sellFundInfoVO.getIsInTransitOrder());
        redeemFundInfoVO.setTotalVol(NumberUtils.bigDecimalToString(sellFundInfoVO.getTotalVol(),fundBasicInfoDTO.getVolPrecision(),RoundingMode.DOWN));
        redeemFundInfoVO.setVolUnit("份");
        if(null != sellFundInfoVO.getAvailableVol()){
            redeemFundInfoVO.setAvailableVol(NumberUtils.bigDecimalToString(sellFundInfoVO.getAvailableVol(),fundBasicInfoDTO.getVolPrecision(),RoundingMode.DOWN));
        }
        redeemFundInfoVO.setFundCategory(fundBasicInfoDTO.getFundCategory());
        redeemFundInfoVO.setTotalAsset(NumberUtils.bigDecimalToString(sellFundInfoVO.getTotalAsset(),2, RoundingMode.DOWN));
        redeemFundInfoVO.setAvailableAsset(NumberUtils.bigDecimalToString(sellFundInfoVO.getAvailableAsset(),2, RoundingMode.DOWN));
        redeemFundInfoVO.setNavDate(DateUtils.formatDateStr(sellFundInfoVO.getNavDt(), DateUtils.MM_dd, DateUtils.YYYYMMDD));
        redeemFundInfoVO.setFundRedeemMethod(fundBasicInfoDTO.getRedemptionMethod());
        redeemFundInfoVO.setAmtRedeemDiscountRatio(NumberUtils.formatNumber(fundBasicInfoDTO.getRedemptionDiscountRatioByAmount(),4,RoundingMode.HALF_UP));
        redeemFundInfoVO.setMinHoldAmt(NumberUtils.bigDecimalToString(fundBasicInfoDTO.getMinRetainedAsset(),2,RoundingMode.DOWN));
        redeemFundInfoVO.setMinHoldVol(NumberUtils.bigDecimalToString(fundBasicInfoDTO.getMinHoldVol(),fundBasicInfoDTO.getVolPrecision(),RoundingMode.DOWN));
        if(CollectionUtils.isNotEmpty(fundLimitDTO)){
            BigDecimal minAppAmt = fundLimitDTO.get(0).getMinAppAmt();
            redeemFundInfoVO.setMinAppAmt(null ==  minAppAmt ? null  : minAppAmt.toString());
            redeemFundInfoVO.setMinAppVol(null ==  fundLimitDTO.get(0).getMinAppVol() ? null  : fundLimitDTO.get(0).getMinAppVol().toString());
            redeemFundInfoVO.setMaxAppAmt(null ==  fundLimitDTO.get(0).getMaxAppAmt() ? null  : fundLimitDTO.get(0).getMaxAppAmt().toString());
            redeemFundInfoVO.setMaxAppVol(null ==  fundLimitDTO.get(0).getMaxAppVol() ? null  : fundLimitDTO.get(0).getMaxAppVol().toString());
        }
    }

    public static DigitalSignSignDTO toRedeemFundContractSignDTO(String hkCustNo, String fundCode, String signFlag, String tradeMode, List<HKFundAgreementRequest> contractList) {
        DigitalSignSignDTO digitalSignSignDTO = new DigitalSignSignDTO();
        digitalSignSignDTO.setHkCustNo(hkCustNo);
        digitalSignSignDTO.setFundCode(fundCode);
        digitalSignSignDTO.setContractSignFlag(signFlag);
        digitalSignSignDTO.setTradeMode(tradeMode);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(contractList)) {
            List<AgreementDTO> hkFundAgreementDTOList = contractList.stream().map(m -> {
                AgreementDTO agreementDTO = new AgreementDTO();
                agreementDTO.setFileCode(m.getFileCode());
                agreementDTO.setFileName(m.getFileName());
                agreementDTO.setFilePathUrl(m.getFilePathUrl());
                return agreementDTO;
            }).collect(Collectors.toList());
            digitalSignSignDTO.setContractList(hkFundAgreementDTOList);
        }
        return digitalSignSignDTO;
    }

    public static HkRedeemFundSubmitInfoDTO toHkRedeemFundSubmitInfoDTO(HKRedeemFundSubmitRequest request, String dealNo) {
        HkRedeemFundSubmitInfoDTO hkRedeemFundSubmitInfoDTO = new HkRedeemFundSubmitInfoDTO();
        hkRedeemFundSubmitInfoDTO.setHkCustNo(request.getHkCustNo());
        hkRedeemFundSubmitInfoDTO.setTxPassword(PassWordUtil.encrypt(request.getTxPassword()));
        hkRedeemFundSubmitInfoDTO.setFundCode(request.getFundCode());
        hkRedeemFundSubmitInfoDTO.setCpAcctNo(request.getCpAcctNo());
        hkRedeemFundSubmitInfoDTO.setRedeemMethod(request.getRedeemMethod());
        hkRedeemFundSubmitInfoDTO.setRedeemDirection(request.getRedeemDirection());
        hkRedeemFundSubmitInfoDTO.setAppVol(NumberUtils.strToBigDecimal(request.getRedeemVol()));
        hkRedeemFundSubmitInfoDTO.setAppAmt(NumberUtils.strToBigDecimal(request.getRedeemAmt()));
        hkRedeemFundSubmitInfoDTO.setPrebookDealNo(request.getPrebookDealNo());
        hkRedeemFundSubmitInfoDTO.setDealNo(dealNo);
        hkRedeemFundSubmitInfoDTO.setExternalDealNo(request.getHbSceneId());
        return hkRedeemFundSubmitInfoDTO;
    }
}
