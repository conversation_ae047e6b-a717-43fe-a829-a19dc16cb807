package com.howbuy.crm.cgi.extservice.request.account.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 海外小程序PDF数据请求
 * <AUTHOR>
 * @date 2024/1/7 18:32
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctPdfRequest extends AccountBaseRequest  implements Serializable {

    private static final long serialVersionUID = 8949149410833765943L;

    /**
     * 香港客户编号
     */
    private String hkCustomerNo;

    /**
     * 渠道编码 0:CGI缓存  1: 账户中心
     */
    private String channelCode;

    /**
     * 业务编码
     */
    private String bizCode;
}
