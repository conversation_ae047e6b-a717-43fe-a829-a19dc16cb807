package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.HkOpenAcctIdTypeUtils;
import com.howbuy.crm.cgi.extservice.common.enums.HkIdTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctGenderEnum;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.HkOpenAcctAddressRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.HkOpenAcctProCityRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.personalInfo.HkOpenPersonalInfoRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenPersonalInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.SplitNameVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctAddressInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctProCitySaveVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.personalinfo.HkOpenPersonalInfoResponseVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkAcctCountryDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoPlaintextDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.*;
import com.howbuy.crm.cgi.manager.domain.ocr.IdentityCardOcrDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import com.howbuy.crm.cgi.manager.enums.HkOpenAcctAddressTypeEnum;
import com.howbuy.crm.cgi.manager.outerservice.baidu.BaiduTranslateOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkAccCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.ocr.IdentityCardOcrOuterService;
import com.howbuy.paycommon.model.enums.IdTypeEnum;
import com.howbuy.paycommon.model.enums.RnAuthStateEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @description: 香港开户步骤二,个人信息填写页
 * <AUTHOR>
 * @date 2024/1/22 18:54
 * @since JDK 1.8
 */
@Service
public class OpenPersonalInfoService extends OpenAcctAbstractService{

    @Resource
    private BaiduTranslateOuterService baiduTranslateOuterService;

    @Resource
    private IdentityCardOcrOuterService identityCardOcrOuterService;

    /**
     * @description: 个人中心的个人信息填写页,查询用户的个人信息
     * 查询逻辑:因为没有数据库,用户在未提交前数据都是存储在redis,提交后数据存储在账户中心,并且删除所有的用户的缓存数据。
     * 如果开户申请订单被驳回,缓存数据没有数据,直接查询账户中心的订单.并且将地址信息的数据回写到缓存中去,不然在提交个人信息页面是获取不到信息
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenPersonalInfoVO
     * @author: jinqing.rao
     * @date: 2024/3/21 9:46
     * @since JDK 1.8
     */
    public OpenPersonalInfoVO queryOpenPersonalInfoDetail() {

        String hkCustNo = getHkCusNo();
        //获取国家信息  转换成map,通过Code获取国家名称,底层数据不存储国家名称
        Map<String, HkAcctCountryDTO> countryMap = hkAccCommonOuterService.getCountryDtoMap();
        //获取城市信息
        Map<String, String> cityInfoMap = hkCommonService.getCityInfoMap();
        //根据客户号查询用户的手机号和邮箱信息,默认带出用户的手机号和邮箱
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        //查询缓存获取开户订单信息
        HkOpenAcctCustInfoDTO hkOpenAcctCustInfoDTO = openAcctCatchService.getHkOpenAcctCustInfoDTO(hkCustNo);

        //从缓存获取开户订单审核退回修改状态的错误信息,这里的信息是在 com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService.querySubmitResult 回写到缓存的。因为没有业务数据表
        String orderCheckReason = openAcctCatchService.getHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.PERSONAL_INFORMATION_CHECK_RESULT, hkCustNo);
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = JSON.parseArray(orderCheckReason, HkOpenAcctCheckVO.class);
        //需要展示用户已经绑定的手机和邮箱的掩码值,以及用户证件信息页OCR识别的姓名,性别,出生日期
        IdentityCardOcrDTO identityCardOcr = openAcctCatchService.getHkOpenAcctIdOcrDTO(hkCustNo);
        //获取步骤一的选的国籍信息,查询的原因是前端反馈,页面无法带出国籍信息和证件信息
        HkOpenAcctIdInfoDTO hkOpenAcctIdInfoDTO = openAcctCatchService.getHkOpenAcctIdInfoDTO(hkCustNo);
        if (null != hkOpenAcctCustInfoDTO) {
            OpenPersonalInfoVO openPersonalInfoVO = OpenAcctConvert.toOpenPersonalInfoVO(hkOpenAcctCustInfoDTO, hkOpenAcctCheckVOS, countryMap, hkCustInfoDTO);
            return getOpenIdentityOcrInfo(identityCardOcr, openPersonalInfoVO,hkOpenAcctIdInfoDTO);
        }
        //查询账户中心获取用户的开户订单
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getAccountCustInfo() && StringUtils.isNotBlank(orderInfoDTO.getDealNo())) {
            //组合现居地的全量通讯地址,账户中心不存储具体的全量地址
            HkOpenAcctCustInfoDTO accountCustInfo = orderInfoDTO.getAccountCustInfo();
            //拼接现居地全量的中文地址
            initResidenceFullDetailCn(accountCustInfo, countryMap, cityInfoMap);
            //拼接通讯地全量的中文地址
            initMailingFullDetailCn(accountCustInfo, countryMap, cityInfoMap);
            //拼接出生地全量的地址信息
            initBirthFullDetailEn(accountCustInfo, countryMap, cityInfoMap);
            //回填用户的现居住地址,通讯地址,出生地址到缓存,不然在提交个人信息页面是获取不到信息
            HkOpenAcctAddressInfoDTO acctAddressInfoDTO = new HkOpenAcctAddressInfoDTO();
            acctAddressInfoDTO.setCountryCode(accountCustInfo.getResidenceCountryCode());
            acctAddressInfoDTO.setCountryDesc(getCoutryDescByCode(countryMap, accountCustInfo.getResidenceCountryCode()));
            acctAddressInfoDTO.setCountryEnglishDesc(accountCustInfo.getResidenceCountryEn());
            acctAddressInfoDTO.setProvCode(accountCustInfo.getResidenceProvCode());
            acctAddressInfoDTO.setProvDesc(cityInfoMap.get(accountCustInfo.getResidenceProvCode()));
            acctAddressInfoDTO.setProvEnglishDesc(accountCustInfo.getResidenceProvEn());
            acctAddressInfoDTO.setCityCode(accountCustInfo.getResidenceCityCode());
            acctAddressInfoDTO.setCityDesc(cityInfoMap.get(accountCustInfo.getResidenceCityCode()));
            acctAddressInfoDTO.setCityEnglishDesc(accountCustInfo.getResidenceCityEn());
            acctAddressInfoDTO.setCountyCode(accountCustInfo.getResidenceCountyCode());
            acctAddressInfoDTO.setCountyDesc(cityInfoMap.get(accountCustInfo.getResidenceCountyCode()));
            acctAddressInfoDTO.setCountyEnglishDesc(accountCustInfo.getResidenceCountyEn());
            acctAddressInfoDTO.setTownEn(accountCustInfo.getResidenceTownEn());
            acctAddressInfoDTO.setStateEn(accountCustInfo.getResidenceStateEn());
            acctAddressInfoDTO.setDetailAddrCn(accountCustInfo.getResidenceAddrCn());
            acctAddressInfoDTO.setDetailAddrEn(accountCustInfo.getResidenceAddrEn());
            openAcctCatchService.saveHkOpenAcctAddressInfoDTO(hkCustNo, HkOpenAccountStepEnum.PERSONAL_INFORMATION_RESIDENCE_DETAIL, acctAddressInfoDTO);

            //回填用户的通讯地址信息
            HkOpenAcctAddressInfoDTO mailingAddressInfoDTO = new HkOpenAcctAddressInfoDTO();
            mailingAddressInfoDTO.setCountryCode(accountCustInfo.getMailingCountryCode());
            mailingAddressInfoDTO.setCountryDesc(getCoutryDescByCode(countryMap,accountCustInfo.getMailingCountryCode()));
            mailingAddressInfoDTO.setCountryEnglishDesc(accountCustInfo.getMailingCountryEn());
            mailingAddressInfoDTO.setProvCode(accountCustInfo.getMailingProvCode());
            mailingAddressInfoDTO.setProvDesc(cityInfoMap.get(accountCustInfo.getMailingProvCode()));
            mailingAddressInfoDTO.setProvEnglishDesc(accountCustInfo.getMailingProvEn());
            mailingAddressInfoDTO.setCityCode(accountCustInfo.getMailingCityCode());
            mailingAddressInfoDTO.setCityDesc(cityInfoMap.get(accountCustInfo.getMailingCityCode()));
            mailingAddressInfoDTO.setCityEnglishDesc(accountCustInfo.getMailingCityEn());
            mailingAddressInfoDTO.setCountyCode(accountCustInfo.getMailingCountyCode());
            mailingAddressInfoDTO.setCountyDesc(cityInfoMap.get(accountCustInfo.getMailingCountyCode()));
            mailingAddressInfoDTO.setCountyEnglishDesc(accountCustInfo.getMailingCountyEn());
            mailingAddressInfoDTO.setTownEn(accountCustInfo.getMailingTownEn());
            mailingAddressInfoDTO.setStateEn(accountCustInfo.getMailingStateEn());
            mailingAddressInfoDTO.setDetailAddrCn(accountCustInfo.getMailingAddrCn());
            mailingAddressInfoDTO.setDetailAddrEn(accountCustInfo.getMailingAddrEn());
            openAcctCatchService.saveHkOpenAcctAddressInfoDTO(hkCustNo, HkOpenAccountStepEnum.PERSONAL_INFORMATION_MAILING_DETAIL, mailingAddressInfoDTO);
            //填写出生地信息
            HkOpenAcctAddressInfoDTO birthAddressInfoDTO = new HkOpenAcctAddressInfoDTO();
            birthAddressInfoDTO.setCountryCode(accountCustInfo.getBirthCountryCode());
            birthAddressInfoDTO.setCountryDesc(getCoutryDescByCode(countryMap,accountCustInfo.getBirthCountryCode()));
            birthAddressInfoDTO.setProvCode(accountCustInfo.getBirthProvCode());
            birthAddressInfoDTO.setProvDesc(cityInfoMap.get(accountCustInfo.getBirthProvCode()));
            birthAddressInfoDTO.setCityCode(accountCustInfo.getBirthCityCode());
            birthAddressInfoDTO.setCityDesc(cityInfoMap.get(accountCustInfo.getBirthCityCode()));
            birthAddressInfoDTO.setCountyCode(accountCustInfo.getBirthCountyCode());
            birthAddressInfoDTO.setCountyDesc(cityInfoMap.get(accountCustInfo.getBirthCountyCode()));
            birthAddressInfoDTO.setTownEn(accountCustInfo.getBirthTown());
            birthAddressInfoDTO.setStateEn(accountCustInfo.getBirthState());
            birthAddressInfoDTO.setDetailAddrCn(accountCustInfo.getBirthAddrCn());
            birthAddressInfoDTO.setDetailAddrEn(accountCustInfo.getBirthAddrEn());
            openAcctCatchService.saveHkOpenAcctAddressInfoDTO(hkCustNo, HkOpenAccountStepEnum.PERSONAL_INFORMATION_BIRTH_DETAIL, birthAddressInfoDTO);
            return OpenAcctConvert.toOpenPersonalInfoVO(orderInfoDTO.getAccountCustInfo(), hkOpenAcctCheckVOS, countryMap, hkCustInfoDTO);
        }
        OpenPersonalInfoVO openPersonalInfoVO = OpenAcctConvert.toOpenPersonalInfoVO(null, null, countryMap, hkCustInfoDTO);
        return getOpenIdentityOcrInfo(identityCardOcr, openPersonalInfoVO,hkOpenAcctIdInfoDTO);
    }

    /**
     * @description: 在个人信息填写回显用户的在开户证件页的信息,前端小程序说是无法带出用户的证件信息
     * @param identityCardOcr	
     * @param openPersonalInfoVO	
     * @param hkOpenAcctIdInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenPersonalInfoVO
     * @author: jinqing.rao
     * @date: 2024/3/21 13:41
     * @since JDK 1.8
     */
    private OpenPersonalInfoVO getOpenIdentityOcrInfo(IdentityCardOcrDTO identityCardOcr, OpenPersonalInfoVO openPersonalInfoVO,HkOpenAcctIdInfoDTO hkOpenAcctIdInfoDTO) {
        if (null != identityCardOcr) {
            openPersonalInfoVO.setBirthday(StringUtils.isNotBlank(identityCardOcr.getBirth()) ? identityCardOcr.getBirth().replace("/", "") : openPersonalInfoVO.getBirthday());
            openPersonalInfoVO.setGender(HkOpenAcctGenderEnum.getDescByCode(identityCardOcr.getGender()));
            SplitNameVO splitNameVO = hkCommonService.splitName(identityCardOcr.getUserName());
            openPersonalInfoVO.setCnSurname(splitNameVO.getFamilyName());
            openPersonalInfoVO.setCnGivenName(splitNameVO.getFirstName());
        }
        if(null != hkOpenAcctIdInfoDTO){
            openPersonalInfoVO.setNationality(hkOpenAcctIdInfoDTO.getIdAreaCode());
            openPersonalInfoVO.setNationalityDesc(hkOpenAcctIdInfoDTO.getIdAreaCodeDesc());
        }
        return openPersonalInfoVO;
    }

    public HkOpenAcctProCitySaveVO saveAddressInfo(HkOpenAcctProCityRequest request) {
        String hkCusNo = getHkCusNo();
        HkOpenAccountStepEnum hkOpenAccountStepEnum = HkOpenAcctAddressTypeEnum.getHkOpenAcctAddressTypeEnumByCode(request.getAddressType());
        if (null == hkOpenAccountStepEnum) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "地址信息类型错误");
        }
        //组装缓存DTO
        HkOpenAcctAddressInfoDTO hkOpenAcctAddressInfoDTO = OpenAcctConvert.toHkOpenAcctAddressInfoDTO(request);
        //获取国家信息
        Map<String, HkAcctCountryDTO> countryDtoMap = hkAccCommonOuterService.getCountryDtoMap();
        //详细地址
        String detail = getAddressDetail(request,countryDtoMap);
        //英文详细地址
        String englishDetail = getEnglishAddressDetail(request);
        BaiduTranslateForeignResponseDTO responseDTO = BaiduTranslateForeignResponseDTO.builder().build();
        //这里主要是兼容英文地址缺失的情况,也是通过翻译获取
        if (StringUtils.isBlank(englishDetail) || YesNoEnum.YES.getCode().equals(request.getTransAddress())) {
            //获取英文地址的格式信息对应的中文描述
            String translateDetail = getTranslateAddressDetail(request);
            responseDTO = baiduTranslateOuterService.translate(OpenAcctConvert.toBaiduTranslateForeignRequestDTO(translateDetail, "zh", "en"));
        }
        openAcctCatchService.saveHkOpenAcctAddressInfoDTO(hkCusNo, hkOpenAccountStepEnum, hkOpenAcctAddressInfoDTO);
        if(StringUtils.isBlank(englishDetail)){
            englishDetail = CollectionUtils.isEmpty(responseDTO.getTranslateList()) ? null : responseDTO.getTranslateList().get(0).getForeignLanguage();
        }
        return OpenAcctConvert.toHkOpenAcctProCitySaveVO(request.getCountryCode(), detail,englishDetail);
    }

    private void initBirthFullDetailEn(HkOpenAcctCustInfoDTO accountCustInfo, Map<String, HkAcctCountryDTO> countryMap, Map<String, String> cityMap) {
        String birthCountryCode = accountCustInfo.getBirthCountryCode();
        String birthCountryCnDesc = null;
        String countryEnglishDesc = null;
        HkAcctCountryDTO hkAcctCountryDTO = countryMap.get(accountCustInfo.getBirthCountryCode());
        if(null != hkAcctCountryDTO){
            birthCountryCnDesc = hkAcctCountryDTO.getChineseName();
            countryEnglishDesc = hkAcctCountryDTO.getEnglishName();
        }
        String birthAddrEn = accountCustInfo.getBirthAddrEn();
        String birthAddrCn = accountCustInfo.getBirthAddrCn();
        String birthProDesc = cityMap.get(accountCustInfo.getBirthProvCode());
        String birthCityDesc = cityMap.get(accountCustInfo.getBirthCityCode());
        String birthCountyDesc = cityMap.get(accountCustInfo.getBirthCountyCode());
        String townEn = accountCustInfo.getBirthTown();
        String statusEn = accountCustInfo.getBirthState();
        if(HkOpenAcctIdTypeUtils.CHINA.contains(birthCountryCode)){
            String addressDetail = getAddressDetail(birthCountryCode, birthCountryCnDesc, birthAddrCn, birthProDesc, birthCityDesc, birthCountyDesc, townEn, statusEn, countryEnglishDesc);
            accountCustInfo.setBirthFullAddrCn(addressDetail);
        }else{
            //拼装出生地的英文地址
            String birthFullAddress = birthAddrEn + MarkConstants.SEPARATOR_COMMA + townEn + MarkConstants.SEPARATOR_COMMA + statusEn + MarkConstants.SEPARATOR_COMMA + countryEnglishDesc;
            accountCustInfo.setBirthFullAddrEn(birthFullAddress);
        }
    }

    private void initMailingFullDetailCn(HkOpenAcctCustInfoDTO accountCustInfo, Map<String, HkAcctCountryDTO> countryMap, Map<String, String> cityInfoMap) {
        String mailingCountryCode = accountCustInfo.getMailingCountryCode();
        String mailingCountryDesc = getCoutryDescByCode(countryMap, mailingCountryCode);
        String mailingCountryEn = accountCustInfo.getMailingCountryEn();
        String mailingAddrCn = accountCustInfo.getMailingAddrCn();
        String proDesc = cityInfoMap.get(accountCustInfo.getMailingProvCode());
        String cityDesc = cityInfoMap.get(accountCustInfo.getMailingCityCode());
        String countyDesc = cityInfoMap.get(accountCustInfo.getMailingCountyCode());
        String townEn = accountCustInfo.getMailingTownEn();
        String mailingStateEn = accountCustInfo.getMailingStateEn();

        String mailingAddrEn = accountCustInfo.getMailingAddrEn();
        String mailingProvEn = accountCustInfo.getMailingProvEn();
        String mailingCityEn = accountCustInfo.getMailingCityEn();
        String mailingCountyEn = accountCustInfo.getMailingCountyEn();
        if(HkOpenAcctIdTypeUtils.CHINA.contains(mailingCountryCode)){
            String addressDetail = getAddressDetail(mailingCountryCode, mailingCountryDesc, mailingAddrCn, proDesc, cityDesc, countyDesc, townEn, mailingStateEn, mailingCountryEn);
            accountCustInfo.setMailingFullAddrCn(addressDetail);
            //拼装通讯地的英文地址
            String residenceFullAddress = intiOpenAcctOrderAddressEn(mailingCountryCode, mailingAddrEn, mailingProvEn, mailingCityEn, mailingCountyEn, townEn, mailingStateEn, mailingCountryEn);
            accountCustInfo.setMailingFullAddrEn(residenceFullAddress);
        }else{
            //拼装通讯地的英文地址
            String residenceFullAddress = intiOpenAcctOrderAddressEn(mailingCountryCode, mailingAddrEn, mailingProvEn, mailingCityEn, mailingCountyEn, townEn, mailingStateEn, mailingCountryEn);
            accountCustInfo.setMailingFullAddrEn(residenceFullAddress);
        }
    }

    private void initResidenceFullDetailCn(HkOpenAcctCustInfoDTO accountCustInfo, Map<String, HkAcctCountryDTO> countryMap, Map<String, String> cityInfoMap) {
        String residenceCountryCode = accountCustInfo.getResidenceCountryCode();
        String residenceCountryDesc = getCoutryDescByCode(countryMap, residenceCountryCode);
        String residenceCountryEn = accountCustInfo.getResidenceCountryEn();
        String residenceAddrCn = accountCustInfo.getResidenceAddrCn();
        String proDesc = cityInfoMap.get(accountCustInfo.getResidenceProvCode());
        String cityDesc = cityInfoMap.get(accountCustInfo.getResidenceCityCode());
        String countyDesc = cityInfoMap.get(accountCustInfo.getResidenceCountyCode());
        String townEn = accountCustInfo.getResidenceTownEn();
        String stateEn = accountCustInfo.getResidenceStateEn();

        String residenceAddrEn = accountCustInfo.getResidenceAddrEn();
        String residenceProvEn = accountCustInfo.getResidenceProvEn();
        String residenceCityEn = accountCustInfo.getMailingCityEn();
        String residenceCountyEn = accountCustInfo.getMailingCountyEn();
        if(HkOpenAcctIdTypeUtils.CHINA.contains(residenceCountryCode)){
            String addressDetail = getAddressDetail(residenceCountryCode, residenceCountryDesc, residenceAddrCn, proDesc, cityDesc, countyDesc, townEn, stateEn, residenceCountryEn);
            accountCustInfo.setResidenceFullAddrCn(addressDetail);
            //拼装现居地的英文地址
            String residenceFullAddress = intiOpenAcctOrderAddressEn(residenceCountryCode, residenceAddrEn, residenceProvEn, residenceCityEn, residenceCountyEn, townEn, stateEn, residenceCountryEn);
            accountCustInfo.setResidenceFullAddrEn(residenceFullAddress);
        }else{
            String residenceFullAddress = intiOpenAcctOrderAddressEn(residenceCountryCode, residenceAddrEn, residenceProvEn, residenceCityEn, residenceCountyEn, townEn, stateEn, residenceCountryEn);
            accountCustInfo.setResidenceFullAddrEn(residenceFullAddress);
        }


    }

    private static String getCoutryDescByCode(Map<String, HkAcctCountryDTO> countryMap, String residenceCountryCode) {
        HkAcctCountryDTO hkAcctCountryDTO = countryMap.get(residenceCountryCode);
        String residenceCountryDesc = null;
        if(null != hkAcctCountryDTO){
            residenceCountryDesc = hkAcctCountryDTO.getChineseName();
        }
        return residenceCountryDesc;
    }

    private String getAddressDetail(HkOpenAcctProCityRequest request,Map<String, HkAcctCountryDTO> countryDtoMap) {
        String countryEnDesc = request.getCountryEnglishDesc();
        if(StringUtils.isBlank(countryEnDesc)){
            HkAcctCountryDTO hkAcctCountryDTO = countryDtoMap.get(request.getCountryCode());
            if(null != hkAcctCountryDTO){
                countryEnDesc = hkAcctCountryDTO.getEnglishName();
            }
        }
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(request.getCountryCode())) {
            return request.getCountryDesc() + request.getDetailAddrCn();
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(request.getCountryCode())) {
            return request.getCountryDesc() + request.getProvDesc() + request.getCityDesc() + request.getCountyDesc() + request.getDetailAddrCn();
        }
        return request.getDetailAddrEn() + MarkConstants.SEPARATOR_COMMA + request.getTownEn() + MarkConstants.SEPARATOR_COMMA + request.getStateEn() + MarkConstants.SEPARATOR_COMMA + countryEnDesc;
    }


    private String getAddressDetail(String countryCode, String countryDesc, String detailAddrCn, String provDesc, String cityDesc, String countyDesc, String townEn, String stateEn, String countryEnglishDesc) {
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(countryCode)) {
            return countryDesc + detailAddrCn;
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(countryCode)) {
            return countryDesc + provDesc + cityDesc + countyDesc + detailAddrCn;
        }
        return detailAddrCn + MarkConstants.SEPARATOR_COMMA + townEn + MarkConstants.SEPARATOR_COMMA + stateEn + MarkConstants.SEPARATOR_COMMA + countryEnglishDesc;
    }
    private String intiOpenAcctOrderAddressEn(String countryCode, String detailAddrEn, String provDescEn, String cityDescEn,
                                              String countyDescEn, String townEn, String stateEn, String countryEnglishDesc) {
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(countryCode)) {
            return detailAddrEn + MarkConstants.SEPARATOR_COMMA + countryEnglishDesc;
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(countryCode)) {
            return detailAddrEn + MarkConstants.SEPARATOR_COMMA + countyDescEn + MarkConstants.SEPARATOR_COMMA + cityDescEn + MarkConstants.SEPARATOR_COMMA + provDescEn + MarkConstants.SEPARATOR_COMMA + countryEnglishDesc;
        }
        return detailAddrEn + MarkConstants.SEPARATOR_COMMA + townEn + MarkConstants.SEPARATOR_COMMA + stateEn + MarkConstants.SEPARATOR_COMMA + countryEnglishDesc;
    }

    private String getTranslateAddressDetail(HkOpenAcctProCityRequest request) {
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(request.getCountryCode())) {
            return request.getDetailAddrCn() + MarkConstants.SEPARATOR_COMMA + request.getCountryEnglishDesc();
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(request.getCountryCode())) {
            return request.getDetailAddrCn() + MarkConstants.SEPARATOR_COMMA + request.getCountyDesc() + MarkConstants.SEPARATOR_COMMA + request.getCityDesc() + MarkConstants.SEPARATOR_COMMA + request.getProvDesc() + MarkConstants.SEPARATOR_COMMA + request.getCountryEnglishDesc();
        }
        return request.getDetailAddrEn() + MarkConstants.SEPARATOR_COMMA + request.getTownEn() + MarkConstants.SEPARATOR_COMMA + request.getStateEn() + MarkConstants.SEPARATOR_COMMA + request.getCountryEnglishDesc();
    }

    /**
     * @description: 拼接英文地址
     * @param request
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/3/26 17:55
     * @since JDK 1.8
     */
    private String getEnglishAddressDetail(HkOpenAcctProCityRequest request) {
        //香港,澳门，台湾 全量英文地址拼接
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(request.getCountryCode())) {
            if(StringUtils.isAnyBlank(request.getDetailAddrEn(),request.getCountryEnglishDesc())){
                return null;
            }
            return request.getDetailAddrEn() + MarkConstants.SEPARATOR_COMMA + request.getCountryEnglishDesc();
        }
        //中国大陆英文地址拼接
        if (HkOpenAcctIdTypeUtils.CN.equals(request.getCountryCode())) {
            if(StringUtils.isAnyBlank(request.getDetailAddrEn(),request.getCountyEnglishDesc(),request.getCityEnglishDesc(),request.getProvEnglishDesc(),request.getCountryEnglishDesc())){
                return null;
            }
            return request.getDetailAddrEn() + MarkConstants.SEPARATOR_COMMA + request.getCountyEnglishDesc() + MarkConstants.SEPARATOR_COMMA + request.getCityEnglishDesc() + MarkConstants.SEPARATOR_COMMA + request.getProvEnglishDesc() + MarkConstants.SEPARATOR_COMMA + request.getCountryEnglishDesc();
        }
        return request.getDetailAddrEn() + MarkConstants.SEPARATOR_COMMA + request.getTownEn() + MarkConstants.SEPARATOR_COMMA + request.getStateEn() + MarkConstants.SEPARATOR_COMMA + request.getCountryEnglishDesc();
    }
    public HkOpenAcctAddressInfoVO queryAddressInfoByType(HkOpenAcctAddressRequest request) {
        String addressType = request.getAddressType();
        String hkCusNo = getHkCusNo();
        HkOpenAccountStepEnum hkOpenAccountStepEnum = HkOpenAcctAddressTypeEnum.getHkOpenAcctAddressTypeEnumByCode(addressType);
        if (null == hkOpenAccountStepEnum) {
            throw new BusinessException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), "地址信息类型错误");
        }
        //从缓存获取
        HkOpenAcctAddressInfoDTO hkOpenAcctAddressInfoDTO = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, hkOpenAccountStepEnum);
        if (null != hkOpenAcctAddressInfoDTO) {
            return OpenAcctConvert.toHkOpenAcctAddressInfoVO(hkOpenAcctAddressInfoDTO);
        }
        //获取国家信息缓存
        Map<String, String> countryInfoMap = hkCommonService.getCountryInfoMap();
        //获取城市信息缓存
        Map<String, String> cityInfoMap = hkCommonService.getCityInfoMap();
        //查询账户中心获取用户的开户订单
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCusNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getAccountCustInfo() && StringUtils.isNotBlank(orderInfoDTO.getDealNo())) {
            if (HkOpenAcctAddressTypeEnum.PERSONAL_INFO_RESIDENCE.getCode().equals(addressType)) {
                //现居地
                return OpenAcctConvert.getHkOpenAcctResidenceAddressInfoVO(orderInfoDTO.getAccountCustInfo(), countryInfoMap, cityInfoMap);
            }
            if (HkOpenAcctAddressTypeEnum.PERSONAL_INFO_MAILING.getCode().equals(addressType)) {
                //通讯地
                return OpenAcctConvert.getHkOpenAcctMailingAddressInfoVO(orderInfoDTO.getAccountCustInfo(), countryInfoMap, cityInfoMap);
            }
            if (HkOpenAcctAddressTypeEnum.PERSONAL_INFO_BIRTH.getCode().equals(addressType)) {
                //出生地
                return OpenAcctConvert.getHkOpenAcctBirthplaceAddressInfoVO(orderInfoDTO.getAccountCustInfo(), countryInfoMap, cityInfoMap);
            }
            if (HkOpenAcctAddressTypeEnum.COMPANY_ACCOUNT.getCode().equals(addressType)) {
                //银行账户页面公司地址
                return OpenAcctConvert.getHkOpenAcctAccountOccupationAddressInfoVO(orderInfoDTO.getAccountOccupationDTO(), countryInfoMap, cityInfoMap);
            }
        }
        HkOpenAcctAddressInfoDTO acctAddressInfoDTO = new HkOpenAcctAddressInfoDTO();
        return OpenAcctConvert.toHkOpenAcctAddressInfoVO(acctAddressInfoDTO);
    }

    public CgiResponse<HkOpenPersonalInfoResponseVO> saveOpenPersonalInfo(HkOpenPersonalInfoRequest request) {
        HkOpenPersonalInfoResponseVO hkOpenPersonalInfoResponseVO = new HkOpenPersonalInfoResponseVO();
        String hkCusNo = getHkCusNo();
        //基本字段校验
        HkOpenAcctValidator.validatorOpenPersonalInfoRequest(request);
        //根据客户号查询用户的手机号和邮箱信息,判断用户的手机号或者邮箱是否需要验证
        checkHkOpenAcctMobileOrEmail(request, hkCusNo);
        //获取用户的证件信息缓存数据
        HkOpenAcctIdInfoDTO idInfoDTO = openAcctCatchService.getHkOpenAcctIdInfoDTO(hkCusNo);
        //如果用户是身份证类型,调用账户中心接口，做实名认证校验
        if (null !=  idInfoDTO && HkIdTypeEnum.MAINLAND_ID_CARD.getKey().equals(idInfoDTO.getIdType())) {
            String status = identityCardOcrOuterService.queryRealNameAuthenticationResult(request.getCustChineseName(), IdTypeEnum.IDCARD, idInfoDTO.getIdNo());
            if (!RnAuthStateEnum.RNAUTH_SUCESS.getValue().equals(status)) {
                HkOpenAcctCheckVO hkOpenAcctCheckVO = new HkOpenAcctCheckVO("cnSurname","姓名与所填的证件信息不一致");
                hkOpenPersonalInfoResponseVO.setCheckResult(Arrays.asList(hkOpenAcctCheckVO));
                return CgiResponse.error(ExceptionCodeEnum.HK_OPEN_ACCOUNT_IDENTITY_AUTH_ERROR.getCode(),ExceptionCodeEnum.HK_OPEN_ACCOUNT_IDENTITY_AUTH_ERROR.getDescription(),hkOpenPersonalInfoResponseVO);
            }
        }
        //获取用户的现居地地址
        HkOpenAcctAddressInfoDTO residenceAddress = getResidenceAddressInfoDTO(hkCusNo);
        //获取用户的通讯地地址
        HkOpenAcctAddressInfoDTO mailAddress = getMailAddressInfoDTO(request, residenceAddress, hkCusNo);
        //获取用户的出生地地址
        HkOpenAcctAddressInfoDTO birthAddress = getBirthAddressInfoDTO(request, residenceAddress, hkCusNo);
        //获取账户中心手机号明文,暂定方案,账户中心保证获取到手机号和手机摘要对应的手机号一致。
        String mobile = getMobile(request, hkCusNo);
        //获取账户中心邮箱明文，暂定方案, 账户中心保证获取到邮箱和邮箱摘要对应的邮箱一致。
        String email = getEmail(request, hkCusNo);
        //组合参数
        HkOpenAcctCustInfoDTO hkOpenAcctCustInfoDTO = OpenAcctConvert.toHkOpenAcctCustInfoDTO(request, residenceAddress, mailAddress, birthAddress, mobile, email);
        //保存redis
        openAcctCatchService.saveHkOpenAcctCustInfoDTO(hkCusNo, hkOpenAcctCustInfoDTO);
        //删除审核不通过的证件信息原因
        openAcctCatchService.removeHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.PERSONAL_INFORMATION_CHECK_RESULT, hkCusNo);
        return CgiResponse.ok(hkOpenPersonalInfoResponseVO);
    }
    private void checkHkOpenAcctMobileOrEmail(HkOpenPersonalInfoRequest request, String hkCusNo) {
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(hkCusNo);
        //账户中心没有手机号,校验手机号的验证状态
        if (StringUtils.isBlank(hkCustInfoDTO.getMobileDigest())) {
            //校验用户的手机验证码验证状态,这里的手机验证只是用户在前端做的手机是否可以接受验证码的验证
            boolean checkStatus = openAcctCatchService.checkUserSmsCodeStatus(hkCusNo,request.getMobile());
            if (!checkStatus) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_VERIFICATION_CODE_ERROR);
            }
        }
        if (StringUtils.isBlank(hkCustInfoDTO.getEmailDigest())) {
            //邮箱是否重复绑定
            HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfoByEmail(DigestUtil.digest(request.getEmail()));
            if (!OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfo.getReturnCode()) && !hkCusNo.equals(hkCustInfo.getHkCustNo())) {
                throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "邮箱已经被绑定");
            }
            //校验邮箱的验证状态
            boolean emailCheckStatus = openAcctCatchService.checkUserEmailCodeStatus(hkCusNo,request.getEmail());
            if (!emailCheckStatus) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_EMAIL_VERIFICATION_CODE_ERROR);
            }
        }
    }


    private String getMobile(HkOpenPersonalInfoRequest request, String hkCusNo) {
        if (YesNoEnum.YES.getCode().equals(request.getMobileResource())) {
            //通过手机摘要查询手机的明文手机号
            String mobile = hkCustInfoOuterService.getMobile(hkCusNo);
            if (StringUtils.isBlank(mobile)) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_NOT_EXIST);
            }
            return mobile;
        }
        return request.getMobile();
    }

    private String getEmail(HkOpenPersonalInfoRequest request, String hkCusNo) {
        if (YesNoEnum.YES.getCode().equals(request.getEmailResource())) {
            //通过邮箱的摘要获取邮箱的明文
            HkCustInfoPlaintextDTO custInfoPlaintext = hkCustInfoOuterService.getCustInfoPlaintext(hkCusNo);
            if (StringUtils.isBlank(custInfoPlaintext.getEmail())) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_EMAIL_NOT_EXIST);
            }
            return custInfoPlaintext.getEmail();
        }
        return request.getEmail();
    }
    private HkOpenAcctAddressInfoDTO getResidenceAddressInfoDTO(String hkCusNo) {
        HkOpenAcctAddressInfoDTO residenceAddress = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, HkOpenAcctAddressTypeEnum.PERSONAL_INFO_RESIDENCE.getHkOpenAccountStepEnum());
        if (null == residenceAddress) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_RESIDENCE_ADDRESS_NOT_EXIST);
        }
        return residenceAddress;
    }

    private HkOpenAcctAddressInfoDTO getMailAddressInfoDTO(HkOpenPersonalInfoRequest request, HkOpenAcctAddressInfoDTO residenceAddress, String hkCusNo) {
        HkOpenAcctAddressInfoDTO mailAddress = new HkOpenAcctAddressInfoDTO();
        if (YesNoEnum.YES.getCode().equals(request.getMailingFlag())) {
            mailAddress = residenceAddress;
            request.setMailingDetail(request.getResidenceDetail());
        } else {
            mailAddress = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, HkOpenAcctAddressTypeEnum.PERSONAL_INFO_MAILING.getHkOpenAccountStepEnum());
        }
        if (null == mailAddress) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MAIL_ADDRESS_NOT_EXIST);
        }
        return mailAddress;
    }

    private HkOpenAcctAddressInfoDTO getBirthAddressInfoDTO(HkOpenPersonalInfoRequest request, HkOpenAcctAddressInfoDTO residenceAddress, String hkCusNo) {
        HkOpenAcctAddressInfoDTO birthAddress = new HkOpenAcctAddressInfoDTO();
        if (YesNoEnum.YES.getCode().equals(request.getBirthFlag())) {
            birthAddress = residenceAddress;
            request.setBirthDetail(request.getResidenceDetail());
        } else {
            birthAddress = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, HkOpenAcctAddressTypeEnum.PERSONAL_INFO_BIRTH.getHkOpenAccountStepEnum());
        }
        if (null == birthAddress) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_BIRTH_ADDRESS_NOT_EXIST);
        }
        return birthAddress;
    }

    public void temporaryStorageOpenPersonalInfo(HkOpenPersonalInfoRequest request) {
        String hkCusNo = getHkCusNo();
        //获取用户的现居地地址
        HkOpenAcctAddressInfoDTO residenceAddress = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, HkOpenAcctAddressTypeEnum.PERSONAL_INFO_RESIDENCE.getHkOpenAccountStepEnum());
        //获取用户的通讯地地址
        HkOpenAcctAddressInfoDTO mailAddress = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, HkOpenAcctAddressTypeEnum.PERSONAL_INFO_MAILING.getHkOpenAccountStepEnum());
        //获取用户的出生地地址
        HkOpenAcctAddressInfoDTO birthAddress = openAcctCatchService.getHkOpenAcctAddressInfoDTO(hkCusNo, HkOpenAcctAddressTypeEnum.PERSONAL_INFO_BIRTH.getHkOpenAccountStepEnum());
        //获取账户中心手机号明文,暂定方案,账户中心保证获取到手机号和手机摘要对应的手机号一致。
        String mobile = getMobile(request, hkCusNo);
        //获取账户中心邮箱明文，暂定方案, 账户中心保证获取到邮箱和邮箱摘要对应的邮箱一致。
        String email = getEmail(request, hkCusNo);
        HkOpenAcctCustInfoDTO hkOpenAcctCustInfoDTO = OpenAcctConvert.toHkOpenAcctCustInfoDTO(request, residenceAddress, mailAddress, birthAddress, mobile, email);
        //保存redis
        openAcctCatchService.saveHkOpenAcctCustInfoDTO(hkCusNo, hkOpenAcctCustInfoDTO);
    }
}
