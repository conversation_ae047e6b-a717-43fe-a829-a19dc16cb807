/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.fund;

/**
 * <AUTHOR>
 * @description: 业务代码枚举
 * @date 2024/4/11 10:03
 * @since JDK 1.8
 */
public enum HkFundMBusiCodeEnum {

    SUBS("020", "1120", "认购"),
    PURCHAS<PERSON>("022", "1122", "申购"),
    REDEEM("024", "1124", "赎回"),
    DIV("143", "1143", "分红"),
    FORCE_ADD("144", "1144", "强增"),
    FORCE_SUBTRACT("145", "1145", "强减"),
    FORCE_REDEEM("142", "1142", "强赎"),
    NO_TRADE_OVER_ACCOUNT_IN("134", "1134", "非交易过户转入"),
    NO_TRADE_OVER_ACCOUNT_OUT("135", "1135", "非交易过户转出"),
    FUND_TERMINATION("151", "1151", "基金终止"),
    FUND_LIQUIDATION("150", "1150", "基金清盘"),
    _112A("02A", "112A", "认缴"),
    _112B("02B", "112B", "实缴"),
    _1136("036", "1136", "基金转换"),
    _119F("09F", "119F", "展期修改"),
    _113B("13B", "113B", "系列合并转出"),
    _113C("13C", "113C", "系列合并转入"),
    _119A("09A", "119A", "交易过户申请"),
    _119B("09B", "119B", "赎回"),
    _119C("09C", "119C", "申购"),
    _119D("19D", "119D", "平衡因子更新"),
    _119E("19E", "119E", "平衡因子兑换");

    private final String code;
    private final String mCode;
    private final String description;

     HkFundMBusiCodeEnum(String code, String mCode, String description) {
        this.code = code;
        this.mCode = mCode;
        this.description = description;
    }

    public static String getDescByMCode(String mCode) {
        for (HkFundMBusiCodeEnum value : HkFundMBusiCodeEnum.values()) {
            if (value.getMCode().equals(mCode)) {
                return value.getDescription();
            }
        }
        return null;
    }

    public static String getCodeByMCode(String mCode) {
        for (HkFundMBusiCodeEnum value : HkFundMBusiCodeEnum.values()) {
            if (value.getMCode().equals(mCode)) {
                return value.getCode();
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getMCode() {
        return this.mCode;
    }

    public String getDescription() {
        return this.description;
    }


}
