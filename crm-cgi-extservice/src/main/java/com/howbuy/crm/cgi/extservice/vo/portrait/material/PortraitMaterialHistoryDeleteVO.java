package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 素材库对话检索历史删除响应
 * @Date 2024-09-06 10:02:18
 */
@Getter
@Setter
@ToString
public class PortraitMaterialHistoryDeleteVO extends Body {

    private static final long serialVersionUID = 1L;
    
    // 无需返回额外数据
} 