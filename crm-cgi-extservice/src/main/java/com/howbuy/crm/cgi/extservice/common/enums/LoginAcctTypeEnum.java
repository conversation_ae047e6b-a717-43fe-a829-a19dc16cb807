package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: 登录账户类型
 * @date 2023/6/8 13:48
 * @since JDK 1.8
 */
public enum LoginAcctTypeEnum {

    ID_NO("0", "证件号码"),
    MO<PERSON>LE("1", "手机号"),
    EMAIL("2", "邮箱");

    private final String key;
    private final String desc;

    public static LoginAcctTypeEnum getLoginAcctTypeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        LoginAcctTypeEnum loginAcctTypeEnum = getLoginAcctTypeEnum(code);
        return loginAcctTypeEnum == null ? null : loginAcctTypeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    LoginAcctTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
