package com.howbuy.crm.cgi.extservice.request.account.hkapp;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/3/5 9:03
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctInvestAuditRequest  implements Serializable {

    private static final long serialVersionUID = -6018053592334547421L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 投资者类型
     */
    @NotBlank(message = "投资者类型不能为空")
    private String investorType;

    /**
     * 证明图片相对路径
     */
    @Valid
    private List<ImageVO> propertyImages;


    /**
     * 设备ID
     */
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String version;
}
