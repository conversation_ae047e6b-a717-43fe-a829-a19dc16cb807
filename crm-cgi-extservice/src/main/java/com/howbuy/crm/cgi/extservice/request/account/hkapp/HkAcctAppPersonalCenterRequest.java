package com.howbuy.crm.cgi.extservice.request.account.hkapp;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 海外APP个人中心请求类
 * <AUTHOR>
 * @date 2024/2/23 11:05
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAcctAppPersonalCenterRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 4890602923102590260L;

    @NotBlank(message = "香港客户号参数错误")
    private String hkCustNo;
}
