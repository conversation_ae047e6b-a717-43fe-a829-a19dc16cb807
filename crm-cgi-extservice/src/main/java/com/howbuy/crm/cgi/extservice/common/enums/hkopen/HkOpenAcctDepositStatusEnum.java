/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * <AUTHOR>
 * @description: 海外开户小程序,入金状态枚举
 * @date 2023/12/4 15:41
 * @since JDK 1.8
 */
public enum HkOpenAcctDepositStatusEnum {
    NOT_REQUIRED("0", "不需审核"),
    PENDING_REVIEW("1", "等待审核"),
    PENDING_RECHECK("2", "等待复核"),
    APPROVED("3", "审核通过"),
    REJECTED("4", "审核不通过/作废"),
    REJECTED_TO_INITIAL("5", "驳回至初审"),
    REJECTED_TO_CUSTOMER("6", "驳回至客户");

    /**
     *枚举编码
     */
    private String code;

    /**
     *枚举描述
     */
    private String desc;


    /**
     * @description: 构造函数
     * @param code	枚举编码
     * @param desc	枚举描述
     * @return 
     * @author: jinqing.rao
     * @date: 2023/12/4 18:46
     * @since JDK 1.8
     */
    HkOpenAcctDepositStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

