package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitHistoryMsgRemindRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitMsgRemindRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.remindrecord.PortraitRemindRecordService;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitHistoryMsgRemindVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMsgShowRemindVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 客户画像消息提醒事件控制器
 * @Date 2024/9/2 16:15
 */
@Slf4j
@RestController
@RequestMapping("/portrait/msgremind")
@Validated
public class PortraitMsgRemindController {

    @Resource
    private PortraitRemindRecordService portraitRemindRecordService;

    /**
     * @api {POST} /ext/portrait/msgremind/queryshowlist queryshowlist()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMsgRemindController
     * @apiName queryshowlist()
     * @apiDescription 客户画像-消息提醒事件可展示列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {Object} request 请求对象
     * @apiParam (请求参数) {String} request.hboneNo 一账通号
     * @apiParam (请求参数) {String} request.conscode 投顾号
     * @apiParamExample {json} 请求参数示例
     * {
     *   "hboneNo": "HB123456789",
     *   "conscode": "SA123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.msgList 消息提醒事件列表
     * @apiSuccess (响应结果) {String} data.msgList.remindType 提醒类型
     * @apiSuccess (响应结果) {String} data.msgList.remindName 提醒类型名称
     * @apiSuccess (响应结果) {String} data.msgList.materialTitle 素材标题
     * @apiSuccess (响应结果) {String} data.msgList.imgUrl 图片地址
     * @apiSuccess (响应结果) {String} data.msgList.remindDesc 素材分享描述
     * @apiSuccess (响应结果) {String} data.msgList.link 链接地址
     * @apiSuccess (响应结果) {String} data.msgList.remindContent 提醒内容
     * @apiSuccess (响应结果) {String} data.msgList.crateTime 创建时间 yyyy-MM-dd HH:mm:ss
     * @apiSuccess (响应结果) {String} data.msgList.vbId 微伴id
     * @apiSuccess (响应结果) {String} data.msgList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.msgList.materialId 素材Id
     * @apiSuccess (响应结果) {String} data.msgList.materialSendType 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample {json} 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "msgList": [
     *       {
     *         "remindType": "1",
     *         "remindName": "生日提醒",
     *         "materialTitle": "生日快乐",
     *         "imgUrl": "http://example.com/image.jpg",
     *         "remindDesc": "今天是客户生日",
     *         "link": "http://example.com/birthday",
     *         "remindContent": "您的客户今天过生日",
     *         "crateTime": "2024-03-15 10:00:00",
     *         "vbId": "VB123456",
     *         "materialId": "M123456",
     *         "materialSendType": "1"
     *       }
     *     ]
     *   },
     *   "timestampServer": "2024-03-15 10:00:00"
     * }
     */
    @PostMapping("/queryshowlist")
    public CgiResponse<PortraitMsgShowRemindVO> queryshowlist(
            @RequestBody PortraitMsgRemindRequest request
    ) {
        return CgiResponse.appOk(portraitRemindRecordService.getPortraitMsgShowRemindRecordList(request));
    }

    /**
     * @api {POST} /ext/portrait/msgremind/queryhislist queryhislist()
     * @apiVersion 1.0.0
     * @apiGroup PortraitMsgRemindController
     * @apiName queryhislist()
     * @apiDescription 客户画像-消息提醒事件历史列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {Object} request 请求对象
     * @apiParam (请求参数) {String} request.hboneNo 一账通号
     * @apiParam (请求参数) {String} request.conscode 投顾号
     * @apiParam (请求参数) {Array} request.remindTypes 提醒类型筛选列表 1-生日提醒 2-周年提醒 3-未作业提醒 4-已读提醒 5-产品到期提醒 6-投后报告提醒 7-内容上新提醒 8-自定义模版提醒 9-APP产品浏览提醒 10-APP自选浏览提醒 11-APP资讯浏览提醒 12-APP视频浏览提醒
     * @apiParam (请求参数) {Number} request.pageNo 页码，从1开始
     * @apiParam (请求参数) {Number} request.pageSize 每页记录数
     * @apiParamExample {json} 请求参数示例
     * {
     *   "hboneNo": "HB123456789",
     *   "conscode": "SA123456",
     *   "remindTypes": [1, 2, 3],
     *   "pageNo": 1,
     *   "pageSize": 10
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 总数量
     * @apiSuccess (响应结果) {Array} data.dataList 日期提醒数据列表
     * @apiSuccess (响应结果) {String} data.dataList.date 提醒日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {Array} data.dataList.msgList 消息提醒事件列表
     * @apiSuccess (响应结果) {String} data.dataList.msgList.remindType 提醒类型
     * @apiSuccess (响应结果) {String} data.dataList.msgList.remindName 提醒类型名称
     * @apiSuccess (响应结果) {String} data.dataList.msgList.materialTitle 素材标题
     * @apiSuccess (响应结果) {String} data.dataList.msgList.imgUrl 图片地址
     * @apiSuccess (响应结果) {String} data.dataList.msgList.remindDesc 素材分享描述
     * @apiSuccess (响应结果) {String} data.dataList.msgList.link 链接地址
     * @apiSuccess (响应结果) {String} data.dataList.msgList.remindContent 提醒内容
     * @apiSuccess (响应结果) {String} data.dataList.msgList.crateTime 创建时间 yyyy-MM-dd HH:mm:ss
     * @apiSuccess (响应结果) {String} data.dataList.msgList.vbId 微伴id
     * @apiSuccess (响应结果) {String} data.dataList.msgList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.dataList.msgList.materialId 素材Id
     * @apiSuccess (响应结果) {String} data.dataList.msgList.materialSendType 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample {json} 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "total": "100",
     *     "dataList": [
     *       {
     *         "date": "2024-03-15",
     *         "msgList": [
     *           {
     *             "remindType": "1",
     *             "remindName": "生日提醒",
     *             "materialTitle": "生日快乐",
     *             "imgUrl": "http://example.com/image.jpg",
     *             "remindDesc": "今天是客户生日",
     *             "link": "http://example.com/birthday",
     *             "remindContent": "您的客户今天过生日",
     *             "crateTime": "2024-03-15 10:00:00",
     *             "vbId": "VB123456",
     *             "materialId": "M123456",
     *             "materialSendType": "1"
     *           }
     *         ]
     *       }
     *     ]
     *   },
     *   "timestampServer": "2024-03-15 10:00:00"
     * }
     */
    @PostMapping("/queryhislist")
    public CgiResponse<PortraitHistoryMsgRemindVO> queryhislist(
            @RequestBody PortraitHistoryMsgRemindRequest request
    ) {
        return CgiResponse.appOk(portraitRemindRecordService.getPortraitHistoryRemindRecordPageList(request));
    }

}
