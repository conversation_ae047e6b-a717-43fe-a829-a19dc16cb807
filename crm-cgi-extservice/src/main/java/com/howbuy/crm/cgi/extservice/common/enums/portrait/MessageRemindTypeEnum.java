/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.portrait;

/**
 * <AUTHOR>
 * @Description 消息提醒类型枚举
 * @Date 2024/10/16 13:09
 */
public enum MessageRemindTypeEnum {

    Birthday("1", "生日提醒"),

    Anniversary("2", "周年提醒"),

    NotWork("3", "作业提醒"),

    Read("4", "已读提醒"),

    Trade("5", "到期提醒"),

    PorductReport("6", "报告提醒"),

    HasNew("7", "上新提醒"),

    Custom("8", "自定义提醒"),

    ProductBrowse("9", "产品浏览"),

    SelfSelectBrowse("10", "自选浏览"),

    NewsBrowse("11", "资讯浏览"),

    VideoBrowse("12", "视频浏览"),
    ;

    /**
     * 类型
     */
    private String type;

    /**
     * 策略名称
     */
    private String name;

    MessageRemindTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static MessageRemindTypeEnum getByType(String type) {
        for (MessageRemindTypeEnum item : MessageRemindTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }
}
