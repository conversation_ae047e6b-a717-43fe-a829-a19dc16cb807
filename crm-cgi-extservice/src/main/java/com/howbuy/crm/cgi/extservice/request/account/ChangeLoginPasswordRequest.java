package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 修改登录密码
 * @date 2023/6/6 14:27
 * @since JDK 1.8
 */
@Data
public class ChangeLoginPasswordRequest extends AccountBaseRequest {

    /**
     * 原登录密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "原登录密码", isRequired = true)
    private String oldLoginPassword;
    /**
     * 新登录密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "新登录密码", isRequired = true)
    private String newLoginPassword;
}
