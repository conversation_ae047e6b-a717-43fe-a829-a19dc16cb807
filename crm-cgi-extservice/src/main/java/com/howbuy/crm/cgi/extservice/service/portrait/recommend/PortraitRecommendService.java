/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.recommend;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.extservice.convert.portrait.PortraitRecommendConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitRecommendRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.recommend.PortraitRecommendVO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.material.QueryRecommendMaterialOuterService;
import com.howbuy.crm.portrait.client.domain.response.material.QueryRecommendMaterialResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户画像-首页为您推荐服务
 * <AUTHOR>
 * @date 2024-03-19 14:45:00
 */
@Slf4j
@Service
public class PortraitRecommendService {

    @Resource
    private QueryRecommendMaterialOuterService queryRecommendMaterialOuterService;

    /**
     * 获取推荐列表
     * @description 获取首页为您推荐列表信息
     * @param request 推荐列表请求对象
     * @return PortraitRecommendVO 推荐列表响应对象
     * <AUTHOR>
     * @date 2024-03-19 14:45:00
     */
    public PortraitRecommendVO getRecommendList(PortraitRecommendRequest request) {
        log.info("获取推荐列表-请求参数：{}", JSON.toJSONString(request));

        // 调用外部服务获取推荐列表
        QueryRecommendMaterialResponse response = queryRecommendMaterialOuterService.queryRecommendMaterial(
                request.getConscode(),
                request.getHboneNo(),
                request.getRecommendTab(),
                request.getPage(),
                request.getSize()
        );

        log.info("获取推荐列表-响应结果：{}", JSON.toJSONString(response));

        // 使用转换类构建响应对象
        return PortraitRecommendConvert.convertToVO(response);
    }
} 