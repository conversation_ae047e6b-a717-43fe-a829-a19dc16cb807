package com.howbuy.crm.cgi.extservice.vo.hkfund;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:  申购费率计算接口
 * <AUTHOR>
 * @date 2024/4/9 15:25
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKPurchaseFundFeeComputeVO extends Body implements Serializable {

    private static final long serialVersionUID = -1119900485029212300L;

    /**
     * 实际支付金额
     */
    private String actualPayAmt;

    /**
     * 手续费费率 ####.00000000
     */
    private String feeRate;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 原始手续费
     */
    private String originalFee;

    /**
     * 是否大于预约金额 0-否 1-是
     */
    private String largerPrebookAmt;

    /**
     * 折扣是否生效 0-否 1-是
     */
    private String validDiscountRate;

    /**
     * 实际折扣率
     */
    private String actualDiscountRate;

    /**
     * 预约折扣率
     */
    private String prebookDiscountRate;

    /**
     * 分次CALL产品，手续费类型是  1 认缴   2 实缴
     */
    private String feeRateType;


}
