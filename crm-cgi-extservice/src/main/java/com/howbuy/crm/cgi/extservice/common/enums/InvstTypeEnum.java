package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/6/9 10:51
 * @since JDK 1.8
 */
public enum InvstTypeEnum {

    /**
     * 机构
     */
    COMP("0", "机构"),
    /**
     * 个人
     */
    PERS("1", "个人"),
    /**
     * 产品
     */
    PROD("2", "产品");

    private final String key;
    private final String desc;

    public static InvstTypeEnum getInvstTypeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        InvstTypeEnum invstTypeEnum = getInvstTypeEnum(code);
        return invstTypeEnum == null ? null : invstTypeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    InvstTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
