/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.common.*;
import com.howbuy.crm.cgi.extservice.service.portrait.common.PortraitCommonService;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.CustLabelValueVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.CustomizeOptionVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.LabelValueDictVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 公共控制器
 * @date 2024/9/3 15:23
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/portrait/common")
public class PortraitCommonController {

    @Resource
    private PortraitCommonService portraitCommonService;


    /**
     * @api {POST} /ext/portrait/common/updatelabelvalue updateLabelValue()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommonController
     * @apiName updateLabelValue()
     * @apiDescription 修改标签值
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} conscode 投顾编号
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} labelId 标签id
     * @apiParam (请求体) {Array} labelValues 标签值列表
     * @apiParam (请求体) {Array} labelCustomizeValues 标签自定义值列表
     * @apiParamExample 请求体示例
     * {"labelCustomizeValues":["LmoNZH"],"labelValues":["X4"],"labelId":"fYS3","consCode":"3JLyvip","hboneNo":"bYYN0epE5"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"HJePQRUt","description":"yBhHVp4wC","timestampServer":"BL"}
     */
    @PostMapping("/updatelabelvalue")
    @ResponseBody
    public CgiResponse<Body> updateLabelValue(@RequestBody UpdateLabelValueRequest request) {
        portraitCommonService.updateLabelValue(request);
        return CgiResponse.appOk(new Body());
    }


    /**
     * @api {POST} /ext/portrait/common/addcustomizeoption addCustomizeOption()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommonController
     * @apiName addCustomizeOption()
     * @apiDescription 添加自定义标签选项
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} conscode 投顾编号
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} labelId 标签id
     * @apiParam (请求体) {String} customizeOption 自定义选项
     * @apiParamExample 请求体示例
     * {"labelId":"7tjm","customizeOption":"PZdKrS78ti","consCode":"MBhu4T63Cm","hboneNo":"gR8iL"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"jj","description":"A0G","timestampServer":"4qgA"}
     */
    @PostMapping("/addcustomizeoption")
    @ResponseBody
    public CgiResponse<Body> addCustomizeOption(@RequestBody AddCustomizeOptionRequest request) {
        portraitCommonService.addCustomizeOption(request);
        return CgiResponse.appOk(new Body());
    }


    /**
     * @api {POST} /ext/portrait/common/querycustomizeoption queryCustomizeOption()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommonController
     * @apiName queryCustomizeOption()
     * @apiDescription 查询自定义标签选项
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} labelId 标签id
     * @apiParamExample 请求体示例
     * {"labelId":"a9pwzl7pis","hboneNo":"2iFf"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.customizeOptionList
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"8","data":{"customizeOptionList":["ED"]},"description":"28azZSh10","timestampServer":"TANMW5v"}
     */
    @PostMapping("/querycustomizeoption")
    @ResponseBody
    public CgiResponse<CustomizeOptionVO> queryCustomizeOption(@RequestBody QueryCustomizeOptionRequest request) {
        return CgiResponse.appOk(portraitCommonService.queryCustomizeOption(request));
    }

    /**
     * @api {POST} /ext/portrait/common/deletecustomizeoption deleteCustomizeOption()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommonController
     * @apiName deleteCustomizeOption()
     * @apiDescription 删除自定义标签选项
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} conscode 投顾编号
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} labelId 标签id
     * @apiParam (请求体) {Array} deleteCustomizeOptions 删除自定义选项列表
     * @apiParamExample 请求体示例
     * {"labelId":"B4m","deleteCustomizeOptions":["rh"],"consCode":"qTF06","hboneNo":"NUpMd"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"vvqHjq1xM","description":"33z7","timestampServer":"uMRT"}
     */
    @PostMapping("/deletecustomizeoption")
    @ResponseBody
    public CgiResponse<Body> deleteCustomizeOption(@RequestBody DeleteCustomizeOptionRequest request) {
        portraitCommonService.deleteCustomizeOption(request);
        return CgiResponse.appOk(new Body());
    }


    /**
     * @api {POST} /ext/portrait/common/querylabelvalueenum queryLabelValueEnum()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommonController
     * @apiName queryLabelValueEnum()
     * @apiDescription 查询标签值枚举
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.labelValueEnumMap 标签值枚举map key-标签Id value-List<LabelValueEnumVO>
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"5","data":{"labelValueEnumMap":{}},"description":"IFaHKSNk","timestampServer":"vYd"}
     */
    @PostMapping("/querylabelvalueenum")
    @ResponseBody
    public CgiResponse<LabelValueDictVO> queryLabelValueEnum() {
        return CgiResponse.appOk(portraitCommonService.queryLabelValueEnum());
    }

    /**
     * @api {POST} /ext/portrait/common/querycustlabelvalue queryCustLabelValue()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommonController
     * @apiName queryCustLabelValue()
     * @apiDescription 查询客户标签值
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} labelId 标签id
     * @apiParamExample 请求体示例
     * {"labelId":"UFBoX","hboneNo":"zhtu93D"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.singleLabelValue 单选标签值VO
     * @apiSuccess (响应结果) {String} data.singleLabelValue.labelId 标签id
     * @apiSuccess (响应结果) {String} data.singleLabelValue.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.singleLabelValue.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.singleLabelValue.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.singleLabelValue.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.multipleLabelValue 多选标签值VO
     * @apiSuccess (响应结果) {String} data.multipleLabelValue.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.multipleLabelValue.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.multipleLabelValue.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.multipleLabelValue.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"DiaJpPu","data":{"multipleLabelValue":{"labelBdpValues":["CO"],"labelCustomizeValues":["dqvtd"],"labelValues":["2I"],"labelId":"uSb1kj"},"singleLabelValue":{"labelId":"uZHxL8F","labelBdpValue":"gG","labelValue":"AdtOHoJLfV","labelBdpInputValue":"cAZkWjTJ","labelInputValue":"WDamL"}},"description":"0","timestampServer":"H95IyG"}
     */
    @PostMapping("/querycustlabelvalue")
    @ResponseBody
    public CgiResponse<CustLabelValueVO> queryCustLabelValue(@RequestBody QueryCustLabelValueRequest request) {
        return CgiResponse.appOk(portraitCommonService.queryCustLabelValue(request));
    }
}
