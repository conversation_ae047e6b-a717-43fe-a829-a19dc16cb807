package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

/**
 * Copyright (c) 2023, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 验证手机号或者邮箱是否绑定
 * <AUTHOR>
 * @date 2023/12/20 10:47
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctCheckAccountRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -5369631456472060153L;

    /**
     * 类型 mobile:手机号 email:邮箱
     */
    private String type;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机区号
     */
    private String areaCode;
}
