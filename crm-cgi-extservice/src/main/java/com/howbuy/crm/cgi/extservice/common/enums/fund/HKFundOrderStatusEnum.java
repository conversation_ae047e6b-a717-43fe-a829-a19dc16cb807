/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.fund;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/6 14:05
 * @since JDK 1.8
 */
public enum HKFundOrderStatusEnum {
    /**
     * 申请成功
     */
    APPLY_SUCCESS("1", "申请成功"),
    /**
     * 部分确认
     */
    PART_CONFIRM("2", "部分确认"),
    /**
     * 确认成功
     */
    CONFIRM_SUCCESS("3", "确认成功"),
    /**
     * 确认失败
     */
    CONFIRM_FAIL("4", "确认失败"),
    /**
     * 自行撤销
     */
    SELF_CANCEL("5", "自行撤销"),
    /**
     * 强制取消
     */
    FORCE_CANCEL("6", "强制取消"),
    ;

    /**
     * 枚举code
     */
    private final String code;
    /**
     * 枚举的中文意义
     */
    private final String desc;

    HKFundOrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
