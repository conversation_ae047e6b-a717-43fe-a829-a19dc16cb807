package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.service.hkaccount.PasswordService;
import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description: 密码相关
 * @date 2023/6/6 9:23
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/password")
public class PasswordController {

    @Autowired
    private PasswordService passwordService;


    /**
     * @api {POST} /hkaccount/password/changeloginpassword 修改登录密码
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName changeLoginPassword()
     * @apiDescription 修改登录密码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} oldLoginPassword 原登录密码 必须
     * @apiParam (请求体) {String} newLoginPassword 新登录密码 必须
     * @apiParamExample 请求体示例
     * {"oldLoginPassword":"s1AunHzY9c","newLoginPassword":"kQYdbZQ"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"WaYHNdiDbF","data":{"returnCode":"G1VA5Do34","description":"H"},"description":"25onG3Ws","timestampServer":"26NiuO"}
     */
    @PostMapping("/changeloginpassword")
    public CgiResponse<AccountBaseVO> changeLoginPassword(@RequestBody ChangeLoginPasswordRequest request) {
        passwordService.changeLoginPassword(request);
        return CgiResponse.ok(null);
    }


    /**
     * @api {POST} /hkaccount/password/changetradepassword 修改交易密码
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName changeTradePassword()
     * @apiDescription 修改交易密码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} oldTradePassword 原交易密码 必须
     * @apiParam (请求体) {String} newTradePassword 新交易密码 必须
     * @apiParamExample 请求体示例
     * {"oldTradePassword":"acusfJ","newTradePassword":"6"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"iV","data":{"returnCode":"p","description":"N5aGqwzxk"},"description":"DMGf57","timestampServer":"W4QRX"}
     */
    @PostMapping("/changetradepassword")
    public CgiResponse<AccountBaseVO> changeTradePassword(@RequestBody ChangeTradePasswordRequest request) {
        passwordService.changeTradePassword(request);
        return CgiResponse.ok(null);
    }


    /**
     * @api {POST} /hkaccount/password/resetloginpassword 重置登录密码
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName resetLoginPassword()
     * @apiDescription 重置登录密码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号码 必须，二选一
     * @apiParam (请求体) {String} email 邮箱 必须，二选一
     * @apiParam (请求体) {String} verifyCode 验证码 必须
     * @apiParam (请求体) {String} newLoginPassword 新登录密码 必须
     * @apiParamExample 请求体示例
     * {"verifyCode":"C","newLoginPassword":"gLbiK33JlP","mobile":"65rKyw4","email":"fr"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"bd","data":{"returnCode":"G7urDZCXC","description":"BNZMPGw3IQ"},"description":"Cy1RGDe","timestampServer":"jj4"}
     */
    @PostMapping("/resetloginpassword")
    public CgiResponse<AccountBaseVO> resetLoginPassword(@RequestBody ResetLoginPasswordRequest request) {
        passwordService.resetLoginPassword(request);
        return CgiResponse.ok(null);
    }


    /**
     * @api {POST} /hkaccount/password/resettradepassword 重置交易密码
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName resetTradePassword()
     * @apiDescription 重置交易密码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号码 必须，二选一
     * @apiParam (请求体) {String} email 邮箱 必须，二选一
     * @apiParam (请求体) {String} verifyCode 验证码 必须
     * @apiParam (请求体) {String} newTradePassword 新交易密码 必须
     * @apiParamExample 请求体示例
     * {"verifyCode":"mQHTV","newTradePassword":"f7","mobile":"uXFq3GEAjm","email":"yo1CIO"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"S3Xg","data":{"returnCode":"kBs","description":"op"},"description":"LSoS","timestampServer":"CFW"}
     */
    @PostMapping("/resettradepassword")
    public CgiResponse<AccountBaseVO> resetTradePassword(@RequestBody ResetTradePasswordRequest request) {
        passwordService.resetTradePassword(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /hkaccount/password/setloginpassword 设置登录密码
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName setLoginPassword()
     * @apiDescription 设置登录密码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} newLoginPassword 新登录密码 必须
     * @apiParamExample 请求体示例
     * {"newLoginPassword":"Z"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"lsD","data":{"returnCode":"Zp5Z1UO4E","description":"DErHW"},"description":"NJxMRSuW","timestampServer":"wN"}
     */
    @PostMapping("/setloginpassword")
    public CgiResponse<AccountBaseVO> setLoginPassword(@RequestBody SetLoginPasswordRequest request) {
        passwordService.setLoginPassword(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/password/txpasswordverify checkTradePassword()
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName checkTradePassword()
     * @apiDescription 交易密码校验
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParamExample 请求体示例
     * {"txPassword":"p","hkCustNo":"qO5"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"SZ7el","description":"M6c8zzT","timestampServer":"Y"}
     */
    @PostMapping("/txpasswordverify")
    public CgiResponse<AccountBaseVO> checkTradePassword(@RequestBody CheckTradePasswordRequest request) {
        passwordService.checkTradePassword(request);
        return CgiResponse.ok(null);
    }

    /**
     * @api {POST} /ext/hkaccount/password/loginpasswordverify loginpasswordverify()
     * @apiVersion 1.0.0
     * @apiGroup PasswordController
     * @apiName loginpasswordverify()
     * @apiDescription 登录密码校验
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} password 登录密码
     * @apiParamExample 请求体示例
     * {"password":"p","hkCustNo":"qO5"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample {URL_KEY} 接口key
    {
        "CRM_CGI_HKACCOUNT_PASSWORD_LOGINVERIFY": {
            "path": "/hkaccount/password/loginpasswordverify",
            "encryption": "false",
            "host": "h201"
        }
    }
     * @apiSuccessExample {json} 响应结果示例
     * {"code":"SZ7el","description":"M6c8zzT","timestampServer":"Y"}
     */
    @PostMapping("/loginpasswordverify")
    public CgiResponse<AccountBaseVO> loginPasswordVerify(@RequestBody CheckLoginPasswordRequest request) {
        passwordService.checkLoginPassword(request);
        return CgiResponse.ok(null);
    }
}
