/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 问答检索目录查询request
 *
 * <AUTHOR>
 * @date 2024-03-19 10:40:00
 */
@Getter
@Setter
public class QaDirectoryRequest extends BodyRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 来源：1-智能素材库（默认） 2-工作台素材库
     */
    private String source;
}