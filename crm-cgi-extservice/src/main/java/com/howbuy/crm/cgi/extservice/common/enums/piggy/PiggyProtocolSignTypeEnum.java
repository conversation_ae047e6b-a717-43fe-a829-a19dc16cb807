package com.howbuy.crm.cgi.extservice.common.enums.piggy;

import org.apache.commons.lang3.StringUtils;

/**
 * @description: 储蓄罐协议变更类型枚举
 * @author: jinqing.rao
 * @date: 2024/7/23 19:12
 * @since JDK 1.8
 */
public enum PiggyProtocolSignTypeEnum {
    // 协议签署方式 1-线下自主申请开通 2-线上自主申请开通 3-到期自动续期 4-底层基金更换同意
    AGREEMENT_SIGN_TYPE_OFFLINE("1", "线下自主申请开通","线下自主申请开通"),
    AGREEMENT_SIGN_TYPE_ONLINE("2", "线上自主申请开通","储蓄罐线上签约"),
    AGREEMENT_SIGN_TYPE_EXPIRE("3", "到期自动续期",""),
    AGREEMENT_SIGN_TYPE_FUND_CHANGE("4", "底层基金更换同意","储蓄罐底层基金更换");

    private final String code;
    private final String desc;

    private final String disPlay;

    PiggyProtocolSignTypeEnum(String code, String desc,String disPlay) {
        this.code = code;
        this.desc = desc;
        this.disPlay = disPlay;
    }

    /**
     * @description: 通过Code获取展示名称
     * @param code	编码
     * @return com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolSignTypeEnum
     * @author: jinqing.rao
     * @date: 2024/9/23 16:03
     * @since JDK 1.8
     */
    public static String getDisplayByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (PiggyProtocolSignTypeEnum piggyProtocolSignTypeEnum : PiggyProtocolSignTypeEnum.values()) {
            if (piggyProtocolSignTypeEnum.getCode().equals(code)) {
                return piggyProtocolSignTypeEnum.getDisPlay();
            }
        }
        return null;
    }

    public static PiggyProtocolSignTypeEnum getByCode(String code) {
        for (PiggyProtocolSignTypeEnum piggyProtocolSignTypeEnum : PiggyProtocolSignTypeEnum.values()) {
            if (piggyProtocolSignTypeEnum.getCode().equals(code)) {
                return piggyProtocolSignTypeEnum;
            }
        }
        return null;
    }
    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    public String getDisPlay() {
        return disPlay;
    }
}
