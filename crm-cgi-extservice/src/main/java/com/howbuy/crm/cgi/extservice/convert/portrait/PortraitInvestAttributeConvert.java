/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.InvestStyleVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.TradeActionVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.WealthLevelVO;
import com.howbuy.crm.portrait.client.domain.dto.label.InvestStyleDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.TradeActionDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.WealthLevelDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description 客户画像-投资属性convert
 * @Date 2024/9/14 15:43
 */
@Slf4j
public class PortraitInvestAttributeConvert {

    /**
     * wealthLevelDTO 转 wealthLevelVO
     *
     * @param wealthLevelDTO 财富水平DTO
     * @param wealthLevelVO  财富水平VO
     */
    public static void convertWealthLevelVO(WealthLevelDTO wealthLevelDTO, WealthLevelVO wealthLevelVO) {
        if (wealthLevelDTO == null || wealthLevelVO == null) {
            throw new IllegalArgumentException("参数为空!");
        }

        wealthLevelVO.setDataStatsDate(wealthLevelDTO.getDataStatsDate());
        wealthLevelVO.setFamilyTotalAssetVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getFamilyTotalAssetDTO()));
        wealthLevelVO.setPersonalTotalAssetVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getPersonalTotalAssetDTO()));
        wealthLevelVO.setExpectedCanInvestAmountVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getExpectedCanInvestAmountDTO()));
        wealthLevelVO.setIncomeSourceVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getIncomeSourceDTO()));
        wealthLevelVO.setAnnualIncomeVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getAnnualIncomeDTO()));
        wealthLevelVO.setCanDominateIncomeVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getCanDominateIncomeDTO()));
        wealthLevelVO.setCanInvestableRatioVO(LabelValueConvert.convertSingleLabelValueVO(wealthLevelDTO.getCanInvestableRatioDTO()));
    }

    /**
     * @param investStyleDTO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.InvestStyleVO
     * @description:investStyleDTO 转 investStyleVO
     * <AUTHOR>
     * @date 2024/9/18 10:17
     * @since JDK 1.8
     */
    public static InvestStyleVO convertInvestStyleVO(InvestStyleDTO investStyleDTO) {
        if (investStyleDTO == null) {
            throw new IllegalArgumentException("参数为空!");
        }
        InvestStyleVO investStyleVO = new InvestStyleVO();
        investStyleVO.setSubjectiveInvestTargetVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveInvestTargetDTO()));
        investStyleVO.setObjectiveInvestTargetVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getObjectiveInvestTargetDTO()));
        investStyleVO.setSubjectiveRiskToleranceVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveRiskToleranceDTO()));
        investStyleVO.setObjectiveRiskToleranceVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getObjectiveRiskToleranceDTO()));
        investStyleVO.setSubjectiveLiquidityVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveLiquidityDTO()));
        investStyleVO.setObjectiveLiquidityVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getObjectiveLiquidityDTO()));
        investStyleVO.setSubjectiveInvestDeadlineVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveInvestDeadlineDTO()));
        investStyleVO.setObjectiveInvestDeadlineVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getObjectiveInvestDeadlineDTO()));
        investStyleVO.setSubjectiveInvestExperienceVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveInvestExperienceDTO()));
        investStyleVO.setObjectiveInvestExperienceVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getObjectiveInvestExperienceDTO()));
        investStyleVO.setSubjectiveInvestExperienceYearVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveInvestExperienceYearDTO()));
        investStyleVO.setObjectiveInvestExperienceYearVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getObjectiveInvestExperienceYearDTO()));
        investStyleVO.setSubjectiveStrategyPreferenceVO(LabelValueConvert.convertMultipleLabelValueVO(investStyleDTO.getSubjectiveStrategyPreferenceDTO()));
        investStyleVO.setObjectiveStrategyPreferenceVO(LabelValueConvert.convertMultipleLabelValueVO(investStyleDTO.getObjectiveStrategyPreferenceDTO()));
        investStyleVO.setSubjectiveFofPreferenceVO(LabelValueConvert.convertMultipleLabelValueVO(investStyleDTO.getSubjectiveFofPreferenceDTO()));
        investStyleVO.setObjectiveFofPreferenceVO(LabelValueConvert.convertMultipleLabelValueVO(investStyleDTO.getObjectiveFofPreferenceDTO()));
        investStyleVO.setSubjectiveGlobalAssetPreferenceVO(LabelValueConvert.convertMultipleLabelValueVO(investStyleDTO.getSubjectiveGlobalAssetPreferenceDTO()));
        investStyleVO.setObjectiveGlobalAssetPreferenceVO(LabelValueConvert.convertMultipleLabelValueVO(investStyleDTO.getObjectiveGlobalAssetPreferenceDTO()));
        investStyleVO.setSubjectiveIncomeExpectedVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveIncomeExpectedDTO()));
        investStyleVO.setSubjectiveVolatilityExpectedVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveVolatilityExpectedDTO()));
        investStyleVO.setSubjectiveWithdrawnExpectedVO(LabelValueConvert.convertSingleLabelValueVO(investStyleDTO.getSubjectiveWithdrawnExpectedDTO()));
        investStyleVO.setObjectiveIncomeExpected(investStyleDTO.getObjectiveIncomeExpected());
        investStyleVO.setObjectiveVolatilityExpected(investStyleDTO.getObjectiveVolatilityExpected());
        investStyleVO.setObjectiveWithdrawnExpected(investStyleDTO.getObjectiveWithdrawnExpected());
        return investStyleVO;
    }

    /**
     * wealthLevelDTO 转 wealthLevelVO
     *
     * @param wealthLevelDTO 财富水平DTO
     * @return wealthLevelVO
     */
    public static WealthLevelVO convertWealthLevelVO(WealthLevelDTO wealthLevelDTO) {
        WealthLevelVO vo = new WealthLevelVO();
        convertWealthLevelVO(wealthLevelDTO, vo);
        return vo;
    }

    /**
     * tradeActionDTO 转 tradeActionVO
     *
     * @param tradeActionDTO 交易行为DTO
     * @param tradeActionVO  交易行为VO
     */
    public static void convertTradeActionVO(TradeActionDTO tradeActionDTO, TradeActionVO tradeActionVO) {
        if (tradeActionDTO == null || tradeActionVO == null) {
            throw new IllegalArgumentException("参数为空!");
        }

        tradeActionVO.setDataStatsDate(tradeActionDTO.getDataStatsDate());
        tradeActionVO.setCustLevel(tradeActionDTO.getCustLevel());
        tradeActionVO.setAverageBuyCountVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getAverageBuyCountDTO()));
        tradeActionVO.setAverageSellCountVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getAverageSellCountDTO()));
        tradeActionVO.setAverageHoldTimeVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getAverageHoldTimeDTO()));
        tradeActionVO.setSingleCountHoldTimeVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getSingleCountHoldTimeDTO()));
        tradeActionVO.setSingleProductHoldTimeVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getSingleProductHoldTimeDTO()));
        tradeActionVO.setMaxEmptyHoldTimeVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getMaxEmptyHoldTimeDTO()));
        tradeActionVO.setClearHoldSinceTimeVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getClearHoldSinceTimeDTO()));
        tradeActionVO.setAverageBuyPriceVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getAverageBuyPriceDTO()));
        tradeActionVO.setAverageSellPriceVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getAverageSellPriceDTO()));
        tradeActionVO.setTradeWayPreferenceVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getTradeWayPreferenceDTO()));
        tradeActionVO.setPayWayPreferenceVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getPayWayPreferenceDTO()));
        tradeActionVO.setDividendsPreferenceVO(LabelValueConvert.convertSingleLabelValueVO(tradeActionDTO.getDividendsPreferenceDTO()));
        tradeActionVO.setBehaviorDeviationVO(LabelValueConvert.convertMultipleLabelValueVO(tradeActionDTO.getBehaviorDeviationDTO()));
    }

    /**
     * tradeActionDTO 转 tradeActionVO
     *
     * @param tradeActionDTO 交易行为DTO
     */
    public static TradeActionVO convertTradeActionVO(TradeActionDTO tradeActionDTO) {
        TradeActionVO vo = new TradeActionVO();
        convertTradeActionVO(tradeActionDTO, vo);
        return vo;
    }

}
