/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.report;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.convert.portrait.PortraitReportConvert;
import com.howbuy.crm.cgi.extservice.convert.portrait.CheckProductReportPermissionConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBalanceReportRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitProductReportRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitRecommendUpdateRedpointRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.CheckProductReportPermissionRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.PortraitBalanceReportVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.PortraitProductReportVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.recommend.PortraitRecommendUpdateRedpointVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.CheckProductReportPermissionVO;
import com.howbuy.crm.cgi.manager.domain.portrait.report.BalanceReportDTO;
import com.howbuy.crm.cgi.manager.domain.portrait.report.ProductBalanceReportDTO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.report.BalanceReportOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.report.ProductBalanceReportOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.report.UpdateReportRedPointOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.report.CheckProductReportPermissionOuterService;
import com.howbuy.crm.portrait.client.domain.response.report.CheckProductReportPermissionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户画像报告服务实现类
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Slf4j
@Service
public class PortraitReportService {

    @Resource
    private BalanceReportOuterService balanceReportOuterService;

    @Resource
    private ProductBalanceReportOuterService productBalanceReportOuterService;

    @Resource
    private UpdateReportRedPointOuterService updateReportRedPointOuterService;

    @Resource
    private CheckProductReportPermissionOuterService checkProductReportPermissionOuterService;

    /**
     * 查询持仓投后报告
     * @description 根据交易账号和好买账号查询客户的持仓投后报告，包含好买基金、好臻投资和好买香港三个模块的报告信息
     * @param request 持仓投后报告请求对象，包含交易账号和好买账号
     * @return PortraitBalanceReportVO 持仓投后报告响应对象，包含三个模块的报告列表
     * <AUTHOR>
     * @date 2024-03-19 15:30:00
     */
    public PortraitBalanceReportVO getBalanceReport(PortraitBalanceReportRequest request) {
        log.info("查询持仓投后报告-请求参数：request={}", JSON.toJSONString(request));
        
        // 调用外部服务查询持仓投后报告
        BalanceReportDTO balanceReportDTO = balanceReportOuterService.queryBalanceReport(request.getConscode(), request.getHboneNo());
        // 转换为VO
        PortraitBalanceReportVO vo = PortraitReportConvert.getInstance().toPortraitBalanceReportVO(balanceReportDTO);
        
        log.info("查询持仓投后报告-响应结果：{}", vo);
        return vo;
    }

    /**
     * 查询产品持仓投后报告
     * @description 根据交易账号、好买账号、产品代码等参数查询特定产品的持仓投后报告，支持按年份和分页查询
     * @param request 产品持仓投后报告请求对象，包含交易账号、好买账号、产品代码、年份、分页参数
     * @return PortraitProductReportVO 产品持仓投后报告响应对象，包含按年份分组的报告列表
     * <AUTHOR>
     * @date 2024-03-19 15:30:00
     */
    public PortraitProductReportVO getProductReport(PortraitProductReportRequest request) {
        log.info("查询产品持仓投后报告-请求参数：request={}", JSON.toJSONString(request));
        
        // 调用外部服务查询产品持仓投后报告
        ProductBalanceReportDTO productBalanceReportDTO = productBalanceReportOuterService.queryProductBalanceReport(
                request.getConscode(), request.getHboneNo(), request.getFundCode(), request.getYear(), request.getPage(), request.getSize());
        // 转换为VO
        PortraitProductReportVO vo = PortraitReportConvert.getInstance().toPortraitProductReportVO(productBalanceReportDTO);

        log.info("查询产品持仓投后报告-响应结果：{}", vo);
        return vo;
    }

    /**
     * 更新投后报告小红点
     * @description 更新投后报告小红点状态，用于标记用户是否已读
     * @param request 更新小红点请求对象，包含投顾编号和一账通号
     * @return PortraitRecommendUpdateRedpointVO 更新小红点响应对象，包含更新结果
     * <AUTHOR>
     * @date 2024-03-19 15:30:00
     */
    public PortraitRecommendUpdateRedpointVO updateRedpoint(PortraitRecommendUpdateRedpointRequest request) {
        log.info("更新投后报告小红点-请求参数：request={}", JSON.toJSONString(request));
        
        // 调用外部服务更新小红点
        boolean success = updateReportRedPointOuterService.updateReportRedPoint(request.getHboneNo());
        
        // 构建响应对象
        PortraitRecommendUpdateRedpointVO vo = new PortraitRecommendUpdateRedpointVO();
        vo.setResult(success ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        
        log.info("更新投后报告小红点-响应结果：{}", vo);
        return vo;
    }

    /**
     * 校验产品报告发送权限
     * @description 根据投顾编号和产品代码校验产品报告发送权限
     * @param request 产品报告发送权限请求对象
     * @return CheckProductReportPermissionVO 产品报告发送权限响应对象
     * <AUTHOR>
     * @date 2025-03-13 13:18:57
     */
    public CheckProductReportPermissionVO checkPermission(CheckProductReportPermissionRequest request) {
        log.info("校验产品报告发送权限-请求参数：request={}", JSON.toJSONString(request));
        
        // 调用外部服务校验产品报告发送权限
        CheckProductReportPermissionResponse response = checkProductReportPermissionOuterService.checkPermission(request.getConscode(), request.getFundCode());
        // 转换为VO
        CheckProductReportPermissionVO vo = CheckProductReportPermissionConvert.convertToVO(response);
        
        log.info("校验产品报告发送权限-响应结果：{}", vo);
        return vo;
    }
} 