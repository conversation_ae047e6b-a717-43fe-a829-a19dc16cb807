/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/7 15:27
 * @since JDK 1.8
 */
@Data
public class RiskExamListVO extends AccountBaseVO implements Serializable {

    /**
     * 问卷ID
     */
    private String examId;

    /**
     * 基本信息问题列表
     */
    private List<QuestionInfoVO> baseQuestions;

    /**
     * 是否有衍生品知识
     */
    private String derivativeKnowledge;

    /**
     * 衍生金融工具知识得分
     */
    private BigDecimal derivativeKnowledgeScore;

    /**
     * 风险测评日期 YYYYMMDD
     */
    private String riskToleranceDate;

    /**
     * 历史结果数据
     */
    private List<HkDerAnswerVO> answerList;

    /**
     * @description: 问题
     * @author: jinqing.rao
     * @date: 2023/11/30 15:09
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class QuestionInfoVO implements Serializable {

        private static final long serialVersionUID = 2378122836349340135L;

        /**
         * 题目id
         */
        private String questionId;

        /**
         * 题目
         */
        private String question;

        /**
         * 多选标识 1是 0否
         */
        private String multipleOption;


        /**
         * 题目类别  暂定 A\B\C\D 基本信息、资产情况、投资经验、投资计划
         */
        private String questionClassify;


        /**
         * 题目类别名称
         */
        private String classifyName;


        /**
         * 历史缓存答案
         */
        private List<String> historyAnswer;

        /**
         * 排序
         */
        private Integer sortNum;

        /**
         * 选项列表
         */
        private List<OptionInfoVO> options;

    }

    /**
     * @description: 选项列表
     * @author: jinqing.rao
     * @date: 2023/11/30 15:17
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class OptionInfoVO implements Serializable {

        private static final long serialVersionUID = 2378122836349340135L;

        /**
         * 选项id
         */
        private String optionId;

        /**
         * 选项 例如 A ,B ,C,D
         */
        private String optionChar;

        /**
         * 选项描述
         */
        private String optionDesc;

        /**
         *默认风险等级
         */
        private String defaultLevel;

        /**
         *限制类型(NONE-无，AGE-年龄)
         */
        private String limitType;

        /**
         *限制值（年龄-存最低年龄）
         */
        private String limitValue;

        /**
         * 分值
         */
        private String score;

        /**
         * 是否衍生知识标识 0-否 1-是
         */
        private String derivativeFlag;
    }

}