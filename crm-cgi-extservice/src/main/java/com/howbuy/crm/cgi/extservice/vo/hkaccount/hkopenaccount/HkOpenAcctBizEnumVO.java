package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 业务枚举
 * @date 2023/12/19 19:09
 * @since JDK 1.8
 */
@Getter
@Setter
@Builder
public class HkOpenAcctBizEnumVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -2552220522602943360L;
    /**
     * 枚举参数
     */
    private List<BizEnumInfo> bizEnumInfoList;

    @Getter
    @Setter
    @Builder
    public static class BizEnumInfo implements Serializable {

        private static final long serialVersionUID = 4992152933887668375L;
        /**
         * 业务编码
         */
        private String bizCode;
        /**
         * 业务描述
         */
        private String bizDesc;

    }

}
