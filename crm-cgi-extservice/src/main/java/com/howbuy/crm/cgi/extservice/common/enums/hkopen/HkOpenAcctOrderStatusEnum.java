package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


/**
 * <AUTHOR>
 * @description: 海外小程序(香港)开户订单状态枚举
 * @date 2023/12/4 15:10
 * @since JDK 1.8
 */
public enum HkOpenAcctOrderStatusEnum {


    NOT_REQUIRED("0", "不需审核","00"),
    PENDING_REVIEW("1", "等待审核","01"),
    PENDING_RECHECK("2", "等待复核","02"),
    APPROVED("3", "审核通过","03"),
    REJECTED("4", "审核不通过/作废","04"),
    REJECTED_TO_INITIAL("5", "驳回至初审","05"),
    REJECTED_TO_CUSTOMER("6", "驳回至客户","06"),

    INVALID("7", "无效","07");
    /**
     * 枚举编码
     */
    private String code;

    /**
     * 枚举描述
     */
    private String desc;

    private String accountCenter;

    HkOpenAcctOrderStatusEnum(String code, String desc,String accountCenter) {
        this.code = code;
        this.desc = desc;
        this.accountCenter = accountCenter;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getAccountCenter() {
        return accountCenter;
    }
}
