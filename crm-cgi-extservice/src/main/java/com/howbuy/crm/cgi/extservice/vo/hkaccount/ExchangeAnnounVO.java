/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

/**
 * @description: (提供小程序的公告对象)
 * <AUTHOR>
 * @date 2023/11/29 10:16
 * @since JDK 1.8
 */
@Data
public class ExchangeAnnounVO extends AccountBaseVO {

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 位置
     */
    private String position;

    /**
     * 重要程度 1: 一次性关闭 2: 重复显示 3: 不可关闭
     */
    private String important;

    /**
     * 顺序
     */
    private String seq;

    /**
     * 说明
     */
    private String desc;

    /**
     * 链接
     */
    private String link;




}