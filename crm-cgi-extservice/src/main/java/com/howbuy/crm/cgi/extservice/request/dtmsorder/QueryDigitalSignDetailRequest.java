/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.dtmsorder;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.DtmsBaseRequest;
import lombok.Data;

/**
 * @description: (查询海外产品电子签约列表接口 dtms-order)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class QueryDigitalSignDetailRequest extends DtmsBaseRequest {
    /**
     * 签约订单号 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "签约订单号", isRequired = true)
    private String signDealNo;

}