package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.bankinfo.HkOpenAcctBankRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.BankInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.HkOpenAcctBankInfoVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctBankInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctSwiftCodeDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 开户步骤六,银行信息服务类
 * <AUTHOR>
 * @date 2024/1/22 19:17
 * @since JDK 1.8
 */
@Service
public class OpenAcctBankInfoService extends OpenAcctAbstractService {


    @Value("${applet.open.cms.bank.logo.key}")
    private String hkOpenAccCmsBankLogoKey;

    @Resource
    private HkBankCardInfoOuterService hkBankCardInfoOuterService;

    public void saveOpenBankInfo(HkOpenAcctBankRequest request) {
        //参数校验
        HkOpenAcctValidator.validatorOpenBankRequest(request);
        String hkCusNo = getHkCusNo();
        //根据是否关联账户,检验银行账户是否重复
        if (YesNoEnum.NO.getCode().equals(request.getJointAccount())) {
            this.checkBankJointAccount(request.getBankAcct());
        }
        //获取银行卡英文名称,账户中心只认英文名称。
        String bankEnName = request.getBankAcctName();
        //获取代理银行的英文名称
        String brokeBankEnName = request.getBrokerBankName();
        //保存缓存
        HkOpenAcctBankInfoDTO hkOpenAcctBankDTO = OpenAcctConvert.toHkOpenAcctBankDTO(request);
        openAcctCatchService.saveHkOpenAcctBankInfo(getHkCusNo(),hkOpenAcctBankDTO);
        //删除审核不通过的银行卡保存原因
        openAcctCatchService.removeHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.BANK_CARD_CHECK_RESULT, hkCusNo);
    }

    private String getBankInfoChinesName(String swiftCode) {
        List<HkOpenAcctSwiftCodeDTO> bankInfo = hkBankCardInfoOuterService.queryHkBlankBySwiftCode(swiftCode);
        if (CollectionUtils.isEmpty(bankInfo)) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_BANK_ACCT_SWIFT_BANK_ERROR);
        }
        return bankInfo.stream().filter(m -> StringUtils.equals(m.getSwiftCode(), swiftCode)).map(HkOpenAcctSwiftCodeDTO::getBankChineseName).findFirst().orElse(null);
    }

    private String getBankInfoEnglishName(String swiftCode) {
        List<HkOpenAcctSwiftCodeDTO> bankInfo = hkBankCardInfoOuterService.queryHkBlankBySwiftCode(swiftCode);
        if (CollectionUtils.isEmpty(bankInfo)) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_BANK_ACCT_SWIFT_BANK_ERROR);
        }
        return bankInfo.stream().filter(m -> StringUtils.equals(m.getSwiftCode(), swiftCode)).map(HkOpenAcctSwiftCodeDTO::getBankName).findFirst().orElse(null);
    }
    /**
     * @param bankAcct 银行账号
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature.ESignatureVO
     * @description: 校验银行卡是否重复
     * @author: jinqing.rao
     * @date: 2023/12/22 9:55
     * @since JDK 1.8
     */
    public void checkBankJointAccount(String bankAcct) {
        List<HkBankCardInfoDTO> hkBankCardInfoDTOS = hkBankCardInfoOuterService.queryHkBankCardInfo(bankAcct);
        if (CollectionUtils.isNotEmpty(hkBankCardInfoDTOS)) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_BANK_ACCT_REPEAT);
        }
    }

    public BankInfoVO queryBankInfoList(String swiftOrBankName) {
        //调用香港账户中心的银行列表查询接口QueryHkSwiftCodeFacade，银行名称默认取银行中文名称，银行中文名称为空，则取银行英文名称。
        List<HkOpenAcctSwiftCodeDTO> hkOpenAcctSwiftCodes = hkBankCardInfoOuterService.queryHkBlankCardList(swiftOrBankName);
        if (CollectionUtils.isEmpty(hkOpenAcctSwiftCodes)) {
            return new BankInfoVO();
        }
        //调用cms的银行logo配置接口
        Map<String, String> cmsConfigInfo = cmsOuterService.getCmsConfigInfo(hkOpenAccCmsBankLogoKey);
        List<BankInfoVO.BankInfo> bankInfos = hkOpenAcctSwiftCodes.stream().map(m -> {
            BankInfoVO.BankInfo bankInfo = new BankInfoVO.BankInfo();
            bankInfo.setBankCode(m.getBankCode());
            bankInfo.setBankName(StringUtils.isBlank(m.getBankChineseName()) ? m.getBankName() : m.getBankChineseName());
            bankInfo.setBankCnName(m.getBankChineseName());
            bankInfo.setBankEnName(m.getBankName());
            String bankUrl = cmsConfigInfo.get(m.getSwiftCode());
            if(StringUtils.isBlank(bankUrl)){
                bankUrl = cmsConfigInfo.get("MRYHLOGO");
            }
            bankInfo.setBankLogoUrl(bankUrl);
            bankInfo.setSwiftCode(m.getSwiftCode());
            return bankInfo;
        }).collect(Collectors.toList());
        BankInfoVO bankInfoVO = new BankInfoVO();
        bankInfoVO.setBankInfoList(bankInfos);
        return bankInfoVO;
    }

    public HkOpenAcctBankInfoVO queryOpenBankInfoDetail() {
        String hkCusNo = getHkCusNo();
        //获取银行卡Logo
        Map<String, String> cmsConfigInfo = cmsOuterService.getCmsConfigInfo(hkOpenAccCmsBankLogoKey);

        //查询缓存获取开户填写的银行卡信息
        HkOpenAcctBankInfoDTO hkOpenAcctBankInfoDTO = openAcctCatchService.getHkOpenAcctBankInfoDTO(hkCusNo);
        //从缓存获取开户订单审核退回修改状态的错误信息,这里的信息是在 com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService.querySubmitResult 回写到缓存的。因为没有业务数据表
        String orderCheckReason = openAcctCatchService.getHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.BANK_CARD_CHECK_RESULT, hkCusNo);
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = JSON.parseArray(orderCheckReason, HkOpenAcctCheckVO.class);
        if (null != hkOpenAcctBankInfoDTO) {
            //获取银行的中文名称,前端优先展示中文,没有再英文
           // initBankChieseName(hkOpenAcctBankInfoDTO);
            return OpenAcctConvert.toOpenBankVO(hkOpenAcctBankInfoDTO, hkOpenAcctCheckVOS,cmsConfigInfo);
        }
        //查询账户中心获取用户的开户订单
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCusNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getAccountBankInfoDTO() && StringUtils.isNotBlank(orderInfoDTO.getDealNo())) {
            HkOpenAcctBankInfoDTO accountBankInfoDTO = orderInfoDTO.getAccountBankInfoDTO();
            //获取银行的中文名称,前端优先展示中文,没有再英文
           // initBankChieseName(accountBankInfoDTO);
            return OpenAcctConvert.toOpenBankVO(accountBankInfoDTO, hkOpenAcctCheckVOS,cmsConfigInfo);
        }
        return OpenAcctConvert.toOpenBankVO(null, null,cmsConfigInfo);
    }

//    private void initBankChieseName(HkOpenAcctBankInfoDTO hkOpenAcctBankInfoDTO) {
//        //获取银行和代理银行的中文名称,优先展示中文名称,如果中文名称为空,则展示英文名称。且账户中心只会保存英文名称，所以这里要通过接口查询中文名称
//        //获取银行卡英文名称,账户中心只认英文名称。
//        String bankCnName = getBankInfoChinesName(hkOpenAcctBankInfoDTO.getSwiftCode());
//        String bankEnName = getBankInfoEnglishName(hkOpenAcctBankInfoDTO.getSwiftCode());
//        //获取代理银行的英文名称
//        String brokeBankCnName = getBankInfoChinesName(hkOpenAcctBankInfoDTO.getBrokerSwiftCode());
//        String brokeBankEnName = getBankInfoEnglishName(hkOpenAcctBankInfoDTO.getBrokerSwiftCode());
//        if(StringUtils.isNotBlank(bankCnName)){
//            hkOpenAcctBankInfoDTO.setBankAcctName(bankCnName);
//        }else {
//            hkOpenAcctBankInfoDTO.setBankAcctName(bankEnName);
//        }
//        hkOpenAcctBankInfoDTO.setBankAcctCnName(bankCnName);
//        hkOpenAcctBankInfoDTO.setBankAcctEnName(bankEnName);
//        if(StringUtils.isNotBlank(brokeBankCnName)){
//            hkOpenAcctBankInfoDTO.setBrokerBankName(brokeBankCnName);
//        }else {
//            hkOpenAcctBankInfoDTO.setBrokerBankName(brokeBankEnName);
//        }
//        hkOpenAcctBankInfoDTO.setBrokerBankCnName(brokeBankCnName);
//        hkOpenAcctBankInfoDTO.setBrokerBankEnglishName(brokeBankEnName);
//    }
}
