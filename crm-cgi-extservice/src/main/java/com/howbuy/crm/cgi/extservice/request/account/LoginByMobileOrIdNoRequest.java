/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (密码登录接口)
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class LoginByMobileOrIdNoRequest extends AccountBaseRequest {
    /**
     * 登录方式 必须，0-证件；1-手机 2-邮箱
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "登录方式", isRequired = true)
    private String loginAcctType;
    /**
     * 地区码 手机号非空时必须
     */
    private String areaCode;
    /**
     * 手机号 登录方式=1-手机时必须
     */
    private String mobile;

    /**
     * 证件地区码
     */
    private String idAreaCode;
    /**
     * 证件类型  证件号码非空时必须
     */
    private String idType;
    /**
     * 证件号码  登录方式=0-证件时必须
     */
    private String idNo;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 登录密码  必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "登录密码", isRequired = true)
    private String loginPassword;

    /**
     * 小程序版本号
     */
    private String appVersion;

    /**
     * 手机型号 （eg：iPhone15 Pro）
     */
    private String deviceName;

    /**
     * 系统版本号
     */
    private String systemVersion;
}