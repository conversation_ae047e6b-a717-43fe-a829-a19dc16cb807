package com.howbuy.crm.cgi.extservice.controller.hkfund;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementListRequest;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementDetailRequest;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementSubmitRequest;
import com.howbuy.crm.cgi.extservice.service.agreement.SupplementalAgreementService;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementListVO;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementDetailVO;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementSubmitVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 补充协议控制器
 * @author: jin
 * @date: 2024-02-24 10:00:00
 */
@RestController
@RequestMapping("/supplemental/agreement")
public class SupplementalAgreementController {


    @Resource
    private SupplementalAgreementService supplementalAgreementService;

    /**
     * @api {POST} /ext/supplemental/agreement/list list()
     * @apiVersion 1.0.0
     * @apiGroup SupplementalAgreementController
     * @apiName list
     * @apiDescription 补签协议列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {
     *   "hkCustNo": "HK123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.fundAgreementList 协议列表
     * @apiSuccess (响应结果) {String} data.fundAgreementList.fundAddr 协议名称
     * @apiSuccess (响应结果) {String} data.fundAgreementList.fundCode 基金Code
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "data": {
     *     "fundAgreementList": [
     *       {
     *         "fundAddr": "协议A",
     *         "fundCode": "001"
     *       }
     *     ]
     *   },
     *   "description": "成功",
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/list")
    public CgiResponse<SupplementalAgreementListVO> querySupplementalAgreementList(@RequestBody SupplementalAgreementListRequest request) {
      // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(supplementalAgreementService.querySupplementalAgreementList(request));
    }

    /**
     * @api {POST} /ext/supplemental/agreement/detail detail()
     * @apiVersion 1.0.0
     * @apiGroup SupplementalAgreementController
     * @apiName detail
     * @apiDescription 补充协议详情接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金编码
     * @apiParamExample 请求体示例
     * {"fundCode":"96K","hkCustNo":"vgX9xdW9uJ"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fundCode 基金编码
     * @apiSuccess (响应结果) {String} data.fundName 基金名称
     * @apiSuccess (响应结果) {Array} data.supplementalAgreementDetailDtlList 补充协议详情列表
     * @apiSuccess (响应结果) {String} data.supplementalAgreementDetailDtlList.agreementSignEndDt 协议签署截止时间          格式：YYYY-MM-DD HH:MM
     * @apiSuccess (响应结果) {String} data.supplementalAgreementDetailDtlList.agreementName 协议名称
     * @apiSuccess (响应结果) {String} data.supplementalAgreementDetailDtlList.fundCode 基金Code
     * @apiSuccess (响应结果) {String} data.supplementalAgreementDetailDtlList.agreementDescription 协议说明
     * @apiSuccess (响应结果) {String} data.supplementalAgreementDetailDtlList.agreementUrl 协议地址          同一个产品，是一样的，所以通过静态链接地址访问
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"ODr","data":{"fundCode":"It33qJTD","supplementalAgreementDetailDtlList":[{"fundCode":"Dpgy0Yb","agreementUrl":"CG4lwl0","agreementSignEndDt":"qXXURnTX","agreementName":"Ji","agreementDescription":"CDA"}],"fundName":"ahZoa3bR31"},"description":"eaka","timestampServer":"khuNDUf"}
     */
    @PostMapping("/detail")
    public CgiResponse<SupplementalAgreementDetailVO> querySupplementalAgreementDetail(@RequestBody SupplementalAgreementDetailRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(supplementalAgreementService.querySupplementalAgreementDetail(request));
    }

    /**
     * @api {POST} /ext/supplemental/agreement/submit submit()
     * @apiVersion 1.0.0
     * @apiGroup SupplementalAgreementController
     * @apiName submit
     * @apiDescription 补签协议提交接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金编码
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParamExample 请求体示例
     * {
     *   "hkCustNo": "HK123456",
     *   "fundCode": "000001",
     *   "txPassword": "123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     */
    @PostMapping("/submit")
    public CgiResponse<SupplementalAgreementSubmitVO> submitSupplementalAgreement(@RequestBody SupplementalAgreementSubmitRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(supplementalAgreementService.submitSupplementalAgreement(request));
    }
} 