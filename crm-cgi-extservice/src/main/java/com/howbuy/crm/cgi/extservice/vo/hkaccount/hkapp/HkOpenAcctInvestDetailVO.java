package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 海外App专业资质信息审核详情
 * <AUTHOR>
 * @date 2024/3/4 19:19
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctInvestDetailVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 2097986878390600968L;

    /**
     * 审核结果 0：未提交 1:审核通过  2:审核中 3:审核不通过 4 : 已过期
     */
    private String auditStatus;

    /**
     * 图片地址
     */
    private List<ImageVO> propertyImages;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 投资者类型
     */
    private String investorType;

    /**
     * 资产有效时间 时间 yyyy-MM-dd
     */
    private String assetEffectiveTime;
}
