package com.howbuy.crm.cgi.extservice.common.enums.fund;

import com.google.common.collect.Lists;
import com.howbuy.crm.base.YesOrNoEnum;
import crm.howbuy.base.utils.StringUtil;

import java.util.List;

public enum HKRedeemDirectionEnum {

    ELECTRIC_REMITTANCE("1", "回银行卡",0,"本人银行卡"),
    LEAVE_ACCOUNT("2", "留账好买香港账户",1,"现金余额(本人在好买香港的现金账户)"),
    OVERSEAS_CXG("3", "回海外储蓄罐",2,"海外储蓄罐"),
    CHEQUE("4", "基金转投",3,"基金转投");

    private final String key;
    private final String desc;

    private final int index;

    private final String disPlay;

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    public int getIndex(){
        return this.index;
    }

    public String getDisPlay() {
        return disPlay;
    }

    HKRedeemDirectionEnum(String key, String desc,int index,String disPlay) {
        this.key = key;
        this.desc = desc;
        this.index = index;
        this.disPlay = disPlay;
    }

    /**
     * @description: 历史的代码逻辑,设计到很多历史数据的改造，这里直接复用了历史的代码
     * @param payMentMode
     * @return java.util.List<com.howbuy.crm.cgi.extservice.common.enums.fund.HKRedeemDirectionEnum>
     * @author: jinqing.rao
     * @date: 2025/2/18 10:48
     * @since JDK 1.8
     */
    public static List<HKRedeemDirectionEnum> getRedeemDirectionEnumByValue(String payMentMode) {
        if (StringUtil.isEmpty(payMentMode)) {
            return Lists.newArrayList();
        } else {
            List<HKRedeemDirectionEnum> returnList = Lists.newArrayList();
            HKRedeemDirectionEnum[] var2 = values();
            int var3 = var2.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                HKRedeemDirectionEnum redeemDirectionEnum = var2[var4];
                if (YesOrNoEnum.YES.getCode().equals(String.valueOf(payMentMode.charAt(redeemDirectionEnum.index)))) {
                    returnList.add(redeemDirectionEnum);
                }
            }
            return returnList;
        }
    }
}
