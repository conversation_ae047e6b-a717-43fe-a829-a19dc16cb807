/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (合同及协议详情)
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Data
public class AgreementVO implements Serializable, Comparable<AgreementVO> {

    /**
     * 文件代码
     */
    private String fileCode;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String filePathUrl;

    @Override
    public int compareTo(AgreementVO vo) {
        //产品默认合同 排序优先级大于 产品合同，A > B 反正正数，compareTo默认升序，即正数置换位置，负数、0不变。
        if(fileCode.startsWith("D") && vo.getFileCode().startsWith("C")) {
            return -1;
        }
        if(fileCode.startsWith("C") && vo.getFileCode().startsWith("D")) {
            return 1;
        }
        return fileCode.compareTo(vo.getFileCode());
    }
}