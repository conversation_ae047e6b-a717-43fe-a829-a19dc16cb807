/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.portrait;

/**
 * <AUTHOR>
 * @description: 性别 1-女 2-男 3-非自然人
 * @date 2024/9/10 10:05
 * @since JDK 1.8
 */
public enum PortraitGenderEnum {

    WOMAN("1", "女"),
    MAN("2", "男"),
    NON_NATURAL("3", "非自然人");

    private String code;
    private String desc;

    PortraitGenderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PortraitGenderEnum getByCode(String code) {
        for (PortraitGenderEnum genderEnum : PortraitGenderEnum.values()) {
            if (genderEnum.getCode().equals(code)) {
                return genderEnum;
            }
        }
        return null;
    }

    public static PortraitGenderEnum getByDesc(String desc) {
        for (PortraitGenderEnum genderEnum : PortraitGenderEnum.values()) {
            if (genderEnum.getDesc().equals(desc)) {
                return genderEnum;
            }
        }
        return null;
    }

    /**
     * @param desc
     * @return java.lang.String
     * @description:根据desc获取code
     * <AUTHOR>
     * @date 2024/9/10 14:43
     * @since JDK 1.8
     */
    public static String getCodeByDesc(String desc) {
        PortraitGenderEnum portraitGenderEnum = getByDesc(desc);
        return portraitGenderEnum == null ? null : portraitGenderEnum.getCode();
    }

    /**
     * @description:根据code获取desc
     * @param code
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/9/29 18:02
     * @since JDK 1.8
     */
    public static String getDescByCode(String code) {
        PortraitGenderEnum portraitGenderEnum = getByCode(code);
        return portraitGenderEnum == null ? null : portraitGenderEnum.getDesc();
    }

}
