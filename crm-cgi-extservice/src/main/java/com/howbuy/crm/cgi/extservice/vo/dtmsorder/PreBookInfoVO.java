/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (预约详情)
 * <AUTHOR>
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Data
public class PreBookInfoVO implements Serializable {

    /**
     * 预约流水号
     */
    private String preBookId;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品简称
     */
    private String fundNameAbbr;
    /**
     * 交易方式 1-购买；2-赎回
     */
    private String tradeMode;
    /**
     * 预约金额
     */
    private String preAmt;
    /**
     * 预约份额
     */
    private String preVol;
    /**
     * 当前币种
     */
    private String currency;
    /**
     * 预约日期		yyyy-MM-dd
     */
    private String preDt;
    /**
     * 预计交易日期  	yyyy-MM-dd
     */
    private String expectTradeDt;
    /**
     * 线上签约标识  0-不支持；1-支持；2-待确定
     */
    private String onlineSignFlag;
    /**
     * 交易状态	0-全部；1-待签约；2-交易中；3-交易成功；4-交易失败；
     */
    private String tradeState;
    /**
     * 签约日期		yyyy-MM-dd
     */
    private String signDt;
    /**
     * 产品风险等级 1：“R1-低风险”；2：“R2-中低风险”；3：“R3-中风险”；4：“R4-中高风险”；5：“R5-高风险”
     *//*
    private String fundRiskLevel;
    *//**
     * 分红方式 1-现金；2-再投资；
     *//*
    private String divMode;
    *//**
     * 原始手续费率
     *//*
    private String orgiFeeRatio;
    *//**
     * 预约折扣率
     *//*
    private String preDiscountRate;
    *//**
     * 折扣率
     *//*
    private String discountRate;
    *//**
     * 预估手续费
     *//*
    private String fee;
    *//**
     * 赎回方式	 1-按份额赎回；2-按金额赎回；
     *//*
    private String redeemMethod;*/
    /**
     * 确认日期	 	yyyy-MM-dd
     */
    private String ackDt;
    /**
     * 确认金额
     *//*
    private String ackAmt;
    *//**
     * 确认份额
     *//*
    private String ackVol;
    *//**
     * 确认手续费
     *//*
    private String ackFee;
    *//**
     * 交易日净值
     *//*
    private String ackNav;*/
    /**
     * 赎回方式	 1-按份额赎回；2-按金额赎回；
     */
    private String redeemMethod;
}