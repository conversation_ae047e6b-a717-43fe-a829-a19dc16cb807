package com.howbuy.crm.cgi.extservice.request.account.hkopenacct.personalInfo;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 海外开户个人信息请求
 * @date 2023/11/29 17:46
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenPersonalInfoRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 499053593148524406L;

    /**
     * 中文姓名
     */
    @NotBlank(message = "中文姓名不能为空")
    private String custChineseName;

    /**
     * 中文姓
     */
    @NotBlank(message = "中文姓不能为空")
    private String cnSurname;

    /**
     * 中文名
     */
    @NotBlank(message = "中文名不能为空")
    private String cnGivenName;

    /**
     * 英文姓名
     */
    @NotBlank(message = "英文姓名不能为空")
    private String custEnName;

    /**
     * 英文姓
     */
    @NotBlank(message = "英文姓不能为空")
    private String enSurname;

    /**
     * 英文名
     */
    @NotBlank(message = "英文名不能为空")
    private String enGivenName;

    /**
     * 性别 0-女 1-男 2-非自然人
     */
    @NotBlank(message = "性别不能为空")
    private String gender;

    /**
     * 是否有曾用名 1:是  0:否
     */
    @NotBlank(message = "是否有曾用名不能为空")
    private String formerNameFlag;

    /**
     * 中文曾用姓
     */
    private String cnFormerSurName;

    /**
     * 中文曾用名
     */
    private String cnFormerName;

    /**
     * 英文曾用姓
     */
    private String enFormerSurName;


    /**
     * 英文曾用名
     */
    private String enFormerName;

    /**
     * 出生日期
     */
    @NotBlank(message = "出生日期不能为空")
    private String birthday;

    /**
     * 国籍
     */
    @NotBlank(message = "国籍不能为空")
    private String nationality;

    /**
     * 国籍
     */
    @NotBlank(message = "国籍描述不能为空")
    private String nationalityDesc;

    /**
     * 婚姻状况 婚姻状况 0-未婚 1-已婚 2-其他
     */
    @NotBlank(message = "婚姻状况不能为空")
    private String marriageStat;

    /**
     * 婚姻情况
     */
    @NotBlank(message = "婚姻情况描述不能为空")
    private String marriageStatDesc;


    /**
     * 婚姻情况 其他类型 婚姻简介
     */
    private String marriageStatRemark;
// // 2025-02-14 紧急需求 删除这个字段  曹阳紧急邮件
//    /**
//     * 教育程度 教育水平 01-小学或以下 02-中学 03-大专或预科 04-大学或本科 05-硕士或以上
//     */
//    @NotBlank(message = "教育程度不能为空")
//    private String eduLevel;

    /**
     * 手机区号
     */
    @NotBlank(message = "手机区号不能为空")
    private String mobileAreaCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 0:CGI 1:账户中心
     */
    @NotBlank(message = "手机号来源不能为空")
    @Pattern(regexp = "^[0-1]$", message = "手机号来源字段参数错误")
    private String mobileResource;

    /**
     * 邮箱掩码
     */
    private String emailMask;
    /**
     * 邮箱摘要摘要
     */
    private String emailDigest;

    /**
     * 0:CGI 1:账户中心
     */
    @NotBlank(message = "邮箱来源不能为空")
    @Pattern(regexp = "^[0-1]$", message = "邮箱来源字段参数错误")
    private String emailResource;

    /**
     * 通讯地址 1: 同现居住地址一样 0: 其他
     */
    @NotBlank(message = "通讯地址标识不能为空")
    private String mailingFlag;

    /**
     * 出生地址 1: 同现居住地址一样 0: 其他
     */
    @NotBlank(message = "出生地址标识不能为空")
    private String birthFlag;

    /**
     * 地址证明URL列表
     */
    private List<ImageVO> residenceInfoCertUrls;

    /**
     * 通讯地址信息
     */
    private List<ImageVO> mailingInfoCertUrls;


    /**
     * 现居地地址
     */
    @Valid
    private AddressDetail residenceDetail;

    /**
     * 通讯地地址
     */
    private AddressDetail mailingDetail;

    /**
     * 出生地地址
     */
    private AddressDetail birthDetail;

    /**
     * 01-保存，02-退出
     */
    @NotBlank(message = "保存类型不能为空")
    private String openSaveType;

    @Setter
    @Getter
    public static class AddressDetail implements Serializable {

        private static final long serialVersionUID = -6303948871691470028L;
        /**
         * 详细地址中文信息
         */
        private String detailAddrCn;

        /**
         * 比较拼接地址
         */
        private String compareDetail;

        /**
         * 详细地址英文信息
         */
        private String detailAddrEn;

    }
}
