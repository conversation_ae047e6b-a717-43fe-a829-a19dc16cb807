package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/6/12 16:54
 * @since JDK 1.8
 */
public enum PasswordTypeEnum {

    LOGIN_PASSWORD("0", "登录密码"),
    TRADE_PASSWORD("1", "交易密码");

    private final String key;
    private final String desc;

    public static PasswordTypeEnum getPasswordTypeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        PasswordTypeEnum passwordTypeEnum = getPasswordTypeEnum(code);
        return passwordTypeEnum == null ? null : passwordTypeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    PasswordTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
