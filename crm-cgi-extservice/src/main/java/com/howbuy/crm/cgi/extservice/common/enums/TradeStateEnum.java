package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 交易方式
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum TradeStateEnum {

    ALL("0", "全部"),
    WAITSIGN("1", "待签约"),
    TRADEING("2", "交易中"),
    TRADESUCCESS("3", "交易成功"),
    TRADEFAIL("4", "交易失败");

    private final String key;
    private final String desc;

    public static TradeStateEnum getTradeState(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        TradeStateEnum tradeStateEnum = getTradeState(code);
        return tradeStateEnum == null ? null : tradeStateEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    TradeStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
