package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/12/8 14:20
 * @since JDK 1.8
 */
@Setter
@Getter
public class RiskDisclosureRequest implements Serializable {

    private static final long serialVersionUID = -9051212416975789441L;

    /**
     *音频播放状态 0-未播放完成；1-已播放完成
     */
    @NotBlank(message = "音频播放状态不能为空")
    private String audioPlayStatus;

    /**
     * 0-未播放完成时必填
     */
    private String remainderTime;
}
