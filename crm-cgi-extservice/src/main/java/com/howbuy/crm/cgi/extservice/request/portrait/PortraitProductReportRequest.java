package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.common.base.PageRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 客户画像-产品持仓投后报告查询请求
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Getter
@Setter
@ToString
public class PortraitProductReportRequest extends PageRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品代码", isRequired = true)
    private String fundCode;

    /**
     * 年份
     */
    private String year;

} 