/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;

import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.utils.SpringContextUtil;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAppCustomerStatusEnumHandler;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenCustomerStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctDepositInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;

import java.util.Arrays;
import java.util.List;

/**
 * @description: 校验用户的开户信息
 * <AUTHOR>
 * @date 2024/4/24 16:49
 * @since JDK 1.8
 */
public class PiggySignOpenAcctStatusValidator implements PiggyValidator<PiggyBankVerificationVO> {

    private final HkCustInfoDTO hkCustInfo;

    /**
     * 客户信息查询服务
     */
    private final HkCustInfoOuterService hkCustInfoOuterService = SpringContextUtil.getBean(HkCustInfoOuterService.class);

    public PiggySignOpenAcctStatusValidator(HkCustInfoDTO hkCustInfo) {
        this.hkCustInfo = hkCustInfo;
    }

    public PiggyBankVerificationVO verification() {
        //用户的状态不等于正常/休眠 去开户
        if (!(HkOpenAcctStatusEnum.NORMAL.getCode().equals(hkCustInfo.getCustState()) || HkOpenAcctStatusEnum.DORMANT.getCode().equals(hkCustInfo.getCustState()))) {
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            //采用责任链模式,获取用户对应的客户状态,hkOpenAccOrderInfoDTO,hkOpenDepositInfoDTO会在具体的枚举中赋值,然后透传到下个枚举
            //获取开户订单信息
            HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustInfo.getHkCustNo());
            //获取用户的入金信息
            List<String> txChannelEnums = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
            HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO = hkCustInfoOuterService.queryHkOpenDepositInfo(hkCustInfo.getHkCustNo(), PayVoucherTypeEnum.OPEN_ACCOUNT_CONFIRM.getCode(), txChannelEnums);
            HkOpenCustomerStatusEnum HkOpenCustomerStatusEnum = HkOpenAppCustomerStatusEnumHandler.HIDE_ACCOUNT_DEPOSIT_AREA_HANDLER.handleRequest(hkCustInfo.getHkCustNo(), hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            // 只有再继续开户的时候,才返回openAcctStep
            piggyBankVerificationVO.setVerfiyState(HkOpenCustomerStatusEnum.getNumberTypeCode());
            piggyBankVerificationVO.setOpenAcctStep(hkOpenAccOrderInfoDTO.getOpenAcctStep());
            return piggyBankVerificationVO;
        }
        return null;
    }
}
