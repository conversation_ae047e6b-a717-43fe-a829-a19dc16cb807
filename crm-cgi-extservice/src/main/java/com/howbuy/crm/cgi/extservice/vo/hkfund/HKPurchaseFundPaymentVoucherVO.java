package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class HKPurchaseFundPaymentVoucherVO extends Body implements Serializable {
    private static final long serialVersionUID = 8490121341515728349L;

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行名称
     */
    private String bankName;


    /**
     * 币种代码 币种编码
     */
    private String currencyCode;


    /**
     * 币种描述  币种名称
     */
    private String currencyDesc;

    /**
     * 申请金额  ####.00
     */
    private String appAmt;


    /**
     * 银行默认logo
     */
    private String bankLogoUrl;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * swiftCode
     */
    private String swiftCode;
}
