package com.howbuy.crm.cgi.extservice.controller.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKPurchaseFundPayVoucherUploadRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKPurchaseFundPaymentVoucherRequest;
import com.howbuy.crm.cgi.extservice.service.hkfund.PurchaseFundPaymentVoucherService;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherRecordVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/purchase/fund/pay/voucher/")
public class PurchaseFundPaymentVoucherController {


    @Resource
    private PurchaseFundPaymentVoucherService purchaseFundPaymentVoucherService;

    /**
     * @api {POST} /ext/purchase/fund/pay/voucher/queryOrderInfo queryPurchaseFundOrderAmountInfo()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundPaymentVoucherController
     * @apiName queryPurchaseFundOrderAmountInfo()
     * @apiDescription 购买页-查询打款凭证页信息接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"orderNo":"q1Erea5Vh","hkCustNo":"m3LK3Ue"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.currencyCode 币种代码 币种编码
     * @apiSuccess (响应结果) {String} data.currencyDesc 币种描述  币种名称
     * @apiSuccess (响应结果) {String} data.appAmt 申请金额  ####.00
     * @apiSuccess (响应结果) {String} data.bankLogoUrl 银行默认logo
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"E9","data":{"bankLogoUrl":"yHOeWKZ","currencyDesc":"L04x4GOAc","appAmt":"N","bankAcctMask":"Pu8fWPftN","bankName":"mtdQR","currencyCode":"H"},"description":"6gSxjbHsk","timestampServer":"jk"}
     */
    @PostMapping("queryOrderInfo")
    public CgiResponse<HKPurchaseFundPaymentVoucherVO> queryPurchaseFundOrderAmountInfo(@RequestBody HKPurchaseFundPaymentVoucherRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(purchaseFundPaymentVoucherService.queryPurchaseFundOrderAmountInfo(request));
    }

    /**
     * @api {POST} /ext/purchase/fund/pay/voucher/submit submitPayVoucher()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundPaymentVoucherController
     * @apiName upload()
     * @apiDescription 购买页-打款凭证上传接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParam (请求体) {String} bankAcctMask 汇款账户
     * @apiParam (请求体) {String} bankName 银行英文名称
     * @apiParam (请求体) {String} swiftCode swiftCode
     * @apiParam (请求体) {String} orderId 资料id
     * @apiParam (请求体) {String} fileTypeId 文件类型id
     * @apiParam (请求体) {String} remitCurCode 汇款币种代码
     * @apiParam (请求体) {String} remitAmt 汇款金额
     * @apiParam (请求体) {String} remark 备注
     * @apiParam (请求体) {Array} delFileIdList 删除图片List
     * @apiParam (请求体) {Array} payVoucherImages 打款凭证图片上传列表
     * @apiParam (请求体) {String} payVoucherImages.url 图片地址
     * @apiParam (请求体) {String} payVoucherImages.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} payVoucherImages.fileName 文件名称
     * @apiParam (请求体) {String} payVoucherImages.exampleFileFormatType 文件类型
     * @apiParam (请求体) {String} tokenId tokenId 用于重复提交
     * @apiParamExample 请求体示例
     * {"orderNo":"Q0ZTorEHVc","tokenId":"kUbgMcB8","bankAcctMask":"fIP","orderId":"2P6CiVHkoa","hkCustNo":"Gi5","swiftCode":"K4arTH8OF8","bankName":"MW","remark":"p","fileTypeId":"EACKd","remitCurCode":"RGeG","delFileIdList":["I"],"payVoucherImages":[{"fileName":"dlRc","exampleFileFormatType":"Pt","url":"ki5a7UGx6","thumbnailUrl":"OYbqeryb1"}],"remitAmt":"BSDJlXk9h"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"DHVCw","description":"e","timestampServer":"l5k2T"}
     */
    @PostMapping("submit")
    public CgiResponse<Body> submitPayVoucher(@RequestBody HKPurchaseFundPayVoucherUploadRequest  request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        purchaseFundPaymentVoucherService.submitPayVoucher(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/purchase/fund/pay/voucher/querypayvoucherrecord queryPayVoucherRecord()
     * @apiVersion 1.0.0
     * @apiGroup PurchaseFundPaymentVoucherController
     * @apiName queryPayVoucherRecord()
     * @apiDescription 打款凭证页-查询打款凭证记录接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParamExample 请求体示例
     * {"orderNo":"vAdB"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} data.bankLogoUrl 银行图标的url
     * @apiSuccess (响应结果) {String} data.bankName 银行中文名称(没有获取英文名称)
     * @apiSuccess (响应结果) {String} data.remitCurCode 汇款币种代码
     * @apiSuccess (响应结果) {String} data.remitAmt 汇款金额
     * @apiSuccess (响应结果) {String} data.remark 备注
     * @apiSuccess (响应结果) {String} data.orderId 资料id
     * @apiSuccess (响应结果) {String} data.fileTypeId 文件类型id
     * @apiSuccess (响应结果) {String} data.payVoucherSource 打款凭证来源 1 : 线上  0：线下
     * @apiSuccess (响应结果) {String} data.payVoucherState 打款凭证状态  1-未上传、2-已上传、3-审核通过、4-审核不通过
     * @apiSuccess (响应结果) {String} data.auditOpinion 审核意见
     * @apiSuccess (响应结果) {String} data.returnReason 退回原因
     * @apiSuccess (响应结果) {Array} data.payVoucherImages 打款凭证图片列表
     * @apiSuccess (响应结果) {String} data.payVoucherImages.id 文件ID
     * @apiSuccess (响应结果) {String} data.payVoucherImages.url 图片地址
     * @apiSuccess (响应结果) {String} data.payVoucherImages.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.payVoucherImages.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"qIVPsokJL","data":{"bankLogoUrl":"cmfI","auditOpinion":"3fSRO4cn7","bankAcctMask":"mJ","orderId":"l38JU","bankName":"ugrtw","remark":"CID","remitCurCode":"Hd0Nu","fileTypeId":"S","returnReason":"33C","payVoucherImages":[{"id":"DQ1SuSS","exampleFileFormatType":"MhEes","url":"XPS603ox4s","thumbnailUrl":"b"}],"remitAmt":"R7Wnk7","payVoucherSource":"X","payVoucherState":"rlKnk"},"description":"4xNaBSUaeG","timestampServer":"P"}
     */
    @PostMapping("querypayvoucherrecord")
    public CgiResponse<HKPurchaseFundPaymentVoucherRecordVO> queryPayVoucherRecord(@RequestBody HKPurchaseFundPaymentVoucherRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(purchaseFundPaymentVoucherService.queryPayVoucherRecord(request));
    }
}
