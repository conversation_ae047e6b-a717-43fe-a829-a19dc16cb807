/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (银行卡信息详情)
 * <AUTHOR>
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Data
public class BankCardVO implements Serializable {

    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 银行卡号
     * a）默认展示：银行名称“前四位”+“****”+“后四位”；
     * b）若客户存在多张银行卡首尾4位数字一致，则增加中间位校验，即将中间位不一致的第一位展示出来，
     * 样式为：银行名称“前四位”+“**”+“第一个不一样的”+“**”+“后四位”
     */
    private String bankAcctNo;
    /**
     * 银行logo地址
     */
    private String bankLogoUrl;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行编号
     */
    private String bankCode;
}