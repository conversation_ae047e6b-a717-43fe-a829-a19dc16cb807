/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.fundfold;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.fundfold.CustFundFoldBaseRequest;
import com.howbuy.crm.cgi.extservice.service.fundfold.CustFundFoldService;
import com.howbuy.crm.cgi.extservice.vo.fundfold.CustFundFoldResponseVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 用户基金持仓信息
 * <AUTHOR>
 * @date 2024/9/6 10:15
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/fund/hold/")
public class CustFundFoldController {

    @Resource
    private CustFundFoldService custFundFoldService;

    /**
     * @api {POST} /ext/fund/hold/queryfundlist queryCustHKFundList()
     * @apiVersion 1.0.0
     * @apiGroup CustFundFoldController
     * @apiName queryCustHKFundList()
     * @apiDescription 查询客户持仓接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"eO6"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.fundHoldList 基金信息
     * @apiSuccess (响应结果) {String} data.fundHoldList.fundCode 基金编码
     * @apiSuccess (响应结果) {String} data.fundHoldList.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.fundHoldList.fundName 基金名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"l77dy2N","data":{"fundHoldList":[{"fundCode":"VeA","fundName":"ZnfL"}]},"description":"L","timestampServer":"YT9Vz"}
     */
    @RequestMapping("queryfundlist")
    public CgiResponse<CustFundFoldResponseVO> queryCustHKFundList(@RequestBody CustFundFoldBaseRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(custFundFoldService.getFundHoldInfo(request.getHkCustNo()));
    }

}
