/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.session;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 香港客户信息
 * <AUTHOR>
 * @date 2023/5/17 13:42
 * @since JDK 1.8
 */
@Data
public class HkCustInfo implements Serializable {
    private static final long serialVersionUID = -8607685132363229954L;
    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 客户类型，0-机构,1-个人,2-产品户
     */
    private String invstType;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 证件号摘要
     */
    private String idNoDigest;
    /**
     * 证件号掩码
     */
    private String idNoMask;
}