/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.hkfund;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundCategoryEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/24 17:05
 * @since JDK 1.8
 */
public class HkFundInvestorQualificationValidator implements HkFundValidator{

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;
    /**
     * 基金信息
     */
    private final FundBasicInfoDTO fundBasicInfoDTO;

    public HkFundInvestorQualificationValidator(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO) {
        this.hkCustInfo = hkCustInfo;
        this.fundBasicInfoDTO = fundBasicInfoDTO;
    }

    @Override
    public HKFundVerificationVO verification() {
        String investorQualification = hkCustInfo.getInvestorQualification();
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getIsInvestmentVirtualAssets())
                || HKFundCategoryEnum.PRIVATE_FUND.getCode().equals(fundBasicInfoDTO.getFundCategory())) {
            //普通投资者
            if (Constants.INVESTOR_QUALIFICATION_NORMAL.equals(investorQualification)) {
                HKFundVerificationVO fundVerificationVO = new HKFundVerificationVO();
                fundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_NOT_PROFESSIONAL_INVESTOR.getCode());
                return fundVerificationVO;
            }
            //专业投资者的资产证明时间是否已经过期
            if (StringUtils.isBlank(hkCustInfo.getAssetCertExpiredDate())) {
                HKFundVerificationVO fundVerificationVO = new HKFundVerificationVO();
                fundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_ASSET_PROOF_EXPIRED.getCode());
                return fundVerificationVO;
            }
            if (!LocalDate.now().isBefore(LocalDate.parse(hkCustInfo.getAssetCertExpiredDate(), DateTimeFormatter.ofPattern(DateUtils.YYYYMMDD)))) {
                HKFundVerificationVO fundVerificationVO = new HKFundVerificationVO();
                fundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_ASSET_PROOF_EXPIRED.getCode());
                return fundVerificationVO;
            }
        }
        return null;
    }
}
