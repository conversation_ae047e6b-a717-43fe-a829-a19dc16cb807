/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

/**
 * @description: (开户文件对象vo)
 * <AUTHOR>
 * @date 2023/11/29 20:25
 * @since JDK 1.8
 */
@Data
public class OpenFileVO {

    /**
     * 文件名称
     */
    private String filaName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件类型名称
     */
    private String fileTypeName;

    /**
     * 文件地址类型,1 ： 静态地址链接,可以直接访问  2：相对路径需要通过文件下载接口，获取文件流下载
     */
    private String fileUrlType;

    public OpenFileVO(String filaName, String fileUrl,String fileTypeName,String fileType) {
        this.filaName = filaName;
        this.fileUrl = fileUrl;
        this.fileTypeName = fileTypeName;
        this.fileType = fileType;
    }

    public OpenFileVO(String filaName, String fileUrl,String fileTypeName,String fileType,String fileUrlType) {
        this.filaName = filaName;
        this.fileUrl = fileUrl;
        this.fileTypeName = fileTypeName;
        this.fileType = fileType;
        this.fileUrlType = fileUrlType;
    }
}