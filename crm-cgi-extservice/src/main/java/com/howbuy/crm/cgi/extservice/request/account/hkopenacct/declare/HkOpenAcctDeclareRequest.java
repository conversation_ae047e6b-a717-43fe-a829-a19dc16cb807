package com.howbuy.crm.cgi.extservice.request.account.hkopenacct.declare;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 声明信息-开户暂存接口
 * <AUTHOR>
 * @date 2023/11/30 10:19
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctDeclareRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 7063638413347178897L;

    /**
     * 声明1选项
     */
    @NotBlank(message = "声明1选项不能为空")
    private String declareOne;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 交易所名称
     */
    private String exchangeName;

    /**
     * 交易编号
     */
    private String tradeNo;

    /**
     * 声明2选项
     */
    @NotBlank(message = "声明2选项不能为空")
    private String declareTwo;

    /**
     * 持证号码
     */
    private String licensedNo;

    /**
     * 雇主书面同意书URL列表
     */
    private List<ImageVO> employerAgreeThumbnailUrls;

    /**
     * 声明3选项
     */
    @NotBlank(message = "声明3选项不能为空")
    private String declareThree;

    /**
     * 相关人士姓名
     */
    private String relateName;

    /**
     * 声明4选项
     */
    @NotBlank(message = "声明4选项不能为空")
    private String declareFour;

    /**
     * 详细说明
     */
    private String declareFourDesc;

    /**
     * 开户暂存类型 必填，01-保存，02-退出
     */
    @NotBlank(message = "开户暂存类型不能为空")
    private String openSaveType;
}
