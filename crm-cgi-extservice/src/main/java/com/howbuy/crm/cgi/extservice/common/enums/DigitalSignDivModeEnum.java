package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 分红方式 1-现金；2-再投资；
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum DigitalSignDivModeEnum {

    CASH("1", "现金"),
    REINVESTMENT("2", "再投资");

    private final String key;
    private final String desc;

    public static DigitalSignDivModeEnum getDigitalSignDivModeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        DigitalSignDivModeEnum digitalSignDivModeEnum = getDigitalSignDivModeEnum(code);
        return digitalSignDivModeEnum == null ? null : digitalSignDivModeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    DigitalSignDivModeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
