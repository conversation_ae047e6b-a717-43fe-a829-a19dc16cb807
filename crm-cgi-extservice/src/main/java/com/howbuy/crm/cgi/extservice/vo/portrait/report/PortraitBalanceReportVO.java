package com.howbuy.crm.cgi.extservice.vo.portrait.report;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 客户画像-持仓投后报告查询响应
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Getter
@Setter
@ToString
public class PortraitBalanceReportVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 好买基金模块list
     */
    private List<FundReportVO> hmjjList;

    /**
     * 好臻投资模块list
     */
    private List<FundReportVO> hztzList;

    /**
     * 好买香港模块list
     */
    private List<FundReportVO> hmxgList;

    /**
     * 用户是否授权（0：否，1：是）
     */
    private String isAuthorization;

    /**
     * 投顾所属组织架构是否香港（0：否，1：是）
     */
    private String tgIsXg;

    /**
     * 产品模块
     */
    @Getter
    @Setter
    @ToString
    public static class FundReportVO {
        /**
         * 基金代码
         */
        private String jjdm;

        /**
         * 基金简称
         */
        private String jjjc;

        /**
         * 产品子类型名称
         */
        private String productSubTypeName;

        /**
         * 是否更多
         * 0：否，1：是
         */
        private String isMoreReport;

        /**
         * 报告集合
         */
        private List<ReportVO> reportList;
    }

    /**
     * 报告
     */
    @Getter
    @Setter
    @ToString
    public static class ReportVO {

        /**
         * 报告ID
         */
        private String reportId;

        /**
         * 报告日期 (MM月dd日)
         */
        private String date;

        /**
         * 报告标题
         */
        private String title;

        /**
         * 是否new标签
         * 0：否，1：是
         */
        private String isNew;

        /**
         * 发送次数
         */
        private String sendNum;

        /**
         * 报告URL
         */
        private String reportUrl;

        /**
         * 素材发送ID
         */
        private String materialId;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;
    }
} 