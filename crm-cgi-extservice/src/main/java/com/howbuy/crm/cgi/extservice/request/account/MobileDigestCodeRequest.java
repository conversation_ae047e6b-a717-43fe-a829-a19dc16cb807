/**
 * Copyright (c) 2023, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;
/**
 * @description: (手机号摘要请求实体)
 * <AUTHOR>
 * @date 2023/11/29 14:29
 * @since JDK 1.8
 */
@Data
public class MobileDigestCodeRequest extends AccountBaseRequest {

    /**
     * 手机号摘要
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "手机号", isRequired = true)
    private String mobileDigest;

    /**
     * 验证码类型  04-交易账户激活短信验证码；05-登录账户激活短信验证码；06-绑定一账通香港手机号短信验证码；07-解绑一账通香港手机号短信验证码；08-解绑一账通好买手机号短信验证码；
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码类型", isRequired = true)
    private String verifyCodeType;

}