package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 照片批量上传
 * @date 2023/11/30 14:24
 * @since JDK 1.8
 */
@Setter
@Getter
public class UploadIdPhotoRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -5149967291167661624L;

    /**
     * 证件类型 00-证件；01-现居地址证明；02-通讯地址证明；03-雇主书面同意书；04-资产证明文件；05-银行子账号照片；06-联名账户补充材料；07-电子签名图片；08-入金打款凭证；
     */
    @NotBlank(message = "图片类型不能为空")
    private String imageType;

    /**
     * 文件流
     */
    private String fileBase64;


    /**
     * 文件流集合,后加的
     */
    private List<String> fileBase64List;

    /**
     * 文件格式类型
     */
    @NotBlank(message = "图片格式类型不能为空")
    private String imageFormat;

    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称不能为空")
    private String imageName;
}
