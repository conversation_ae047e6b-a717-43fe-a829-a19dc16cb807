/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (绑定的一账通客户信息实体类)
 * @date 2023/11/29 19:11
 * @since JDK 1.8
 */
@Data
public class HboneNoInfoVO extends AccountBaseVO {


    /**
     * 一账通绑定状态 0-未绑定 1-已绑定
     */
    private String hboneBindStatus;

    /**
     * 好买姓名
     */
    private String hbCustName;

    /**
     * 好买证件类型
     */
    private String hbIdType;

    /**
     * 好买证件类型描述
     */
    private String hbIdTypeDesc;

    /**
     * 好买证件号码摘要
     */
    private String hbIdNoDigest;

    /**
     * 好买证件号码掩码
     */
    private String hbIdNoMask;

    /**
     * 好买手机号摘要
     */
    private String hbMobileDigest;

    /**
     * 好买手机号掩码
     */
    private String hbMobileMask;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 证件号码摘要
     */
    private String IdNoDigest;

    /**
     * 证件号码掩码
     */
    private String IdNoMask;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 手机号掩码
     */
    private String mobileMask;

    /**
     * 邮箱摘要
     */
    private String emailDigest;

    /**
     * 邮箱掩码
     */
    private String emailMask;


}