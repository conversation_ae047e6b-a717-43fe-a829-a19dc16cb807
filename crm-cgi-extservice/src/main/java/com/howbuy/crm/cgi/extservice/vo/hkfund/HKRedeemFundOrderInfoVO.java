/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/18 15:32
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKRedeemFundOrderInfoVO extends Body implements Serializable {

    private static final long serialVersionUID = 2230273964820527717L;

    /**
     * 赎回方式  0-按份额、1-按金额
     */
    private String redeemMethod;

    /**
     * 是否支持预约 1:是 0：否
     */
    private String supportPrebook;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 赎回金额
     */
    private String redeemAmt;


    /**
     * 赎回份额
     */
    private String redeemVol;

}
