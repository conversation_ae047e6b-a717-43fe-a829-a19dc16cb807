/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: APP个人中心 资产中心现金余额TAB页查询接口
 * <AUTHOR>
 * @date 2024/8/7 14:01
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAppCenterCashTabVO extends Body implements Serializable {

    private static final long serialVersionUID = -1782635011224933372L;

    private String cashBalanceAsset;
    /**
     * 现金余额
     */
    private List<AppCenterCashTabDetail> cashCurrencyList;

    @Setter
    @Getter
    public static class AppCenterCashTabDetail implements Serializable {

        private static final long serialVersionUID = 6181058240715668201L;

        /**
         * 币种
         */
        private String currency;

        /**
         * 换算后的币种金额
         */
        private String conversionAmt;

        /**
         * 原币种金额
         */
        private String originalAmt;

        /**
         * 币种描述
         */
        private String currencyDesc;

        /**
         * 汇率
         */
        private String excRate;

        /**
         * 汇率时间 yyyyMMdd
         */
        private String excRateDate;
    }
}
