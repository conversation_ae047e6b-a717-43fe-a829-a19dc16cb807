package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 素材库对话检索历史新增响应
 * @Date 2024-09-06 09:59:11
 */
@Getter
@Setter
@ToString
public class PortraitMaterialHistoryAddVO extends Body {

    private static final long serialVersionUID = 1L;
    
    // 无需返回额外数据
} 