/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

/**
 * @description: (广告数据的实体对象)
 * <AUTHOR>
 * @date 2023/11/29 10:35
 * @since JDK 1.8
 */
@Data
public class AdMobileVO{

    /**
     * 广告ID 唯一标识
     */
    private String id;

    /**
     * 标题
     */
    private String adTitle;

    /**
     * 广告url
     */
    private String onClick;

    /**
     * 手机系统类型
     */
    private String cId;

    /**
     * 广告位宽度
     */
    private String imgWidth;

    /**
     * 广告位高度
     */
    private String imgHeight;

    /**
     * 广告图片地址
     */
    private String adImg;


}