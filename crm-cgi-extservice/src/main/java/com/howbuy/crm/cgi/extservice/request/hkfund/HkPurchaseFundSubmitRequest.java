package com.howbuy.crm.cgi.extservice.request.hkfund;

/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @description: 申请购买海外基金提交
 * <AUTHOR>
 * @date 2024/4/9 16:19
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkPurchaseFundSubmitRequest extends BodyRequest {

    /**
     * 客户号
     */
    @NotBlank(message = "客户号不能为空")
    private String hkCustNo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String txPassword;

    /**
     * 验证码类型 申请申购 传15
     */
    private String verifyCodeType;

    /**
     * 验证码
     */
    private String verifyCode;

    /**
     * 基金代码
     */
    @NotBlank(message = "基金代码不能为空")
    private String fundCode;

    /**
     * 申购金额
     */
    @NotBlank(message = "申购金额不能为空")
    private String appAmt;

    /**
     * 实缴金额
     */
    private String paidAmt;

    /**
     * 手续费
     */
    @NotBlank(message = "手续费不能为空")
    private String fee;

    /**
     * 实际支付金额
     */
    @NotBlank(message = "实际支付金额不能为空")
    private String actualPayAmt;

    /**
     * 1-电汇、2-支票、3-海外储蓄罐
     */
    @NotBlank(message = "支付方式不能为空")
    private String payMethod;

    /**
     * 1-电汇时必须
     */
    private String cpAcctNo;

    /**
     * 0-否 1-是
     */
    //@NotBlank(message = "同意货币转换不能为空")
    private String agreeCurrencyExchange;

    /**
     * 签署协议标识
     */
    @NotBlank(message = "签署协议标识不能为空")
    private String signFlag;

    /**
     * 预约单号 非必传
     */
    private String prebookDealNo;

    @NotBlank(message = "防重标识不能为空")
    private String hbSceneId;

    /**
     *  认缴 实缴 手续费类型  1 认缴 2 实缴
     */
    private String feeRateType;

    /**
     * 合同及列表
     */
    @Valid
    private List<HKFundAgreementRequest> contractList;

}
