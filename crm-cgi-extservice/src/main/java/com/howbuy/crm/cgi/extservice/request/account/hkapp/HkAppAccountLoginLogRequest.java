package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import com.howbuy.crm.cgi.common.base.PageRequest;
import crm.howbuy.base.validation.MyValidation;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description App登录日志req
 * @Date 2024/8/16 10:15
 */
public class HkAppAccountLoginLogRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = -8094201984249312226L;

    /**
     * App 基础传参,
     */
    @MyValidation(fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }
}
