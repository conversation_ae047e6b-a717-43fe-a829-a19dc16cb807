/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;
import com.howbuy.crm.cgi.common.utils.SpringContextUtil;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHwDealOrderOuterService;

/**
 * @description: 基金有在途交易验证器
 * <AUTHOR>
 * @date 2024/7/22 16:14
 * @since JDK 1.8
 */
public class FundHasInTransitTransValidator implements PiggyValidator<PiggyBankVerificationVO>{

    /**
     * 查询在途交易服务
     */

    private final QueryHwDealOrderOuterService queryHwDealOrderOuterService = SpringContextUtil.getBean(QueryHwDealOrderOuterService.class);

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;

    private final FundBasicInfoDTO fundBasicInfoDTO;


    public FundHasInTransitTransValidator(HkCustInfoDTO hkCustInfo,FundBasicInfoDTO fundBasicInfoDTO) {
        this.fundBasicInfoDTO = fundBasicInfoDTO;
        this.hkCustInfo = hkCustInfo;
    }

    /**
     * @description: 基金存在在途交易
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO
     * @author: jinqing.rao
     * @date: 2024/8/19 14:46
     * @since JDK 1.8
     */
    @Override
    public PiggyBankVerificationVO verification() {
        boolean result = queryHwDealOrderOuterService.hasFundInTransitOrder(fundBasicInfoDTO.getFundCode(),hkCustInfo.getHkCustNo());
        if(result){
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_HAS_PIGGY_IN_TRANSIT.getCode());
            return piggyBankVerificationVO;
        }
        return null;
    }
}
