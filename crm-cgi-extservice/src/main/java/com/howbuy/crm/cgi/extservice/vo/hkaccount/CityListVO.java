/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (省市区列表数据实体类)
 * @date 2023/12/14 15:24
 * @since JDK 1.8
 */
@Data
public class CityListVO{

    /**
     * 名称
     */
    private String mc;
    /**
     * 地区编号
     */
    private String szm;
    /**
     * 地区代码
     */
    private String dm;

    /**
     * 省市区的列表的数据实体类
     */
    private List<CityListVO> dataList;

}