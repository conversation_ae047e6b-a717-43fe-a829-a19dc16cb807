package com.howbuy.crm.cgi.extservice.common.enums.fund;

public enum HKFundPayVoucherStateEnum {

    // 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
    NONE("0", "无需上传"),
    NOT_UPLOAD("1", "未上传"),
    UPLOADED("2", "已上传"),
    AUDIT_PASS("3", "审核通过"),
    AUDIT_FAIL("4", "审核不通过")
    ;

    private final String code;
    private final String desc;

    HKFundPayVoucherStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescriptionByCode(String payVoucherState) {
        for (HKFundPayVoucherStateEnum stateEnum : HKFundPayVoucherStateEnum.values()) {
            if (stateEnum.getCode().equals(payVoucherState)) {
                return stateEnum.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

   public String getDesc() {
        return desc;
   }
}
