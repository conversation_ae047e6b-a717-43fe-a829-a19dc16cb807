/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.dtmsproduct;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.DtmsBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.dtmsproduct.ParamMobileAreaListVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsproduct.ParamMobileAreaVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ParamMobileAreaDTO;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.ParamMobileAreaOuterService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: (手机地区码)
 * <AUTHOR>
 * @date 2023/5/17 14:36
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/dtmsproduct/mobilearea")
public class ParamMobileAreaController {

    @Autowired
    private ParamMobileAreaOuterService paramMobileAreaOuterService;

    /**
     * @api {POST} /inner/dtmsproduct/mobilearea/query 查询手机地区码
     * @apiVersion 1.0.0
     * @apiGroup ParamMobileAreaController
     * @apiName query
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {Object} requestBody
     * @apiParamExample 请求体示例
     * null
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.index 索引
     * @apiSuccess (响应结果) {Array} data.itemList 数据
     * @apiSuccess (响应结果) {String} data.itemList.type 索引
     * @apiSuccess (响应结果) {String} data.itemList.areacode 地区码
     * @apiSuccess (响应结果) {String} data.itemList.areaname 地区名称
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Ivl","data":{"returnCode":"z7vTfBgt","index":["0QMzrpwt"],"description":"b","itemList":[{"areaname":"oUE4al","type":"rycR","areacode":"FW3oh"}]},"description":"Mm5LPk978","timestampServer":"4AG6CnLsj"}
     */
    @PostMapping("/query")
    public CgiResponse<ParamMobileAreaListVO> query(@RequestBody DtmsBaseRequest req) {
        CrmResult<ParamMobileAreaDTO> crmResult = paramMobileAreaOuterService.query();
        ParamMobileAreaListVO paramMobileAreaListVO = new ParamMobileAreaListVO();
        //数据
        ArrayList<List<ParamMobileAreaVO>> common = new ArrayList<>();
        if(Objects.nonNull(crmResult) && Objects.nonNull(crmResult.getData())) {
            ParamMobileAreaDTO data = crmResult.getData();
            //索引
            paramMobileAreaListVO.setIndex(data.getIndex());
            if(CollectionUtils.isNotEmpty(data.getItemList())){
                for(List<com.howbuy.crm.cgi.manager.domain.dtmsproduct.ParamMobileAreaVO> list :data.getItemList()){
                    ArrayList<ParamMobileAreaVO> temp = new ArrayList<>();
                    for(com.howbuy.crm.cgi.manager.domain.dtmsproduct.ParamMobileAreaVO dto:list){
                        ParamMobileAreaVO vo = new ParamMobileAreaVO();
                        transMobileArea(vo,dto);
                        temp.add(vo);
                    }
                    common.add(temp);
                }
            }
        }
        paramMobileAreaListVO.setItemList(common);
        //海外App成功返回 0000
        if(RequestUtil.isHkAppLogin()){
            return CgiResponse.appOk(paramMobileAreaListVO);
        }
        return CgiResponse.ok(paramMobileAreaListVO);
    }

    /**
     * @description:(转化)
     * @param vo	
     * @param dto
     * @return void
     * @author: shuai.zhang
     * @date: 2023/6/8 16:17
     * @since JDK 1.8
     */
    private void transMobileArea(ParamMobileAreaVO vo, com.howbuy.crm.cgi.manager.domain.dtmsproduct.ParamMobileAreaVO dto) {
        vo.setType(dto.getType());
        vo.setAreacode(dto.getAreacode());
        vo.setAreaname(dto.getAreaname());
    }
}