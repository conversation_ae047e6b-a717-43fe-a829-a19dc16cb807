/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.handle;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.validator.piggy.*;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 海外储蓄罐签约验证
 * <AUTHOR>
 * @date 2024/7/19 14:51
 * @since JDK 1.8
 */
public class PiggySignVerificationHandle implements VerificationHandle<PiggyBankVerificationVO> {
    private final List<PiggyValidator<PiggyBankVerificationVO>> validators;

    private final PiggyVerificationContext context;

    public PiggySignVerificationHandle(PiggyVerificationContext context) {
        this.context = context;
        this.validators =  buildHkFundValidators(context.getHkCustInfoDTO(), context.getAgeLimitMap(), context.getFundBasicInfoDTO());
    }

    public PiggyBankVerificationVO verification(){
        //添加过滤器
        PiggyBankVerificationVO verificationVO = new PiggyBankVerificationVO();
        for (PiggyValidator<PiggyBankVerificationVO> validator : validators) {
            verificationVO = validator.verification();
            //校验不通过,返回错误信息
            if(null != verificationVO && StringUtils.isNotBlank(verificationVO.getVerfiyState())){
                return verificationVO;
            }
        }
        //没有错误信息,最后设置成功状态
        if(null == verificationVO){
            verificationVO = new PiggyBankVerificationVO();
        verificationVO.setVerfiyState(HkFundVerificationStatusEnum.NORMAL.getCode());
        verificationVO.setCustRiskLevel(context.getHkCustInfoDTO().getRiskToleranceLevel());
        verificationVO.setFundRiskLevel(context.getFundBasicInfoDTO().getFundRiskLevel());
        }
        return verificationVO;
    }
    /**
     * @description: 海外储蓄罐签约校验器
     * @param hkCustInfo	香港客户号
     * @param ageLimitMap	年龄限制
     * @param fundBasicInfoDTO 基金基础信息
     * @return java.util.List<com.howbuy.crm.cgi.extservice.validator.piggy.PiggyValidator<com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO>>
     * @author: jinqing.rao
     * @date: 2024/8/19 13:43
     * @since JDK 1.8
     */
    protected List<PiggyValidator<PiggyBankVerificationVO>> buildHkFundValidators(HkCustInfoDTO hkCustInfo, Map<String, Integer> ageLimitMap, FundBasicInfoDTO fundBasicInfoDTO) {
        return new ArrayList<PiggyValidator<PiggyBankVerificationVO>>() {
            private static final long serialVersionUID = -5640545335979781797L;
            {
                //客户状态过滤器
                add(new PiggySignCustStatusValidator(hkCustInfo));
                //校验用户的开户信息
                add(new PiggySignOpenAcctStatusValidator(hkCustInfo));
                //年龄限制
                add(new PiggySignAgeLimitValidator(hkCustInfo, ageLimitMap.get(fundBasicInfoDTO.getFundCode())));
                //客户状态过滤器
                add(new PiggySignCustTxAccountValidator(hkCustInfo));
                //底层产品与客户风险匹配校验
                add(new CustRiskLevelLessThanFundRiskLevelValidator(hkCustInfo,fundBasicInfoDTO, YesNoEnum.NO.getCode()));
            }
        };
    }

}
