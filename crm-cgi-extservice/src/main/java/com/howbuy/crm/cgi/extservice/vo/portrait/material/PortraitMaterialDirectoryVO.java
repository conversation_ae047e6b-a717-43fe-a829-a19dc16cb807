package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 素材库索引目录查询响应
 * @Date 2024-09-06 10:03:30
 */
@Getter
@Setter
@ToString
public class PortraitMaterialDirectoryVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 大类列表
     */
    private List<Category> categories;

    /**
     * 分类
     */
    @Getter
    @Setter
    @ToString
    public static class Category {
        /**
         * 大类名称
         */
        private String category;

        /**
         * 子目录列表
         */
        private List<Directory> directoryList;
    }

    /**
     * 子目录
     */
    @Getter
    @Setter
    @ToString
    public static class Directory {
        /**
         * 索引目录名
         */
        private String directory;

        /**
         * 索引目录排序
         */
        private String sortCode;
    }
} 