/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.wechat;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.portrait.wechat.ConfigSignatureRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.wechat.GetWechatUserIdRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.entrance.PortraitLoginService;
import com.howbuy.crm.cgi.extservice.vo.portrait.wechat.ConfigSignatureVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.wechat.WeChatUserIdVO;
import com.howbuy.crm.cgi.manager.domain.crmwechat.ConfigSignatureDTO;
import com.howbuy.crm.cgi.manager.outerservice.crmwechat.WechatCommonOuterService;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/5 16:11
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitWechatService {

    @Resource
    private WechatCommonOuterService wechatCommonOuterService;

    @Resource
    private PortraitLoginService portraitLoginService;

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.wechat.WeChatUserIdVO
     * @description:获取企微用户id
     * <AUTHOR>
     * @date 2024/9/9 16:07
     * @since JDK 1.8
     */
    public WeChatUserIdVO getUserId(GetWechatUserIdRequest request) {
        String userId = wechatCommonOuterService.getUserId(request.getCode(), WechatAppEnum.WEALTH_PORTRAIT.getKey());
        if (StringUtils.isEmpty(userId)) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "获取企微用户Id失败");
        }
        // 创建会话
        portraitLoginService.createSession(userId, null);

        WeChatUserIdVO weChatUserIdVO = new WeChatUserIdVO();
        weChatUserIdVO.setUserId(userId);
        return weChatUserIdVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.wechat.AgentConfigSignatureVO
     * @description:获取企微代理配置签名
     * <AUTHOR>
     * @date 2024/9/5 16:50
     * @since JDK 1.8
     */
    public ConfigSignatureVO getConfigSignature(ConfigSignatureRequest request) {
        ConfigSignatureDTO configSignatureDTO =
                wechatCommonOuterService.getConfigSignature(request.getUrl(), WechatAppEnum.WEALTH_PORTRAIT.getKey());
        if (Objects.isNull(configSignatureDTO)) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "获取企微配置签名失败");
        }
        ConfigSignatureVO configSignatureVO = new ConfigSignatureVO();
        BeanUtils.copyProperties(configSignatureDTO, configSignatureVO);
        return configSignatureVO;
    }
}
