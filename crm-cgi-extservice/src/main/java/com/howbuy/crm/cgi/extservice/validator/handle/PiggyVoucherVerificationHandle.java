/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.handle;

import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggyBindBankCardValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggySignCustStatusValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggySignCustTxAccountValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggyValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 海外储蓄罐签约验证
 * @date 2024/7/19 14:51
 * @since JDK 1.8
 */
public class PiggyVoucherVerificationHandle implements VerificationHandle<PiggyBankVerificationVO> {
    private final List<PiggyValidator<PiggyBankVerificationVO>> validators;

    private final PiggyVerificationContext context;

    public PiggyVoucherVerificationHandle(PiggyVerificationContext context) {
        this.context = context;
        this.validators = buildHkFundValidators(context.getHkCustInfoDTO());
    }

    public PiggyBankVerificationVO verification() {
        //添加过滤器
        PiggyBankVerificationVO verificationVO = new PiggyBankVerificationVO();
        for (PiggyValidator<PiggyBankVerificationVO> validator : validators) {
            verificationVO = validator.verification();
            //校验不通过,返回错误信息
            if (null != verificationVO && StringUtils.isNotBlank(verificationVO.getVerfiyState())) {
                return verificationVO;
            }
        }
        //没有错误信息,最后设置成功状态
        if(null == verificationVO){
            verificationVO = new PiggyBankVerificationVO();
        }
        verificationVO.setVerfiyState(HkFundVerificationStatusEnum.NORMAL.getCode());
        verificationVO.setCustRiskLevel(context.getHkCustInfoDTO().getRiskToleranceLevel());
        verificationVO.setFundRiskLevel(context.getFundBasicInfoDTO().getFundRiskLevel());
        return verificationVO;
    }

    protected List<PiggyValidator<PiggyBankVerificationVO>> buildHkFundValidators(HkCustInfoDTO hkCustInfo) {
        return new ArrayList<PiggyValidator<PiggyBankVerificationVO>>() {
            private static final long serialVersionUID = -5640545335979781797L;
            {
                //交易状态过滤器
                add(new PiggySignCustTxAccountValidator(hkCustInfo));
                //校验银行卡过滤器
                add(new PiggyBindBankCardValidator(hkCustInfo));
            }
        };
    }
}
