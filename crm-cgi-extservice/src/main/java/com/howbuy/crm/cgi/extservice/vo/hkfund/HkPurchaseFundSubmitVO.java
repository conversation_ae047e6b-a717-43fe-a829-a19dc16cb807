package com.howbuy.crm.cgi.extservice.vo.hkfund;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/9 16:24
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkPurchaseFundSubmitVO extends Body implements Serializable {

    private static final long serialVersionUID = 749284013191988307L;

    /**
     * 订单号, 用于结果页查询订单数据
     */
    private String orderNo;

    /**
     * 实缴订单号
     */
    private String paidOrderNo;

    /**
     * 当前时间
     */
    private String currentDate;
}
