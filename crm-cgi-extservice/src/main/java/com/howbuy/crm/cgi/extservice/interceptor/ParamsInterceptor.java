/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.manager.filter.trace.TradeParamLocalUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.util.JSONUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @description: 公共参数处理
 * <AUTHOR>
 * @date 2023/6/14 14:54
 * @since JDK 1.8
 */
@Slf4j
@Component
public class ParamsInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //App请求参数不处理,参数在LoginAppInterceptor中处理
        if(RequestUtil.isHkAppLogin()){
            return true;
        }
        JSONObject params = processParams(request);
        // 兼容逻辑,香港客户号是空的情况下，从登录赋值的Request属性获取
        if(StringUtils.isBlank(params.getString(Constants.HKCUSTNO))){
            params.put(Constants.HKCUSTNO,request.getAttribute(Constants.HKCUSTNO));
        }
        processReqQueryStringMap(params);
        // 设置其他参数
        setOtherParams(request,params);
        // 设置请求参数
        RequestUtil.setRequestParams(params);
        printRequestLog(request);
        return true;
    }

    /**
     * @description: 其他参数设置
     * @param request	请求
     * @param params	参数
     * @return void
     * @author: hongdong.xie
     * @date: 2023/5/19 15:58
     * @since JDK 1.8
     */
    private void setOtherParams(HttpServletRequest request, JSONObject params){
        // 设置渠道号
        setCorpId(request,params);
        // 设置网点号
        setCoopId(request,params);
        // 设置客户IP
        setCustIp(request, params);
        // 设置渠道号
        if(RequestUtil.isHkAppLogin()){
            request.setAttribute(Constants.TX_CHANNEL, TxChannelEnum.HK_APP.getCode());
            params.put(Constants.TX_CHANNEL,TxChannelEnum.HK_APP.getCode());
            MDC.put(Constants.TX_CHANNEL,TxChannelEnum.HK_APP.getCode());
        }else{
            request.setAttribute(Constants.TX_CHANNEL, TxChannelEnum.WAP.getCode());
            params.put(Constants.TX_CHANNEL,TxChannelEnum.WAP.getCode());
            MDC.put(Constants.TX_CHANNEL,TxChannelEnum.WAP.getCode());
        }

        // 设置请求日期和请求时间
        setAppDtAndTm(request,params);
    }

    /**
     * @description: 设置请求日期和请求时间
     * @param request
     * @param params
     * @return void
     * @author: hongdong.xie
     * @date: 2023/6/6 19:30
     * @since JDK 1.8
     */
    private void setAppDtAndTm(HttpServletRequest request,JSONObject params){
        // 设置请求日期
        Date now = new Date();
        String appDt = request.getParameter(Constants.APP_DT);
        if(StringUtils.isEmpty(appDt)){
            appDt = DateUtils.dateFormatToString(now,DateUtils.YYYYMMDD);
            request.setAttribute(Constants.APP_DT,appDt);
            MDC.put(Constants.APP_DT,appDt);
            TradeParamLocalUtils.setAppDt(appDt);
        }
        params.put(Constants.APP_DT,appDt);
        // 设置请求时间
        String appTm = request.getParameter(Constants.APP_TM);
        if(StringUtils.isEmpty(appTm)){
            appTm = DateUtils.dateFormatToString(now,DateUtils.HHMMSS);
            request.setAttribute(Constants.APP_TM,appTm);
            MDC.put(Constants.APP_TM,appTm);
            TradeParamLocalUtils.setAppTm(appTm);
        }
        params.put(Constants.APP_TM,appTm);
    }

    /**
     * @description: 设置corpId，分销机构号
     * @param request	请求
     * @param params 参数
     * @return void
     * @author: hongdong.xie
     * @date: 2023/5/19 15:48
     * @since JDK 1.8
     */
    private void setCorpId(HttpServletRequest request,JSONObject params){
        Object corpObj = params.get(Constants.CORPID);
        if(Objects.nonNull(corpObj)){
            String corpId = (String)corpObj;
            params.put(Constants.CORPID,corpId);
            request.setAttribute(Constants.CORPID,corpId);
            MDC.put(Constants.CORPID,corpId);
        }
    }

    /**
     * @description: 设置coopId,渠道网点号
     * @param request	请求
     * @param params	参数
     * @return void
     * @author: hongdong.xie
     * @date: 2023/5/19 15:55
     * @since JDK 1.8
     */
    private void setCoopId(HttpServletRequest request,JSONObject params){
        Object coopObj = params.get(Constants.COOPID);
        if(Objects.nonNull(coopObj)){
            String coopId = (String)coopObj;
            params.put(Constants.COOPID,coopId);
            request.setAttribute(Constants.COOPID,coopId);
            MDC.put(Constants.COOPID,coopId);
            TradeParamLocalUtils.setOutletCode(coopId);
        }
    }

    /**
     * 设置客户IP
     * @param request
     * @param params
     * @return void
     * @author: hongdong.xie
     * @date: 2023/5/30 14:36
     * @since JDK 1.8
     */
    private void setCustIp(HttpServletRequest request,JSONObject params){
        String custIp = WebUtil.getCustIP(request);
        if(StringUtils.isNotEmpty(custIp)){
            params.put(Constants.CUST_IP,custIp);
            request.setAttribute(Constants.CUST_IP,custIp);
            MDC.put(Constants.CUST_IP,custIp);
            TradeParamLocalUtils.setCustIp(custIp);
        }
    }

    /**
     * @description: 处理请求的参数
     * @param request
     * @return com.alibaba.fastjson.JSONObject
     * @author: hongdong.xie
     * @date: 2023/5/19 14:55
     * @since JDK 1.8
     */
    private JSONObject processParams(HttpServletRequest request) {
        JSONObject params = new JSONObject();
        params = RequestUtil.getRequestParams();
        if(params == null){
            params = JSON.parseObject("{}");
        }

        Enumeration<?> parameterNames = RequestUtil.getHttpRequest().getParameterNames();
        if (parameterNames != null) {
            while (parameterNames.hasMoreElements()) {
                String paramName = (String) parameterNames.nextElement();
                if(!Constants.TIMESTAMP.equals(paramName) && !Constants.ACCESS_SEQ.equals(paramName)){
                    params.put(paramName, request.getParameter(paramName));
                }
            }
        }
        return params;
    }

    /**
     * @description: 将请求链接中带的参数设置到params中
     * @param params
     * @return void
     * @author: hongdong.xie
     * @date: 2023/5/19 15:23
     * @since JDK 1.8
     */
    private void processReqQueryStringMap(JSONObject params) {
        Map<String, String> paramMaps = RequestUtil.getQueryStringMap();
        for (Map.Entry<String, String> entry : paramMaps.entrySet()) {
            String paramName = entry.getKey();
            if (!Constants.TIMESTAMP.equals(paramName) && !Constants.ACCESS_SEQ.equals(paramName)) {
                try {
                    if (!params.containsKey(paramName)) {
                        params.put(paramName, paramMaps.get(paramName));
                    }
                } catch (Exception e) {
                    log.error("LoginSessionInterceptor|params.getString is error:", e);
                }
            }
        }
    }

    /**
     * @description: 日志打印
     * @param request
     * @return void
     * @author: hongdong.xie
     * @date: 2023/6/14 14:51
     * @since JDK 1.8
     */
    private void printRequestLog(HttpServletRequest request){
        if(!log.isInfoEnabled()) {
            return;
        }
        String requestStr = getRequestStr();
        log.info("###### Http Request Info,url:{}, params:{}",request.getRequestURI(),requestStr);
        try {
            if(StringUtil.isEmpty(requestStr)) {
                String queryString = request.getQueryString();
                log.info("###### Http Request Info queryString,url:{},params:{}", request.getRequestURI(),queryString);
            }
        } catch (Exception e2) {
            log.error("getRequestStr is error:", e2);
        }
    }

    /**
     * @description: 获取请求参数字符串
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/6/14 14:44
     * @since JDK 1.8
     */
    private String getRequestStr(){
        try {
            JSONObject temp = RequestUtil.getRequestParams();
            if(temp == null){
                return null;
            }
            Iterator<String> keys = temp.keySet().iterator();
            String key = null;
            String lowerKey = null;
            Map<String, Object> ret = new HashMap<>();
            while(keys.hasNext()) {
                key = keys.next();
                lowerKey = key.toLowerCase();
                boolean skipFlag = lowerKey.contains("pswd")
                        || lowerKey.contains("pwd") || lowerKey.contains("password")
                        || lowerKey.contains("pasword") || lowerKey.contains("passwd");
                if(!log.isDebugEnabled() && skipFlag) {
                    // 密码日志不打印出来
                    continue;
                }
                //图片类的base64不打印
                if(lowerKey.contains("frontpicturebase64") || lowerKey.contains("backpicturebase64")) {
                    continue;
                }
                ret.put(key, !JSONUtils.isNull(temp.get(key))?temp.get(key):"NULL");
            }
            return JSON.toJSONString(ret);
        } catch (Exception e) {
            log.error("LoginSessionInterceptor|JSONObject is error.", e);
        }
        return null;
    }
}