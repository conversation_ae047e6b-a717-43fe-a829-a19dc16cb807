/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.handle;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.validator.piggy.*;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 海外储蓄罐底层协议变更
 * @date 2024/7/22 16:07
 * @since JDK 1.8
 */
public class PiggyChangeVerificationHandle implements VerificationHandle<PiggyBankVerificationVO> {

    /**
     * 迭代器
     */
    private final List<PiggyValidator<PiggyBankVerificationVO>> validators;

    public PiggyChangeVerificationHandle(PiggyVerificationContext context) {
        this.validators = buildHkFundValidators(context.getHkCustInfoDTO(), context.getSignFundBasicInfoDTO(),context.getFundBasicInfoDTO());
    }

    public PiggyBankVerificationVO verification() {
        //添加过滤器
        PiggyBankVerificationVO verificationVO = new PiggyBankVerificationVO();
        for (PiggyValidator<PiggyBankVerificationVO> validator : validators) {
            verificationVO = validator.verification();
            //校验不通过,返回错误信息
            if (null != verificationVO && StringUtils.isNotBlank(verificationVO.getVerfiyState())) {
                return verificationVO;
            }
        }
        if(null == verificationVO){
            verificationVO = new PiggyBankVerificationVO();
        }
        verificationVO.setVerfiyState(HkFundVerificationStatusEnum.NORMAL.getCode());
        return verificationVO;
    }

    /**
     * @param hkCustInfo
     * @param fundBasicInfoDTO
     * @return java.util.List<com.howbuy.crm.cgi.extservice.validator.piggy.PiggyValidator < com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO>>
     * @description: 储蓄管底层切换校验-添加对应的校验器
     * @author: jinqing.rao
     * @date: 2024/8/19 14:35
     * @since JDK 1.8
     */
    protected List<PiggyValidator<PiggyBankVerificationVO>> buildHkFundValidators(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO signFundBasicInfoDTO,FundBasicInfoDTO fundBasicInfoDTO) {
        return new ArrayList<PiggyValidator<PiggyBankVerificationVO>>() {
            private static final long serialVersionUID = -5640545335979781797L;

            {
                //客户状态校验
                add(new PiggySignCustStatusValidator(hkCustInfo));
                //客户分险等级小于基金分险等级
                add(new CustRiskLevelLessThanFundRiskLevelValidator(hkCustInfo, fundBasicInfoDTO, YesNoEnum.NO.getCode()));
                //基金在途交易
                add(new FundHasInTransitTransValidator(hkCustInfo, signFundBasicInfoDTO));
            }
        };
    }
}
