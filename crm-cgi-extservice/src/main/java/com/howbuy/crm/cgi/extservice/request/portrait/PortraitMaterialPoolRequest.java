package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.crm.cgi.common.base.PageRequest;
import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 智能素材库-推荐池列表查询请求
 * @Date 2024-09-06 10:05:45
 */
@Getter
@Setter
@ToString
public class PortraitMaterialPoolRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 为您推荐tab页
     * 0-全部 1-A基础 2-B投教 3-市场资讯 4-金融产品
     */
    private String tab;

    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;
} 