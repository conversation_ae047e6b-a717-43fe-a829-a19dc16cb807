/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.session;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 香港交易Session类
 * <AUTHOR>
 * @date 2023/5/17 13:40
 * @since JDK 1.8
 */
@Data
public class HkTradeSession implements Serializable {
    private static final long serialVersionUID = 5883887679012253114L;
    /**
     * 渠道号
     */
    private String corpId;
    /**
     * 网点号
     */
    private String coopId;

    /**
     * 分销号
     */
    private String disCode;
    /**
     * 香港客户信息
     */
    private HkCustInfo custInfo;

    /**
     * 当前登录用户IP
     */
    private String loginIp;


}