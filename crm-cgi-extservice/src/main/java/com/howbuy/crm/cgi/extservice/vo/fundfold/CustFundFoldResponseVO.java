/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.fundfold;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/6 10:31
 * @since JDK 1.8
 */
@Setter
@Getter
public class CustFundFoldResponseVO extends Body implements Serializable {

    private static final long serialVersionUID = -4893787872170935112L;

    /**
     * 基金信息
     */
    private List<FundHoldVO> fundHoldList;

    @Setter
    @Getter
    public static class FundHoldVO implements Serializable {

        private static final long serialVersionUID = 416218707365460546L;

        /**
         * 基金编码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;
    }
}
