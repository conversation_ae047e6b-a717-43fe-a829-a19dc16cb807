/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.common.enums.LoginAcctTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountLoginLogRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountLoginOutRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountNeedExitRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.LoginService;
import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.HkCustNoVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.LoginIdTypeListVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.LoginLogListVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.LoginNeedExitVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (登录)
 * @date 2023/5/18 14:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/login")
@Slf4j
public class LoginController {

    @Autowired
    private LoginService loginService;

    @Value("${his.appversion}")
    private String hisappVersion;


    /**
     * @api {POST} /hkaccount/login/loginbymobileandverifycode loginByMobileAndVerifyCode()
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName loginByMobileAndVerifyCode()
     * @apiDescription 手机验证码登录接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 必须
     * @apiParam (请求体) {String} verifyCode 验证码 必须
     * @apiParam (请求体) {String} mobileAreaCode 手机号区号
     * @apiParamExample 请求体示例
     * {"verifyCode":"X5fpk3r","mobile":"Z"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custState 客户状态 0 正常  1-注册成功  2-未开户
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"FOFxH","data":{"hkCustNo":"5bvPrK6x05","custState":"QsGxbIfN63"},"description":"40bZTg","timestampServer":"7zg"}
     */
    @PostMapping("/loginbymobileandverifycode")
    public CgiResponse<HkCustNoVO> loginByMobileAndVerifyCode(@RequestBody LoginByMobileAndVerifyCodeRequest req) {
        log.info("登录接口中的appversion:,{}", req.getAppVersion());
        HkCustNoVO hkCustNoVO = loginService.loginByMobileAndVerifyCode(req);
        if (StringUtils.isNotEmpty(hkCustNoVO.getReturnCode())) {
            return CgiResponse.error(hkCustNoVO);
        }
        return CgiResponse.ok(hkCustNoVO);
    }

    /**
     * @api {POST} /ext/hkaccount/login/loginappbymobileandverifycode loginAppByMobileAndVerifyCode()
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName loginAppByMobileAndVerifyCode()
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiDescription App验证码登录接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 必须
     * @apiParam (请求体) {String} verifyCode 验证码 必须
     * @apiParam (请求体) {String} mobileAreaCode 手机号区号
     * @apiParam (请求体) {String} appVersion 版本ID
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"mobileAreaCode":"p6","appVersion":"knJl2f","verifyCode":"aD1ygZ3UMf","mobile":"USCfH"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custState 客户状态 0 正常  1-注册成功  2-未开户
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"bgqlgNObcE","data":{"hkCustNo":"90s","custState":"nTxb6Zu7y"},"description":"3f3s69g","timestampServer":"Vr"}
     */
    @PostMapping("/loginappbymobileandverifycode")
    public CgiResponse<HkCustNoVO> loginAppByMobileAndVerifyCode(@RequestBody LoginByMobileAndVerifyCodeRequest req) {
        HkCustNoVO hkCustNoVO = loginService.loginAppByMobileAndVerifyCode(req);
        if (StringUtils.isNotEmpty(hkCustNoVO.getReturnCode())) {
            return CgiResponse.error(hkCustNoVO);
        }
        return CgiResponse.appOk(hkCustNoVO);
    }

    /**
     * @api {POST} /hkaccount/login/loginbymobileoridno 密码登录接口
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName loginByMobileOrIdNo
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} loginAcctType 登录方式 必须，0-证件；1-手机 2-邮箱
     * @apiParam (请求体) {String} areaCode 地区码 手机号非空时必须
     * @apiParam (请求体) {String} mobile 手机号 登录方式=1-手机时必须
     * @apiParam (请求体) {String} idAreaCode 证件地区码
     * @apiParam (请求体) {String} idType 证件类型  证件号码非空时必须
     * @apiParam (请求体) {String} idNo 证件号码  登录方式=0-证件时必须
     * @apiParam (请求体) {String} email 邮箱
     * @apiParam (请求体) {String} loginPassword 登录密码  必须
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"areaCode":"RAjBx1XL","idType":"QK","mobile":"6KzKuN","loginPassword":"vzABTwTKG","idNo":"RiUAc05","loginAcctType":"LdeQbsu7P"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccessExample 响应结果示例
     * {"code":"eNBIbwy","data":{"returnCode":"DP5UPXkc9g","description":"Hbg"},"description":"9jmkY"}
     */
    @PostMapping("/loginbymobileoridno")
    public CgiResponse<HkCustNoVO> loginByMobileOrIdNo(@RequestBody LoginByMobileOrIdNoRequest req) {
        log.info("登录接口中的appversion:,{}", req.getAppVersion());
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        // 获取前端所给的版本号 格式 为 1.0.1
        // 把"1.0.1"版本号转换为纯数字、
        long appVersionNum = 0L;
        if (StringUtils.isNotBlank(req.getAppVersion())) {
            appVersionNum = Long.parseLong(req.getAppVersion().replaceAll("\\.", ""));
        }
        // 转换配置的历史版本
        long hisAppVersionNum = Long.parseLong(hisappVersion.replaceAll("\\.", ""));
        if (Objects.isNull(req.getAppVersion()) || appVersionNum <= hisAppVersionNum) {
            if (LoginAcctTypeEnum.ID_NO.getKey().equals(req.getLoginAcctType())) {
                ParamsValidator.validateParams(req, "idType", "idNo");
            } else if (LoginAcctTypeEnum.MOBILE.getKey().equals(req.getLoginAcctType())) {
                ParamsValidator.validateParams(req, "areaCode", "mobile");
            } else {
                ParamsValidator.throwParamsException("loginAcctType");
            }
            hkCustNoVO = loginService.loginByMobileOrIdNoOldVersion(req.getLoginAcctType(), req.getAreaCode(), req.getMobile(),
                    req.getIdType(), req.getIdNo(), req.getLoginPassword());
        }else {
            // 证件登录  校验证件号 和登录密码不能为空
            if (LoginAcctTypeEnum.ID_NO.getKey().equals(req.getLoginAcctType())) {
                ParamsValidator.validateParams(req, "idNo", "loginPassword");
            } else if (LoginAcctTypeEnum.MOBILE.getKey().equals(req.getLoginAcctType())) {
                // 手机号登录 地区码 手机号，登录密码不能为空
                ParamsValidator.validateParams(req, "areaCode", "mobile", "loginPassword");
            } else if (LoginAcctTypeEnum.EMAIL.getKey().equals(req.getLoginAcctType())){
                // 根据邮箱登录 校验邮箱号 密码不为空
                ParamsValidator.validateParams(req, "email", "loginPassword");
            }else {
                ParamsValidator.throwParamsException("loginAcctType");
            }
            hkCustNoVO = loginService.loginByMobileOrIdNoNewVersion(req.getLoginAcctType(), req.getIdAreaCode(), req.getAreaCode(), req.getMobile(),
                    req.getIdType(), req.getEmail(), req.getIdNo(), req.getLoginPassword(), req.getDeviceName());
        }
        if (StringUtils.isNotEmpty(hkCustNoVO.getReturnCode())) {
            return CgiResponse.error(hkCustNoVO);
        }
        return CgiResponse.ok(hkCustNoVO);
    }

    /**
     * @api {POST} /ext/hkaccount/login/loginappbymobileoridno loginAppByMobileOrIdNo()
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiName loginAppByMobileOrIdNo()
     * @apiDescription 手机号/证件信息登录等登录接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} loginAcctType 登录方式 必须，0-证件；1-手机 2-邮箱
     * @apiParam (请求体) {String} areaCode 地区码 手机号非空时必须
     * @apiParam (请求体) {String} mobile 手机号 登录方式=1-手机时必须
     * @apiParam (请求体) {String} idAreaCode 证件地区码
     * @apiParam (请求体) {String} idType 证件类型  证件号码非空时必须
     * @apiParam (请求体) {String} idNo 证件号码  登录方式=0-证件时必须
     * @apiParam (请求体) {String} email 邮箱
     * @apiParam (请求体) {String} loginPassword 登录密码  必须
     * @apiParam (请求体) {String} appVersion 小程序版本号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"areaCode":"bimwoVr","appVersion":"itN","idType":"C","mobile":"KtU","loginPassword":"a71sAr","idAreaCode":"NjwnK5","idNo":"s1ycK1u3wH","loginAcctType":"XGL","email":"2"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custState 客户状态 0 正常  1-注册成功  2-未开户
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"uF","data":{"hkCustNo":"CJYW4S","custState":"WIA6szoVH"},"description":"z2DX","timestampServer":"MorEY5uB"}
     */
    @PostMapping("/loginappbymobileoridno")
    public CgiResponse<HkCustNoVO> loginAppByMobileOrIdNo(@RequestBody LoginByMobileOrIdNoRequest req) {
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        // 证件登录  校验证件号 和登录密码不能为空
        if (LoginAcctTypeEnum.ID_NO.getKey().equals(req.getLoginAcctType())) {
            ParamsValidator.validateParams(req, "idNo", "loginPassword");
        } else if (LoginAcctTypeEnum.MOBILE.getKey().equals(req.getLoginAcctType())) {
            // 手机号登录 地区码 手机号，登录密码不能为空
            ParamsValidator.validateParams(req, "areaCode", "mobile", "loginPassword");
        } else if (LoginAcctTypeEnum.EMAIL.getKey().equals(req.getLoginAcctType())){
            // 根据邮箱登录 校验邮箱号 密码不为空
            ParamsValidator.validateParams(req, "email", "loginPassword");
        }else {
            ParamsValidator.throwParamsException("loginAcctType");
        }
        hkCustNoVO = loginService.loginAppByMobileOrIdNoNewVersion(req.getLoginAcctType(), req.getIdAreaCode(), req.getAreaCode(), req.getMobile(),
                req.getIdType(), req.getEmail(), req.getIdNo(), req.getLoginPassword(), req.getDeviceName());
        if (StringUtils.isNotEmpty(hkCustNoVO.getReturnCode())) {
            return CgiResponse.error(hkCustNoVO);
        }
        return CgiResponse.appOk(hkCustNoVO);
    }


    /**
     * @api {POST} /hkaccount/login/verifymobile 手机验证接口
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName verifyMobile
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号明文  必须
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idNo 证件号码  必须
     * @apiParam (请求体) {String} verifyCode 验证码  必须
     * @apiParamExample 请求体示例
     * {"idType":"sJRk","verifyCode":"ONl6k","mobile":"zJC","idNo":"0"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"P","data":{"returnCode":"PHG","description":"OWUpa"},"description":"6","timestampServer":"VVywD"}
     */
    @PostMapping("/verifymobile")
    public CgiResponse<Body> verifyMobile(@RequestBody VerifyMobileRequest req) {
        loginService.verifyMobile(req);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {POST} /hkaccount/login/activatetxaccount 交易账户激活接口
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName activateTxAccount()
     * @apiDescription 交易账户激活接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobileDigest 手机号摘要  二选一
     * @apiParam (请求体) {String} emailDigest 邮箱地址摘要  二选一
     * @apiParam (请求体) {String} idType 证件类型  必须
     * @apiParam (请求体) {String} idNo 证件号码  必须
     * @apiParam (请求体) {String} verifyCode 验证码  必须
     * @apiParam (请求体) {String} txPassword 交易密码  必须
     * @apiParamExample 请求体示例
     * {"txPassword":"a9hlCCg","idType":"EqTB","verifyCode":"fnfFUwF17","mobileDigest":"nLxpsTJ","emailDigest":"WrfUYCj","idNo":"9P"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"OIbIFE","data":{"returnCode":"UgDdgGDZ","description":"0hBln1pNa"},"description":"j19I9eb","timestampServer":"Eu0jBw"}
     */
    @PostMapping("/activatetxaccount")
    public CgiResponse<Body> activateTxAccount(@RequestBody ActivateTxAccountRequest req) {
        loginService.activateTxAccount(req);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {POST} /hkaccount/login/activateloginaccount 登录账号激活接口
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName activateLoginAccount()
     * @apiDescription 登录账号激活接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobileDigest 手机号摘要  二选一
     * @apiParam (请求体) {String} emailDigest 邮箱地址摘要  二选一
     * @apiParam (请求体) {String} idType 证件类型
     * @apiParam (请求体) {String} idNo 证件号码  必须
     * @apiParam (请求体) {String} verifyCode 验证码  必须
     * @apiParam (请求体) {String} loginPassword 登录密码  必须
     * @apiParamExample 请求体示例
     * {"idType":"BAt62JCZ","verifyCode":"je","mobileDigest":"ke7dGtbgBT","loginPassword":"cXE","emailDigest":"AfHBeIYm","idNo":"C3QA12"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"WEPI","data":{"returnCode":"nhIOPNaB8","description":"Ry23"},"description":"maTVbFQ","timestampServer":"N5B"}
     */
    @PostMapping("/activateloginaccount")
    public CgiResponse<Body> activateLoginAccount(@RequestBody ActivateLoginAccountRequest req) {
        loginService.activateLoginAccount(req);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {POST} /hkaccount/login/loginout 退出登录
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName loginOut()
     * @apiDescription 退出登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"VjniaVYnpH","data":{"returnCode":"z","description":"ittGe"},"description":"AGfW","timestampServer":"QJ4BFi"}
     */
    @PostMapping("/loginout")
    public CgiResponse<AccountBaseVO> loginOut() {
        loginService.loginOut();
        return CgiResponse.ok(null);
    }


    /**
     * @api {POST} /hkaccount/login/getloginidtypelist 获取登录证件类型列表
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName getLoginIdTypeList()
     * @apiDescription 获取登录证件类型列表
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.voList
     * @apiSuccess (响应结果) {String} data.voList.idType 证件类型           大陆身份证-0      香港身份证-D      澳门身份证-E      台湾身份证-F      中国护照-1      外国护照-6      港澳通行证-4      台胞证-A      港澳台居民居住证-C      其他证件-7
     * @apiSuccess (响应结果) {String} data.voList.idTypeDesc 证件类型描述
     * @apiSuccess (响应结果) {String} data.voList.inputBoxDesc 输入框描述
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"QRGB","data":{"returnCode":"S30jy","description":"2V","voList":[{"inputBoxDesc":"P","idType":"wyBj","idTypeDesc":"X8"}]},"description":"S","timestampServer":"IAsuhbJ"}
     */
    @PostMapping("/getloginidtypelist")
    public CgiResponse<LoginIdTypeListVO> getLoginIdTypeList() {
        LoginIdTypeListVO listVO = loginService.getLoginIdTypeList();
        return CgiResponse.ok(listVO);
    }


    /**
     * @api {POST} /ext/hkaccount/login/applogout appLogout()
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName appLogout()
     * @apiDescription App安全退出接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo App 基础传参,
     * @apiParam (请求体) {String} tokenId
     * @apiParamExample 请求体示例
     * {"tokenId":"t2yklgw","hkCustNo":"COnuH"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Bf","description":"7c","timestampServer":"0G7Emy"}
     */
    @PostMapping("/applogout")
    public CgiResponse<Body> appLogout(@RequestBody HkAppAccountLoginOutRequest req) {
        loginService.appLogout(req);
        return CgiResponse.appOk(new Body());
    }

    /**
     * @api {POST} /ext/hkaccount/login/needloginexit needloginexit()
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName needloginexit()
     * @apiDescription App是否需要退出登录接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} tokenId
     * @apiParamExample 请求体示例
     * {"tokenId":"t2yklgw","hkCustNo":"COnuH"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.nextDayFlag 次日是否需要退出 1-需要退出 0-不需要退出
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample {URL_KEY} 接口key
    {
        "CRM_CGI_HKACCOUNT_LOGIN_NEEDLOGINEXIT": {
            "path": "/hkaccount/login/needloginexit",
            "encryption": "false",
            "host": "h201"
        }
    }
     * @apiSuccessExample {json} 响应结果示例
     * {"code":"Bf","description":"7c","data":{"nextDayFlag":"1"},"timestampServer":"0G7Emy"}
     */
    @PostMapping("/needloginexit")
    public CgiResponse<LoginNeedExitVO> needloginexit(@RequestBody HkAppAccountNeedExitRequest req) {
        LoginNeedExitVO vo = loginService.appNeedExit(req);
        return CgiResponse.appOk(vo);
    }

    /**
     * @api {POST} /ext/hkaccount/login/loginloglist loginloglist()
     * @apiVersion 1.0.0
     * @apiGroup LoginController
     * @apiName loginloglist()
     * @apiDescription App登录日志列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} tokenId
     * @apiParam (请求体) {String} page 页码
     * @apiParam (请求体) {String} size 每页条数
     * @apiParamExample 请求体示例
     * {"tokenId":"t2yklgw","hkCustNo":"COnuH"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 总条数
     * @apiSuccess (响应结果) {Array} data.loginMsgList 登录日志列表
     * @apiSuccess (响应结果) {String} data.loginMsgList.loginIp 登录IP
     * @apiSuccess (响应结果) {String} data.loginMsgList.loginDtm 登录时间 格式 yyyy-MM-dd HH:mm:ss
     * @apiSuccess (响应结果) {String} data.loginMsgList.loginSource 登录来源
     * @apiSuccess (响应结果) {String} data.loginMsgList.loginSourceMemo 登录来源描述
     * @apiSuccess (响应结果) {String} data.loginMsgList.deviceName 设备名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample {URL_KEY} 接口key
    {
        "CRM_CGI_HKACCOUNT_LOGIN_LOGLIST": {
            "path": "/hkaccount/login/loginloglist",
            "encryption": "false",
            "host": "h201"
        }
    }
     * @apiSuccessExample {json} 响应结果示例
     * {"code":"Bf","description":"7c","data":{"loginMsgList":[{"loginIp":"***********","loginDtm":"2024-08-01 12:00:00","deviceName":"iPhone15"}]},"timestampServer":"0G7Emy"}
     */
    @PostMapping("/loginloglist")
    public CgiResponse<LoginLogListVO> loginloglist(@RequestBody HkAppAccountLoginLogRequest req) {
        LoginLogListVO vo = loginService.appLoginLogList(req);
        return CgiResponse.appOk(vo);
    }
}