package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: app广告位响应
 * <AUTHOR>
 * @date 2024/2/29 13:59
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAcctAdvertisVO extends AccountBaseVO implements Serializable {


    private static final long serialVersionUID = -7853994248420165664L;


    /**
     * 广告信息列表
     */
    private List<AdvertisingInfo> advertisings;
    
    @Setter
    @Getter
    public static class AdvertisingInfo implements Serializable{

        private static final long serialVersionUID = 2257517297637030450L;

        /**
         * 标题
         */
        private String adTitle;

        /**
         * 排序
         */
        private String adOrder;

        /**
         * 图片地址
         */
        private String adImg;

        /**
         * 广告URL
         */
        private String onClick;

        /**
         * 用于判断是否校验开户，枚举包括 0-无需开户/1-需开户；
         */
        private String verifyAccount;
    }
}
