/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.file;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 默认的文件上传类, 特殊的业务可以继承 AbstractFileUpload 完成自定义实现
 * @date 2024/5/10 19:57
 * @since JDK 1.8
 */
public class DefaultUploadFile extends AbstractUploadFile {

    /**
     * 香港客户号
     */
    private final String hkCustNo;

    public DefaultUploadFile(MultipartFile multipartFile, String fileType, String hkCustNo) {
        super(multipartFile, fileType);
        this.hkCustNo = hkCustNo;
    }

    @Override
    protected String initFileName() {
        String originalFilename = this.multipartFile.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BusinessException(ExceptionCodeEnum.HFILE_FILE_UPLOAD_FILE_GET_FILE_NAME_ERROR);
        }
        //拆分文件名称和文件类型后缀
        String fileNamePrefix = originalFilename.substring(0, originalFilename.lastIndexOf(MarkConstants.SEPARATOR_DOT));
        //获取文件后缀
         this.fileType = originalFilename.substring(originalFilename.lastIndexOf(MarkConstants.SEPARATOR_DOT) + 1);
        //判断文件名称是否包含特殊符号
        if (StringUtils.containsAny(fileNamePrefix, MarkConstants.SEPARATOR_DOT, File.separator, MarkConstants.SEPARATOR_SPACE, MarkConstants.SEPARATOR_SLASH, "\\n", "\n")) {
            //不拦截 替换默认名称,防止特殊字符 李齐的需求
            fileNamePrefix = "defaultFile";
        }
        // 去空格
        fileNamePrefix =fileNamePrefix.trim();
        // 灰度环境,中文名称乱码,文件名称转UTF-8
        byte[] utf8Bytes = fileNamePrefix.getBytes(StandardCharsets.UTF_8);
        fileNamePrefix = new String(utf8Bytes, StandardCharsets.UTF_8);
        return hkCustNo.concat(MarkConstants.SEPARATOR_DOWN).concat(String.valueOf(System.currentTimeMillis())).concat(MarkConstants.SEPARATOR_DOWN).concat(fileNamePrefix).concat(MarkConstants.SEPARATOR_DOT).concat(fileType);
    }


    @Override
    protected String initFilePath() {
        // 这里的文件的相对路径不能超过3级目录,不然上传HFILE会报错
        String fileDir = fileBizTypeEnum.getFileDir();
        if (StringUtils.isNotBlank(fileDir)) {
            return fileDir + File.separator + DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN)+ File.separator;
        }
        // 当前日期 + 用户客户号
        return DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN) + this.hkCustNo+ File.separator;
    }

    @Override
    public String getEncryptCustomizeUrl() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(URL, getUrl());
        jsonObject.put(Constants.HKCUSTNO, hkCustNo);
        jsonObject.put(FILE_NAME, fileName);
        jsonObject.put(FILE_BIZ_TYPE, fileBizTypeEnum.getCode());
        // 数据加密
        return initEncryptAlgorithm(jsonObject.toJSONString(), Constants.HK_APP_CUST_NO_KEY);
    }
}
