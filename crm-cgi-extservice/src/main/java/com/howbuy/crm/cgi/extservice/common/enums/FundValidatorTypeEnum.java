package com.howbuy.crm.cgi.extservice.common.enums;

import org.apache.commons.lang3.StringUtils;

public enum FundValidatorTypeEnum {

    /**
     *  海外储蓄罐签约校验
     */
    PIGGY_SIGN_VALIDATOR("1","海外储蓄罐签约校验"),
    /**
     *  海外储蓄罐打款凭证校验
     */
    PIGGY_DEPOSIT_VALIDATOR("2","海外储蓄罐打款凭证校验"),

    /**
     *  海外储蓄罐底层变更校验
     */
    PIGGY_CHANGE_VALIDATOR("3","海外储蓄罐底层变更校验"),

    /**
     * 海外储蓄罐变更场景关闭的校验
     */
    PIGGY_CHANGE_CLOSE_VALIDATOR("4","海外储蓄罐变更场景关闭的校验"),

    /**
     * 海外储蓄罐专区列表入口关闭的校验
     */
    PIGGY_SIGN_LIST_CLOSE_VALIDATOR("5","海外储蓄罐专区列表入口关闭的校验");



    private final String code;

    private final String desc;

    private FundValidatorTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * @description: 通过Code获取对应的枚举
     * @param code 业务编码
     * @return
     * @author: jinqing.rao
     * @date: 2024/7/19 17:40
     * @since JDK 1.8
     */
    public static FundValidatorTypeEnum getEnumByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (FundValidatorTypeEnum fundValidatorTypeEnum : FundValidatorTypeEnum.values()) {
            if (fundValidatorTypeEnum.getCode().equals(code)) {
                return fundValidatorTypeEnum;
            }
        }
        return null;
    }
}
