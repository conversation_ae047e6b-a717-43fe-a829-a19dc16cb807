/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (证件类型文案实体类)
 * @date 2023/11/29 14:21
 * @since JDK 1.8
 */
@Data
public class IdTypeDescVO extends AccountBaseVO {


    /**
     * 证件类型描述
     */
    private String idTypeDesc;

    /**
     * 证件预览地址
     */
    private String idUrl;

    /**
     * 是否身份证
     */
    private String isIdCard;

    /**
     * 上传证件的描述
     */
    private List<String> upLoadIdCardDescList;



}