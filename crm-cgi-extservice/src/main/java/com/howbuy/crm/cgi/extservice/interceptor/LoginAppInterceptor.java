package com.howbuy.crm.cgi.extservice.interceptor;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.utils.AppVersionUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.manager.enums.AppExitRemarkEnum;
import com.howbuy.crm.cgi.manager.filter.trace.TradeParamLocalUtils;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkAcctCreditCodeOuterService;
import com.howbuy.hkacconline.facade.query.queryhkcreditsecuritycode.QueryHkCreditSecurityCodeResponse;
import crm.howbuy.base.utils.DesUtil;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @description: App登录校验
 * @date 2024/2/21 10:20
 * @since JDK 1.8
 */
@Component
public class LoginAppInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LoginAppInterceptor.class);


    @Resource
    private HkAcctCreditCodeOuterService hkAcctCreditCodeOuterService;

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        String hkCustNo = null;
        String tokenId  = request.getParameter(Constants.TOKEN_ID);
        if(StringUtils.isBlank(tokenId)){
            tokenId = (String) request.getAttribute(Constants.TOKEN_ID);
        }
        //post请求且是JSON提交
        if (request.getMethod().equals(HttpMethod.POST.name())) {
            if(request.getContentType().contains(MediaType.APPLICATION_JSON_VALUE)){
                // 从body中获取HkCustNo,并解密重新放到body中
                hkCustNo = getDecryptHkCustNo((MyHttpServletRequestWrapper) request, tokenId);
            }else{
                // 从参数中获取HkCustNo,并解密重新放到request中
                hkCustNo = getDecryptHkCustNo(request, tokenId);
            }
        } else if (request.getMethod().equals(HttpMethod.GET.name())) {
            // 从参数中获取HkCustNo,并解密重新放到request中
            hkCustNo = getDecryptHkCustNo(request, tokenId);
        }
        //验证登录必传信息
        hkCustNo = checkLoginInfo(hkCustNo, tokenId);
        //获取授信码,校验登录态
        List<QueryHkCreditSecurityCodeResponse.HkCreditSecurityCodeDTO> creditCodeByHkCustNoAndTokenIdMaskList = hkAcctCreditCodeOuterService.getCreditCodeByHkCustNoAndTokenIdMaskList(hkCustNo, MD5Utils.md5Hex(tokenId, String.valueOf(StandardCharsets.UTF_8)));
        String version = RequestUtil.getPublicParams(Constants.VERSION);
        // 校验其他设备是否需要退出
        checkExitReason(creditCodeByHkCustNoAndTokenIdMaskList, version, hkCustNo);
        // 校验是否需要30天退出登录态
        check30DayExitLogin(version, creditCodeByHkCustNoAndTokenIdMaskList);
        // 设置请求参数
        initRequestParams(request,hkCustNo);
        return true;
    }

    private static void check30DayExitLogin(String version, List<QueryHkCreditSecurityCodeResponse.HkCreditSecurityCodeDTO> creditCodeByHkCustNoAndTokenIdMaskList) {
        if (!StringUtil.isEmpty(version) && AppVersionUtils.compare(version, Constants.APP_VERSION) >= 0) {
            // 新版本
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            Date thirtyDaysAgo = calendar.getTime();
            // 30天内无授信安全码
            boolean allMatch = creditCodeByHkCustNoAndTokenIdMaskList.stream()
                    .filter(Objects::nonNull)
                    .map(v -> {
                        // 更新时间戳
                        Date updatedStimestamp = v.getUpdatedStimestamp();
                        // 创建时间戳
                        Date stimestamp = v.getStimestamp();
                        return null != updatedStimestamp ? updatedStimestamp : stimestamp;
                    })
                    .filter(Objects::nonNull)
                    .allMatch(thirtyDaysAgo::after);
            if (allMatch) {
                // 30天内无授信码时 重新登录
                throw new BusinessException(ExceptionCodeEnum.SESSION_TIMEOUT);
            }
        }
    }

    private void checkExitReason(List<QueryHkCreditSecurityCodeResponse.HkCreditSecurityCodeDTO> creditCodeByHkCustNoAndTokenIdMaskList, String version, String hkCustNo) {
        if (CollectionUtils.isEmpty(creditCodeByHkCustNoAndTokenIdMaskList)) {
            // 当前版本
            if (!StringUtil.isEmpty(version) && AppVersionUtils.compare(version, Constants.APP_VERSION) >= 0) {
                // 新版本
                AppExitRemarkEnum appExitRemarkEnum = hkAcctCreditCodeOuterService.getCreditCodeExitRemark(hkCustNo);
                if (AppExitRemarkEnum.MODIFY_PASSWORD_EXIT == appExitRemarkEnum) {
                    // 修改密码 重新登录
                    throw new BusinessException(ExceptionCodeEnum.HK_ACCOUNT_PASSWORD_MODIFY);
                } else if (AppExitRemarkEnum.RESET_PASSWORD_EXIT == appExitRemarkEnum) {
                    // 重置密码 重新登录
                    throw new BusinessException(ExceptionCodeEnum.HK_ACCOUNT_PASSWORD_MODIFY);
                }
            }
            // 无授信码时 重新登录
            throw new BusinessException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
    }

    private  void initRequestParams(HttpServletRequest request,String hkCustNo) {
        //从请求头获取公共参数
        JSONObject requestParams = RequestUtil.getCommonHeaderParams();
        JSONObject params = processParams(request);
        processReqQueryStringMap(params);
        //历史原因,get的请求在参数中有hkCustNo,并且是加密的,这里需要替换成解密后的，post的请求在body中有hkCustNo
        requestParams.put(Constants.HKCUSTNO, hkCustNo);
        setCorpId(request, requestParams);
        setCoopId(request,requestParams);
        setCustIp(request,requestParams);
        MDC.put(Constants.CUST_NO,hkCustNo);
        RequestUtil.setRequestParams(requestParams);
    }

    private JSONObject processParams(HttpServletRequest request) {
        JSONObject params = new JSONObject();
        params = RequestUtil.getRequestParams();
        if(params == null){
            params = JSON.parseObject("{}");
        }

        Enumeration<?> parameterNames = RequestUtil.getHttpRequest().getParameterNames();
        if (parameterNames != null) {
            while (parameterNames.hasMoreElements()) {
                String paramName = (String) parameterNames.nextElement();
                if(!Constants.TIMESTAMP.equals(paramName) && !Constants.ACCESS_SEQ.equals(paramName)){
                    params.put(paramName, request.getParameter(paramName));
                }
            }
        }
        return params;
    }

    private void processReqQueryStringMap(JSONObject params) {
        Map<String, String> paramMaps = RequestUtil.getQueryStringMap();
        for (Map.Entry<String, String> entry : paramMaps.entrySet()) {
            String paramName = entry.getKey();
            if (!Constants.TIMESTAMP.equals(paramName) && !Constants.ACCESS_SEQ.equals(paramName)) {
                try {
                    if (!params.containsKey(paramName)) {
                        params.put(paramName, paramMaps.get(paramName));
                    }
                } catch (Exception e) {
                    log.error("LoginSessionInterceptor|params.getString is error:", e);
                }
            }
        }
    }

    /**
     * @description: 设置corpId
     * @param request	
     * @param params
     * @return void
     * @author: jinqing.rao
     * @date: 2024/3/13 18:19
     * @since JDK 1.8
     */
    private  void setCorpId(HttpServletRequest request,JSONObject params){
        String corpId = params.getString(Constants.CORPID);
        if(Objects.nonNull(corpId)){
            params.put(Constants.CORPID,corpId);
            request.setAttribute(Constants.CORPID,corpId);
            MDC.put(Constants.CORPID,corpId);
            TradeParamLocalUtils.setTxChannel(corpId);
        }
    }

    private void setCoopId(HttpServletRequest request,JSONObject params){
        String coopId = params.getString(Constants.COOPID);
        if(Objects.nonNull(coopId)){
            params.put(Constants.COOPID,coopId);
            request.setAttribute(Constants.COOPID,coopId);
            MDC.put(Constants.COOPID,coopId);
            TradeParamLocalUtils.setOutletCode(coopId);
        }
    }

    private void setCustIp(HttpServletRequest request,JSONObject params){
        String custIp = WebUtil.getCustIP(request);
        if(StringUtils.isNotEmpty(custIp)){
            params.put(Constants.CUST_IP,custIp);
            request.setAttribute(Constants.CUST_IP,custIp);
            MDC.put(Constants.CUST_IP,custIp);
            TradeParamLocalUtils.setCustIp(custIp);
        }
    }

    /**
     * @description: 解密HkCustNo,并重新放到request中
     * @param request
     * @param tokenId
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/3/12 10:43
     * @since JDK 1.8
     */
    private static String getDecryptHkCustNo(HttpServletRequest request, String tokenId) {
        String hkCustNo = request.getParameter(Constants.HKCUSTNO);
        if(StringUtils.isBlank(hkCustNo)){
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求HK");
        }
        String decryptHkCustNo = decryptHkCustNO(tokenId, hkCustNo);
        request.setAttribute(Constants.HKCUSTNO, decryptHkCustNo);
        return decryptHkCustNo;
    }

    /**
     * @description: 从body中获取HkCustNo,并解密重新放到body中
     * @param request	
     * @param tokenId
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/3/12 10:37
     * @since JDK 1.8
     */
    private static String getDecryptHkCustNo(MyHttpServletRequestWrapper request, String tokenId) {
        String hkCustNo;
        MyHttpServletRequestWrapper myHttpServletRequestWrapper = request;
        String body = myHttpServletRequestWrapper.getBody();
        log.info("LoginAppInterceptor>>>getDecryptHkCustNo,请求参数 params: {}",body);
        JSONObject jsonObject = JSON.parseObject(body);
        if(null == jsonObject || StringUtils.isBlank(jsonObject.getString(Constants.HKCUSTNO))){
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求HK");
        }
        hkCustNo = jsonObject.getString(Constants.HKCUSTNO);
        //香港客户号解密
        String decryptHkCustNo = decryptHkCustNO(tokenId, hkCustNo);
        jsonObject.put(Constants.HKCUSTNO, decryptHkCustNo);
        myHttpServletRequestWrapper.setBody(jsonObject.toJSONString());
        return decryptHkCustNo;
    }

    /**
     * @description: 香港客户号解密
     * @param tokenId	tokenId
     * @param hkCustNo  香港客户号
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/3/12 10:36
     * @since JDK 1.8
     */
    private static String decryptHkCustNO(String tokenId, String hkCustNo) {
        try {
            return DesUtil.decrypt(hkCustNo, Constants.HK_APP_CUST_NO_KEY);
        } catch (Exception e) {
            log.info("LoginAppInterceptor>>>decryptHkCustNO>>>解密失败 hkCustNo:{},tokenId:{}", hkCustNo, tokenId);
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求HK_ENC");
        }
    }


    /**
     * @param hkCustNo
     * @param tokenId
     * @return java.lang.String
     * @description: 校验登录的必传信息
     * @author: jinqing.rao
     * @date: 2024/3/7 18:41
     * @since JDK 1.8
     */
    private static String checkLoginInfo(String hkCustNo, String tokenId) {
        log.info("LoginAppInterceptor>>>参数信息 hkCustNo:{},tokenId:{}", hkCustNo, tokenId);
        if (StringUtils.isBlank(hkCustNo)) {
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求HK");
        }
        if (StringUtils.isBlank(tokenId)) {
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求TI");
        }
        return hkCustNo;
    }
}
