/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/28 13:38
 * @since JDK 1.8
 */
@Setter
@Getter
public class HwOverseaReportListVO extends Body implements Serializable {

    private static final long serialVersionUID = -7340411324035740591L;

    /**
     * 境外报告列表
     */
    private List<HwOverseaReportVO> overseaReportList;

    @Setter
    @Getter
    public static class HwOverseaReportVO implements Serializable {
        private static final long serialVersionUID = -7860015066740828979L;
        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件的URl
         */
        private String fileUrl;

        /**
         * 文件类型 1 PDF 2 图片
         */
        private String fileType;

        /**
         * 创建日期时间
         */
        @JsonIgnore
        private LocalDateTime createTime;
        /**
         * 更新日期时间
         */
        @JsonIgnore
        private LocalDateTime updateTime;
    }

}
