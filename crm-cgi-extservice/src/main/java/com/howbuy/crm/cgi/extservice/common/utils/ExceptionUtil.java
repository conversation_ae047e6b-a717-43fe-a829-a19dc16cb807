package com.howbuy.crm.cgi.extservice.common.utils;

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.HkCustNoVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCommonDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/6/13 10:56
 * @since JDK 1.8
 */
public class ExceptionUtil {

    /**
     * @description: 抛出异常
     * @param: [exceptionCodeEnum]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 10:40
     * @since JDK 1.8
     */
    public static void throwBusinessException(ExceptionCodeEnum exceptionCodeEnum) {
        throw new BusinessException(exceptionCodeEnum);
    }

    /**
     * @description:(填充异常信息)
     * @param hkCustNoVO
     * @param exceptionCodeEnum
     * @return void
     * @author: shaoyang.li
     * @date: 2023/8/23 16:39
     * @since JDK 1.8
     */
    public static void fillExceptionInfo(HkCustNoVO hkCustNoVO, ExceptionCodeEnum exceptionCodeEnum){
        hkCustNoVO.setReturnCode(exceptionCodeEnum.getCode());
        hkCustNoVO.setDescription(exceptionCodeEnum.getDescription());
    }

    /**
     * @description: 校验hkCustInfoDTO
     * @param: [hkCustInfoDTO, exceptionCodeEnum]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 10:47
     * @since JDK 1.8
     */
    public static void validateHkCustInfoDTO(HkCustInfoDTO hkCustInfoDTO, ExceptionCodeEnum exceptionCodeEnum) {
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfoDTO.getReturnCode())) {
            throw new BusinessException(exceptionCodeEnum);
        }
    }

    /**
     * @description: 校验密码HkCommonDTO
     * @param: [hkLoginDTO]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 11:24
     * @since JDK 1.8
     */
    public static void validatePwdHkCommonDTO(HkCommonDTO hkCommonDTO) {
        if (OutReturnCodes.HK_ACC_ONLINE_OLD_LOGIN_PASSWORD_ERROR.equals(hkCommonDTO.getReturnCode())
                || OutReturnCodes.HK_ACC_ONLINE_OLD_TRADE_PASSWORD_ERROR.equals(hkCommonDTO.getReturnCode())) {
            throw new BusinessException(ExceptionCodeEnum.OLD_PASSWORD_ERROR);
        } else if (OutReturnCodes.HK_ACC_ONLINE_OLD_NEW_EQUAL.equals(hkCommonDTO.getReturnCode())) {
            throw new BusinessException(ExceptionCodeEnum.NEW_OLD_PASSWORD_EQUAL);
        }
    }

    /**
     * @description: 校验登录HkCommonDTO
     * @param: [hkLoginDTO]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 11:24
     * @since JDK 1.8
     */
    public static void validateLoginHkCommonDTO(HkCommonDTO hkCommonDTO) {
        if (OutReturnCodes.HK_ACC_ONLINE_LOGIN_FAIL_ERROR_PASSWORD.equals(hkCommonDTO.getReturnCode())) {
            // 登录密码错误时,提示语修改
            throw new BusinessException(ExceptionCodeEnum.LOGIN_NAME_OR_PSWD_ERROR.getCode(), hkCommonDTO.getDescription());
        } else if (OutReturnCodes.HK_ACC_ONLINE_LOGIN_FAIL_LOCKED.equals(hkCommonDTO.getReturnCode())) {
            throw new BusinessException(ExceptionCodeEnum.LOGIN_ACCT_LOCKED);
        }
    }
}
