/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignResultVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/2 18:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkRemittanceBankVO extends Body implements Serializable {

    private static final long serialVersionUID = 958016026964402059L;

    /**
     * 转账银行信息列表
     */
    private TransferBankInfo transferBankInfos;

    @Setter
    @Getter
    public static class TransferBankInfo implements Serializable {


        private static final long serialVersionUID = 7253301825548869373L;
        /**
         * 转账账户名称
         */
        private String transferBankAcctName;

        /**
         * 转账银行名称
         */
        private String transferBankName;

        /**
         * 转账银行地址
         */
        private String transferBankAddress;

        /**
         *  转账银行账号列表
         */
        private List<PiggySignResultVO.TransferBankInfo.TransferBankAcct> transferBankAccts;

        /**
         * 国际汇款识别码
         */
        private String transferSwiftCode;


        /**
         * 银行代码
         */
        private String transferBankCode;

        /**
         * 银行logo
         */
        private String bankLogoUrl;

        @Setter
        @Getter
        public static class TransferBankAcct implements Serializable {

            private static final long serialVersionUID = 1650273541093533205L;

            /**
             * 银行账号
             */
            private String bankAcct;

            /**
             * 币种代码
             */
            private String currencyCode;

            /**
             * 币种描述
             */
            private String currencyDesc;

        }
    }
}
