/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: (交易确认状态 0-未确认 1-交易确认)
 * @date 2023/8/22 9:24
 * @since JDK 1.8
 */
public enum TradeStatusEnum {
    NOT_ACK("0", "未确认"),
    TRADE_ACK("1", "交易确认");

    private final String key;
    private final String desc;

    public static TradeStatusEnum getTradeStatus(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        TradeStatusEnum tradeStatus = getTradeStatus(code);
        return tradeStatus == null ? null : tradeStatus.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    TradeStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
