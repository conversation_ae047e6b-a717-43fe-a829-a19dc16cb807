package com.howbuy.crm.cgi.extservice.common.enums.fund;

public enum HKFundPrebookEnum {
    // 0-非预约、1-仅购买预约、2-仅赎回预约、3-购买赎回预约
    NON_PREBOOK("0", "非预约"),
    ONLY_BUY_PREBOOK("1", "仅购买预约"),
    ONLY_REDEEM_PREBOOK("2", "仅赎回预约"),
    BUY_REDEEM_PREBOOK("3", "购买赎回预约");

    private final String code;
    private final String desc;

    HKFundPrebookEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    /**
     * @description: code 是 1和3 表示支持购买预约
     * @param code
     * @return
     * @author: jinqing.rao
     * @date: 2024/4/11 16:55
     * @since JDK 1.8
     */
    public static boolean isSupportBuyPrebook(String code) {
        return ONLY_BUY_PREBOOK.getCode().equals(code) || BUY_REDEEM_PREBOOK.getCode().equals(code);
    }
    /**
     * @description: code 是 2和3 表示支持购买预约
     * @param code
     * @return boolean
     * @author: jinqing.rao
     * @date: 2024/4/11 18:20
     * @since JDK 1.8
     */
    public static boolean isSupportRedeemPrebook(String code) {
        return ONLY_REDEEM_PREBOOK.getCode().equals(code) || BUY_REDEEM_PREBOOK.getCode().equals(code);
    }
}
