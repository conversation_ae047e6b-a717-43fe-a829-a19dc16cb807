/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/14 14:11
 * @since JDK 1.8
 */
@Data
public class TradeRecordMultiDetialInfoVO {
    /**
     * 系列号
     */
    private String seriesNo;
    /**
     * 份额注册日期
     */
    private String regDate;
    /**
     * 交易日期
     */
    private String tradeDate;
    /**
     * 确认日期
     */
    private String confirmDate;
    /**
     * 确认金额
     */
    private String confirmAmt;
    /**
     * 确认份额
     */
    private String confirmVol;
    /**
     * 赎回费
     */
    private String redeemFee;
    /**
     * 确认净值
     */
    private String confirmNav;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种描述
     */
    private String currencyDesc;
}