/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryFinancialNineLessonsLabelRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryFinancialNineProgressRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryFinancialNineStudyListRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryOfflineActivityRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.financialnine.PortraitFinancialNineService;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.FinancialNineProgressVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.FinancialNineStudyVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.NineLessonsLabelVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.OfflineActivityVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 客户画像-理财九章控制器
 * @Date 2024/10/18 18:25
 */
@RestController
@RequestMapping("/portrait/behaviorattributes")
public class PortraitFinancialNineController {

    @Resource
    private PortraitFinancialNineService portraitFinancialNineService;

    /**
     * @api {POST} /ext/portrait/behaviorattributes/queryfinancialninelabel queryFinancialNineLabel()
     * @apiVersion 1.0.0
     * @apiGroup PortraitFinancialNineLessonsController
     * @apiName queryFinancialNineLabel()
     * @apiDescription 查询理财九章标签接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"E"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.lastStudyTime 上次学习时间      YYYY/MM/DD HH:MM:SS
     * @apiSuccess (响应结果) {String} data.teacherPreference 讲师偏好
     * @apiSuccess (响应结果) {String} data.registerCount 报名次数
     * @apiSuccess (响应结果) {String} data.attendCount 参会次数
     * @apiSuccess (响应结果) {String} data.attendRatio 参会率
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"N4Z","data":{"lastStudyTime":"mC1x","registerCount":"4EsmG","teacherPreference":"hZxOEi9Ysh","attendRatio":"DzISUf","attendCount":"wZHM"},"description":"K97EqW9hwx","timestampServer":"iOMQc"}
     */
    @PostMapping("/queryfinancialninelabel")
    @ResponseBody
    public CgiResponse<NineLessonsLabelVO> queryFinancialNineLabel(@RequestBody QueryFinancialNineLessonsLabelRequest request) {
        return CgiResponse.appOk(portraitFinancialNineService.queryFinancialNineLabel(request));
    }

    /**
     * @api {POST} /ext/portrait/behaviorattributes/queryfinancialnineprogress queryFinancialNineProgress()
     * @apiVersion 1.0.0
     * @apiGroup PortraitFinancialNineController
     * @apiName queryFinancialNineProgress()
     * @apiDescription 查询理财九章学习进度接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"SyqPzn3"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 数据总数
     * @apiSuccess (响应结果) {Array} data.lessonsProgressList 课程进度列表
     * @apiSuccess (响应结果) {String} data.lessonsProgressList.lessonsSerialNo 课程序号
     * @apiSuccess (响应结果) {String} data.lessonsProgressList.lessonsName 课程名称
     * @apiSuccess (响应结果) {String} data.lessonsProgressList.studyCount 学习次数
     * @apiSuccess (响应结果) {String} data.lessonsProgressList.studyState 学习状态
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"x","data":{"lessonsProgressList":[{"studyCount":"u8IVgETS97","studyState":"o5lkkTuLU","lessonsSerialNo":"Hm","lessonsName":"fIBzh96MX"}]},"description":"Yu","timestampServer":"XM1eJ5MK"}
     */
    @PostMapping("/queryfinancialnineprogress")
    @ResponseBody
    public CgiResponse<FinancialNineProgressVO> queryFinancialNineProgress(@RequestBody QueryFinancialNineProgressRequest request) {
        return CgiResponse.appOk(portraitFinancialNineService.queryFinancialNineProgress(request));
    }

    /**
     * @api {POST} /ext/portrait/behaviorattributes/queryfinancialninestudylist queryFinancialNineStudyList()
     * @apiVersion 1.0.0
     * @apiGroup PortraitFinancialNineController
     * @apiName queryFinancialNineStudyList()
     * @apiDescription 查询理财九章学习明细接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"size":8018,"page":1850,"hboneNo":"dI9"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 数据总数
     * @apiSuccess (响应结果) {Array} data.studyList 学习明细列表
     * @apiSuccess (响应结果) {String} data.studyList.meetingTime 会议时间 yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} data.studyList.meetingAdress 会议地点
     * @apiSuccess (响应结果) {String} data.studyList.lessonsName 课程名称
     * @apiSuccess (响应结果) {String} data.studyList.teacher 讲师
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"PqzOB","data":{"studyList":[{"meetingTime":"7I","teacher":"9","meetingAdress":"CY","lessonsName":"UKs1"}]},"description":"8","timestampServer":"UG8IsOv"}
     */
    @PostMapping("/queryfinancialninestudylist")
    @ResponseBody
    public CgiResponse<FinancialNineStudyVO> queryFinancialNineStudyList(@RequestBody QueryFinancialNineStudyListRequest request) {
        return CgiResponse.appOk(portraitFinancialNineService.queryFinancialNineStudyList(request));
    }

    /**
     * @api {POST} /ext/portrait/behaviorattributes/queryofflineactivitylabel queryOfflineActivityLabel()
     * @apiVersion 1.0.0
     * @apiGroup PortraitFinancialNineController
     * @apiName queryOfflineActivityLabel()
     * @apiDescription 查询线下活动标签接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"size":4434,"page":6723,"hboneNo":"YUP"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 数据总数
     * @apiSuccess (响应结果) {String} data.attendCount 参加次数
     * @apiSuccess (响应结果) {String} data.activityPreference 活动偏好
     * @apiSuccess (响应结果) {Array} data.attendMeetingList 参会列表
     * @apiSuccess (响应结果) {String} data.attendMeetingList.meetingName 会议名称
     * @apiSuccess (响应结果) {String} data.attendMeetingList.meetingAdress 会议地点
     * @apiSuccess (响应结果) {String} data.attendMeetingList.meetingTime 会议时间 yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"GYebf","data":{"activityPreference":"QTJYSqp4Pm","attendCount":"PuI","attendMeetingList":[{"meetingTime":"sLRqn","meetingName":"OsGUapFL","meetingAdress":"X3yKCxYa"}]},"description":"OrM","timestampServer":"MbdlS0Yf"}
     */
    @PostMapping("/queryofflineactivitylabel")
    @ResponseBody
    public CgiResponse<OfflineActivityVO> queryOfflineActivityLabel(@RequestBody QueryOfflineActivityRequest request) {
        return CgiResponse.appOk(portraitFinancialNineService.queryOfflineActivityLabel(request));
    }
}
