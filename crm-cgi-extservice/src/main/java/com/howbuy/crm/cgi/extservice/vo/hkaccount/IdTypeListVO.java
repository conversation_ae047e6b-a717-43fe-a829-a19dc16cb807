/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: (证件类型文案列表)
 * <AUTHOR>
 * @date 2023/11/29 11:09
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IdTypeListVO extends Body {

    /**
     * 证件列表
     */
    private List<IdTypeVO> idTypeVOList;
}