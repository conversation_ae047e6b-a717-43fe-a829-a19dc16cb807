package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 风险评估测算
 * @date 2023/11/30 15:27
 * @since JDK 1.8
 */
@Getter
@Setter
public class AssessmentRiskRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -7452793842622242689L;

    /**
     * 问卷id
     */
    private String examId;

    /**
     * 答案列表
     */
    private List<Answer> answerList;

    /**
     * @description: 答案参数
     * @author: jinqing.rao
     * @date: 2023/11/30 15:30
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class Answer implements Serializable {
        private static final long serialVersionUID = -7452793842622242689L;

        /**
         *问题ID
         */
        private String questionId;

        /**
         *答案选项
         */
        private List<String> optionChars;

    }
}
