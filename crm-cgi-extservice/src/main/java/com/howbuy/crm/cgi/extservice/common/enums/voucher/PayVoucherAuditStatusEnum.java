package com.howbuy.crm.cgi.extservice.common.enums.voucher;

public enum PayVoucherAuditStatusEnum {

    /**
     * 0-无需审核
     */
    NOT_NEED_EXAMINE("0", "无需审核"),

    /**
     * 1-等待审核
     */
    WAIT_EXAMINE("1", "等待审核"),
    /**
     * 2-等待复核
     */
    WAIT_REVIEW("2", "等待复核"),

    /**
     * 3-审核通过
     */
    APPROVE("3", "审核通过"),

    /**
     * 4-审核不通过
     */
    FAILURE("4", "审核不通过"),

    /**
     * 5-驳回至初审
     */
    REJECT_TO_FIRST_EXAMINE("5", "驳回至初审"),

    /**
     * 6-驳回至客户
     */
    REJECT_TO_CUSTOMER("6", "驳回至客户"),

    /**
     * 7-作废
     */
    VOIDED("7", "作废"),
    ;

    private final String code;

    private final String desc;

    PayVoucherAuditStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
