/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBaseRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.baseinfo.PortraitBaseInfoService;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BaseAttributeVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BusinessInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustBaseInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustPositionVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 基础信息控制
 * @date 2024/9/3 14:47
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/portrait/baseinfo")
public class PortraitBaseInfoController {

    @Resource
    private PortraitBaseInfoService portraitBaseInfoService;


    /**
     * @api {POST} /ext/portrait/baseinfo/querybaseinfo queryBaseInfo()
     * @apiVersion 1.0.0
     * @apiGroup PortraitBaseInfoController
     * @apiName queryBaseInfo()
     * @apiDescription 查询基本信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"GvZfHda7VN"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.wechatAvatar 微信头像
     * @apiSuccess (响应结果) {String} data.name 姓名
     * @apiSuccess (响应结果) {String} data.genderEnumKey 性别 1-女 2-男 3-非自然人
     * @apiSuccess (响应结果) {String} data.labelCustStateEnumKey 标签客户状态 1-高端存量客户 2-高端零存量客户 3-高端有投顾潜客 4-高端无投顾潜客
     * @apiSuccess (响应结果) {String} data.labelCustStateDesc 标签-客户状态描述
     * @apiSuccess (响应结果) {String} data.age 年龄 xx
     * @apiSuccess (响应结果) {String} data.residenceProvince 现居省份
     * @apiSuccess (响应结果) {String} data.riskLevel 风险等级
     * @apiSuccess (响应结果) {String} data.investmentExperienceDesc 投资经验描述 投资经验+标签值（丰富）
     * @apiSuccess (响应结果) {String} data.latestCommInfo 最近沟通信息
     * @apiSuccess (响应结果) {String} data.defaultShowTab 默认展示tab
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"EVXJf","data":{"wechatAvatar":"rEKrJ","riskLevel":"Go0bokSA","riskLeveDesc":"p","gender":"h","latestCommInfo":"9ZLLvOGCuM","investmentExperience":"1JiP81z","name":"1","investmentExperienceDesc":"UhRFZw","labelCustState":"EBOmpnvy","age":"d3JtmBMEF","residenceProvince":"xyLijLHHdN"},"description":"GuVdcT","timestampServer":"9FuuKzPN"}
     */
    @PostMapping("/querybaseinfo")
    @ResponseBody
    public CgiResponse<CustBaseInfoVO> queryBaseInfo(@RequestBody PortraitBaseRequest request) {
        return CgiResponse.appOk(portraitBaseInfoService.queryBaseInfo(request));
    }


    /**
     * @api {POST} /ext/portrait/baseinfo/querybaseattribute queryBaseAttribute()
     * @apiVersion 1.0.0
     * @apiGroup PortraitBaseInfoController
     * @apiName queryBaseAttribute()
     * @apiDescription 查询基础属性
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"k"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.age 年龄
     * @apiSuccess (响应结果) {String} data.gender 性别 女 男 非自然人
     * @apiSuccess (响应结果) {String} data.birthday 出生日期
     * @apiSuccess (响应结果) {String} data.zodiac 生肖
     * @apiSuccess (响应结果) {String} data.birthRegion 出生地区
     * @apiSuccess (响应结果) {String} data.residenceRegion 现居地区
     * @apiSuccess (响应结果) {Object} data.degreeLabelVO 学历标签
     * @apiSuccess (响应结果) {String} data.degreeLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.degreeLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.degreeLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.degreeLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.degreeLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} data.professional 职业
     * @apiSuccess (响应结果) {Object} data.industryLabelVO 行业标签
     * @apiSuccess (响应结果) {String} data.industryLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.industryLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.industryLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.industryLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.industryLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} data.lifeStages 人生阶段
     * @apiSuccess (响应结果) {Object} data.marriageLabelVO 婚姻标签
     * @apiSuccess (响应结果) {String} data.marriageLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.marriageLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.marriageLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.marriageLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.marriageLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.childrenLabelVO 子女标签
     * @apiSuccess (响应结果) {String} data.childrenLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.childrenLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.childrenLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.childrenLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.childrenLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.parentsLabelVO 父母标签
     * @apiSuccess (响应结果) {String} data.parentsLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.parentsLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.parentsLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.parentsLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.parentsLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.hobbiesLabelVO 兴趣爱好标签
     * @apiSuccess (响应结果) {String} data.hobbiesLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.hobbiesLabelVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.hobbiesLabelVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.hobbiesLabelVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.natureLabelVO 性格特点标签
     * @apiSuccess (响应结果) {String} data.natureLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.natureLabelVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.natureLabelVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.natureLabelVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.socialPreferencesLabelVO 社交偏好标签
     * @apiSuccess (响应结果) {String} data.socialPreferencesLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.socialPreferencesLabelVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.socialPreferencesLabelVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.socialPreferencesLabelVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.channelPreferencesLabelVO 渠道偏好标签
     * @apiSuccess (响应结果) {String} data.channelPreferencesLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.channelPreferencesLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.channelPreferencesLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.channelPreferencesLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.channelPreferencesLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"NnficUvAT","data":{"birthday":"INvB","gender":"4o9ugec","socialPreferencesLabelVO":{"labelBdpValues":["FB2Eu"],"labelCustomizeValues":["VXTac"],"labelValues":["UWN"],"labelId":"HIfxwlap"},"zodiac":"6EmTmq","residenceRegion":"zmksJEybRJ","degreeLabelVO":{"labelId":"UQMopswK","labelBdpValue":"H","labelValue":"yO3lGPU","labelBdpInputValue":"UCQlqGbe9","labelInputValue":"AHbfA"},"industryLabelVO":{"labelId":"ZwrN","labelBdpValue":"QE4DSzZRQ","labelValue":"Gd7P9fJz","labelBdpInputValue":"PhK9LqU","labelInputValue":"okyd"},"channelPreferencesLabelVO":{"labelId":"ZJLBw","labelBdpValue":"ExD6QornG","labelValue":"7bx6nN","labelBdpInputValue":"IcmjgTDs1H","labelInputValue":"IMa8nr"},"childrenLabelVO":{"labelId":"dzm1vp","labelBdpValue":"J","labelValue":"ZCkNPd5","labelBdpInputValue":"kps","labelInputValue":"Gf"},"professional":"VSEIli","hobbiesLabelVO":{"labelBdpValues":["HEGuFM"],"labelCustomizeValues":["hWemeqe"],"labelValues":["0b0"],"labelId":"vU"},"birthRegion":"ld8324","parentsLabelVO":{"labelId":"XCh7","labelBdpValue":"K7buvGS","labelValue":"c83K3Mq7wO","labelBdpInputValue":"wFLM4vo","labelInputValue":"am0"},"natureLabelVO":{"labelBdpValues":["uOR5kieCuo"],"labelCustomizeValues":["DAbl"],"labelValues":["YdT9EOI"],"labelId":"OZkb1gs"},"lifeStages":"AUB4Z8rCbK","marriageLabelVO":{"labelId":"s","labelBdpValue":"IsyLXKN0","labelValue":"rSg1","labelBdpInputValue":"hbY","labelInputValue":"P"},"age":"p1KSrAVG8c"},"description":"TbQfE6cwCE","timestampServer":"q33pdz1U1C"}
     */
    @PostMapping("/querybaseattribute")
    @ResponseBody
    public CgiResponse<BaseAttributeVO> queryBaseAttribute(@RequestBody PortraitBaseRequest request) {
        return CgiResponse.appOk(portraitBaseInfoService.queryBaseAttribute(request));
    }

    /**
     * @api {POST} /ext/portrait/baseinfo/querycustposition queryCustPosition()
     * @apiVersion 1.0.0
     * @apiGroup PortraitBaseInfoController
     * @apiName queryCustPosition()
     * @apiDescription 查询客户定位
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"Hope"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custLifeCycleEnumKey 客户生命周期
     * @apiSuccess (响应结果) {String} data.custLifeCycle 客户生命周期
     * @apiSuccess (响应结果) {String} data.labelCustState 客户状态
     * @apiSuccess (响应结果) {Object} data.prospectsTypeLabelVO 潜客类型标签
     * @apiSuccess (响应结果) {String} data.prospectsTypeLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.prospectsTypeLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.prospectsTypeLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.prospectsTypeLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.prospectsTypeLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} data.zeroStockState 零存量状态
     * @apiSuccess (响应结果) {String} data.hbInvestorType 好买-投资者类型
     * @apiSuccess (响应结果) {String} data.hzInvestorType 好臻-投资者类型
     * @apiSuccess (响应结果) {String} data.hkInvestorType 香港-投资者类型
     * @apiSuccess (响应结果) {String} data.sourceType 来源类型
     * @apiSuccess (响应结果) {String} data.sourceChannel 来源渠道
     * @apiSuccess (响应结果) {String} data.custLayered 客户分层
     * @apiSuccess (响应结果) {String} data.linkAccount 关联账户
     * @apiSuccess (响应结果) {String} data.accompanyTime 陪伴天数
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"OmlOxKsgtM","data":{"prospectsTypeLabelVO":{"labelId":"qjf5HnCi","labelBdpValue":"gX0sxT","labelValue":"3MDZJ1XV","labelBdpInputValue":"DLMQxWVt8k","labelInputValue":"0lkhjCIY"},"hzInvestorType":"S","accompanyTime":"1IBX","hkInvestorType":"sB4xkxr","sourceType":"S5OJw83v","sourceChannel":"sIlEljk59L","zeroStockState":"XM","hbInvestorType":"7V","labelCustState":"21K","custLayered":"V6EBvk9","custLifeCycle":"g","linkAccount":"B"},"description":"q","timestampServer":"qVI"}
     */
    @PostMapping("/querycustposition")
    @ResponseBody
    public CgiResponse<CustPositionVO> queryCustPosition(@RequestBody PortraitBaseRequest request) {
        return CgiResponse.appOk(portraitBaseInfoService.queryCustPosition(request));
    }


    /**
     * @api {POST} /ext/portrait/baseinfo/querybusinessinfo queryBusinessInfo()
     * @apiVersion 1.0.0
     * @apiGroup PortraitBaseInfoController
     * @apiName queryBusinessInfo()
     * @apiDescription 查询业务信息
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hboneNo
     * @apiParam (请求参数) {String} requestId
     * @apiParamExample 请求参数示例
     * requestId=Hb0AdilOI&hboneNo=NctQf24M1
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.investKnowledgeLabelVO 投资知识
     * @apiSuccess (响应结果) {String} data.investKnowledgeLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.investKnowledgeLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.investKnowledgeLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.investKnowledgeLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.investKnowledgeLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.knowledgeHobbyLabelVO 理财知识兴趣
     * @apiSuccess (响应结果) {String} data.knowledgeHobbyLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.knowledgeHobbyLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.knowledgeHobbyLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.knowledgeHobbyLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.knowledgeHobbyLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.strategyUnderstandLabelVO 策略了解标签
     * @apiSuccess (响应结果) {String} data.strategyUnderstandLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.strategyUnderstandLabelVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.strategyUnderstandLabelVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.strategyUnderstandLabelVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {String} data.latestCommInfo 最近沟通信息
     * @apiSuccess (响应结果) {String} data.commCountSum 累计沟通次数
     * @apiSuccess (响应结果) {String} data.commFrequency 沟通频率
     * @apiSuccess (响应结果) {String} data.weekCommCount 本周沟通次数
     * @apiSuccess (响应结果) {String} data.monthCommCount 本月沟通次数
     * @apiSuccess (响应结果) {String} data.quarterCommCount 本季度沟通次数
     * @apiSuccess (响应结果) {String} data.yearCommCount 今年沟通次数
     * @apiSuccess (响应结果) {Object} data.serviceStageLabelVO 服务阶段
     * @apiSuccess (响应结果) {String} data.serviceStageLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.serviceStageLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.serviceStageLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.serviceStageLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.serviceStageLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.serviceFormPreferencesLabelVO 服务形式偏好
     * @apiSuccess (响应结果) {String} data.serviceFormPreferencesLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.serviceFormPreferencesLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.serviceFormPreferencesLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.serviceFormPreferencesLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.serviceFormPreferencesLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.serviceModePreferencesLabelVO 服务模式偏好
     * @apiSuccess (响应结果) {String} data.serviceModePreferencesLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.serviceModePreferencesLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.serviceModePreferencesLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.serviceModePreferencesLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.serviceModePreferencesLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.trustLabelVO 信任度
     * @apiSuccess (响应结果) {String} data.trustLabelVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.trustLabelVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.trustLabelVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.trustLabelVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.trustLabelVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} data.mgmCount MGM次数
     * @apiSuccess (响应结果) {String} data.hatchTime 孵化时长
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"C6zrH","data":{"monthCommCount":"U3kpuGPS","quarterCommCount":"WmBtJc3","serviceFormPreferencesLabelVO":{"labelId":"i0UeKNNT","labelBdpValue":"hU","labelValue":"Wtxly032n","labelBdpInputValue":"Qf1myP1","labelInputValue":"PYTs38"},"serviceStageLabelVO":{"labelId":"pT6KuKLYM","labelBdpValue":"M4IyVEK","labelValue":"eoAwbB0xvG","labelBdpInputValue":"dZDmTRgZ","labelInputValue":"pc"},"knowledgeHobbyLabelVO":{"labelId":"e3jy0o","labelBdpValue":"5LoDBd","labelValue":"7zdW1t","labelBdpInputValue":"bh","labelInputValue":"qjz97R1S3w"},"commFrequency":"bvAcF","weekCommCount":"GrB3nq2","serviceModePreferencesLabelVO":{"labelId":"WuY","labelBdpValue":"N6MuA","labelValue":"uDv7oFP","labelBdpInputValue":"1no5ZB5","labelInputValue":"Lr7JzryM"},"hatchTime":"ev","investKnowledgeLabelVO":{"labelId":"AzGMMtb","labelBdpValue":"1bDheq","labelValue":"Hn7Ki3NEC","labelBdpInputValue":"hCguVV9r","labelInputValue":"z"},"yearCommCount":"Psja","latestCommInfo":"Ujb1RMtp","trustLabelVO":{"labelId":"LGEXdx","labelBdpValue":"B7Rx4","labelValue":"mlQIcGSd","labelBdpInputValue":"GR","labelInputValue":"XaVln"},"strategyUnderstandLabelVO":{"labelBdpValues":["96MJJ3"],"labelCustomizeValues":["dUeC16c"],"labelValues":["iwW6"],"labelId":"S6N"},"commCountSum":"h37GfLT8ZV","mgmCount":"NP6vekDFjs"},"description":"aXDbGL1Z","timestampServer":"3tLbts"}
     */
    @PostMapping("/querybusinessinfo")
    @ResponseBody
    public CgiResponse<BusinessInfoVO> queryBusinessInfo(@RequestBody PortraitBaseRequest request) {
        return CgiResponse.appOk(portraitBaseInfoService.queryBusinessInfo(request));
    }

}
