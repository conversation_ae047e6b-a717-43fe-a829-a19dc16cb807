/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (入金凭证接口)
 * @date 2023/11/30 17:38
 * @since JDK 1.8
 */
@Data
public class DepositsCertificateVO extends AccountBaseVO {

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行卡号摘要
     */
    private String bankAcctDigest;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行logoUrl
     */
    private String bankLogoUrl;

    /**
     * swift编码
     */
    private String swiftCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行名称
     */
    private String bankCode;

    /**
     * 汇款币种
     * 人民币 CNY 156
     * 欧元 EUR 978
     * 港元（港币） HKD 344
     * 日元 JPY JPY
     * 美元 USD 840
     * 英镑 GBP 826
     * 台币 TWD 901
     * 越南盾 VND 704
     */
    private String remitCurCode;

    /**
     * 金额 后端返回数据精度为8位
     */
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 汇款凭证列表
     */
    private List<RemitImageVO> remitImageList;

    /**
     * 审核原因
     */
    private List<HkOpenAcctCheckVO> checkResult;

    /**
     * 打款凭证类型
     */
    private String voucherType;

    /**
     * 关联订单号
     */
    private String relationOrderNo;

}