package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 返回对象的base64
 * @date 2023/12/29 15:34
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenDownImageVO extends AccountBaseVO {

    /**
     * 图片Base64
     */
    private String imageBase;

    /**
     * 图片名称
     */
    private String imageName;

    /**
     * 图片格式
     */
    private String imageFormat;

}
