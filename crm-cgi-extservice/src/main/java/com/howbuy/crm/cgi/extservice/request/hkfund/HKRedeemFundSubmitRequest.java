/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @description:  赎回页面提交
 * <AUTHOR>
 * @date 2024/4/18 15:09
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKRedeemFundSubmitRequest extends BodyRequest {

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String txPassword;

    /**
     * 验证码类型
     */
    private String verifyCodeType;

    /**
     * 验证码
     */
    private String verifyCode;

    /**
     * 基金代码
     */
    @NotBlank(message = "基金代码不能为空")
    private String fundCode;

    /**
     * 赎回方式 必须 1-按份额、2-按金额
     */
    private String redeemMethod;

    /**
     * 赎回金额
     */
    private String redeemAmt;

    /**
     * 赎回份额
     */
    private String redeemVol;

    /**
     * 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-支票
     */
    @NotBlank(message = "赎回方向不能为空")
    private String redeemDirection;

    /**
     * 资金账号  1-回银行卡|电汇时必须
     */
    private String cpAcctNo;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 合同及列表
     */
    @Valid
    private List<HKFundAgreementRequest> contractList;

    @NotBlank(message = "防重标识不能为空")
    private String hbSceneId;
}
