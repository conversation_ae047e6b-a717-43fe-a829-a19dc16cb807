/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

import java.io.Serializable;
import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/7 13:58
 * @since JDK 1.8
 */
@Data
public class CmsPdfVO extends AccountBase<PERSON> implements Serializable {

    /**
     * pdf预览地址
     */
    private String pdfUrl;

    /**
     * pdf的byte buffer数据
     */
    private ByteBuffer pdfByteBuffer;

}