package com.howbuy.crm.cgi.extservice.vo.agreement;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 补充协议详情响应对象
 * @author: jinqing.rao
 * @date: 2024/3/6 18:20
 * @since JDK 1.8
 */
@Setter
@Getter
public class SupplementalAgreementDetailVO extends Body implements Serializable{

    private static final long serialVersionUID = -2353512086235602228L;

    /**
     * 基金编码
     */
    private String fundCode;


    /**
     * 基金名称
     */
    private String fundName;
    /**
     * 补充协议详情列表
     */
    private List<SupplementalAgreementDetailDtl> supplementalAgreementDetailDtlList;

    @Setter
    @Getter
    public static class SupplementalAgreementDetailDtl implements Serializable {
        private static final long serialVersionUID = 4515074567797724854L;


        /**
         * 协议签署截止时间
         * 格式：YYYY-MM-DD HH:MM
         */
        private String agreementSignEndDt;

        /**
         * 协议名称
         */
        private String agreementName;

        /**
         * 基金Code
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;
        /**
         * 协议说明
         */
        private String agreementDescription;

        /**
         * 协议地址
         * 同一个产品，是一样的，所以通过静态链接地址访问
         */
        private String agreementUrl;

        /**
         * 协议ID
         */
        private String agreementId;

    }
} 