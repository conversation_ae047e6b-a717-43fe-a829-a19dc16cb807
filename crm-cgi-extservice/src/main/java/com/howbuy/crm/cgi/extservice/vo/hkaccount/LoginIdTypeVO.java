/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

/**
 * @description: (登录证件类型VO)
 * <AUTHOR>
 * @date 2023/8/1 17:01
 * @since JDK 1.8
 */
@Data
public class LoginIdTypeVO {

    /**
     * 证件类型
     *
     * 大陆身份证-0
     * 香港身份证-D
     * 澳门身份证-E
     * 台湾身份证-F
     * 中国护照-1
     * 外国护照-6
     * 港澳通行证-4
     * 台胞证-A
     * 港澳台居民居住证-C
     * 其他证件-7
     */
    private String idType;

    /**
     * 证件类型描述
     */
    private String idTypeDesc;

    /**
     * 输入框描述
     */
    private String inputBoxDesc;

}
