/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.piggy;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/16 13:32
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositVoucherSubmitRequest implements Serializable {

    private static final long serialVersionUID = 4836253422652008807L;

    /**
     * 打款凭证号,新增不传值,编辑传值
     */
    private String voucherNo;

    /**
     * 香港资金账号
     */
    @NotBlank(message = "香港资金账号不能为空")
    private String cpAcctNo;
    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;
    /**
     * 汇款账户掩码
     */
    @NotBlank(message = "汇款账户掩码不能为空")
    private String bankAcctMask;

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空")
    private String bankName;


    /**
     * 银行swiftCode码值
     */
    @NotBlank(message = "银行swiftCode码值不能为空")
    private String swiftCode;


    /**
     * 汇款账户币种
     */
    @NotBlank(message = "汇款账户币种不能为空")
    private String remitCurrency;

    /**
     * 汇款金额
     */
    @NotBlank(message = "汇款币种不能为空")
    @DecimalMax(value = "**********", message = "汇款金额错误,超过最大金额")
    @DecimalMin(value = "0", message = "汇款金额需＞0，请检查")
    @Digits(integer = 10, fraction = 2, message = "汇款金额格式错误,请核对汇款金额")
    private String remitAmt;

    /**
     * 备注
     */
    private String remark;


    /**
     * 防重标识不能为空
     */
    @NotBlank(message = "防重标识不能为空")
    private String hbSceneId;

    /**
     * 是否同意换汇 0-否 1-是
     */
    private String agreeCurrencyExchange;

    /**
     * 汇款账户掩码
     */
    private List<PiggyDepositVoucher> payVoucherFiles;
    @Setter
    @Getter
    @Valid
    public static class PiggyDepositVoucher implements Serializable {

        private static final long serialVersionUID = -4202829567460820376L;
        /**
         * 文件名称
         */
        @NotBlank(message = "文件名称不能为空")
        private String fileName;

        /**
         * 文件路径
         */
        @NotBlank(message = "文件路径不能为空")
        private String thumbnailUrl;

        /**
         * 文件的格式类型
         */
        private String fileFormatType;
    }
}
