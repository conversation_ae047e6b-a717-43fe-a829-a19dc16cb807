/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.file;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.utils.ResponseUtils;
import com.howbuy.dfile.HFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/11 10:46
 * @since JDK 1.8
 */
public abstract class AbstractDownLoadFile extends AbstractFile {
    private static final Logger log = LoggerFactory.getLogger(AbstractDownLoadFile.class);


    /**
     * @param response
     * @return void
     * @description: 下载文件信息
     * @author: jinqing.rao
     * @date: 2024/5/11 11:15
     * @since JDK 1.8
     */
    public void downloadFile(HttpServletResponse response) {
        ServletOutputStream outputStream = null;
        //获取文件下载链接
        try {
            byte[] bytes = HFileService.getInstance().read2Bytes(fileBizTypeEnum.getStoreConfig(), filePath, fileName);
            // 设置 response 响应格式
            initServletResponseInfo(response);
            //输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
        } catch (IOException e) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_DOWNLOAD_ERROR);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("AbstractDownLoadFile>>>downloadFile 关闭文件流异常", e);
                }
            }
        }
    }

    /**
     * @param response
     * @return void
     * @description: 设置响应信息
     * @author: jinqing.rao
     * @date: 2024/5/11 11:16
     * @since JDK 1.8
     */
    void initServletResponseInfo(HttpServletResponse response) {
         //获取文件格式后缀
        String type = fileName.substring(fileName.lastIndexOf(".") + 1);
        String contentTypeByFileExtension = ResponseUtils.getContentTypeByFileExtension(type);
        ///设置文件格式
        response.setContentType(contentTypeByFileExtension);
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (Exception e) {
            fileName = "file" + type;
        }
        response.reset();
        // 设置response的Header
        response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
    }
}
