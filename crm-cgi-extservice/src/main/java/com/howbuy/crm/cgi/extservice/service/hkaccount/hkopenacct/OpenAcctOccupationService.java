package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.HkOpenAcctIdTypeUtils;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctEmploymentTypeEnum;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.occupation.HkOpenAcctOccupationRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.OpenOccupationVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctBizEnumVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkAcctCountryDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctAddressInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctOccupationDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import com.howbuy.crm.cgi.manager.enums.HkOpenAcctAddressTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 海外小程序步骤三,职业信息服务类
 * <AUTHOR>
 * @date 2024/1/22 19:06
 * @since JDK 1.8
 */
@Service
public class OpenAcctOccupationService extends OpenAcctAbstractService{

    /**
     * @description: 海外小程序步骤三,职业信息服务 保存接口
     * @param request
     * @return void
     * @author: jinqing.rao
     * @date: 2024/1/22 19:07
     * @since JDK 1.8
     */
    public void saveOpenOccupationInfo(HkOpenAcctOccupationRequest request) {
        //参数校验
        HkOpenAcctValidator.validatorOpenOccupationRequest(request);
        String hkCusNo = getHkCusNo();
        HkOpenAcctAddressInfoDTO emplAddr = null;
        if (!HkOpenAcctEmploymentTypeEnum.STUDENT.getCode().equals(request.getEmplStatus())) {
            //获取用户填写的就业公司地址
            emplAddr = openAcctCatchService.getHkOpenAcctAddressInfoDTO(getHkCusNo(), HkOpenAcctAddressTypeEnum.COMPANY_ACCOUNT.getHkOpenAccountStepEnum());
            if (null == emplAddr) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_COMPANY_ADDRESS_NOT_EXIST.getCode(), "公司地址信息错误,请检查完善地址信息");
            }
        }
        //保存redis缓存
        openAcctCatchService.saveOpenOccupationInfo(getHkCusNo(), OpenAcctConvert.toHkOpenAcctOccupationDTO(request, emplAddr));
        //删除审核不通过的证件信息原因
        openAcctCatchService.removeHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.PROFESSIONAL_INFORMATION_CHECK_RESULT, hkCusNo);
    }

    public void temporaryStorageOpenOccupationInfo(HkOpenAcctOccupationRequest request) {
        //保存redis缓存
        openAcctCatchService.saveOpenOccupationInfo(getHkCusNo(), OpenAcctConvert.toHkOpenAcctOccupationDTO(request, null));
    }

    public OpenOccupationVO queryOpenOccupationInfoDetail() {
        String hkCustNo = getHkCusNo();
        //获取国家信息map
        Map<String, HkAcctCountryDTO> countryInfoMap = hkAccCommonOuterService.getCountryDtoMap();
        //获取城市信息map
        Map<String, String> cityInfoMap = hkAccCommonOuterService.getCityInfoMap();
        //查询缓存信息
        HkOpenAcctOccupationDTO hkOpenAcctOccupationDTO = openAcctCatchService.getOpenOccupationInfoDetail(hkCustNo);
        //从缓存获取开户订单审核退回修改状态的错误信息,这里的信息是在 com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService.querySubmitResult 回写到缓存的。因为没有业务数据表
        String orderCheckReason = openAcctCatchService.getHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.PROFESSIONAL_INFORMATION_CHECK_RESULT, hkCustNo);
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = JSON.parseArray(orderCheckReason, HkOpenAcctCheckVO.class);
        //获取社会性质枚举
        Map<String, String> map = getEmplNatureOfBusinessDescMap();
        if (null != hkOpenAcctOccupationDTO) {
            return OpenAcctConvert.toOpenOccupationVO(hkOpenAcctOccupationDTO, countryInfoMap, hkOpenAcctCheckVOS,map);
        }
        //查询账户中心获取用户的开户订单
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getAccountOccupationDTO() && StringUtils.isNotBlank(orderInfoDTO.getDealNo())) {
            HkOpenAcctOccupationDTO accountOccupationDTO = orderInfoDTO.getAccountOccupationDTO();
            //拼接公司地址的全量地址
            String companyFullAddress = initCompanyFullAddress(accountOccupationDTO, countryInfoMap,cityInfoMap);
            accountOccupationDTO.setEmplAddrCn(companyFullAddress);
            //回填公司的地址信息到缓存,学生没有公司,需要判空
            String emplCountryCode = accountOccupationDTO.getEmplCountryCode();
            if(StringUtils.isNotBlank(emplCountryCode)){
                HkAcctCountryDTO hkAcctCountryDTO = countryInfoMap.get(emplCountryCode);
                HkOpenAcctAddressInfoDTO hkOpenAcctAddressInfoDTO = new HkOpenAcctAddressInfoDTO();
                hkOpenAcctAddressInfoDTO.setCountryCode(accountOccupationDTO.getEmplCountryCode());
                hkOpenAcctAddressInfoDTO.setCountryDesc(null == hkAcctCountryDTO ? null :hkAcctCountryDTO.getChineseName());
                hkOpenAcctAddressInfoDTO.setProvCode(accountOccupationDTO.getEmplProvCode());
                hkOpenAcctAddressInfoDTO.setProvDesc(cityInfoMap.get(accountOccupationDTO.getEmplProvCode()));
                hkOpenAcctAddressInfoDTO.setCityCode(accountOccupationDTO.getEmplCityCode());
                hkOpenAcctAddressInfoDTO.setCityDesc(cityInfoMap.get(accountOccupationDTO.getEmplCityCode()));
                hkOpenAcctAddressInfoDTO.setCountyCode(accountOccupationDTO.getEmplCountyCode());
                hkOpenAcctAddressInfoDTO.setCountyDesc(cityInfoMap.get(accountOccupationDTO.getEmplCountyCode()));
                hkOpenAcctAddressInfoDTO.setTownEn(accountOccupationDTO.getEmplTown());
                hkOpenAcctAddressInfoDTO.setStateEn(accountOccupationDTO.getEmplState());
                hkOpenAcctAddressInfoDTO.setDetailAddrCn(accountOccupationDTO.getEmplAddr());
                hkOpenAcctAddressInfoDTO.setDetailAddrEn(accountOccupationDTO.getEmplAddr());
                openAcctCatchService.saveHkOpenAcctAddressInfoDTO(hkCustNo, HkOpenAccountStepEnum.PROFESSIONAL_INFORMATION_COMPANY_ACCOUNT, hkOpenAcctAddressInfoDTO);
            }
            return OpenAcctConvert.toOpenOccupationVO(accountOccupationDTO, countryInfoMap, hkOpenAcctCheckVOS, map);
        }
        return OpenOccupationVO.builder().build();
    }

    private Map<String, String> getEmplNatureOfBusinessDescMap() {
        HkOpenAcctBizEnumVO hkOpenAcctBizEnumVO = hkCommonService.queryBizEnumByType(HkOpenAcctBizTypeEnum.HK_OPEN_ACCT_BIZ_BUSINESS_TYPE.getBizType());
        Map<String, String> map = new HashMap<>();
        List<HkOpenAcctBizEnumVO.BizEnumInfo> bizEnumInfoList = hkOpenAcctBizEnumVO.getBizEnumInfoList();
        if(!CollectionUtils.isEmpty(bizEnumInfoList)){
           //bizEnumInfoList 转换成 map
            bizEnumInfoList.forEach(item -> {
                map.put(item.getBizCode(), item.getBizDesc());
            });
        }
        return map;
    }

    private String initCompanyFullAddress(HkOpenAcctOccupationDTO accountOccupationDTO, Map<String, HkAcctCountryDTO> countryInfoMap,Map<String, String> cityInfoMap) {
        HkAcctCountryDTO hkAcctCountryDTO = countryInfoMap.get(accountOccupationDTO.getEmplCountryCode());
        String EnglishName = null;
        String chuneseName = null;
        if (null != hkAcctCountryDTO) {
            EnglishName = hkAcctCountryDTO.getEnglishName();
            chuneseName = hkAcctCountryDTO.getChineseName();
        }
        if (HkOpenAcctIdTypeUtils.SPECIAL_ADMINISTRATIVE_REGION.contains(accountOccupationDTO.getEmplCountryCode())) {
            return chuneseName + MarkConstants.SEPARATOR_COMMA + accountOccupationDTO.getEmplAddr();
        }
        if (HkOpenAcctIdTypeUtils.CN.equals(accountOccupationDTO.getEmplCountryCode())) {
            //通过省Code获取省名称
            String provDesc = cityInfoMap.get(accountOccupationDTO.getEmplProvCode());
            //通过市Code获取市名称
            String cityDesc = cityInfoMap.get(accountOccupationDTO.getEmplCityCode());
            //通过区Code获取区名称
            String countyDesc = cityInfoMap.get(accountOccupationDTO.getEmplCountyCode());
            return chuneseName + provDesc + cityDesc + countyDesc + accountOccupationDTO.getEmplAddr();
        }
        return accountOccupationDTO.getEmplAddr() + MarkConstants.SEPARATOR_COMMA + accountOccupationDTO.getEmplTown() + MarkConstants.SEPARATOR_COMMA + accountOccupationDTO.getEmplState() + MarkConstants.SEPARATOR_COMMA + EnglishName;
    }
}
