package com.howbuy.crm.cgi.extservice.interceptor;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.utils.AppNoLoginUtils;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description: 登录态校验拦截器
 * 区分Session登录和APP登录
 * @date 2024/2/21 10:16
 * @since JDK 1.8
 */
@Component
public class ExternalLoginInterceptor extends HandlerInterceptorAdapter {


    @Resource
    private LoginSessionInterceptor loginSessionInterceptor;

    @Resource
    private LoginAppInterceptor loginAppInterceptor;

    @Value("${active.env}")
    private String activeEnv;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 支持海外自动化平台的,因为平台暂不支持，POST请求上带参数,和海外App的请求方式冲突了,所以做兼容
        // 从请求头获取免登录字段
        if (noLoginHandler(request, activeEnv)) {
            return true;
        }
        //H5的Session登录
        if (RequestUtil.isHkAppLogin()) {
            //App token登录
            return loginAppInterceptor.preHandle(request, response, handler);
        } else {
            //H5 Session登录
            return loginSessionInterceptor.preHandle(request, response, handler);
        }
    }

    /**
     * @param request
     * @return boolean
     * @description: 支持海外自动化平台的, 因为平台暂不支持，POST请求上带参数,和海外App的请求方式冲突了,所以做兼容
     * PRODUCT_ID 海外APP的标识
     * TOKEN_ID  设备的tokenId;
     * HKCUSTNO 香港客户号
     * @author: jinqing.rao
     * @date: 2024/5/13 13:35
     * @since JDK 1.8
     */
    private static boolean noLoginHandler(HttpServletRequest request, String activeEnv) {
        boolean appNoLogin = AppNoLoginUtils.isAppNoLogin(activeEnv);
        if (appNoLogin && request.getMethod().equals(HttpMethod.POST.name()) && request.getContentType().contains(MediaType.APPLICATION_JSON_VALUE)) {
            MyHttpServletRequestWrapper myHttpServletRequestWrapper = (MyHttpServletRequestWrapper) request;
            String body = myHttpServletRequestWrapper.getBody();
            JSONObject jsonObject = JSON.parseObject(body);
            if (null == jsonObject || StringUtils.isBlank(jsonObject.getString(Constants.HKCUSTNO))) {
                throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求HK");
            }
            String productId = jsonObject.getString(Constants.PRODUCT_ID);
            if (StringUtils.isNotBlank(productId) && Constants.PRODUCT_ID_VALUE.equals(productId)) {
                String tokenId = jsonObject.getString(Constants.TOKEN_ID);
                request.setAttribute(Constants.TOKEN_ID, tokenId);
                request.setAttribute(Constants.PRODUCT_ID, productId);
                // 自动话平台需要明文参数,不传加密参数
                request.setAttribute(Constants.HKCUSTNO, jsonObject.getString(Constants.HKCUSTNO));
                RequestUtil.setRequestParams(jsonObject);
                return true;
            }
        }
        return false;
    }
}
