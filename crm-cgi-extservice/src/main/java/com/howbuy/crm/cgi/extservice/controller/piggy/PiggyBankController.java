/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.piggy;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.piggy.*;
import com.howbuy.crm.cgi.extservice.service.piggy.PiggyBankService;
import com.howbuy.crm.cgi.extservice.vo.piggy.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 海外储蓄罐接口类
 * <AUTHOR>
 * @date 2024/7/16 10:25
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/piggy/sign/")
public class PiggyBankController {

    @Resource
    private PiggyBankService piggyBankService;

    /**
     * @api {POST} /ext/piggy/sign/verify piggySignVerify()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName piggySignVerify()
     * @apiDescription 海外储蓄罐签约校验接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} bizType 业务类型  1:海外储蓄罐签约校验  2:海外储蓄罐打款凭证校验
     * @apiParamExample 请求体示例
     * {"bizType":"Ra","hkCustNo":"Uf"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.verfiyState 购买验证状态      0-校验通过      1-去开户      2-继续处理      3-查看开户进度      4-修改开户资料      5-去入金      6-查看入金进度      7-修改入金资料      8-客户状态异常      19-交易账号未激活      28-用户年纪是否超过65岁校验
     * @apiSuccess (响应结果) {String} data.showVerify 前端是否展示校验弹框 1 是 0 否 , 0 前端不做校验弹框,只去数据
     * @apiSuccess (响应结果) {String} data.openAcctStep 开户订单信息,填写到具体的步骤,开户步骤标识：1-证件信息页；2-个人信息页；3-职业信息页；4-声明信息页；5-投资经验页；6-银行卡页；7-电子签名页      该字段通过循环查询缓存,判断最大不步骤页
     * @apiSuccess (响应结果) {String} data.piggyLimitAge 用户年纪是否超过65岁校验 返回基金产品配置的储蓄罐年龄限制
     * @apiSuccess (响应结果) {String} data.fundRiskLevel 底层产品风险 状态是21的时候返回，0-保守型（最低）1-保守型 2-稳健型 3-平衡型 4-成长型 5-进取型
     * @apiSuccess (响应结果) {String} data.custRiskLevel 客户分险等级  状态是21的时候返回 0-保守型（最低）1-保守型 2-稳健型 3-平衡型 4-成长型 5-进取型
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"DR","data":{"fundRiskLevel":"GVavbK","piggyLimitAge":"xS6x","custRiskLevel":"qewpdJSF","verfiyState":"PEsISSbFm6","openAcctStep":"OSFwTxBbsp","showVerify":"1hi"},"description":"oi7","timestampServer":"Iavl"}
     */
    @PostMapping("verify")
    public CgiResponse<PiggyBankVerificationVO> piggySignVerify(@RequestBody PiggyBnakVerificationRequest request) {
        // 参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyBankService.piggySignVerify(request));
    }


    /**
     * @api {POST} /ext/piggy/sign/contractList queryPiggySignContractList()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName queryPiggySignContractList()
     * @apiDescription 海外储蓄罐签约合同列表查询
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 海外客户编号
     * @apiParam (请求体) {String} bizType 合同业务类型 1 海外储蓄罐签约 2.海外储蓄罐变更
     * @apiParamExample 请求体示例
     * {"bizType":"Jpn","hkCustNo":"2re5Yfxzia"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.fundCode 基金Code
     * @apiSuccess (响应结果) {Array} data.contractList 签单合同列表
     * @apiSuccess (响应结果) {String} data.contractList.fileName 文件名称
     * @apiSuccess (响应结果) {String} data.contractList.filePathUrl 文件路径
     * @apiSuccess (响应结果) {String} data.contractList.fileCode 文件代码
     * @apiSuccess (响应结果) {String} data.contractList.fileTypeName 文件类型名称
     * @apiSuccess (响应结果) {String} data.contractList.fileBizType 文件排序规则  该字段是服务端用来排序的自定义话字段,字段的具体值,结合业务需要排序的规则
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Tbis","data":{"fundCode":"t","contractList":[{"filePathUrl":"xPl","fileName":"Xp","fileCode":"YkXhWd8gQ","fileTypeName":"RDLFeF","fileBizType":"Mf5eo3"}]},"description":"wnxyy","timestampServer":"fRHg0WQlb"}
     */
    @PostMapping("contractList")
    public CgiResponse<PiggySignContractVO> queryPiggySignContractList(@RequestBody PiggySignContractRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyBankService.queryPiggySignContractList(request));
    }


    /**
     * @api {POST} /ext/ext/piggy/sign/submit submitPiggySign()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName submitPiggySign()
     * @apiDescription 储蓄罐协议签约-提交账户中心接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParam (请求体) {Array} signFilePathList 签名文件路径列表
     * @apiParam (请求体) {String} signFilePathList.fileName 文件名称
     * @apiParam (请求体) {String} signFilePathList.filePathUrl 文件路径
     * @apiParam (请求体) {String} signFilePathList.fileCode 文件代码
     * @apiParam (请求体) {String} signFilePathList.fileTypeName 文件类型名称
     * @apiParam (请求体) {String} hbSceneId
     * @apiParam (请求体) {String} agreed 是否同意协议变更 1 是 0 否
     * @apiParamExample 请求体示例
     * {"txPassword":"a8LObLn","hbSceneId":"GpgG8m2D","hkCustNo":"dXYibi","signFilePathList":[{"filePathUrl":"wq","fileName":"k","fileCode":"yQeHaaky","fileTypeName":"9GS9iEa"}],"agreed":"hSJg"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Rppt7h","description":"tAthPsSva","timestampServer":"jATqURK"}
     */
    @PostMapping("submit")
    public CgiResponse<Body> submitPiggySign(@RequestBody PiggySignSubmitRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        piggyBankService.submitPiggySign(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/piggy/sign/protocol/variation/submit submitProtocolVariation()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName submitProtocolVariation()
     * @apiDescription 海底储蓄罐底层变更-重新提交接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParam (请求体) {Array} signFilePathList 签名文件路径列表
     * @apiParam (请求体) {String} signFilePathList.fileName 文件名称
     * @apiParam (请求体) {String} signFilePathList.filePathUrl 文件路径
     * @apiParam (请求体) {String} signFilePathList.fileCode 文件代码
     * @apiParam (请求体) {String} signFilePathList.fileTypeName 文件类型名称
     * @apiParam (请求体) {String} hbSceneId
     * @apiParam (请求体) {String} agreed 是否同意协议变更 1 是 0 否
     * @apiParamExample 请求体示例
     * {"txPassword":"6B8","hbSceneId":"YAYS6i","hkCustNo":"48G01eNL","signFilePathList":[{"filePathUrl":"CHYtapRW","fileName":"xqDbE2","fileCode":"0jogCST","fileTypeName":"nxLj50pD"}],"agreed":"K"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"SeUbNF","description":"JifBfT","timestampServer":"D"}
     */
    @PostMapping("protocol/variation/submit")
    public CgiResponse<Body> submitProtocolVariation(@RequestBody PiggySignSubmitRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        piggyBankService.submitProtocolVariation(request);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {POST} /ext/piggy/sign/querysubmitresult querySubmitResult()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName querySubmitResult()
     * @apiDescription 海外储蓄罐签约-签约结果页查询接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} voucherNo 打款凭证订单号
     * @apiParamExample 请求体示例
     * {"voucherNo":"KbpfQXV","hkCustNo":"S"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.transferBankInfos 转账银行信息列表
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankAcctName 转账账户名称
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankName 转账银行名称
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankAddress 转账银行地址
     * @apiSuccess (响应结果) {Array} data.transferBankInfos.transferBankAccts 转账银行账号列表
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferSwiftCode 国际汇款识别码
     * @apiSuccess (响应结果) {String} data.transferBankInfos.transferBankCode 银行代码
     * @apiSuccess (响应结果) {String} data.transferBankInfos.bankLogoUrl 银行logo
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1","data":{"transferBankInfos":{"bankLogoUrl":"BRXM3","transferBankCode":"NEK","transferBankAccts":[],"transferBankName":"HJuxpRbNf","transferBankAcctName":"wf","transferBankAddress":"fxIFfrhMY","transferSwiftCode":"7"}},"description":"IyCb9MshE","timestampServer":"gPwYzzDso"}
     */
    @PostMapping("querysubmitresult")
    public CgiResponse<PiggySignResultVO> querySubmitResult(@RequestBody PiggyDepositVoucherDetailRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyBankService.querySubmitResult(request));
    }

    /**
     * @api {POST} /ext/piggy/sign/contract/record queryPiggySignContractRecord()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName queryPiggySignContractRecord()
     * @apiDescription 海外储蓄罐签约记录列表接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"size":9666,"hkCustNo":"mRWYyFM","page":1640}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.piggyContractList
     * @apiSuccess (响应结果) {Array} data.piggyContractList.contractList 海外储蓄罐签约文件
     * @apiSuccess (响应结果) {String} data.piggyContractList.signDate 签约时间
     * @apiSuccess (响应结果) {String} data.piggyContractList.typeName 类型名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"jrdzATiGlN","data":{"piggyContractList":[{"contractList":[],"typeName":"0nWY45h","signDate":"B7UXYJ"}]},"description":"0","timestampServer":"GD6eM"}
     */
    @PostMapping("contract/record")
    public CgiResponse<PiggySignContractRecordVO> queryPiggySignContractRecord(@RequestBody PiggyBaseParamRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyBankService.queryPiggySignContractRecord(request));
    }

    /**
     * @api {POST} /ext/piggy/sign/close closePiggySign()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName closePiggySign()
     * @apiDescription 海外储蓄罐签约列表海外储蓄罐关闭接口
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParamExample 请求体示例
     * {"txPassword":"vajA","hkCustNo":"jsTKOiw"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"V1hQ","description":"H6yIozq","timestampServer":"M"}
     */
    @PostMapping("close")
    public CgiResponse<Body> closePiggySign(@RequestBody PiggySignCloseRequest request) {
        BasicDataTypeValidator.validator(request);
        piggyBankService.closePiggySign(request);
        return CgiResponse.ok(new Body());
    }


    /**
     * @api {POST} /ext/piggy/sign/protocol/variation/result queryProtocolVariationResult()
     * @apiVersion 1.0.0
     * @apiGroup PiggySignController
     * @apiName queryProtocolVariationResult()
     * @apiDescription 海外储蓄罐底层更换-结果页查询
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} agreed 是否同意协议变更 1 是 0 否
     * @apiParamExample 请求体示例
     * {"hkCustNo":"fqMn","agreed":"SQ4lZPfzx"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.keyPointInfoList
     * @apiSuccess (响应结果) {String} data.keyPointInfoList.keyPointFundName 关键点名称
     * @apiSuccess (响应结果) {String} data.keyPointInfoList.keyPointDt 关键点发生时间 yyyyMMdd
     * @apiSuccess (响应结果) {String} data.keyPointInfoList.keyPointTm 关键点发生时间 HHmmss
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"GQZK6","data":{"keyPointInfoList":[{"keyPointTm":"sWCnE","keyPointDt":"4b1guBpX","keyPointFundName":"szb"}]},"description":"Yjv5","timestampServer":"dvlc9gvTC7"}
     */
    @PostMapping("protocol/variation/result")
    public CgiResponse<PiggyProtocolVariationResultVO> queryProtocolVariationResult(@RequestBody PiggyProtocolVariationResultRequest request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyBankService.queryProtocolVariationResult(request));
    }


    /**
     * @api {POST} /ext/piggy/sign/query/sign/status queryCustSignStatus()
     * @apiVersion 1.0.0
     * @apiGroup PiggyBankController
     * @apiName queryCustSignStatus()
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {"size":9625,"hkCustNo":"J5igxq","page":2630}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.signStatus 储蓄罐签约状态
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"YaCDlHAleM","data":{"signStatus":"ejVejOe"},"description":"gf2ejEq","timestampServer":"4ekBoN"}
     */
    @PostMapping("query/sign/status")
    public CgiResponse<PiggyCustSignStatusVO> queryCustSignStatus(@RequestBody PiggyBaseParamRequest  request) {
        BasicDataTypeValidator.validator(request);
        return CgiResponse.ok(piggyBankService.queryCustSignStatus(request));
    }
}
