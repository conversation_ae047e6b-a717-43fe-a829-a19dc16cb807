package com.howbuy.crm.cgi.extservice.request.account.hkopenacct.bankinfo;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.bankinfo.HkOpenAcctBankInfoVO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 银行卡信息暂存接口
 * <AUTHOR>
 * @date 2023/11/30 14:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctBankRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1843454714322331209L;

    /**
     *银行代码
     */
    @NotBlank(message = "银行代码不能为空")
    private String bankCode;

    /**
     * 银行账户名称，前端展示名称
     */
    @NotBlank(message = "银行账户名称不能为空")
    private String bankAcctName;

    /**
     * 银行账户中文名称,不一定有值
     */
    private String bankAcctCnName;

    /**
     * 银行英文名称
     */
    @NotBlank(message = "银行账户英文名称不能为空")
    private String bankAcctEnName;

    /**
     *银行账号
     */
    @NotBlank(message = "银行账号不能为空")
    private String bankAcct;

    /**
     *是否银行联名账户  1:是 0：否
     */
    @NotBlank(message = "是否银行联名账户不能为空")
    private String jointAccount;


    /**
     *关系证明文件列表
     */
    private List<ImageVO> jointAccountFileList;

    /**
     *银行SWIFT编码
     */
    @NotBlank(message = "银行SWIFT编码不能为空")
    private String swiftCode;

    /**
     *代理银行代码
     */
    private String brokerBankCode;

    /**
     * 代理银行名称(前端展示名称)
     */
    private String brokerBankName;

    /**
     * 代理银行的中文名
     */
    private String brokerBankCnName;

    /**
     * 代理银行的英文名
     */
    private String brokerBankEnName;

    /**
     *代理银行账号
     */
    private String brokerBankAcct;

    /**
     *代理银行swiftCode
     */
    private String brokerSwiftCode;

    /**
     *币种列表
     */
    private List<HkOpenAcctBankInfoVO.CurrencyVO> currencyVOList;

    /**
     *银行账号照片列表
     */
    private List<ImageVO> bankAcctImageList;


    /**
     * 账号持有人
     */
    private String bankAcctHolder;


    /**
     * 是否有代理银行 1是 0否
     */
    private String hasBrokerBank;

    /**
     * 1:保存 2:退出
     */
    @NotBlank(message = "保存类型不能为空")
    private String openSaveType;

}
