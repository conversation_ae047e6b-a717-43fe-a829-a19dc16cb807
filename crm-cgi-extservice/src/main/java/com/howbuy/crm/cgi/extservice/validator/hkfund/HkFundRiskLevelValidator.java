/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.hkfund;

import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/24 16:59
 * @since JDK 1.8
 */
public class HkFundRiskLevelValidator implements HkFundValidator{

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;
    /**
     * 基金信息
     */
    private final FundBasicInfoDTO fundBasicInfoDTO;

    public HkFundRiskLevelValidator(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO) {
        this.hkCustInfo = hkCustInfo;
        this.fundBasicInfoDTO = fundBasicInfoDTO;
    }
    @Override
    public HKFundVerificationVO verification() {
        if(StringUtils.isNotBlank(fundBasicInfoDTO.getFundRiskLevel())){
            if(fundBasicInfoDTO.getFundRiskLevel().compareTo(hkCustInfo.getRiskToleranceLevel()) > 0){
                HKFundVerificationVO fundVerificationVO = new HKFundVerificationVO();
                fundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.RISK_LEVEL_NOT_MATCH.getCode());
                fundVerificationVO.setCustomRiskLevelCode(hkCustInfo.getRiskToleranceLevel());
                fundVerificationVO.setFundRiskLevelCode(fundBasicInfoDTO.getFundRiskLevel());
                return fundVerificationVO;
            }
        }
        return null;
    }
}
