package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description T@T服务指引req
 * @Date 2024/9/2 17:28
 */
public class PortraitServiceGuideRequest implements Serializable {

    private static final long serialVersionUID = -2997811246984835878L;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 服务阶段key
     */
    private String fwjdKey;

    /**
     * 服务卡片key
     */
    private String cardKey;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getFwjdKey() {
        return fwjdKey;
    }

    public void setFwjdKey(String fwjdKey) {
        this.fwjdKey = fwjdKey;
    }

    public String getCardKey() {
        return cardKey;
    }

    public void setCardKey(String cardKey) {
        this.cardKey = cardKey;
    }
}
