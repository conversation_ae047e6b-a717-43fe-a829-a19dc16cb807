package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitTaskIgnoreRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitWorkbenchRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.todotask.PortraitTodotaskService;
import com.howbuy.crm.cgi.extservice.service.portrait.workbench.PortraitWorkBenchService;
import com.howbuy.crm.cgi.extservice.vo.portrait.workbench.PortraitTaskIgnoreVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.workbench.PortraitWorkbenchVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户画像-工作台相关接口
 * <AUTHOR>
 * @date 2024-03-19 14:50:00
 */
@Slf4j
@RestController
@RequestMapping("/portrait/workbench")
public class PortraitWorkbenchController {

    @Resource
    private PortraitTodotaskService portraitTodotaskService;

    @Resource
    private PortraitWorkBenchService portraitWorkBenchService;

    /**
     * @api {POST} /ext/portrait/workbench/home getWorkbenchHome()
     * @apiVersion 1.0.0
     * @apiGroup PortraitWorkbenchController
     * @apiName getWorkbenchHome()
     * @apiDescription 获取工作台首页数据
     *
     * @apiParam (请求体) {String} userId 用户id（必填）
     *
     * @apiParamExample 请求体示例
     * {
     *     "userId": "12345"
     * }
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.todoTaskList 待办任务列表
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskId 任务ID
     * @apiSuccess (响应结果) {String} data.todoTaskList.cardName 卡片名称
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskName 任务名称
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskLink 任务链接
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskDesc 任务描述
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskImg 任务图片
     * @apiSuccess (响应结果) {String} data.todoTaskList.dataType 数据类型
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskTime 任务时间
     * @apiSuccess (响应结果) {String} data.todoTaskList.taskType 待办任务类型(1-IPS报告、2-投后报告、3-素材上新任务)
     * @apiSuccess (响应结果) {String} data.todoTaskList.dataId 待办任务数据源Id
     * @apiSuccess (响应结果) {String} data.todoTaskList.materialSendType 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
     * @apiSuccess (响应结果) {Array} data.todoTaskList.matchCustList 适用客户列表
     * @apiSuccess (响应结果) {String} data.todoTaskList.matchCustList.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.todoTaskList.matchCustList.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.todoTaskList.matchCustList.label 标签
     * @apiSuccess (响应结果) {String} data.todoTaskList.matchCustList.mobileMask 手机号(掩码)
     * @apiSuccess (响应结果) {Object} data.toolBoxInfo 工具箱信息
     * @apiSuccess (响应结果) {String} data.toolBoxInfo.toolBoxTitle 工具箱标题
     * @apiSuccess (响应结果) {Array} data.toolBoxInfo.toolBoxs 工具箱列表
     * @apiSuccess (响应结果) {String} data.toolBoxInfo.toolBoxs.title 工具箱标题
     * @apiSuccess (响应结果) {Array} data.toolBoxInfo.toolBoxs.tools 工具列表
     * @apiSuccess (响应结果) {String} data.toolBoxInfo.toolBoxs.tools.title 工具标题
     * @apiSuccess (响应结果) {String} data.toolBoxInfo.toolBoxs.tools.icon 工具图标
     * @apiSuccess (响应结果) {String} data.toolBoxInfo.toolBoxs.tools.link 工具链接
     * @apiSuccess (响应结果) {String} data.toolBoxInfo.toolBoxs.tools.iconCode 工具code
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiSuccessExample 响应结果示例
     * {
     *     "code": "0000",
     *     "description": "成功",
     *     "data": {
     *         "todoTaskList": [{
     *             "taskId": "123",
     *             "cardName": "待办卡片",
     *             "taskName": "任务名称",
     *             "taskLink": "http://example.com",
     *             "taskDesc": "任务描述",
     *             "taskImg": "http://example.com/img.jpg",
     *             "dataType": "1",
     *             "taskTime": "2024-03-19 15:00:00",
     *             "taskType": "1",
     *             "dataId": "456",
     *             "materialSendType": "1",
     *             "matchCustList": [{
     *                 "hboneNo": "HB123456",
     *                 "custName": "张三",
     *                 "label": "重要客户",
     *                 "mobileMask": "138****8888"
     *             }]
     *         }],
     *         "toolBoxInfo": {
     *             "toolBoxTitle": "常用工具",
     *             "toolBoxs": [{
     *                 "title": "工具分类",
     *                 "tools": [{
     *                     "title": "工具名称",
     *                     "icon": "http://example.com/icon.png",
     *                     "link": "http://example.com/tool",
     *                     "iconCode": "123"
     *                 }]
     *             }]
     *         }
     *     },
     *     "timestampServer": "1710831600000"
     * }
     */
    @PostMapping("/home")
    public CgiResponse<PortraitWorkbenchVO> getWorkbenchHome(@RequestBody PortraitWorkbenchRequest request) {
        log.info("工作台首页查询请求参数：{}", JSON.toJSONString(request));
        PortraitWorkbenchVO benchVo = portraitWorkBenchService.getWorkbenchHome(request);
        return CgiResponse.appOk(benchVo);
    }

    /**
     * @api {POST} /ext/portrait/workbench/task/ignore ignoreTask()
     * @apiVersion 1.0.0
     * @apiGroup PortraitWorkbenchController
     * @apiName ignoreTask()
     * @apiDescription 工作台待办任务忽略操作
     *
     * @apiParam (请求体) {String} taskId 任务id（必填）
     * @apiParam (请求体) {String} userId 用户id（必填）
     *
     * @apiParamExample 请求体示例
     * {
     *     "taskId": "123",
     *     "userId": "TA123456"
     * }
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiSuccessExample 响应结果示例
     * {
     *     "code": "0000",
     *     "description": "成功",
     *     "data": {},
     *     "timestampServer": "1710831600000"
     * }
     */
    @PostMapping("/task/ignore")
    public CgiResponse<PortraitTaskIgnoreVO> ignoreTask(@RequestBody PortraitTaskIgnoreRequest request) {
        log.info("工作台待办任务忽略操作请求参数：{}", JSON.toJSONString(request));
        PortraitTaskIgnoreVO  ignoreVO=portraitTodotaskService.executeIgnoreTask(request);
        return CgiResponse.appOk(ignoreVO);
    }
} 