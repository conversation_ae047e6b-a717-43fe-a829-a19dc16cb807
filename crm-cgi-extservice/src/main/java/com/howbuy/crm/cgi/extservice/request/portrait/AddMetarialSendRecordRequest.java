package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户画像-新增素材推送记录req
 * @Date 2024/9/2 18:31
 */
public class AddMetarialSendRecordRequest implements Serializable {

    private static final long serialVersionUID = -7771784061971677372L;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 投顾号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 内容地址
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "内容地址", isRequired = true)
    private String contentId;

    /**
     * 素材ID
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "素材ID", isRequired = true)
    private String materialId;

    /**
     * 素材类型（1-素材 2-资配报告 3-持仓报告）
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "素材类型", isRequired = true)
    private String materialType;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }
}
