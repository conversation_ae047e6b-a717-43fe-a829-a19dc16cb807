package com.howbuy.crm.cgi.extservice.request.account.hkopenacct;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 海外小程序开户页面, 地址信息保存
 * @date 2023/12/19 15:57
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctProCityRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 3909835273658247335L;

    /**
     * 地址类型  1：居住地址  2：通讯地址  3：出生地  4: 银行填写页公司地址
     */
    @NotBlank(message = "地址类型不能为空")
    private String addressType;

    /**
     * 是否需要翻译详细地址, 0:不需要, 1:需要
     */
    @NotBlank(message = "是否需要翻译详细地址不能为空")
    private String transAddress;

    /**
     * 国家代码
     */
    @NotBlank(message = "国家代码不能为空")
    private String countryCode;

    /**
     * 国家代码名称描述
     */
    @NotBlank(message = "国家名称描述不能为空")
    private String countryDesc;

    /**
     * 国家英文描述
     */
    private String countryEnglishDesc;

    /**
     * 省份代码
     */
    private String provCode;


    /**
     * 省份代码名称
     */
    private String provDesc;

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 城市代码名称
     */
    private String cityDesc;


    /**
     * 区县代码
     */
    private String countyCode;

    /**
     * 区县描述
     */
    private String countyDesc;

    /**
     * 省代码英文名称
     */
    private String provEnglishDesc;

    /**
     * 城市英文
     */
    private String cityEnglishDesc;


    /**
     * 区县英文
     */
    private String countyEnglishDesc;

    /**
     * 详细地址中文描述
     */
    private String detailAddrCn;

    /**
     * 详细地址英文描述
     */
    private String detailAddrEn;


    /**
     * 城/镇（英文）
     */
    private String townEn;

    /**
     * 省/州（英文）
     */
    private String stateEn;
}
