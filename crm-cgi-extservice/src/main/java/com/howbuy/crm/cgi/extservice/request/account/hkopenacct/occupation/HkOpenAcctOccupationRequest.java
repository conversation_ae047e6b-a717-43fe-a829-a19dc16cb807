package com.howbuy.crm.cgi.extservice.request.account.hkopenacct.occupation;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 海外开户暂存职业信息接口
 * <AUTHOR>
 * @date 2023/11/30 10:11
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctOccupationRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -3096821449631336766L;

    /**
     * 就业状况 01-雇主 02-全职 03-兼职 04-主妇 05-学生 06-退休 07-非在职
     */
    @NotBlank(message = "就业状况不能为空")
    private String emplStatus;

    /**
     * 就业公司名称
     */
    private String emplCompanyName;

    /**
     * 1: 填写上一份工作, 0:填写配偶的工作资料
     */
    private String emplStatusType;

    /**
     * 就业公司地址-详细地址
     */
    @NotBlank(message = "就业公司地址-详细地址")
    private String emplAddrCn;

    /**
     * 就业公司地址-英文详细地址
     */
    @NotBlank(message = "就业公司地址-英文详细地址")
    private String emplAddrEn;

    /**
     * 业务性质
     */
    @NotBlank(message = "业务性质不能为空")
    private String emplNatureOfBusiness;


    /**
     * 业务性质描述
     */
    private String emplNatureOfBusinessDesc;


    /**
     * 其他业务性质
     */
    private String otherEmplNatureOfBusiness;

    /**
     * 职位/称衔
     */
    private String designation;

//    /**
//     * 每年收入水平
//     * ['01-≦HK$500', '000;
//     * 02-HK$500', '001', '-', 'HK$1', '000', '000;
//     * 03-HK$1', '000', '001', '-', 'HK$2', '000', '000;
//     * 04-HK$2', '000', '001', '-', 'HK$5', '000', '000;
//     * 05->HK$5', '000', '000']
//     */
//    @NotBlank(message = "每年收入水平不能为空")
//    private String incLevel;


    /**
     * 税务管辖区代码
     */
    @NotBlank(message = "税务管辖区代码不能为空")
    private String taxJurisdictionCode;


    @NotBlank(message = "税务管辖区名称不能为空")
    private String taxJurisdictionDesc;

    /**
     * 是否有税务编号 1是 0否
     */
    @NotBlank(message = "是否有税务编号不能为空")
    private String hasTin;

    /**
     * 无税务编号理由
     * ['01-账户持有人的居留司法税务管辖区并没有向其居民发出税务编号;02-账户持有人不能取得税务编号;03-账户持有人无须提供税务编号']
     */
    private String noTinReason;


    /**
     * 不能取的税务编号的原因
     */
    private String noObtainTinReason;

    /**
     * 税务编号类型
     */
    private String tinType;


    /**
     * 税务编号类型说明
     */
    private String otherTinTypeDesc;

    /**
     * 税务编号
     */
    private String tin;

    /**
     * 开户保存类型 必填，01-保存，02-退出
     */
    @NotBlank(message = "开户保存类型不能为空")
    private String openSaveType;

}
