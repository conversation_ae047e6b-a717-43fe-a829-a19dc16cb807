package com.howbuy.crm.cgi.extservice.request.account.hkapp;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import crm.howbuy.base.validation.MyValidation;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: App登出类
 * <AUTHOR>
 * @date 2024/2/29 16:57
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAppAccountLoginOutRequest extends HkAppAccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -1218397300491423177L;

}
