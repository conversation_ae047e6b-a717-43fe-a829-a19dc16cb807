/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.VerifyCodeTypeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.BindStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.CustStateEnum;
import com.howbuy.crm.cgi.extservice.common.enums.UnBindStatusEnum;
import com.howbuy.crm.cgi.extservice.common.utils.ExceptionUtil;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.HboneInfoRequest;
import com.howbuy.crm.cgi.extservice.request.account.UnBindHboneInfoRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoPlaintextDTO;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkHboneNoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: (一账通相关操作的实现)
 * <AUTHOR>
 * @date 2023/12/11 15:57
 * @since JDK 1.8
 */
@Slf4j
@Service("hboneNoService")
public class HboneNoService {

    //客服电话 配置数据
    @Value("${CUSTOMER_TEL}")
    private String customerTel;

    @Autowired
    private CustInfoService custInfoService;

    @Autowired
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Autowired
    private HkHboneNoOuterService hkHboneNoOuterService;

    @Autowired
    private VerifyCodeService verifyCodeService;

    /**
     * @description:(获取绑定了的一账通数据)
     * @param hkCustNo
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HboneNoInfoVO
     * @author: xufanchao
     * @date: 2024/1/9 11:30
     * @since JDK 1.8
     */
    public HboneNoInfoVO getNoInfoVO(String hkCustNo) {
        HkCustInfoDTO bindHboneNo = hkCustInfoOuterService.getHkCustInfoByBindHboneNo(hkCustNo);
        return transDTOToVO(bindHboneNo);
    }


    /**
     * @description:(根据验证码的类型进行绑定校验)
     * @param req 绑定的请求对象
     * @return void
     * @author: xufanchao
     * @date: 2023/12/11 16:01
     * @since JDK 1.8
     */
    public BindStatusVO checkBindByVerifyCodeType(HboneInfoRequest req) {
        BindStatusVO bindStatusVO = new BindStatusVO();
        // 当验证码类型为 11 --- 绑定一账通-好买手机号验证码 进行校验的时候
        if (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.BIND_HBONE_MOBILE_VERIFY_CODE.getCode())) {
            return bindHboneByHbMobile(req, bindStatusVO);
        }
        else if (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.BIND_HK_MOBILE_VERIFY_CODE.getCode()) || (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.BIND_HK_EMAIL_VERIFY_CODE.getCode()))) {
            // 根据当前登陆用户获取到对应的香港客户信息
            CustInfoVO custInfo = custInfoService.getCustInfo();
            if (!CacheServiceImpl.getInstance().exists(CacheKeyPrefix.BIND_VERIFY_CODE_KEY_PREFIX + custInfo.getHkCustNo())) {
                throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
            }
            if (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.BIND_HK_MOBILE_VERIFY_CODE.getCode())) {
                // 校验验证码
                String mobile = hkCustInfoOuterService.getMobile(custInfo.getHkCustNo());
                verifyCodeService.checkSmsVerifyCode(mobile, req.getVerifyCode(), VerifyCodeTypeEnum.BIND_HK_MOBILE_VERIFY_CODE.getMessageTempleteEnum());
                // 从缓存中获取一账通号
                String bindHboneNo = CacheServiceImpl.getInstance().get(CacheKeyPrefix.BIND_VERIFY_CODE_KEY_PREFIX + custInfo.getHkCustNo()).toString();
                hkHboneNoOuterService.bindHkBindHbone(custInfo.getHkCustNo(), bindHboneNo);
            }else {
                // 校验验证码
                HkCustInfoPlaintextDTO custInfoPlaintext = custInfoService.getCustInfoPlaintext(custInfo.getHkCustNo());
                verifyCodeService.checkEmailVerifyCode(custInfoPlaintext.getEmail(), req.getVerifyCode(), VerifyCodeTypeEnum.BIND_HK_EMAIL_VERIFY_CODE.getMessageTempleteEnum());
                // 从缓存中获取一账通号
                String bindHboneNo = CacheServiceImpl.getInstance().get(CacheKeyPrefix.BIND_VERIFY_CODE_KEY_PREFIX + custInfo.getHkCustNo()).toString();
                hkHboneNoOuterService.bindHkBindHbone(custInfo.getHkCustNo(), bindHboneNo);
            }
            bindStatusVO.setBindStatus(BindStatusEnum.DONE.getCode());
            return bindStatusVO;
        }
        return bindStatusVO;
    }

    /**
     * @description:(好买手机号绑定)
     * @param req
     * @param bindStatusVO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.BindStatusVO
     * @author: xufanchao
     * @date: 2024/1/15 19:18
     * @since JDK 1.8
     */
    private BindStatusVO bindHboneByHbMobile(HboneInfoRequest req, BindStatusVO bindStatusVO) {
        // 根据当前登陆用户获取到对应的香港客户信息
        CustInfoVO custInfo = custInfoService.getCustInfo();
        if (!custInfo.getCustName().equals(req.getHbCustName())) {
            BindStatusVO.WarnVo warnVo = new BindStatusVO.WarnVo();
            warnVo.setTitle("无法发起绑定");
            warnVo.setTips(Collections.singletonList(ExceptionCodeEnum.BIND_HBONE_NAME_CHECK_ERROR.getDescription()));
            warnVo.setNotice("如有疑问，请联系您的专属服务人员或好买香港客服");
            warnVo.setPhone(customerTel);
            bindStatusVO.setWarnVo(warnVo);
            fillBindStatusVO(bindStatusVO);
            return bindStatusVO;
        }

        // 校验手机号和证件号是否均一致
        boolean checkMobileAndIdNo = checkMobileAndIdNo(req, custInfo);
        if (checkMobileAndIdNo) {
            BindStatusVO.WarnVo warnVo = new BindStatusVO.WarnVo();
            warnVo.setTitle("无法发起绑定");
            warnVo.setTips(Arrays.asList(ExceptionCodeEnum.BIND_HBONE_ID_MOBILE_CHECK_ERROR.getDescription(), ExceptionCodeEnum.BIND_HBONE_ID_MOBILE_CHECK_ERROR_1.getDescription()));
            warnVo.setNotice("如有疑问，请联系您的专属服务人员或好买香港客服");
            warnVo.setPhone(customerTel);
            bindStatusVO.setWarnVo(warnVo);
            fillBindStatusVO(bindStatusVO);
            return bindStatusVO;
        }
        // 校验一账通信息是否存在
        HkCustInfoDTO hkCustInfoByMobile = hkCustInfoOuterService.getHkCustInfoByMobile(req.getHbMobile());
        if (Objects.isNull(hkCustInfoByMobile) || !(Objects.equals(hkCustInfoByMobile.getCustName(), req.getHbCustName()) &&
                Objects.equals(hkCustInfoByMobile.getIdNoDigest(), DigestUtil.digest(req.getHbIdNo())) && Objects.equals(hkCustInfoByMobile.getHbMobileDigest(), DigestUtil.digest(req.getHbMobile())))) {
            BindStatusVO.WarnVo warnVo = new BindStatusVO.WarnVo();
            warnVo.setTitle("验证不通过");
            warnVo.setTips(Arrays.asList(ExceptionCodeEnum.BIND_HBONE_CHECK_ERROR.getDescription()));
            warnVo.setNotice("如有疑问，请联系您的专属服务人员或好买香港客服");
            warnVo.setPhone(customerTel);
            bindStatusVO.setWarnVo(warnVo);
            fillBindStatusVO(bindStatusVO);
            return bindStatusVO;
        }
        // 校验一账通号是否绑定过其他账户
        String bindHboneNo = hkCustInfoOuterService.getBindHboneNo(hkCustInfoByMobile.getHboneNo());
        if (!Objects.equals(bindHboneNo, custInfo.getHboneNo())) {
            BindStatusVO.WarnVo warnVo = new BindStatusVO.WarnVo();
            warnVo.setTitle("验证不通过");
            warnVo.setTips(Arrays.asList(ExceptionCodeEnum.UNBIND_HBONE_ID_MOBILE_CHECK_ERROR.getDescription()));
            warnVo.setNotice("如有疑问，请联系您的专属服务人员或好买香港客服");
            warnVo.setPhone(customerTel);
            bindStatusVO.setWarnVo(warnVo);
            fillBindStatusVO(bindStatusVO);
            return bindStatusVO;
        }
        // 验证验证码
        verifyCodeService.checkSmsVerifyCode(req.getHbMobile(), req.getVerifyCode(), VerifyCodeTypeEnum.BIND_HBONE_MOBILE_VERIFY_CODE.getMessageTempleteEnum());
        // 调用香港账户中心的一账通绑定接口
        if (DigestUtil.digest(req.getHbMobile()).equals(custInfo.getMobileDigest())) {
            hkHboneNoOuterService.bindHkBindHbone(custInfo.getHkCustNo(), hkCustInfoByMobile.getHboneNo());
            bindStatusVO.setBindStatus(BindStatusEnum.DONE.getCode());
            return bindStatusVO;
        } else {
            String hkMobile = hkCustInfoOuterService.getMobile(custInfo.getHkCustNo());
            // 把当前香港客户号和 一账通号做缓存 五分钟过期
            CacheServiceImpl.getInstance().put(CacheKeyPrefix.BIND_VERIFY_CODE_KEY_PREFIX + custInfo.getHkCustNo(), hkCustInfoByMobile.getHboneNo());

            boolean validChinesePhoneNumber = custInfo.getMobileAreaCode().equals(Constants.DEFAULT_MOBILE_AREA_CODE);
            bindStatusVO.setBindStatus(validChinesePhoneNumber ? BindStatusEnum.VERIFY_HK_MOBILE.getCode() : BindStatusEnum.VERIFY_HK_EMAIL.getCode());
            if (validChinesePhoneNumber) {
                bindStatusVO.setMobileDigest(custInfo.getMobileDigest());
                bindStatusVO.setDescription(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_VERIFY.getDescription() + custInfo.getMobileMask());
                bindStatusVO.setReturnCode(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_VERIFY.getCode());
            } else {
                bindStatusVO.setEmailDigest(custInfo.getEmailDigest());
                bindStatusVO.setDescription(ExceptionCodeEnum.HK_OPEN_ACCOUNT_EMAIL_VERIFY.getDescription()  + custInfo.getEmailMask());
                bindStatusVO.setReturnCode(ExceptionCodeEnum.HK_OPEN_ACCOUNT_EMAIL_VERIFY.getCode());
            }
            return bindStatusVO;
        }
    }

    private void fillBindStatusVO(BindStatusVO bindStatusVO) {
        bindStatusVO.setReturnCode(ExceptionCodeEnum.SYSTEM_ERROR.getCode());
        bindStatusVO.setDescription(ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
    }

    /**
     * @description:(一账通号解绑数据校验以及实际调用)
     * @param req
     * @return void
     * @author: xufanchao
     * @date: 2023/12/12 20:04
     * @since JDK 1.8
     */
    public UnBindStatusVO checkUnBindByVerifyCodeType(UnBindHboneInfoRequest req) {
        UnBindStatusVO bindStatusVO = new UnBindStatusVO();
        // 首先校验验证码
        // 当第一步传入的验证码为空的时候，即初始化步骤
        if (StringUtils.isBlank(req.getVerifyCode())) {
            // 获取当前会话的客户号
            String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
            // 获取好买手机号
            String hbMobile = hkCustInfoOuterService.getHbMobile(hkCustNo);
            bindStatusVO.setDescription(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_UNBIND.getDescription() + MaskUtil.maskMobile(hbMobile));
            bindStatusVO.setBindStatus(UnBindStatusEnum.UN_VERIFY_HB_MOBILE.getCode());
            bindStatusVO.setMobileDigest(DigestUtil.digest(hbMobile));
            return bindStatusVO;
        }

        // 根据当前会话信息获取到对应的香港客户信息  03-解绑一账通号-香港手机号验证码  --- 实际值 07；04-解绑一账通号-好买手机号验证码 - 实际值 08；05-解绑一账通号-香港邮箱验证码--- 57；
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 根据会话里面的香港客户号调用基础信息查询接口，判断是存在对应关系
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        if (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.UNBIND_HK_MOBILE_VERIFY_CODE.getCode())) {
            // 当类型为 解绑 --- 香港手机号是 校验验证码
            // 获取香港手机号
            String hkMobile = hkCustInfoOuterService.getMobile(hkCustNo);
            verifyCodeService.checkSmsVerifyCode(hkMobile, req.getVerifyCode(), VerifyCodeTypeEnum.UNBIND_HK_MOBILE_VERIFY_CODE.getMessageTempleteEnum());
        } else if (Objects.equals(req.getVerifyCodeType(), VerifyCodeTypeEnum.UNBIND_HK_EMAIL_VERIFY_CODE.getCode())) {
            // 当类型为 解绑 --- 邮箱时 校验验证码
            HkCustInfoPlaintextDTO custInfoPlaintext = hkCustInfoOuterService.getCustInfoPlaintext(hkCustNo);
            String email = custInfoPlaintext.getEmail();
            verifyCodeService.checkEmailVerifyCode(email, req.getVerifyCode(), VerifyCodeTypeEnum.UNBIND_HK_EMAIL_VERIFY_CODE.getMessageTempleteEnum());
        }
        if (Objects.isNull(hkCustInfo.getHboneNo())) {
            throw new BusinessException(ExceptionCodeEnum.HBONE_BIND_RELATION_NOT_EXIST);
        }
        // 当验证码类型为 解绑一账通号-好买手机号验证码
        if (req.getVerifyCodeType().equals(VerifyCodeTypeEnum.UNBIND_HBONE_MOBILE_VERIFY_CODE.getCode())) {
            // 删除并且新建缓存
            CacheServiceImpl.getInstance().remove(CacheKeyPrefix.UNBIND_VERIFY_CODE_KEY_PREFIX + hkCustNo);
            CacheServiceImpl.getInstance().put(CacheKeyPrefix.UNBIND_VERIFY_CODE_KEY_PREFIX + hkCustNo, hkCustInfo.getHboneNo());
            // 获取好买手机号
            String hbMobile = hkCustInfoOuterService.getHbMobile(hkCustNo);
            // 校验验证码
            verifyCodeService.checkSmsVerifyCode(hbMobile, req.getVerifyCode(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()));
            return getBindStatusVO(bindStatusVO, hkCustNo, hkCustInfo, hbMobile);
        }
        // 当验证码类型为 解绑一账通号-香港手机号验证码或者 解绑一账通邮箱验证码 并且缓存存在
        else if ((req.getVerifyCodeType().equals(VerifyCodeTypeEnum.UNBIND_HK_MOBILE_VERIFY_CODE.getCode()) || req.getVerifyCodeType().equals(VerifyCodeTypeEnum.UNBIND_HK_EMAIL_VERIFY_CODE.getCode())) &&
                CacheServiceImpl.getInstance().exists(CacheKeyPrefix.UNBIND_VERIFY_CODE_KEY_PREFIX + hkCustNo)) {
            try {
                hkHboneNoOuterService.unbindHkBindHbone(hkCustNo, hkCustInfo.getHboneNo());
                bindStatusVO.setBindStatus(BindStatusEnum.DONE.getCode());
                return bindStatusVO;
            } catch (Exception e) {
                log.error("解绑一账通号失败", e);
                throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
            }
        }
        return null;
    }

    /**
     * @description:(调解绑接口获取解绑状态)
     * @param bindStatusVO
     * @param hkCustNo 香港客户号
     * @param hkCustInfo 具体对象
     * @param hbMobile 香港手机号
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.BindStatusVO
     * @author: xufanchao
     * @date: 2023/12/26 09:24
     * @since JDK 1.8
     */
    private UnBindStatusVO getBindStatusVO(UnBindStatusVO bindStatusVO, String hkCustNo, HkCustInfoDTO hkCustInfo, String hbMobile) {
        // 当香港手机号和好买手机号相等时 直接调用香港账户中心的解绑接口
        if (hkCustInfo.getMobileDigest().equals(DigestUtil.digest(hbMobile))) {
            try {
                hkHboneNoOuterService.unbindHkBindHbone(hkCustNo, hkCustInfo.getHboneNo());
                bindStatusVO.setBindStatus(BindStatusEnum.DONE.getCode());
                return bindStatusVO;
            } catch (Exception e) {
                log.error("解绑一账通号失败", e);
                throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
            }
        } else{
            // 获取香港手机号
            String hkMobile = hkCustInfoOuterService.getMobile(hkCustNo);
            if (!Objects.isNull(hkMobile) && !hkMobile.equals(hbMobile) && hkCustInfo.getMobileAreaCode().equals(Constants.DEFAULT_MOBILE_AREA_CODE)) {
                bindStatusVO.setDescription(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_UNBIND_1.getDescription() + MaskUtil.maskMobile(hkMobile));
                bindStatusVO.setBindStatus(UnBindStatusEnum.UN_VERIFY_HK_MOBILE.getCode());
                bindStatusVO.setMobileDigest(DigestUtil.digest(hkMobile));

            }else {
                bindStatusVO.setDescription(ExceptionCodeEnum.HK_OPEN_ACCOUNT_EMAIL_UNBIND.getDescription() + hkCustInfo.getEmailMask());
                bindStatusVO.setBindStatus(UnBindStatusEnum.UN_VERIFY_HK_EMAIL.getCode());
                bindStatusVO.setEmailDigest(hkCustInfo.getEmailDigest());
            }
            return bindStatusVO;
        }
    }

    /**
     * @description:(注销账户)
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.CancelHboneNoVO
     * @author: xufanchao
     * @date: 2023/12/14 14:08
     * @since JDK 1.8
     */
    public CancelHboneNoVO cancelHkBindHbone() {
        CancelHboneNoVO vo = new CancelHboneNoVO();
        // 获取当前登陆的香港客户号
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(loginHkCustNo);
        // 当香港客户状态 == 开户/ 休眠时 不可以注销账户
        if (hkCustInfo.getCustState().equals(CustStateEnum.NORMAL.getKey()) || hkCustInfo.getCustState().equals(CustStateEnum.SLEEP.getKey())) {
            CancelHboneNoVO.WarnVo warnVo = new CancelHboneNoVO.WarnVo();
            warnVo.setTitle("提示");
            warnVo.setPhone(customerTel);
            warnVo.setNotice(ExceptionCodeEnum.NOT_ALLOW_SIGNOFF.getDescription());
            vo.setWarnVo(warnVo);
            return vo;
        }
        if (hkCustInfo.getCustState().equals(CustStateEnum.OPEN_ACCOUNT_SUCCESS.getKey())) {
            CancelHboneNoVO.WarnVo warnVo = new CancelHboneNoVO.WarnVo();
            warnVo.setTitle("注销失败");
            warnVo.setPhone(customerTel);
            warnVo.setNotice(ExceptionCodeEnum.NOT_ALLOW_SIGNOFF_1.getDescription());
            vo.setWarnVo(warnVo);
            return vo;
        }
        try {
            // 调用香港账户中心的注销接口
            hkHboneNoOuterService.cancelHkCustInfo(loginHkCustNo);
            return vo;
        }catch (BusinessException e) {
            log.error("注销账户失败", e);
            CancelHboneNoVO.WarnVo warnVo = new CancelHboneNoVO.WarnVo();
            if (e.getCode().equals("H5530046")) {
                warnVo.setTitle("提示");
                warnVo.setNotice("无法注销,您的开户资料审核中，为确保您的账户安全，销户须进行身份验证，详情请咨询您的专属服务人员或拨打客服电话");
                warnVo.setPhone(customerTel);
                vo.setWarnVo(warnVo);
                vo.setReturnCode(ExceptionCodeEnum.SYSTEM_ERROR.getCode());
            } else {
                warnVo.setTitle("提示");
                warnVo.setNotice("无法注销,详情请咨询您的专属服务人员或拨打客服电话");
                warnVo.setPhone(customerTel);
                vo.setWarnVo(warnVo);
                vo.setReturnCode(ExceptionCodeEnum.SYSTEM_ERROR.getCode());
            }
            return vo;
        }
    }


    /**
     * @param request      绑定传入的请求实体
     * @param custInfo 香港客户信息实体
     * @return boolean
     * @description:(比较传入的手机号以及证件号 和香港客户信息中的是否一致)
     * @author: xufanchao
     * @date: 2023/12/11 16:38
     * @since JDK 1.8
     */
    public boolean checkMobileAndIdNo(HboneInfoRequest request, CustInfoVO custInfo) {
        if (DigestUtil.digest(request.getHbMobile()).equals(custInfo.getMobileDigest()) || DigestUtil.digest(request.getHbIdNo()).equals(custInfo.getIdNoDigest())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param hkCustInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HboneNoInfoVO
     * @description:(数据格式转换)
     * @author: xufanchao
     * @date: 2024/1/9 11:28
     * @since JDK 1.8
     */
    public HboneNoInfoVO transDTOToVO(HkCustInfoDTO hkCustInfoDTO) {
        HboneNoInfoVO hboneNoInfoVO = new HboneNoInfoVO();
        hboneNoInfoVO.setCustName(hkCustInfoDTO.getCustName());
        hboneNoInfoVO.setHboneBindStatus(hkCustInfoDTO.getHboneBindStatus());
        hboneNoInfoVO.setEmailMask(hkCustInfoDTO.getEmailMask());
        hboneNoInfoVO.setEmailDigest(hkCustInfoDTO.getEmailDigest());
        hboneNoInfoVO.setHbCustName(hkCustInfoDTO.getHbCustName());
        hboneNoInfoVO.setHbIdType(hkCustInfoDTO.getHbIdType());
        hboneNoInfoVO.setHbIdTypeDesc(hkCustInfoDTO.getHbIdTypeDesc());
        hboneNoInfoVO.setHbIdNoMask(hkCustInfoDTO.getHbIdNoMask());
        hboneNoInfoVO.setHbIdNoDigest(hkCustInfoDTO.getHbIdNoDigest());
        hboneNoInfoVO.setHbMobileMask(hkCustInfoDTO.getHbMobileMask());
        hboneNoInfoVO.setHbMobileDigest(hkCustInfoDTO.getHbMobileDigest());
        hboneNoInfoVO.setIdNoMask(hkCustInfoDTO.getIdNoMask());
        hboneNoInfoVO.setIdNoDigest(hkCustInfoDTO.getIdNoDigest());
        hboneNoInfoVO.setMobileMask(hkCustInfoDTO.getMobileMask());
        hboneNoInfoVO.setMobileDigest(hkCustInfoDTO.getMobileDigest());
        return hboneNoInfoVO;
    }


}