/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.extservice.request.hkfund.HkRevokeFundOrderRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HkRevokeFundVerRequest;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/5/10 15:39
 * @since JDK 1.8
 */
@Service
public class RevokeFundService {

    @Resource
    private HkFundOuterService hkFundOuterService;

    public void verification(HkRevokeFundVerRequest request) {
        hkFundOuterService.revokeFundVerification(request.getHkCustNo(), request.getOrderNo(), request.getOrderType());
    }

    /**
     * @param request
     * @return void
     * @description: 撤销订单
     * @author: jinqing.rao
     * @date: 2024/5/10 15:49
     * @since JDK 1.8
     */
    public void redeemFundOrder(HkRevokeFundOrderRequest request) {
        try {
            hkFundOuterService.redeemFundOrder(request.getHkCustNo(), "1", request.getDealNo(), PassWordUtil.encrypt(request.getTxPassword()), request.getHbSceneId());
        } catch (BusinessException e) {
            if (OutReturnCodes.HW_FUND_TRADE_PASSWORD_ERROR_CODE.equals(e.getCode())) {
                throw new BusinessException(e.getCode(), "交易密码错误，请重新输入");
            } else {
                throw new BusinessException(ExceptionCodeEnum.HW_REDEEM_FUND_ORDER_ERROR);
            }
        } catch (Exception e) {
            // 拦截统一提示,撤单失败
            throw new BusinessException(ExceptionCodeEnum.HW_REDEEM_FUND_ORDER_ERROR);
        }
    }
}
