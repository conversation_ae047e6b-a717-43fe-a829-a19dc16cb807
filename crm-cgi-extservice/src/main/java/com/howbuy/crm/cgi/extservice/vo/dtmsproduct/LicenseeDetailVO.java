package com.howbuy.crm.cgi.extservice.vo.dtmsproduct;

/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.DtmsBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 获取持牌人信息
 * @date 2023/11/30 16:11
 * @since JDK 1.8
 */
@Setter
@Getter
public class LicenseeDetailVO extends DtmsBaseVO implements Serializable {

    private static final long serialVersionUID = 7204896022476323630L;

    /**
     * 持牌人姓名
     */
    private String licenseeName;

    /**
     * 持牌人执政编号
     */
    private String licenseeCode;

    /**
     * 持牌人照片url
     */
    private String licenseeImageUrl;

    /**
     * 持牌人录音url
     */
    private String licenseeAudioUrl;

    /**
     * 持牌人录音时长
     */
    private String licenseeAudioTime;


    /**
     * 风险披露列表
     */
    private List<String> riskDiscloseList;


    /**
     * 音频播放状态 0-未播放完成；1-已播放完成
     */
    private String audioPlayStatus;

    /**
     * 剩余时间 0-未播放完成时有值
     */
    private String remainderTime;

}
