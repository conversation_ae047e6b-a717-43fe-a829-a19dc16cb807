/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 客户的风险等级小于基金的风险等级
 * @date 2024/4/24 16:54
 * @since JDK 1.8
 */
public class CustRiskLevelLessThanFundRiskLevelValidator implements PiggyValidator<PiggyBankVerificationVO> {

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;
    /**
     * 基金信息
     */
    private final FundBasicInfoDTO fundBasicInfoDTO;

    /**
     * 是否弹框展示 1 是 0 否
     */
    private final String showVerify;

    public CustRiskLevelLessThanFundRiskLevelValidator(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO, String showVerify) {
        this.fundBasicInfoDTO = fundBasicInfoDTO;
        this.hkCustInfo = hkCustInfo;
        this.showVerify = showVerify;
    }

    /**
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO
     * @description: 客户分险等级小于基金配置的分险等级, 返回客户分险等级和基金配置的分险等级
     * showVerify 表示前端是否需要进行强拦截 1 是 0否
     * @author: jinqing.rao
     * @date: 2024/8/19 14:27
     * @since JDK 1.8
     */
    @Override
    public PiggyBankVerificationVO verification() {
        if (StringUtils.isBlank(hkCustInfo.getRiskToleranceLevel())) {
            throw new BusinessException(ExceptionCodeEnum.CUST_RISK_LEVEL_ERROR);
        }
        if (StringUtils.isNotBlank(fundBasicInfoDTO.getFundRiskLevel())) {
            if (fundBasicInfoDTO.getFundRiskLevel().compareTo(hkCustInfo.getRiskToleranceLevel()) > 0) {
                PiggyBankVerificationVO fundVerificationVO = new PiggyBankVerificationVO();
                fundVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.RISK_LEVEL_NOT_MATCH.getCode());
                fundVerificationVO.setCustRiskLevel(hkCustInfo.getRiskToleranceLevel());
                fundVerificationVO.setFundRiskLevel(fundBasicInfoDTO.getFundRiskLevel());
                fundVerificationVO.setShowVerify(showVerify);
                return fundVerificationVO;
            }
        }
        return null;
    }
}
