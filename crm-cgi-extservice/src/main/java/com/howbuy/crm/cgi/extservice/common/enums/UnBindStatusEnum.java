package com.howbuy.crm.cgi.extservice.common.enums;

/**
 * @description:(绑定状态枚举类)
 * @return
 * @author: xufanchao
 * @date: 2023/12/14 10:14
 * @since JDK 1.8
 */
public enum UnBindStatusEnum {

    /**
     * 失败
     */
    FAIL("00", "失败"),

    /**
     * 完成
     */
    DONE("01", "完成"),
    /**
     * 解绑好买手机号手机号
     */
    UN_VERIFY_HB_MOBILE("08", "验证好买手机号"),
    /**
     * 解绑香港手机号
     */
    UN_VERIFY_HK_MOBILE("07", "验证香港手机号"),
    /**
     * 验证香港邮箱
     */
    UN_VERIFY_HK_EMAIL("57", "验证香港邮箱"),
    ;
    private String code;
    private String desc;

    UnBindStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static UnBindStatusEnum getBindStatusEnum(String key) {
        for (UnBindStatusEnum bindStatusEnum : UnBindStatusEnum.values()) {
            if (bindStatusEnum.getCode().equals(key)) {
                return bindStatusEnum;
            }
        }
        return null;
    }
}
