package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 客户实名信息
 * @date 2023/6/6 10:25
 * @since JDK 1.8
 */
@Data
public class CustRealNameInfoVO extends AccountBaseVO {

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 客户名称
     */
    private String custName;

    /**
     * 客户英文名
     */
    private String custEnName;
    /**
     * 投资者类型0-机构,1-个人,2-产品户
     */
    private String invstType;

    /**
     * 证件类型
     * <p>
     * 大陆身份证-0
     * 香港身份证-D
     * 澳门身份证-E
     * 台湾身份证-F
     * 中国护照-1
     * 外国护照-6
     * 港澳通行证-4
     * 台胞证-A
     * 港澳台居民居住证-C
     * 其他证件-7
     */
    private String idType;

    /**
     * 证件类型描述
     */
    private String idTypeDesc;

    /**
     * 证件号码摘要
     */
    private String idNoDigest;

    /**
     * 证件号码掩码
     */
    private String idNoMask;

    /**
     * 证件有效期截止日 yyyy-MM-dd
     */
    private String idValidityEnd;

    /**
     * 证件是否长期有效0-否 1-是
     */
    private String idAlwaysValidFlag;

    /**
     * 证件图片上传状态0-未上传 1-已上传
     */
    private String idImageUploadStatus;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 国籍描述
     */
    private String nationalityDesc;

    /**
     * 出生日期 yyyy-MM-dd
     */
    private String birthday;

    /**
     * 性别 0-女，1-男
     */
    private String gender;

    /**
     * 现居地址描述
     */
    private String residentialAddressDesc;

    /**
     * 通信地址描述
     */
    private String mailingAddressDesc;

    /**
     * 就业状况描述
     */
    private String emplStatusDesc;

    /**
     * 就业状况
     */
    private String emplStatus;

    /**
     * 年收入
     */
    private String incLevel;

    /**
     * 年收入描述
     */
    private String incLevelDesc;
}
