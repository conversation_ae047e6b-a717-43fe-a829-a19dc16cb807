/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * @description: (文件信息类型枚举)
 * <AUTHOR>
 * @date 2023/12/14 17:30
 * @since JDK 1.8
 */
public enum HkOpenFileTypeEnum {
    /**
     * 香港开户表
     */
    HK_OPENING_FORM("001", "香港开户表"),
    /**
     * 风险问卷
     */
    RISK_QUESTIONNAIRE("002", "风测问卷"),
    /**
     * 美国税务表
     */
    US_TAX_FORM("003", "美国税务表"),
    /**
     * 个人专业投资者通知
     */
    INDIVIDUAL_QUALIFIED_INVESTOR_NOTICE("004", "个人专业投资者通知"),
    /**
     * 个人专业投资者声明
     */
    INDIVIDUAL_QUALIFIED_INVESTOR_DECLARATION("005", "个人专业投资者声明"),
    /**
     * 证件照片
     */
    ID_PHOTO("101", "证件照片"),
    /**
     * 现居地址证明
     */
    CURRENT_RESIDENCE_ADDRESS_PROOF("102", "现居地址证明"),
    /**
     * 通讯地址证明
     */
    MAILING_ADDRESS_PROOF("103", "通讯地址证明"),
    /**
     * 雇主书面同意书-声明文件
     */
    EMPLOYER_WRITTEN_CONSENT_DECLARATION("104", "雇主书面同意书-声明文件"),
    /**
     * 银行账户图片
     */
    BANK_ACCOUNT_PHOTO("105", "银行账户图片"),
    /**
     * 联名账户补充材料
     */
    JOINT_ACCOUNT_ADDITIONAL_MATERIALS("106", "联名账户补充材料"),
    /**
     * 资产证明文件
     */
    ASSET_PROOF_DOCUMENT("107", "资产证明文件"),
    /**
     * 电子签名图片
     */
    ELECTRONIC_SIGNATURE_PHOTO("108", "电子签名图片"),
    /**
     * 用户条款文件
     */
    USER_TERMS_AND_CONDITIONS("109", "用户条款文件"),
    /**
     * 警告声明文件
     */
    WARNING_DECLARATION("110", "警告声明文件");

    private  String code;
    private  String description;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    HkOpenFileTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}