/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.workbench;

import com.howbuy.crm.cgi.extservice.common.enums.portrait.PortraitCustStateEnum;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitWorkbenchRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.common.PortraitCustCommonService;
import com.howbuy.crm.cgi.extservice.vo.portrait.workbench.PortraitWorkbenchVO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.workbench.PortraitWorkBenchOuterService;
import com.howbuy.crm.portrait.client.domain.dto.workbench.PortraitWorkBenchDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户画像-T@T工作台
 * @Date 2024/9/10 14:02
 */
@Slf4j
@Service
public class PortraitWorkBenchService {

    @Resource
    private PortraitWorkBenchOuterService portraitWorkBenchOuterService;

    @Resource
    private PortraitCustCommonService portraitCustCommonService;

    /**
     * 获取工作台首页数据
     */
    public PortraitWorkbenchVO getWorkbenchHome(PortraitWorkbenchRequest request) {
        PortraitWorkBenchDTO workBenchDTO = portraitWorkBenchOuterService.getWorkbenchHome(request.getUserId());
        if (null == workBenchDTO) {
            log.info("查询T@T工作台数据失败！ userId={}", request.getUserId());
            return null;
        }

        PortraitWorkbenchVO vo = new PortraitWorkbenchVO();
        vo.setTodoTaskList(convertTodoTaskList(workBenchDTO.getTodoTaskList()));
        vo.setToolBoxInfo(convertToolBoxInfo(workBenchDTO.getToolBoxInfo()));
        return vo;
    }

    /**
     * 转换待办任务列表
     */
    private List<PortraitWorkbenchVO.TodoTask> convertTodoTaskList(List<PortraitWorkBenchDTO.TodoTask> dtoTaskList) {
        if (dtoTaskList == null) {
            return Collections.emptyList();
        }

        List<PortraitWorkbenchVO.TodoTask> todoTaskList = new ArrayList<>();
        for (PortraitWorkBenchDTO.TodoTask dtoTask : dtoTaskList) {
            PortraitWorkbenchVO.TodoTask voTask = new PortraitWorkbenchVO.TodoTask();
            voTask.setTaskId(dtoTask.getTaskId());
            voTask.setTaskType(dtoTask.getTaskType());
            voTask.setDataId(dtoTask.getDataId());
            voTask.setCardName(dtoTask.getCardName());
            voTask.setTaskName(dtoTask.getTaskName());
            voTask.setTaskLink(dtoTask.getTaskLink());
            voTask.setTaskDesc(dtoTask.getTaskDesc());
            voTask.setTaskImg(dtoTask.getTaskImg());
            voTask.setDataType(dtoTask.getDataType());
            voTask.setTaskTime(dtoTask.getTaskTime());
            voTask.setMaterialSendType(dtoTask.getMaterialSendType());
            voTask.setMatchCustList(convertMatchCustomerList(dtoTask.getMatchCustList()));
            todoTaskList.add(voTask);
        }
        return todoTaskList;
    }

    /**
     * 转换适用客户列表
     */
    private List<PortraitWorkbenchVO.MatchCustomer> convertMatchCustomerList(List<PortraitWorkBenchDTO.MatchCustomer> dtoCustomerList) {
        if (dtoCustomerList == null) {
            return Collections.emptyList();
        }

        List<PortraitWorkbenchVO.MatchCustomer> matchCustList = new ArrayList<>();
        for (PortraitWorkBenchDTO.MatchCustomer dtoCustomer : dtoCustomerList) {
            PortraitWorkbenchVO.MatchCustomer voCustomer = new PortraitWorkbenchVO.MatchCustomer();
            voCustomer.setHboneNo(dtoCustomer.getHboneNo());
            String custName= portraitCustCommonService.getCustName(dtoCustomer.getHboneNo());
            voCustomer.setCustName(custName);
            String labelValue=dtoCustomer.getLabel();
            String labelCode = StringUtils.isNotEmpty(labelValue) ? PortraitCustStateEnum.getCodeByDesc(labelValue) : null;
            voCustomer.setLabel(labelCode);
            voCustomer.setMobileMask(dtoCustomer.getMobileMask());
            matchCustList.add(voCustomer);
        }
        return matchCustList;
    }




    /**
     * 转换工具箱信息
     */
    private PortraitWorkbenchVO.ToolBoxInfo convertToolBoxInfo(PortraitWorkBenchDTO.ToolBoxInfo dtoToolBoxInfo) {
        if (dtoToolBoxInfo == null) {
            return null;
        }

        PortraitWorkbenchVO.ToolBoxInfo voToolBoxInfo = new PortraitWorkbenchVO.ToolBoxInfo();
        voToolBoxInfo.setToolBoxTitle(dtoToolBoxInfo.getToolBoxTitle());
        voToolBoxInfo.setToolBoxs(convertToolBoxList(dtoToolBoxInfo.getToolBoxs()));
        return voToolBoxInfo;
    }

    /**
     * 转换工具箱列表
     */
    private List<PortraitWorkbenchVO.ToolBox> convertToolBoxList(List<PortraitWorkBenchDTO.ToolBox> dtoToolBoxList) {
        if (dtoToolBoxList == null) {
            return Collections.emptyList();
        }

        List<PortraitWorkbenchVO.ToolBox> toolBoxList = new ArrayList<>();
        for (PortraitWorkBenchDTO.ToolBox dtoToolBox : dtoToolBoxList) {
            PortraitWorkbenchVO.ToolBox voToolBox = new PortraitWorkbenchVO.ToolBox();
            voToolBox.setTitle(dtoToolBox.getTitle());
            voToolBox.setTools(convertToolList(dtoToolBox.getTools()));
            toolBoxList.add(voToolBox);
        }
        return toolBoxList;
    }

    /**
     * 转换工具列表
     */
    private List<PortraitWorkbenchVO.Tool> convertToolList(List<PortraitWorkBenchDTO.Tool> dtoToolList) {
        if (dtoToolList == null) {
            return Collections.emptyList();
        }

        List<PortraitWorkbenchVO.Tool> toolList = new ArrayList<>();
        for (PortraitWorkBenchDTO.Tool dtoTool : dtoToolList) {
            PortraitWorkbenchVO.Tool voTool = new PortraitWorkbenchVO.Tool();
            voTool.setTitle(dtoTool.getTitle());
            voTool.setIcon(dtoTool.getIcon());
            voTool.setLink(dtoTool.getLink());
            voTool.setIconCode(dtoTool.getIconCode());
            toolList.add(voTool);
        }
        return toolList;
    }
}
