/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.portrait.recommend;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 客户画像-更新投后报告小红点响应
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Getter
@Setter
@ToString
public class PortraitRecommendUpdateRedpointVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 更新结果
     * 1-成功，0-失败
     */
    private String result;
} 