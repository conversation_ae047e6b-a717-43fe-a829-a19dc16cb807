/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import lombok.Data;


/**
 * @description: (交易记录的列表VO)
 * <AUTHOR>
 * @date 2024/2/22 18:42
 * @since JDK 1.8
 */
@Data
public class TradeRecordInfoVO {

    /** 订单号 */
    private String dealNo;

    /** 交易类型 */
    private String tradeType;

    /**
     * 子交易类型
     */
    private String subTradeType;

    /** 交易类型描述 */
    private String tradeTypeDesc;

    /** 交易方向 ("+", "-") */
    private String tradeDirection;

    /** 交易状态 (1-交易成功、2-交易失败、3-部分成功、4-确认中、5-付款中、6-等待付款、7-平衡中、8-已撤单、9-强制撤单) */
    private String tradeStatus;

    /** 交易状态文字颜色 (ff8800 、ff8800、f14a51、) */
    private String tradeStatusColor;

    /** 月份时间 */
    private String appDate;

    /** 产品名称 */
    private String productName;

    /** 预计确认时间 */
    private String appTime;

    /** 数量 金额/份额 的单位值数据 */
    private String num;

    /** 单位 (当前记录类型的单位展示 份额/金额/比例/克数) */
    private String unit;

    /** 交易类型展示 枚举值 0，1，2，3 【style.tb_gray, style.tb_red, style.tb_green, style.tb_blue】  */
    private String typeIcon;

    /** 私募标签 */
    private String specialTag;

    /** 币种 */
    private String currency;

    /**
     * 转入产品名称
     */
    private String transferProductName;


    /** 是否可以跳转到详情页 0-否、1-是
     */
    private String canGoToDetail;

}