/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.todotask;

import com.howbuy.crm.cgi.extservice.request.portrait.PortraitTaskIgnoreRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.workbench.PortraitTaskIgnoreVO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.todotask.PortraitTodotaskOuterService;
import com.howbuy.crm.portrait.client.domain.dto.common.OptResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 客户画像-待办任务-service
 * @Date 2025年2月28日 17:40:23
 */
@Slf4j
@Service
public class PortraitTodotaskService {

    @Resource
    private PortraitTodotaskOuterService portraitTodotaskOuterService;

    /**
     * 工作台待办任务忽略操作
     *
     * @param request   req
     * @return  PortraitServiceGuideVO
     */
    public PortraitTaskIgnoreVO executeIgnoreTask(PortraitTaskIgnoreRequest  request) {
        String taskId=request.getTaskId();
        String consCode = request.getUserId();
        OptResultDTO optDto= portraitTodotaskOuterService.executeIgnoreTask(taskId, consCode);
        PortraitTaskIgnoreVO portraitTaskIgnoreVO=new PortraitTaskIgnoreVO();
        return portraitTaskIgnoreVO;
    }

}
