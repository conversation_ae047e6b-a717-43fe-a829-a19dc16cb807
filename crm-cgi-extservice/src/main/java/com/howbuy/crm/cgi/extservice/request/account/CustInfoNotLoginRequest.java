/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/8/31 10:34
 * @since JDK 1.8
 */
@Data
public class CustInfoNotLoginRequest extends AccountBaseRequest {
    /**
     * 手机号  优先级1 手机号
     */
    private String mobile;

    /**
     * 手机区号  优先级1 一期不用该字段
     */
    private String mobileAreaCode;

    /**
     * 邮箱 优先级2 邮箱
     */
    private String email;

}
