/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.howbuy.crm.portrait.client.enums.LabelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 标签工具类
 * @date 2024/9/11 13:33
 * @since JDK 1.8
 */
@Slf4j
public class LabelUtils {

    /**
     * 天后缀
     */
    private static final String DAY_SUFFIX = "天";


    /**
     * 基础信息标签ID列表
     * 姓名、性别、客户状态、年龄、现居地址、最新风险等级、投资经验、最近沟通Json、距离上次沟通天数Json
     */
    public static final List<String> BASE_INFO_LABEL_LIST = Lists.newArrayList(LabelEnum.LABEL_010101.getId(),
            LabelEnum.LABEL_010104.getId(), LabelEnum.LABEL_020108.getId(), LabelEnum.LABEL_010102.getId(),
            LabelEnum.LABEL_010107.getId(), LabelEnum.LABEL_020115.getId(), LabelEnum.LABEL_030105.getId(),
            LabelEnum.LABEL_02030101.getId(), LabelEnum.LABEL_02030301.getId(), LabelEnum.LABEL_020107.getId(),
            LabelEnum.LABEL_050101.getId());

    /**
     * @param jsonStr
     * @param consCode
     * @return java.lang.String
     * @description:根据投顾编号、json字符串获取标签值
     * <AUTHOR>
     * @date 2024/9/11 13:01
     * @since JDK 1.8
     */
    public static String getConsCodeValueByJson(String jsonStr, String consCode) {
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        String value = null;
        try {
            List<Map<String, String>> list = JSON.parseObject(jsonStr, List.class);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Map<String, String> map : list) {
                    if (map.containsKey(consCode)) {
                        value = map.get(consCode);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("标签值解析失败", Throwables.getStackTraceAsString(e));
        }
        return value;
    }

    /**
     * @param dayStr
     * @return java.lang.Integer
     * @description:获取天数
     * <AUTHOR>
     * @date 2024/9/11 13:05
     * @since JDK 1.8
     */
    public static Integer getDayInt(String dayStr) {
        if (StringUtils.isEmpty(dayStr) || dayStr.indexOf(DAY_SUFFIX) == -1) {
            return null;
        }
        int index = dayStr.indexOf(DAY_SUFFIX);
        String dayInt = dayStr.substring(0, index);
        return Integer.parseInt(dayInt);
    }

    /**
     * @param dateStr yyyy-MM-dd HH:mm:ss
     * @return java.lang.String
     * @description:获取日期字符串截取
     * <AUTHOR>
     * @date 2024/9/11 13:16
     * @since JDK 1.8
     */
    public static String getDateSubstring(String dateStr) {
        if (StringUtils.isEmpty(dateStr) || dateStr.length() != 19) {
            return null;
        }
        // yyyy-MM-dd HH:mm
        return dateStr.substring(0, dateStr.length() - 3);
    }

}
