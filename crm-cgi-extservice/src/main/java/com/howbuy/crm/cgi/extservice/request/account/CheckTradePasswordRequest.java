package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 校验交易密码
 * <AUTHOR>
 * @date 2024/4/9 15:43
 * @since JDK 1.8
 */
@Setter
@Getter
public class CheckTradePasswordRequest extends AccountBaseRequest {

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 交易密码
     */
    private String txPassword;

}
