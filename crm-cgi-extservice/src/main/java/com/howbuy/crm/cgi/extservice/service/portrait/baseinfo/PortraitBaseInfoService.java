/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.baseinfo;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.portrait.LabelShowTabEnum;
import com.howbuy.crm.cgi.extservice.common.enums.portrait.PortraitCustStateEnum;
import com.howbuy.crm.cgi.extservice.common.enums.portrait.PortraitGenderEnum;
import com.howbuy.crm.cgi.extservice.common.utils.LabelUtils;
import com.howbuy.crm.cgi.extservice.convert.portrait.PortraitBaseInfoConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBaseRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.comm.PortraitCommRecordService;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BaseAttributeVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BusinessInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustBaseInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustPositionVO;
import com.howbuy.crm.cgi.manager.domain.acccenter.QueryAccHboneInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.acccenter.AccCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConscustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmwechat.WechatCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.DsLabelValueDTO;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.QueryCustLabelOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.label.PortraitQueryLabelOuterService;
import com.howbuy.crm.portrait.client.domain.dto.label.BaseAttributeDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.BusinessInfoDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.CustPositionDTO;
import com.howbuy.crm.portrait.client.enums.LabelEnum;
import com.howbuy.crm.portrait.client.enums.PortraitCustLevelEnum;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/10 11:23
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitBaseInfoService {

    @Resource
    private QueryCustLabelOuterService queryCustLabelOuterService;

    @Resource
    private WechatCustInfoOuterService wechatCustInfoOuterService;

    @Resource
    private ConscustInfoOuterService conscustInfoOuterService;

    @Resource
    private PortraitQueryLabelOuterService portraitQueryLabelOuterService;

    @Resource
    private AccCommonOuterService accCommonOuterService;

    @Resource
    private PortraitCommRecordService portraitCommRecordService;




    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustBaseInfoVO
     * @description:查询基本信息
     * <AUTHOR>
     * @date 2024/9/10 11:24
     * @since JDK 1.8
     */
    public CustBaseInfoVO queryBaseInfo(PortraitBaseRequest request) {
        CustBaseInfoVO custBaseInfoVO = new CustBaseInfoVO();
        // 查询客户标签
        List<DsLabelValueDTO> dsLabelValueDTOList = queryCustLabelOuterService.queryCustMultiLabel(request.getHboneNo(), LabelUtils.BASE_INFO_LABEL_LIST);
        Map<String, String> labelValueMap = dsLabelValueDTOList.stream()
                .collect(Collectors.toMap(label -> label.getLabelId(), label -> label.getLabelValue(), (v1, v2) -> v2));

        // 设置custBaseInfoVO
        // 姓名
        String name = labelValueMap.get(LabelEnum.LABEL_010101.getId());
        custBaseInfoVO.setName(name);

        // 查询微信客户信息
        WechatCustInfoVO wechatCustInfoVO = wechatCustInfoOuterService.queryWechatCustInfo(null, request.getHboneNo());
        if(Objects.nonNull(wechatCustInfoVO)) {
            if(StringUtils.isEmpty(custBaseInfoVO.getName())) {
                custBaseInfoVO.setName(wechatCustInfoVO.getNickName());
            }
            custBaseInfoVO.setWechatAvatar(wechatCustInfoVO.getWechatAvatar());
        }

        // 性别
        String genderDesc = labelValueMap.get(LabelEnum.LABEL_010104.getId());
        custBaseInfoVO.setGenderEnumKey(StringUtils.isNotEmpty(genderDesc) ? PortraitGenderEnum.getCodeByDesc(genderDesc) : null);

        // 客户状态
        String custStateDesc = labelValueMap.get(LabelEnum.LABEL_020108.getId());
        custBaseInfoVO.setLabelCustStateEnumKey(StringUtils.isNotEmpty(custStateDesc) ? PortraitCustStateEnum.getCodeByDesc(custStateDesc) : null);
        custBaseInfoVO.setLabelCustStateDesc(StringUtils.isNotEmpty(custStateDesc) ? custStateDesc : null);

        // 年龄
        custBaseInfoVO.setAge(labelValueMap.get(LabelEnum.LABEL_010102.getId()));

        // 现居省份
        // 标签现居地址 省-市-县
        String residentialAddress = labelValueMap.get(LabelEnum.LABEL_010107.getId());
        if (StringUtils.isNotEmpty(residentialAddress)
                && residentialAddress.split(Constants.LABEL_ADDRESS_SPLIT).length == 3) {
            custBaseInfoVO.setResidenceProvince(residentialAddress.split(Constants.LABEL_ADDRESS_SPLIT)[0]);
        }

        // 风险等级描述
        custBaseInfoVO.setRiskLevel(labelValueMap.get(LabelEnum.LABEL_020115.getId()));

        // 投资经验描述
        if (StringUtils.isNotEmpty(labelValueMap.get(LabelEnum.LABEL_030105.getId()))) {
            custBaseInfoVO.setInvestmentExperienceDesc("投资经验" + labelValueMap.get(LabelEnum.LABEL_030105.getId()));
        }

        // 最近沟通信息
        String latestCommInfo = this.getLatestCommInfo(request.getHboneNo());
        custBaseInfoVO.setLatestCommInfo(latestCommInfo);

        // 默认展示tab
        custBaseInfoVO.setDefaultShowTab(getDefaultShowTab(labelValueMap));
        return custBaseInfoVO;
    }

    /**
     * 获取上次沟通时间（取画像&crm&企微 三个沟通记录最新一条）
     *
     * @param hboneNo 一账通号
     * @return String
     */
    private String getLatestCommInfo(String hboneNo) {
        // 获取上次沟通时间（取三个数据源最新一条） 格式：yyyyMMddHHmmss
        String lastCommTime = portraitCommRecordService.queryNewestCommRecord(hboneNo);
        if (StringUtils.isEmpty(lastCommTime)) {
            return null;
        }

        // 沟通时间 yyyy-MM-dd HH:mm:ss
        String commTime = DateUtil.format(lastCommTime, DateUtil.STR_PATTERN, DateUtil.DEFAULT_DATESFM);
        // 相隔天数
        int elapsedDay = DateUtil.elapsedDay(DateUtil.string2Date(commTime, DateUtil.DEFAULT_DATESFM), new Date());
        return elapsedDay > 0 ? StringUtils.join(commTime,"(", elapsedDay, "天前)") : commTime;
    }

    /**
     * @param consCode
     * @param newCommJson
     * @param lastCommDayJson
     * @return java.lang.String
     * @description:获取最近沟通信息
     * <AUTHOR>
     * @date 2024/10/9 19:52
     * @since JDK 1.8
     */
    private String getLatestCommInfo(String consCode, String newCommJson, String lastCommDayJson) {
        // 最近沟通
        String consCodeComm = LabelUtils.getConsCodeValueByJson(newCommJson, consCode);
        String newComm = LabelUtils.getDateSubstring(consCodeComm);
        // 距离上次沟通天数
        String consCodeLastCommDay = LabelUtils.getConsCodeValueByJson(lastCommDayJson, consCode);
        Integer dayInt = null;
        if (StringUtils.isNotEmpty(consCodeLastCommDay)) {
            dayInt = LabelUtils.getDayInt(consCodeLastCommDay);
        }
        // 最近沟通信息
        if (StringUtils.isNotEmpty(newComm)) {
            StringBuffer sb = new StringBuffer();
            sb.append(newComm);
            if (Objects.nonNull(dayInt) && dayInt > 0) {
                sb.append("(").append(consCodeLastCommDay).append("前)");
            }
            return sb.toString();
        }
        return null;
    }

    /**
     * @param labelValueMap
     * @return java.lang.String
     * @description:获取默认展示tab
     * <AUTHOR>
     * @date 2024/9/24 15:43
     * @since JDK 1.8
     */
    private String getDefaultShowTab(Map<String, String> labelValueMap) {
        // 客户生命周期
        String custLevel = labelValueMap.get(LabelEnum.LABEL_020107.getId());
        PortraitCustLevelEnum portraitCustLevelEnum = PortraitCustLevelEnum.getByValue(custLevel);
        // 客户生命周期非空 且 不等于潜客
        if (Objects.nonNull(portraitCustLevelEnum) && portraitCustLevelEnum != PortraitCustLevelEnum.QZ) {
            return LabelShowTabEnum.INVESTMENT.getCode();
        } else {
            // 客户生命周期为潜客 或者为空
            // 风险等级不为空null
            if (StringUtils.isNotEmpty(labelValueMap.get(LabelEnum.LABEL_020115.getId()))) {
                return LabelShowTabEnum.INVESTMENT.getCode();
            }
            // app最近访问时间
            String appLastVisitedTimeStr = labelValueMap.get(LabelEnum.LABEL_050101.getId());
            if (StringUtils.isNotEmpty(appLastVisitedTimeStr)) {
                // 计算与当前时间的相差天数
                Long daysBetween = DateUtils.computeDateDifferDay(appLastVisitedTimeStr, DateUtils.YYYY_MM_DD, LocalDate.now());
                // 相差天数<=10天
                if (Objects.nonNull(daysBetween) && daysBetween <= 10) {
                    return LabelShowTabEnum.BEHAVIOR.getCode();
                }
            }
            return LabelShowTabEnum.BASIC.getCode();
        }
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BaseAttributeVO
     * @description:查询基础属性
     * <AUTHOR>
     * @date 2024/9/29 15:48
     * @since JDK 1.8
     */
    public BaseAttributeVO queryBaseAttribute(PortraitBaseRequest request) {
        BaseAttributeDTO baseAttributeDTO = portraitQueryLabelOuterService.queryBaseAttribute(request.getHboneNo());
        if (null == baseAttributeDTO) {
            log.warn("查询客户基础属性数据为空！ 一账通号={}", request.getHboneNo());
            return new BaseAttributeVO();
        }

        // 领域对象转换
        return PortraitBaseInfoConvert.convertBaseAttributeVO(baseAttributeDTO);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustPositionVO
     * @description:查询客户定位信息
     * <AUTHOR>
     * @date 2024/10/11 9:07
     * @since JDK 1.8
     */
    public CustPositionVO queryCustPosition(PortraitBaseRequest request) {
        CustPositionDTO custPositionDTO = portraitQueryLabelOuterService.queryCustPosition(request.getHboneNo());
        if (null == custPositionDTO) {
            log.warn("查询客户定位数据为空！ 一账通号={}", request.getHboneNo());
            return new CustPositionVO();
        }
        CustPositionVO custPositionVO = PortraitBaseInfoConvert.convertCustPositionVO(custPositionDTO);
        // 陪伴天数处理
        custPositionVO.setAccompanyTime(handlerAccompanyTime(request.getHboneNo()));
        return custPositionVO;
    }

    /**
     * @param hboneNo
     * @return java.lang.String
     * @description:处理陪伴天数
     * <AUTHOR>
     * @date 2024/10/9 19:20
     * @since JDK 1.8
     */
    private String handlerAccompanyTime(String hboneNo) {
        // 查询一账通信息
        String hboneRegDt = null;
        QueryAccHboneInfoDTO queryAccHboneInfoDTO = accCommonOuterService.queryAccHboneInfo(hboneNo);
        if (Objects.nonNull(queryAccHboneInfoDTO)) {
            hboneRegDt = queryAccHboneInfoDTO.getRegDt();
        }
        // 根据一账通号查询投顾客户简单信息
        List<CmConsCustSimpleVO> consCustSimpleVOList =
                conscustInfoOuterService.queryCustconstantByHboneNo(hboneNo);
        String consCustRegDt = null;
        if (CollectionUtils.isNotEmpty(consCustSimpleVOList)) {
            consCustRegDt = consCustSimpleVOList.get(0).getRegDt();
        }
        if (StringUtils.isEmpty(hboneRegDt) && StringUtils.isEmpty(consCustRegDt)) {
            return null;
        }

        // 获取最小注册日期
        LocalDate minRegDate = getMinRegDate(hboneRegDt, consCustRegDt);
        if (Objects.isNull(minRegDate)) {
            log.error("处理陪伴天数失败！ hboneRegDt={}, consCustRegDt={}", hboneRegDt, consCustRegDt);
            return null;
        }
        // 计算天数=当前日期-最小注册日期+1
        long differenceDay = DateUtils.calculateDays(minRegDate, LocalDate.now()) + 1;
        // 转中文
        return DateUtils.convertDaysToChinese((int) differenceDay);
    }

    /**
     * @param hboneRegDt
     * @param consCustRegDt
     * @return java.time.LocalDate
     * @description:获取最小注册日期
     * <AUTHOR>
     * @date 2024/10/9 19:38
     * @since JDK 1.8
     */
    private LocalDate getMinRegDate(String hboneRegDt, String consCustRegDt) {
        if (StringUtils.isNotEmpty(hboneRegDt) && StringUtils.isNotEmpty(consCustRegDt)) {
            return hboneRegDt.compareTo(consCustRegDt) < 0 ? DateUtils.getLocalDate(hboneRegDt, DateUtils.YYYYMMDD) : DateUtils.getLocalDate(consCustRegDt, DateUtils.YYYYMMDD);
        } else if (StringUtils.isNotEmpty(hboneRegDt)) {
            return DateUtils.getLocalDate(hboneRegDt, DateUtils.YYYYMMDD);
        } else {
            return DateUtils.getLocalDate(consCustRegDt, DateUtils.YYYYMMDD);
        }
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BusinessInfoVO
     * @description:查询业务信息
     * <AUTHOR>
     * @date 2024/10/9 19:42
     * @since JDK 1.8
     */
    public BusinessInfoVO queryBusinessInfo(PortraitBaseRequest request) {
        BusinessInfoDTO businessInfoDTO = portraitQueryLabelOuterService.queryBusinessInfo(request.getHboneNo());
        if (null == businessInfoDTO) {
            log.warn("查询业务信息数据为空！ 一账通号={}", request.getHboneNo());
            return new BusinessInfoVO();
        }
        BusinessInfoVO businessInfoVO = PortraitBaseInfoConvert.convertBusinessInfoVO(businessInfoDTO);

        // 处理最新沟通相关信息
        // 根据一账通号查询投顾客户简单信息
        List<CmConsCustSimpleVO> consCustSimpleVOList =
                conscustInfoOuterService.queryCustconstantByHboneNo(request.getHboneNo());
        if (CollectionUtils.isNotEmpty(consCustSimpleVOList) &&
                StringUtils.isNotEmpty(consCustSimpleVOList.get(0).getConsCode())) {
            String consCode = consCustSimpleVOList.get(0).getConsCode();

            String latestCommInfo = getLatestCommInfo(consCode, businessInfoDTO.getLatestCommTime(), businessInfoDTO.getLastTimeCommDay());
            businessInfoVO.setLatestCommInfo(latestCommInfo);

            businessInfoVO.setCommCountSum(LabelUtils.getConsCodeValueByJson(businessInfoDTO.getCommCountSum(), consCode));
            businessInfoVO.setCommFrequency(LabelUtils.getConsCodeValueByJson(businessInfoDTO.getCommFrequency(), consCode));
            businessInfoVO.setWeekCommCount(LabelUtils.getConsCodeValueByJson(businessInfoDTO.getWeekCommCount(), consCode));
            businessInfoVO.setMonthCommCount(LabelUtils.getConsCodeValueByJson(businessInfoDTO.getMonthCommCount(), consCode));
            businessInfoVO.setQuarterCommCount(LabelUtils.getConsCodeValueByJson(businessInfoDTO.getQuarterCommCount(), consCode));
            businessInfoVO.setYearCommCount(LabelUtils.getConsCodeValueByJson(businessInfoDTO.getYearCommCount(), consCode));
        } else {
            log.error("根据一账通号查询投顾编号失败！ 一账通号={}", request.getHboneNo());
        }
        return businessInfoVO;
    }
}
