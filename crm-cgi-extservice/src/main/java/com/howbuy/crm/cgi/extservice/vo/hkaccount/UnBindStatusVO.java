/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;


/**
 * <AUTHOR>
 * @description: (绑定状态vo)
 * @date 2023/11/29 19:30
 * @since JDK 1.8
 */
@Data
public class UnBindStatusVO extends AccountBaseVO {

    /**
     * 绑定状态 01-完成 |03-重置登录密码短信验证码|
     * |04-交易账户激活短信验证码|
     * |05-登录账户激活短信验证码|
     * |06-绑定一账通香港手机号短信验证码|
     * |07-解绑一账通香港手机号短信验证码|
     * |08-解绑一账通好买手机号短信验证码|
     * |09-重置交易密码短信验证码|
     * |10-设置交易密码短信验证码|
     * |11-绑定一账通好买手机号短信验证码|
     * |12-修改手机号短信验证码|
     * |13-绑定手机号短信验证码|
     */
    private String bindStatus;

    /**
     * 手机摘要
     */
    private String mobileDigest;

    /**
     * 邮箱摘要
     */
    private String emailDigest;


}