package com.howbuy.crm.cgi.extservice.vo.portrait.workbench;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 工作台首页查询响应对象
 * <AUTHOR>
 * @date 2024-03-19 14:50:00
 */
@Getter
@Setter
public class PortraitWorkbenchVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 待办任务列表
     */
    private List<TodoTask> todoTaskList;

    /**
     * 工具箱信息
     */
    private ToolBoxInfo toolBoxInfo;

    /**
     * 待办任务
     */
    @Getter
    @Setter
    public static class TodoTask implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 任务id
         */
        private String taskId;

        /**
         * 任务卡片名称
         */
        private String cardName;

        /**
         * 待办任务名称
         */
        private String taskName;

        /**
         * 任务链接地址
         */
        private String taskLink;

        /**
         * 任务描述
         */
        private String taskDesc;

        /**
         * 任务图片
         */
        private String taskImg;

        /**
         * 任务客户类型（1-个人 0-全部）
         */
        private String dataType;

        /**
         * 任务生成时间
         */
        private String taskTime;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;

        /**
         * 适用客户列表
         */
        private List<MatchCustomer> matchCustList;

        /**
         * 待办任务类型:1-IPS报告、2-投后报告、3-素材上新任务
         */
        private String taskType;

        /**
         * 待办任务数据源Id
         */
        private String dataId;
    }

    /**
     * 适用客户
     */
    @Getter
    @Setter
    public static class MatchCustomer implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 客户一账通
         */
        private String hboneNo;

        /**
         * 客户姓名
         */
        private String custName;
        
        /**
         * 客户标签
         */
        private String label;
        
        /**
         * 手机号掩码
         */
        private String mobileMask;
    }


    /**
     * 工具箱信息
     */
    @Getter
    @Setter
    public static class ToolBoxInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 工具箱模块名称: 工具箱
         */
        private String toolBoxTitle;

        /**
         * 工具箱列表
         */
        private List<ToolBox> toolBoxs;
    }

    /**
     * 工具箱
     */
    @Getter
    @Setter
    public static class ToolBox implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 工具箱模块名称
         */
        private String title;

        /**
         * 工具列表
         */
        private List<Tool> tools;
    }

    /**
     * 工具
     */
    @Getter
    @Setter
    public static class Tool implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 工具名称
         */
        private String title;

        /**
         * 工具图标
         */
        private String icon;

        /**
         * 链接地址
         */
        private String link;

        /**
         * icon code
         */
        private String iconCode;
    }
} 