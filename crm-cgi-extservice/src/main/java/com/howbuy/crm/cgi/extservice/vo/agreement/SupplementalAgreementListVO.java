package com.howbuy.crm.cgi.extservice.vo.agreement;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 补签协议列表响应对象
 * @author: hongdong.xie
 * @date: 2024-02-24 10:00:00
 */
@Setter
@Getter
public class SupplementalAgreementListVO extends Body {
    
    /**
     * 协议列表
     */
    private List<FundAgreementVO> fundAgreementList;

    /**
     * @description: 基金协议信息
     * @author: hongdong.xie
     * @date: 2024-02-24 10:00:00
     */
    @Setter
    @Getter
    public static class FundAgreementVO {

        /**
         * 协议名称
         */
        private String fundAddr;

        /**
         * 基金Code
         */
        private String fundCode;
    }
}

