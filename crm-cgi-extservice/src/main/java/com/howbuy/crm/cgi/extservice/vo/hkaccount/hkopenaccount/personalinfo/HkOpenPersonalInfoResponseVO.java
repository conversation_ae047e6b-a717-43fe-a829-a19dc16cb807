package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.personalinfo;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 保存个人信息返回实体类
 * <AUTHOR>
 * @date 2024/1/10 10:29
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenPersonalInfoResponseVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 6805832248132200059L;

    /**
     * 校验信息或者审核不通过的信息,需要提示到具体字段
     */
    private List<HkOpenAcctCheckVO> checkResult;
}
