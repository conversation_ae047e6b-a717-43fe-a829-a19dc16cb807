/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkfund;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.hkfund.HwOverseaReportRequest;
import com.howbuy.crm.cgi.extservice.service.hkfund.HwOverseaReportService;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/28 13:34
 * @since JDK 1.8
 */
@RestController
@RequestMapping("oversea/market/observation/report")
public class HwOverseaReportController {


    @Resource
    private HwOverseaReportService hwOverseaReportService;


    /**
     * @api {POST} /ext/oversea/market/observation/report/list queryHwOverseaReport()
     * @apiVersion 1.0.0
     * @apiGroup HwOverseaReportController
     * @apiName queryHwOverseaReport()
     * @apiDescription 海外市场观察报告列表接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParamExample 请求体示例
     * {
     *   "hkCustNo": "6ZA"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.overseaReportList
     * @apiSuccess (响应结果) {String} data.overseaReportList.fileName 文件名称
     * @apiSuccess (响应结果) {String} data.overseaReportList.fileUrl 文件的URl
     * @apiSuccess (响应结果) {String} data.overseaReportList.fileType 文件类型 1PDF   2 图片
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "mfOv4uh9",
     *   "data": {
     *     "overseaReportList": {
     *       "fileName": "9W9Ug",
     *       "fileUrl": "ZyKgpnYS",
     *       "fileType": "dCM"
     *     }
     *   },
     *   "description": "1DH9ntarna",
     *   "timestampServer": "mofkGU2J"
     * }
     */
    @PostMapping("/list")
    public CgiResponse<HwOverseaReportListVO>   queryHwOverseaReport(@RequestBody HwOverseaReportRequest request) {
        return CgiResponse.ok(hwOverseaReportService.queryHwOverseaReport(request));
    }
}
