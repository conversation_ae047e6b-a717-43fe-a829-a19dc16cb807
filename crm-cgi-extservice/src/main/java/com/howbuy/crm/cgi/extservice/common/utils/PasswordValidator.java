package com.howbuy.crm.cgi.extservice.common.utils;

import com.google.common.base.Objects;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description: 密码校验
 * @date 2023/6/16 14:07
 * @since JDK 1.8
 */
public class PasswordValidator {

    /**
     * 6~20位非纯数字字符，且字符类型为数字或者字母或者符号
     */
    private static final String LOGIN_PASSWORD_PATTERN = "^(?![0-9]+$)[a-zA-Z0-9~!@#$%^&*()_+`\\-={}\\[\\]:;\"'<>,.?\\\\/]{6,20}$";

    /**
     * 6位不连续的数字
     */
    private static final String TX_PASSWORD_PATTERN = "^(?!012345|123456|234567|345678|456789|567890)(?!([0-9])\\1{5})[0-9]{6}$";

    private static final Pattern loginPattern = Pattern.compile(LOGIN_PASSWORD_PATTERN);

    private static final Pattern txPattern = Pattern.compile(TX_PASSWORD_PATTERN);

    private static final int birthdayIntercept = 2;


    /**
     * @description: 校验登录密码格式
     * @param: [password]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/16 15:33
     * @since JDK 1.8
     */
    public static void validateLoginPassword(String password) {
        if (!loginPattern.matcher(password).matches()) {
            throw new BusinessException(ExceptionCodeEnum.LOGIN_PASSWORD_FORMAT_ERROR);
        }
    }

    /**
     * @description:(校验交易密码格式)
     * @param password
     * @param birthday	YYYYMMDD --> YYMMDD
     * @return void
     * @author: shaoyang.li
     * @date: 2023/6/28 10:44
     * @since JDK 1.8
     */
    public static void validateTxPassword(String password, String birthday) {
        if (!txPattern.matcher(password).matches() || Objects.equal(password, StringUtils.substring(birthday, birthdayIntercept))) {
            throw new BusinessException(ExceptionCodeEnum.TRADE_PASSWORD_FORMAT_ERROR);
        }
    }

    /**
     * @description: 校验新旧密码是否一致
     * @param: [oldPwd, passwordSalt, newPassword]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/16 13:33
     * @since JDK 1.8
     */
    public static void validateOldNewPwdEqual(String oldPassword, String newPassword) {
        if (Objects.equal(oldPassword, newPassword)) {
            throw new BusinessException(ExceptionCodeEnum.NEW_OLD_PASSWORD_EQUAL);
        }
    }


//    public static void main(String[] args) {
//        PasswordValidator.validateTxPassword("190911", null);
//    }
}
