/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;
/**
 * @description: 业务中不同场景的枚举类型,可以通过枚举接口,获取到对应的枚举类型
 * <AUTHOR>
 * @date 2023/12/19 19:16
 * @since JDK 1.8
 */
public enum HkOpenAcctBizTypeEnum {

    /**
     * 1 : 开户职业信息页业务性质枚举
     */
    HK_OPEN_ACCT_BIZ_BUSINESS_TYPE("1","开户职业信息页业务性质枚举"),

    /**
     * 2 : 开户证件信息页,证件类型枚举
     */
    HK_OPEN_ACCT_BIZ_OPEN_ID_TYPE("2","开户证件信息页,证件类型枚举"),
    ;


    private String bizType;

    private String bizTypeDesc;

    HkOpenAcctBizTypeEnum(String bizType, String bizTypeDesc) {
        this.bizType = bizType;
        this.bizTypeDesc = bizTypeDesc;
    }
    public String getBizType() {
        return bizType;
    }
    public String getBizTypeDesc() {
        return bizTypeDesc;
    }
}

