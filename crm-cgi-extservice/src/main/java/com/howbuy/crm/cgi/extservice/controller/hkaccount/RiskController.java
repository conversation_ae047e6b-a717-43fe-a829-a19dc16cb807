package com.howbuy.crm.cgi.extservice.controller.hkaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.account.RiskQuestionRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountBaseRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.RiskService;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkCustFundHoldRiskLevelVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.RiskQuestionnaireVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.RiskToleranceLevelVO;
import com.howbuy.hkacconline.facade.common.HkAccBaseRequest;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.xml.ws.Response;

/**
 * <AUTHOR>
 * @description: 风险问卷
 * @date 2023/11/30 15:03
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/risk")
public class RiskController {

    @Resource
    private RiskService riskService;


    /**
     * @api {GET} /ext/hkaccount/risk/queryriskquestionnaire queryRiskQuestionnaireInfo()
     * @apiVersion 1.0.0
     * @apiGroup RiskController
     * @apiName queryRiskQuestionnaireInfo()
     * @apiDescription 步骤5：投资经验填写页-风险问卷查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} sourceType 来源类型 0是开户页面风险测评，1是个人中心的风险评测
     * @apiParamExample 请求参数示例
     * sourceType=7I9ODT
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.examId 问卷id
     * @apiSuccess (响应结果) {String} data.examType 问卷类型
     * @apiSuccess (响应结果) {String} data.age 客户账户中心年龄,或者用户年龄
     * @apiSuccess (响应结果) {Array} data.baseQuestions 基本信息问题列表
     * @apiSuccess (响应结果) {String} data.baseQuestions.questionId 题目id
     * @apiSuccess (响应结果) {String} data.baseQuestions.question 题目
     * @apiSuccess (响应结果) {String} data.baseQuestions.multipleOption 多选标识 1是 0否
     * @apiSuccess (响应结果) {String} data.baseQuestions.questionClassify 题目类别  暂定 A\B\C\D 基本信息、资产情况、投资经验、投资计划
     * @apiSuccess (响应结果) {String} data.baseQuestions.classifyName 题目类别名称
     * @apiSuccess (响应结果) {Array} data.baseQuestions.historyAnswer 历史缓存答案
     * @apiSuccess (响应结果) {String} data.baseQuestions.sortNum 排序
     * @apiSuccess (响应结果) {Array} data.baseQuestions.options 选项列表
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.optionId 选项id
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.optionChar 选项 例如 A ,B ,C,D
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.optionDesc 选项描述
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.defaultLevel 默认风险等级
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.limitType 限制类型(NONE-无，AGE-年龄)
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.limitValue 限制值（年龄-存最低年龄）
     * @apiSuccess (响应结果) {String} data.baseQuestions.options.score 分值
     * @apiSuccess (响应结果) {Array} data.assetsSituationQuestions 资产情况问题列表
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.questionId 题目id
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.question 题目
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.multipleOption 多选标识 1是 0否
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.questionClassify 题目类别  暂定 A\B\C\D 基本信息、资产情况、投资经验、投资计划
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.classifyName 题目类别名称
     * @apiSuccess (响应结果) {Array} data.assetsSituationQuestions.historyAnswer 历史缓存答案
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.sortNum 排序
     * @apiSuccess (响应结果) {Array} data.assetsSituationQuestions.options 选项列表
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.optionId 选项id
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.optionChar 选项 例如 A ,B ,C,D
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.optionDesc 选项描述
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.defaultLevel 默认风险等级
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.limitType 限制类型(NONE-无，AGE-年龄)
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.limitValue 限制值（年龄-存最低年龄）
     * @apiSuccess (响应结果) {String} data.assetsSituationQuestions.options.score 分值
     * @apiSuccess (响应结果) {Array} data.investmentExperienceQuestions 投资经验问题列表
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.questionId 题目id
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.question 题目
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.multipleOption 多选标识 1是 0否
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.questionClassify 题目类别  暂定 A\B\C\D 基本信息、资产情况、投资经验、投资计划
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.classifyName 题目类别名称
     * @apiSuccess (响应结果) {Array} data.investmentExperienceQuestions.historyAnswer 历史缓存答案
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.sortNum 排序
     * @apiSuccess (响应结果) {Array} data.investmentExperienceQuestions.options 选项列表
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.optionId 选项id
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.optionChar 选项 例如 A ,B ,C,D
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.optionDesc 选项描述
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.defaultLevel 默认风险等级
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.limitType 限制类型(NONE-无，AGE-年龄)
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.limitValue 限制值（年龄-存最低年龄）
     * @apiSuccess (响应结果) {String} data.investmentExperienceQuestions.options.score 分值
     * @apiSuccess (响应结果) {Array} data.investmentPlanQuestions 资产计划问题列表
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.questionId 题目id
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.question 题目
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.multipleOption 多选标识 1是 0否
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.questionClassify 题目类别  暂定 A\B\C\D 基本信息、资产情况、投资经验、投资计划
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.classifyName 题目类别名称
     * @apiSuccess (响应结果) {Array} data.investmentPlanQuestions.historyAnswer 历史缓存答案
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.sortNum 排序
     * @apiSuccess (响应结果) {Array} data.investmentPlanQuestions.options 选项列表
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.optionId 选项id
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.optionChar 选项 例如 A ,B ,C,D
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.optionDesc 选项描述
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.defaultLevel 默认风险等级
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.limitType 限制类型(NONE-无，AGE-年龄)
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.limitValue 限制值（年龄-存最低年龄）
     * @apiSuccess (响应结果) {String} data.investmentPlanQuestions.options.score 分值
     * @apiSuccess (响应结果) {String} timestampServer 耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"sAbN","data":{"assetsSituationQuestions":[{"multipleOption":"T","questionId":"NycBNek","question":"O","options":[{"optionDesc":"bAipF","limitValue":"KN2L","score":"s","defaultLevel":"LP8kU","optionId":"Ds2cFMt6","optionChar":"Hc5YG75cS","limitType":"q"}],"questionClassify":"Z3Ipb","sortNum":"9Njkk0","historyAnswer":["YBnzE"],"classifyName":"y5fkelUzgP"}],"investmentPlanQuestions":[{"multipleOption":"yZPHNoFRy","questionId":"EIq","question":"1sitGWvfX6","options":[{"optionDesc":"lxbbu1F","limitValue":"Xp","score":"Bou9WnaGE","defaultLevel":"n","optionId":"MY","optionChar":"eQLQkS","limitType":"GfDC2C"}],"questionClassify":"PwH","sortNum":"F0PhAtAxjm","historyAnswer":["tKnoC8"],"classifyName":"bnK"}],"examId":"X","examType":"y0FwOjyw","baseQuestions":[{"multipleOption":"fufe","questionId":"oOjCFLkdbR","question":"6TmADuR","options":[{"optionDesc":"Q","limitValue":"dzTHp2","score":"AzzRLpT","defaultLevel":"eW","optionId":"S6NtpUxs","optionChar":"7dMXS","limitType":"mSo7G"}],"questionClassify":"ruziP","sortNum":"vrX5zUx","historyAnswer":["d5QFqIFE6x"],"classifyName":"Zxt2GGc"}],"investmentExperienceQuestions":[{"multipleOption":"EabGYkH","questionId":"YC","question":"UqXi","options":[{"optionDesc":"6beqPGPHD6","limitValue":"zih","score":"jvps1QeY","defaultLevel":"ytylIUA3Z","optionId":"ecJ","optionChar":"v2sBjtI","limitType":"WQ"}],"questionClassify":"65uTcD4","sortNum":"7TqqLpmi0","historyAnswer":["GafV7R"],"classifyName":"k8Kv0p"}],"age":"NQPZ"},"description":"QvdUge","timestampServer":"6ui4J1TPL9"}
     */
    @GetMapping("queryriskquestionnaire")
    public CgiResponse<RiskQuestionnaireVO> queryRiskQuestionnaireInfo(@RequestParam("sourceType") String sourceType) {
        HkOpenAcctValidator.validatorStringType(sourceType);
        return CgiResponse.ok(riskService.queryRiskQuestionnaireInfo(sourceType));
    }


    /**
     * @api {POST} /ext/hkaccount/risk/queryassessmentrisk 风险测评试算接口
     * @apiVersion 1.0.0
     * @apiGroup RiskController
     * @apiName queryAssessmentRisk()
     * @apiDescription 风险测评试算接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} examId 问卷ID
     * @apiParam (请求体) {String} riskWarning 分险测试中的分险合适性选择 1校验 0不校验
     * @apiParam (请求体) {Array} answerDTOList 答案结果list
     * @apiParam (请求体) {String} answerDTOList.questionId 问卷ID
     * @apiParam (请求体) {Array} answerDTOList.optionChars 选项列表
     * @apiParamExample 请求体示例
     * {"answerDTOList":[{"questionId":"9QWruVOUc","optionChars":["MUrDU4xK02"]}],"examId":"g9Ns1"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 风险测评试算等级
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelDesc 风险测评试算等级描述
     * @apiSuccess (响应结果) {String} data.score 风险测评试算分数
     * @apiSuccess (响应结果) {String} data.assessmentTime 测算时间 日期格式：YYYY年MM月DD日
     * @apiSuccess (响应结果) {String} data.expirationDate 有效时间  日期格式：YYYY年MM月DD日
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"aKV","data":{"score":"ZvHKyN8yo","riskToleranceLevel":"r4W2pyf","riskToleranceLevelDesc":"vJ8uc"},"description":"LHBVHqRvnM","timestampServer":"DH"}
     */
    @PostMapping("queryassessmentrisk")
    public CgiResponse<RiskToleranceLevelVO> queryAssessmentRisk(@RequestBody RiskQuestionRequest request) {
        return CgiResponse.ok(riskService.queryAssessmentRisk(request));
    }


    /**
     * @api {POST} /ext/hkaccount/risk/saveassessmentrisk 风险测评提交接口
     * @apiVersion 1.0.0
     * @apiGroup RiskController
     * @apiName saveAssessmentRisk()
     * @apiDescription 风险测评提交接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} examId 问卷ID
     * @apiParam (请求体) {String} riskWarning 分险测试中的分险合适性选择 1校验 0不校验
     * @apiParam (请求体) {Array} answerDTOList 答案结果list
     * @apiParam (请求体) {String} answerDTOList.questionId 问卷ID
     * @apiParam (请求体) {Array} answerDTOList.optionChars 选项列表
     * @apiParamExample 请求体示例
     * {"answerDTOList":[{"questionId":"NlXJa","optionChars":["t115"]}],"examId":"Y00vE99FJ"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 风险测评试算等级
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelDesc 风险测评试算等级描述
     * @apiSuccess (响应结果) {String} data.score 风险测评试算分数
     * @apiSuccess (响应结果) {String} data.assessmentTime 测算时间 日期格式：YYYY年MM月DD日
     * @apiSuccess (响应结果) {String} data.expirationDate 有效时间  日期格式：YYYY年MM月DD日
     * @apiSuccess (响应结果) {String} timestampServer 业务耗时
     * @apiSuccessExample 响应结果示例
     * {"code":"TG","data":{"score":"OjBCgd","assessmentTime":"lMziOL12C8","riskToleranceLevel":"sRK","riskToleranceLevelDesc":"duJAnCRTf","expirationDate":"YasvdE9z8"},"description":"Gxyz","timestampServer":"PKxID"}
     */
    @PostMapping("saveassessmentrisk")
    public CgiResponse<RiskToleranceLevelVO> saveAssessmentRisk(@RequestBody RiskQuestionRequest request) {
        return CgiResponse.ok(riskService.saveAssessmentRisk(request));
    }

    /**
     * @api {GET} /ext/hkaccount/risk/risk/querycalculateresult 分险测算/提交结果查询接口
     * @apiVersion 1.0.0
     * @apiGroup RiskController
     * @apiName queryCalculateResult()
     * @apiDescription 分险测算/提交结果查询接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} sourceType 来源类型 0是开户页面风险测评，1是个人中心的风险评测
     * @apiParamExample 请求参数示例
     * sourceType=7
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.riskToleranceLevel 风险测评试算等级 风险承受能力 1-低风险等级 2-中低风险等级 3-中风险等级 4-中高风险等级 5-高风险等级
     * @apiSuccess (响应结果) {String} data.riskToleranceLevelDesc 风险测评试算等级描述
     * @apiSuccess (响应结果) {Number} data.score 风险测评试算分数
     * @apiSuccess (响应结果) {String} data.assessmentTime 测算时间 日期格式：yyyyMMdd
     * @apiSuccess (响应结果) {String} data.expirationDate 有效时间  日期格式：yyyyMMdd
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"TnLGdp","data":{"score":4598.************,"assessmentTime":"B","riskToleranceLevel":"A","riskToleranceLevelDesc":"sGW5A","expirationDate":"zHw8"},"description":"Kt9GXzyp1Q","timestampServer":"g8"}
     */
    @GetMapping("risk/querycalculateresult")
    public CgiResponse<RiskToleranceLevelVO> queryCalculateResult(@RequestParam("sourceType") String sourceType) {
        return CgiResponse.ok(riskService.queryCalculateResult(sourceType));
    }


    /**
     * @api {POST} /ext/hkaccount/risk/queryfundholdrisklevel queryFundHoldRiskLevel()
     * @apiVersion 1.0.0
     * @apiGroup RiskController
     * @apiName queryFundHoldRiskLevel()
     * @apiDescription 查询用户风险等级已经持仓的产品的风险等级
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo App 基础传参,
     * @apiParamExample 请求体示例
     * {"hkCustNo":"PnV"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custRiskLevel 客户的分险等级
     * @apiSuccess (响应结果) {Array} data.fundRiskLevelInfoList 客户持仓的基金风险等级
     * @apiSuccess (响应结果) {String} data.fundRiskLevelInfoList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.fundRiskLevelInfoList.fundRiskLevel 基金风险等级
     * @apiSuccess (响应结果) {String} data.fundRiskLevelInfoList.fundName 基金名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"kpA","data":{"fundRiskLevelInfoList":[{"fundRiskLevel":"84x79","fundCode":"OXJE7jT","fundName":"FmJS"}],"custRiskLevel":"klBZV"},"description":"OemyXvIMV","timestampServer":"M6"}
     */
    @PostMapping("/queryfundholdrisklevel")
    public CgiResponse<HkCustFundHoldRiskLevelVO> queryFundHoldRiskLevel(@RequestBody HkAppAccountBaseRequest request) {
        return CgiResponse.ok(riskService.queryFundHoldRiskLevel(request));
    }
}
