package com.howbuy.crm.cgi.extservice.common.enums;

public enum ExamCategoryEnum {
    // 0-kyc风测问卷
    //1-衍生知识
    KYC("0", "kyc风测问卷"),
    DERIVATIVE("1", "衍生知识");


    private String key;
    private String desc;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    ExamCategoryEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
