package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 交易方式
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum TradeModeEnum {

    ALL("0", "全部"),
    BUY("1", "购买"),
    REDEEM("2", "赎回");

    private final String key;
    private final String desc;

    public static TradeModeEnum getTradeModeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        TradeModeEnum tradeModeEnum = getTradeModeEnum(code);
        return tradeModeEnum == null ? null : tradeModeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    TradeModeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
