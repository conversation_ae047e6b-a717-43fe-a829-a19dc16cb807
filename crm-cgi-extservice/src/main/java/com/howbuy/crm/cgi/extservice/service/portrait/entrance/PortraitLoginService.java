/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.entrance;

import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.exception.SessionTimeOutException;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.session.PortraitSession;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConscustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConsultantInfoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/1/6 16:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitLoginService {

    private static final CacheService cacheService = CacheServiceImpl.getInstance();

    private static final Integer EXPIRE_TIME = 600;

    @Resource
    private ConscustInfoOuterService conscustInfoOuterService;

    @Resource
    private ConsultantInfoOuterService consultantInfoOuterService;

    /**
     * 客户画像入口投顾白名单客户列表
     */
    @Value("${portrait_conscode_whitelist}")
    private String portraitConscodeWhitelist;

    /**
     * @param userId
     * @param outletCode
     * @return void
     * @description:(创建会话)
     * <AUTHOR>
     * @date 2025/1/6 15:06
     * @since JDK 1.8
     */
    public void createSession(String userId, String outletCode) {
        HttpServletRequest request = RequestUtil.getHttpRequest();
        log.info("PortraitLoginService|createSession userId:{}, sessionId:{}", new Object[]{userId, request.getSession().getId()});

        PortraitSession portraitSession = getLoginSessionNoException(request);
        if (portraitSession == null) {
            portraitSession = new PortraitSession();
            portraitSession.setCoopId(outletCode);
        }
        if (StringUtils.isNotEmpty(userId)) {
            String custIp = WebUtil.getCustIP(request);
            portraitSession.setLoginIp(custIp);
            portraitSession.setUserId(userId);
            addLoginSession(request, portraitSession);
        }
    }

    /**
     * @param
     * @return void
     * @description:(保存匹配关系)
     * <AUTHOR>
     * @date 2025/1/6 17:09
     * @since JDK 1.8
     */
    private void saveConsRelCache(String userId, String hboneNo) {
        log.info("PortraitLoginService|saveConsRelRedis userId:{}, hboneNo:{}", new Object[]{userId, hboneNo});
        cacheService.put(EXPIRE_TIME, getConsRelKey(userId, hboneNo), "");
    }

    /**
     * @param userId
     * @param hboneNo
     * @return void
     * @description:(是否存在匹配关系缓存)
     * <AUTHOR>
     * @date 2025/1/7 10:35
     * @since JDK 1.8
     */
    public boolean existsConsRelCache(String userId, String hboneNo) {
        boolean result = cacheService.exists(getConsRelKey(userId, hboneNo));
        log.info("PortraitLoginService|existsConsRelCache userId:{}, hboneNo:{}, result：{}", new Object[]{userId, hboneNo, result});
        return result;
    }

    /**
     * @param userId
     * @param hboneNo
     * @return java.lang.String
     * @description:(获取匹配关系key)
     * <AUTHOR>
     * @date 2025/1/7 10:30
     * @since JDK 1.8
     */
    private String getConsRelKey(String userId, String hboneNo) {
        String cacheKeyPrefix = CacheKeyPrefix.PORTRAIT_HBONE_NO_USER_ID_MATCH_KEY_PREFIX;
        return cacheKeyPrefix + userId + MarkConstants.SEPARATOR_MIDDLE + hboneNo;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.common.session.PortraitSession
     * @description:(获取登录会话)
     * <AUTHOR>
     * @date 2025/1/6 15:06
     * @since JDK 1.8
     */
    public static PortraitSession getLoginSessionNoException(HttpServletRequest request) {
        PortraitSession portraitSession = null;
        // JSON 序列化
        Object loginInfo = request.getSession().getAttribute(Constants.PORTRAIT_LOGIN_SESSION_NAME);
        if (loginInfo instanceof PortraitSession) {
            portraitSession = (PortraitSession) loginInfo;
        } else if(loginInfo instanceof String){
            portraitSession = JSON.parseObject((String) loginInfo, PortraitSession.class);
        }
        return portraitSession;
    }

    /**
     * @param request
     * @param portraitSession
     * @return void
     * @description:(添加登录会话)
     * <AUTHOR>
     * @date 2025/1/6 15:05
     * @since JDK 1.8
     */
    public void addLoginSession(HttpServletRequest request, PortraitSession portraitSession) {
        request.getSession().setAttribute(Constants.PORTRAIT_LOGIN_SESSION_NAME, JSON.toJSONString(portraitSession));
    }

    /**
     * @param
     * @return java.lang.String
     * @description:(获取用户ID)
     * <AUTHOR>
     * @date 2025/1/6 18:03
     * @since JDK 1.8
     */
    public static String getUserId() {
        String userId = RequestUtil.getHttpAttribute(Constants.PORTRAIT_USER_ID);
        if (StringUtils.isEmpty(userId)) {
            log.error("portraitSession error, userId is null");
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_ERROR);
        }
        return userId;
    }

    /**
     * @param userId
     * @param hboneNo
     * @return java.lang.String
     * @description:(校验userId与一账通号匹配)
     * <AUTHOR>
     * @date 2025/1/6 17:27
     * @since JDK 1.8
     */
    public String checkUserIdHboneNoMatch(String userId, String hboneNo) {
        // 查询投顾编号
        List<CmConsultantInfo> consultantInfoList = consultantInfoOuterService.queryConsultantInfo(userId, YesNoEnum.YES.getCode(),null);
        if (CollectionUtils.isEmpty(consultantInfoList)) {
            throw new BusinessException(ExceptionCodeEnum.PORTRAIT_CONSCODE_GET_ERROR);
        }
        String conscode = consultantInfoList.get(0).getConscode();
        // 根据一账通号查询投顾客户简单信息
        List<CmConsCustSimpleVO> consCustSimpleVOList = conscustInfoOuterService.queryCustconstantByHboneNo(hboneNo);
        // 一账通号对应多个投顾客户号
        if (CollectionUtils.isNotEmpty(consCustSimpleVOList) && consCustSimpleVOList.size() > 1) {
            throw new BusinessException(ExceptionCodeEnum.PORTRAIT_HBONENO_MANY_CONSCUST);
        }
        // 一账通未绑定客户
        if (CollectionUtils.isEmpty(consCustSimpleVOList)) {
            throw new BusinessException(ExceptionCodeEnum.PORTRAIT_HBONENO_NOBAND_CONSCUST);
        }

        // 校验是否画像白名单投顾
        if (StringUtils.isNotEmpty(portraitConscodeWhitelist) && StringUtils.contains(portraitConscodeWhitelist, conscode)) {
            // 是画像投顾白名单投顾 时则返回客户所属投顾
            return consCustSimpleVOList.get(0).getConsCode();
        } else {
            // 投顾关系不匹配
            if (!Objects.equals(consCustSimpleVOList.get(0).getConsCode(), conscode)) {
                throw new BusinessException(ExceptionCodeEnum.PORTRAIT_CONSCUST_NOT_MATCH);
            }
        }

        // 保存匹配关系缓存
        saveConsRelCache(userId, hboneNo);
        return conscode;
    }
}
