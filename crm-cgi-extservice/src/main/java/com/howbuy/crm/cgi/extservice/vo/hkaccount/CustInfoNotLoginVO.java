/**
 * Copyright (c) 2023, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/8/31 10:39
 * @since JDK 1.8
 */
@Data
public class CustInfoNotLoginVO extends AccountBaseVO {
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 一账通号
     */
    private String hboneNo;
    /**
     * 客户类型，0-机构,1-个人,2-产品户
     */
    private String invstType;

    /**
     * 证件类型
     *
     * 大陆身份证-0
     * 香港身份证-D
     * 澳门身份证-E
     * 台湾身份证-F
     * 中国护照-1
     * 外国护照-6
     * 港澳通行证-4
     * 台胞证-A
     * 港澳台居民居住证-C
     * 其他证件-7
     */
    private String idType;

    /**
     * 证件类型描述
     */
    private String idTypeDesc;

    /**
     * 证件号摘要
     */
    private String idNoDigest;
    /**
     * 证件号掩码
     */
    private String idNoMask;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号摘要
     */
    private String mobileDigest;
    /**
     * 手机号验证状态 0:未验证 1:已验证
     */
    private String mobileVerifyStatus;
    /**
     * 邮箱地址掩码
     */
    private String emailMask;
    /**
     * 邮箱地址密文
     */
    private String emailDigest;
    /**
     * 邮箱验证状态0:未验证 1:已验证
     */
    private String emailVerifyStatus;
    /**
     * 出生日期 yyyy-MM-dd
     */
    private String birthday;

}
