package com.howbuy.crm.cgi.extservice.request.hkfund;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Setter
@Getter
public class HKPurchaseFundPayVoucherUploadRequest extends HKFundBaseRequest {
    
    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 汇款账户
     */
    @NotBlank(message = "汇款账户不能为空")
    private String bankAcctMask;

    /**
     * 银行英文名称
     */
    private String bankName;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 资料id
     */
    private String orderId;

    /**
     * 文件类型id
     */
    private String fileTypeId;

    /**
     * 汇款币种代码
     */
    @NotBlank(message = "汇款币种不能为空")
    private String remitCurCode;

    /**
     * 汇款金额
     */
    @NotBlank(message = "汇款币种不能为空")
    @DecimalMax(value = "**********", message = "汇款金额错误,超过最大金额")
    @Digits(integer = 10, fraction = 2, message = "汇款金额错误,请核对汇款金额")
    private String remitAmt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除图片List
     */
    private List<String> delFileIdList;

    /**
     * 打款凭证图片上传列表
     */
    @Valid
    private List<HKUploadFileUrlRequest> payVoucherFiles;

    @NotBlank(message = "防重标识不能为空")
    private String hbSceneId;

    /**
     * 打款凭证号
     */
    private String voucherNo;
}
