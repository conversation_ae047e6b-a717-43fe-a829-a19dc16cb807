package com.howbuy.crm.cgi.extservice.common.enums.fund;

/**
 * @description: 订单的支付状态
 * @author: jinqing.rao
 * @date: 2024/5/6 14:30
 * @since JDK 1.8
 */
public enum HKFundOrderPayStatusEnum {

    NO_NEED_PAY("0", "无需支付"),
    UN_PAY("1", "未支付"),
    PAYING("2", "支付中"),
    PART_SUCCESS("3", "部分成功"),
    PAY_SUCCESS("4", "支付成功"),
    PAY_FAILURE("5", "支付失败"),
    // 退款
    REFUNDED("6", "部分退款"),
    ;

    private final String code;
    private final String desc;

    HKFundOrderPayStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
