/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import com.howbuy.crm.cgi.extservice.vo.DtmsBaseVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.RiskControlDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (查询海外产品电子签约详情)
 * <AUTHOR>
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
@Data
public class DigitalSignDetailVO extends DtmsBaseVO  implements Serializable {
    /**
     *签约订单信息
     */
    private SignDealInfoVO signDealInfo;

    /**
     * 是否开户 0: 已开户 1: 未开户
     */
    private String isOpenAcct;

    /**
     * 交易账号是否激活 0:已激活 1:未激活
     */
    private String isTradeAcctActive;

    /**
     * 交易密码状态     NORMAL("0", "正常"),RESET("1", "重置"),UNSET("2", "未设置");
     */
    private String txPasswdType;

    /**
     *交易信息
     */
    private TradeInfoVO tradeInfo;
    /**
     *银行卡列表
     */
    private List<BankCardVO> bankCardList;
    /**
     * 协议明细签署时间 yyyy-MM-dd HH:mm:ss
     */
    private String signDate;
    /**
     *合同及协议列表
     */
    private List<AgreementVO> agreementList;

    /**
     * 风测等级相关内容
     */
    private RiskControlDto qualification;


}