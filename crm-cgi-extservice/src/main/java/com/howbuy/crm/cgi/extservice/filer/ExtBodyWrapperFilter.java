package com.howbuy.crm.cgi.extservice.filer;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.interceptor.MyHttpServletRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @description: 过滤器，把request里的数据读出放入自定义的requestWrapper
 * <AUTHOR>
 * @date 2024/2/21 15:01
 * @since JDK 1.8
 */
public class ExtBodyWrapperFilter implements Filter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        if(HttpMethod.GET.matches(httpServletRequest.getMethod().toUpperCase())){
            filterChain.doFilter(servletRequest, servletResponse);
        }else{
            String contentType = httpServletRequest.getContentType();
            if (StringUtils.isNotBlank(contentType) && contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
                //把request里的数据读出放入自定义的requestWrapper
                MyHttpServletRequestWrapper myHttpServletRequestWrapper = new MyHttpServletRequestWrapper(httpServletRequest);
                filterChain.doFilter(myHttpServletRequestWrapper, servletResponse);
            }else{
                filterChain.doFilter(servletRequest, servletResponse);
            }
        }
    }
}
