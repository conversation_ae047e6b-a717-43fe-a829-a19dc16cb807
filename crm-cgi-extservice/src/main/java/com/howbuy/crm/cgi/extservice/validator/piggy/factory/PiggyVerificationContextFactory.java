/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy.factory;

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.DtmsProductFundChannelEnum;
import com.howbuy.crm.cgi.extservice.common.enums.FundValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggySignStatusEnum;
import com.howbuy.crm.cgi.extservice.request.piggy.PiggyContextRequest;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.ValidatorContext;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryPiggyFundChangeCfgOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryProductAgeLimitOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QuerySupportedPiggyBuyFundListOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/7/19 14:31
 * @since JDK 1.8
 */
@Service
public class PiggyVerificationContextFactory implements ContextFactory<ValidatorContext, PiggyContextRequest> {

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private QueryPiggyFundChangeCfgOuterService queryPiggyFundChangeCfgOuterService;

    @Resource
    private QuerySupportedPiggyBuyFundListOuterService querySupportedPiggyBuyFundListOuterService;

    @Resource
    private QueryProductAgeLimitOuterService queryProductAgeLimitOuterService;

    @Resource
    private QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    private HkCustInfoDTO getHkCustInfoDTO(String hkCustNo) {
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfo.getReturnCode())) {
            throw new BusinessException(ExceptionCodeEnum.HK_CUST_INFO_NOT_EXIST);
        }
        return hkCustInfo;
    }

    @Override
    public PiggyVerificationContext createContext(PiggyContextRequest request) {
        PiggyVerificationContext context = new PiggyVerificationContext();
        //设置校验类型
        context.setValidatorTypeEnum(FundValidatorTypeEnum.getEnumByCode(request.getBizType()));
        //添加客户信息
        context.setHkCustInfoDTO(getHkCustInfoDTO(request.getHkCustNo()));
        //可购买的储蓄罐底层基金
        FundBasicInfoDTO fundBasicInfoDTO = querySupportedPiggyBuyFundListOuterService.querySupportBuyPiggyBuyFundList(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        context.setFundCodeList(Collections.singletonList(fundBasicInfoDTO.getFundCode()));
        context.setFundBasicInfoDTO(fundBasicInfoDTO);

        //海外储蓄罐变更-获取当前签约的底层基金和获取最新的基金信息
        if (FundValidatorTypeEnum.PIGGY_CHANGE_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            //是否签约海外储蓄罐
            PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
            //海外储蓄罐不是有效状态,拦截报错
            if (!PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())) {
                throw new BusinessException(ExceptionCodeEnum.PIGGY_SIGN_FUND_NOT_EXIST_ERROR);
            }
            //添加基金基础信息
            FundBasicInfoDTO signFundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(hkCustPiggyAgreement.getPiggyFundCode());
            context.setSignFundCode(hkCustPiggyAgreement.getPiggyFundCode());
            context.setSignFundBasicInfoDTO(signFundBasicInfoDTO);
        }
        //海外储蓄罐关闭-获取当前签约的底层基金
        if (FundValidatorTypeEnum.PIGGY_CHANGE_CLOSE_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            //是否签约海外储蓄罐
            PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
            //海外储蓄罐不是有效状态,拦截报错
            if (!PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())) {
                throw new BusinessException(ExceptionCodeEnum.PIGGY_SIGN_FUND_NOT_EXIST_ERROR);
            }
            context.setSignFundCode(hkCustPiggyAgreement.getPiggyFundCode());
            //添加基金基础信息
            FundBasicInfoDTO signFundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(hkCustPiggyAgreement.getPiggyFundCode());
            context.setSignFundBasicInfoDTO(signFundBasicInfoDTO);
        }

        if (FundValidatorTypeEnum.PIGGY_SIGN_LIST_CLOSE_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            //是否签约海外储蓄罐
            PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
            //海外储蓄罐不是有效状态,拦截报错
            if (!PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())) {
                throw new BusinessException(ExceptionCodeEnum.PIGGY_SIGN_FUND_NOT_EXIST_ERROR);
            }
            context.setSignFundCode(hkCustPiggyAgreement.getPiggyFundCode());
            //添加基金基础信息
            FundBasicInfoDTO signFundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(hkCustPiggyAgreement.getPiggyFundCode());
            context.setSignFundBasicInfoDTO(signFundBasicInfoDTO);
        }
        //获取基金年龄限制
        Map<String, Integer> ageLimitMap = queryProductAgeLimitOuterService.queryProductAgeLimitMap(context.getFundCodeList().get(0), DtmsProductFundChannelEnum.NOT_COUNTER.getCode());
        context.setAgeLimitMap(ageLimitMap);
        return context;
    }
}
