package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description App登录日志VO
 * @Date 2024/8/19 09:47
 */
public class LoginLogListVO extends AccountBaseVO {

    private static final long serialVersionUID = -155187229986929203L;

    /**
     * 数据总量
     */
    private String total;

    /**
     * 登录日志列表
     */
    private List<LoginMsg> loginMsgList;

    public static class LoginMsg implements Serializable {
        private static final long serialVersionUID = 7939036196665756346L;

        /**
         * 登录ip
         */
        private String loginIp;

        /**
         * 登录时间 格式 yyyy-MM-dd HH:mm:ss
         */
        private String loginDtm;

        /**
         * 登录来源
         */
        private String loginSource;

        /**
         * 登录来源描述
         */
        private String loginSourceMemo;

        /**
         * 设备名称
         */
        private String deviceName;

        public String getLoginIp() {
            return loginIp;
        }

        public void setLoginIp(String loginIp) {
            this.loginIp = loginIp;
        }

        public String getLoginDtm() {
            return loginDtm;
        }

        public void setLoginDtm(String loginDtm) {
            this.loginDtm = loginDtm;
        }

        public String getLoginSource() {
            return loginSource;
        }

        public void setLoginSource(String loginSource) {
            this.loginSource = loginSource;
        }

        public String getLoginSourceMemo() {
            return loginSourceMemo;
        }

        public void setLoginSourceMemo(String loginSourceMemo) {
            this.loginSourceMemo = loginSourceMemo;
        }

        public String getDeviceName() {
            return deviceName;
        }

        public void setDeviceName(String deviceName) {
            this.deviceName = deviceName;
        }
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public List<LoginMsg> getLoginMsgList() {
        return loginMsgList;
    }

    public void setLoginMsgList(List<LoginMsg> loginMsgList) {
        this.loginMsgList = loginMsgList;
    }
}
