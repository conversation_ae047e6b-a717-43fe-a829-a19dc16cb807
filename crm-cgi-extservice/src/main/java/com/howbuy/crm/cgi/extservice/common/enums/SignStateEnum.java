package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 协议签订是否成功状态 0 成功  1失败
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum SignStateEnum {

    SUCCESS("0", "成功"),
    FAIL("1", "1失败");

    private final String key;
    private final String desc;

    public static SignStateEnum getSignStateEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        SignStateEnum signStateEnum = getSignStateEnum(code);
        return signStateEnum == null ? null : signStateEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    SignStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
