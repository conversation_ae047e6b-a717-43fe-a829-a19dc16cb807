/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @description: 上传文件路径请求
 * <AUTHOR>
 * @date 2024/4/17 15:04
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKUploadFileUrlRequest {
    /**
     * 图片地址
     */
    @NotBlank(message = "文件地址不能为空")
    private String url;

    /**
     * 缩略图地址
     */
    private String thumbnailUrl;

    /**
     * 文件名称
     */
    private String fileName;


    /**
     * 文件类型
     */
    private String exampleFileFormatType;

    public HKUploadFileUrlRequest() {
    }
    public HKUploadFileUrlRequest(String url, String thumbnailUrl) {
        this.url = url;
        this.thumbnailUrl = thumbnailUrl;
        this.exampleFileFormatType = url.substring(url.lastIndexOf(".") + 1);
    }
}
