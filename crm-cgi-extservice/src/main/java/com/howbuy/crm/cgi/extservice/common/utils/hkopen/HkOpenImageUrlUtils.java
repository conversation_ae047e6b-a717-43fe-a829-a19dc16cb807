package com.howbuy.crm.cgi.extservice.common.utils.hkopen;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.Date;

/**
 * @description: 海外小程序图片上传路径工具类
 * <AUTHOR>
 * @date 2024/1/5 10:56
 * @since JDK 1.8
 */
@Slf4j
public class HkOpenImageUrlUtils {

    private static final String THUMBNAIL = "thumbnail";

    /**
     * @description: 根据文件的种类和香港客户号生成相对路劲
     * @param fileType	文件类型, 名字不是很合理
     * @param hkCusNo	香港客户号
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/1/5 10:59
     * @since JDK 1.8
     */
    public static String getRelativePathUrl(String fileType, String hkCusNo) {
        return fileType + File.separator + hkCusNo + File.separator + DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN) + File.separator;
    }

    /**
     * @description: 生成文件名称 基础文件名称 格式 原始名称+ 客户号+ 文件类型+时间戳
     * @param imageType	 文件类型, 名字不是很合理
     * @param hkCusNo	香港客户号
     * @param imageName 图片名称
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/1/5 11:20
     * @since JDK 1.8
     */
    public static String getBaseFileName(String imageType, String hkCusNo, String imageName) {
        return imageName + MarkConstants.SEPARATOR_MIDDLE + hkCusNo + MarkConstants.SEPARATOR_DOWN + imageType + MarkConstants.SEPARATOR_DOWN + System.currentTimeMillis();
    }

    /**
     * @description: 获取缩略图名称
     * @param baseFileName	原文件名称
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/1/5 11:31
     * @since JDK 1.8
     */
    public static String getThumbnailNameByBaseFileName(String baseFileName) {
        return baseFileName + "_" + THUMBNAIL;
    }

    public static String getThumbnailUrlByBaseFileUrl(String baseFileUrl) {
        if(StringUtils.isBlank(baseFileUrl)){
            return null;
        }
        try {
            String type = baseFileUrl.substring(baseFileUrl.lastIndexOf(MarkConstants.SEPARATOR_DOT) + 1);
            String baseFile = baseFileUrl.substring(0, baseFileUrl.lastIndexOf(MarkConstants.SEPARATOR_DOT));
            return baseFile + "_" + THUMBNAIL+MarkConstants.SEPARATOR_DOT+type;
        } catch (Exception e) {
            log.error("getThumbnailUrlByBaseFileUrl>>>> 解析异常",e);
            return baseFileUrl;
        }
    }
}
