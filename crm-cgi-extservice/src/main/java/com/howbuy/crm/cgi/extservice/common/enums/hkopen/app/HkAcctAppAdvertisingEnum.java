/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen.app;

import org.apache.commons.lang3.StringUtils;

/**
 * @description: App 广告位枚举类
 * <AUTHOR>
 * @date 2024/2/29 14:08
 * @since JDK 1.8
 */
public enum HkAcctAppAdvertisingEnum {

    /**
     * app 个人中心
     */
    PERSONAL_CENTER("1", "appCenter", "app个人中心"),
        ;
    private final String code;

    private final String value;

    private final String desc;

    HkAcctAppAdvertisingEnum(String code, String value,String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }
    public static HkAcctAppAdvertisingEnum getEnumByCode(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (HkAcctAppAdvertisingEnum hkAcctAppAdvertisingEnum : HkAcctAppAdvertisingEnum.values()) {
            if (hkAcctAppAdvertisingEnum.getCode().equals(code)) {
                return hkAcctAppAdvertisingEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getValue() {
        return value;
    }

}

