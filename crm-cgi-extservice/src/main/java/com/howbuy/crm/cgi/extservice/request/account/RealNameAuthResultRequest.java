package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 实名信息验证接口请求类
 * @date 2023/11/30 17:44
 * @since JDK 1.8
 */
@Setter
@Getter
public class RealNameAuthResultRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -6138644819557944146L;

    /**
     * 客户姓名
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "客户姓名", isRequired = true)
    private String custName;

    /**
     * 证件类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "证件类型", isRequired = true)
    private String idType;

    /**
     * 证件号码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "证件号码", isRequired = true)
    private String idNo;

}
