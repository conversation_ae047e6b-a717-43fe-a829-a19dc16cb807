/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.serviceguide;

import com.howbuy.crm.cgi.extservice.convert.portrait.PortraitServiceGuideConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitServiceGuideRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.PortraitServiceGuideVO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.serviceguide.PortraitServiceGuideOuterService;
import com.howbuy.crm.portrait.client.domain.dto.serviceguide.PortraitServiceGuideDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 客户画像-服务指引服务
 * @Date 2024/9/10 14:02
 */
@Slf4j
@Service
public class PortraitServiceGuideService {

    @Resource
    private PortraitServiceGuideOuterService portraitServiceGuideOuterService;

    /**
     * 查询服务指引数据
     *
     * @param request   req
     * @return  PortraitServiceGuideVO
     */
    public PortraitServiceGuideVO getPortraitServiceGuideInfo(PortraitServiceGuideRequest request) {
        PortraitServiceGuideDTO portraitServiceGuideDTO = portraitServiceGuideOuterService.queryServiceGuideInfo(request.getHboneNo(), request.getConscode(), request.getFwjdKey(), request.getCardKey());
        if (null == portraitServiceGuideDTO) {
            log.info("查询服务指引数据失败！ hboneNo={}, conscode={}, fwjdKey={}, cardKey={}", request.getHboneNo(), request.getConscode(), request.getFwjdKey(), request.getCardKey());
            return null;
        }

        return PortraitServiceGuideConvert.convertToVO(portraitServiceGuideDTO);
    }

}
