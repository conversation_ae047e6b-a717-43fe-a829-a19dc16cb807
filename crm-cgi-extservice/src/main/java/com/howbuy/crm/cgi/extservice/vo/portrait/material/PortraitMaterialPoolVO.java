package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.portrait.client.enums.MaterialLabelEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 智能素材库-推荐池列表查询响应
 * @Date 2024-09-06 10:07:48
 */
@Getter
@Setter
@ToString
public class PortraitMaterialPoolVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 检索池类型 0-全池 1-推荐池
     * @see com.howbuy.crm.portrait.client.enums.SearchPoolEnum
     */
    private String searchPool;

    /**
     * 展示tab列表 [1-A基础 2-B投教 3-C市场资讯 4-D金融产品]
     * @see MaterialLabelEnum
     */
    private List<String> showTabList;

    /**
     * 总数量
     */
    private String total;

    /**
     * 推荐列表
     */
    private List<MaterialItem> dataList;

    /**
     * 素材项
     */
    @Getter
    @Setter
    @ToString
    public static class MaterialItem {
        /**
         * 素材id
         */
        private String materialId;

        /**
         * 素材标题
         */
        private String title;

        /**
         * 素材链接
         */
        private String link;

        /**
         * 素材分类标签
         * A-A基础 B-B投教 C-C市场资讯 D-D金融产品
         */
        private String label;

        /**
         * 专栏名称
         */
        private String column;

        /**
         * 最新标签
         * 1-是 0-否
         */
        private String newestTag;

        /**
         * 产品标签列表
         */
        private List<String> productTag;

        /**
         * 关键词列表
         */
        private List<String> keywordList;

        /**
         * 发送次数
         */
        private String sendNum;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;

        /**
         * 产品代码
         */
        private String fundCode;
    }
} 