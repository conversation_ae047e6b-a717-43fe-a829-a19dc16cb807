/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy.context;

import com.howbuy.crm.cgi.extservice.common.enums.FundValidatorTypeEnum;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHwDealOrderOuterService;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @description: 储蓄罐校验上下文接口
 * <AUTHOR>
 * @date 2024/7/19 14:15
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyVerificationContext extends ValidatorContext{

    /**
     * 客户基础信息
     */
    private HkCustInfoDTO hkCustInfoDTO;

    /**
     * 海外储蓄罐底层基金,默认一个，暂不支持多个
     */
    private List<String> fundCodeList;

    /**
     * 基金信息
     */
    private FundBasicInfoDTO fundBasicInfoDTO;

    /**
     * 基金最大年龄限制
     */
    private Map<String, Integer> ageLimitMap;

    /**
     * 验证类型
     */
    private FundValidatorTypeEnum validatorTypeEnum;

    /**
     * 当前签约的储蓄罐基金Code
     */
    private String signFundCode;

    /**
     * 当前签约的基金信息
     */
    private FundBasicInfoDTO signFundBasicInfoDTO;
}
