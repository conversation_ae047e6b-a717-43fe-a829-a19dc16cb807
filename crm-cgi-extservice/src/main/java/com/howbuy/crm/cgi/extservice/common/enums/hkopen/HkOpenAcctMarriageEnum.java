package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * Copyright (c) 2024, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 婚姻枚举
 * <AUTHOR>
 * @date 2024/1/24 18:56
 * @since JDK 1.8
 */
public enum HkOpenAcctMarriageEnum {
    SPINSTERHOOD("0","未婚"),
    MARRIED("1","已婚"),
    OTHER("2","其他"),
    ;

    private String code;

    private String desc;

    private HkOpenAcctMarriageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    /**
     * 通过Code获取desc
     */
    public static String getDescByCode(String code) {
        for (HkOpenAcctMarriageEnum hkOpenAcctMarriageEnum : HkOpenAcctMarriageEnum.values()) {
            if (hkOpenAcctMarriageEnum.getCode().equals(code)) {
                return hkOpenAcctMarriageEnum.getDesc();
            }
        }
        return null;
    }
}
