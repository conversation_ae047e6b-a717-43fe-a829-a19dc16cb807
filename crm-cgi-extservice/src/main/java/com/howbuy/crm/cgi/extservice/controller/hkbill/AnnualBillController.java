/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkbill;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.account.HkAnnualBillRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKPurchaseFundBaseRequest;
import com.howbuy.crm.cgi.extservice.service.annualbill.AnnualBillService;
import com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualAssetIncomeVO;
import com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualTradeVO;
import com.howbuy.crm.cgi.extservice.vo.annualbill.BillInfoVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/11 19:23
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkannual/bill")
public class AnnualBillController {

    @Autowired
    private AnnualBillService annualBillService;


    /**
     * @api {POST} /ext/hkannual/bill/querybehaviorinfo queryBehaviorInfo()
     * @apiVersion 1.0.0
     * @apiGroup AnnualBillController
     * @apiName queryBehaviorInfo()
     * @apiDescription 查询陪伴以及浏览数据
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.accompanyInfoVO 陪伴时间对象
     * @apiSuccess (响应结果) {String} data.accompanyInfoVO.registerTime 初识时间 yyyy-MM-dd
     * @apiSuccess (响应结果) {Number} data.accompanyInfoVO.accompanyDays 陪伴天数
     * @apiSuccess (响应结果) {Object} data.browseInfoVO 浏览行为对象
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseNewsCount 浏览文章总次数
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseProductCount 浏览产品总次数
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseNewsName 浏览最多文章名称
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseProductName 浏览最多产品名称
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseNewsMaxCount 浏览最多文章浏览次数
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseProductMaxCount 浏览最多产品浏览次数
     * @apiSuccess (响应结果) {String} data.browseInfoVO.browseMaxNewsTitle 浏览最多文章标题
     * @apiSuccess (响应结果) {String} data.browseInfoVO.recommendNewsContent 推荐浏览内容
     * @apiSuccess (响应结果) {String} data.browseInfoVO.recommendNewsUrl 推荐浏览文章地址
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"tdPjK8","data":{"browseInfoVO":{"browseNewsCount":"3zE3cc1m","browseNewsMaxCount":"3emoe","browseProductMaxCount":"CQKvQD","browseNewsName":"ys","recommendNewsUrl":"9FCaTVzouJ","browseProductName":"Na4vM12p4","browseMaxNewsTitle":"3kVAS","recommendNewsContent":"7","browseProductCount":"U"},"accompanyInfoVO":{"accompanyDays":8618,"registerTime":"a4"}},"description":"VN","timestampServer":"aIfaYZb"}
     */
    @PostMapping("/querybehaviorinfo")
    @ResponseBody
    public CgiResponse<BillInfoVO> queryBehaviorInfo() {
        return CgiResponse.ok(annualBillService.queryBehaviorInfo());
    }


    /**
     * @api {POST} /ext/hkannual/bill/queryannualtradeinfo queryAnnualTradeInfo()
     * @apiVersion 1.0.0
     * @apiGroup AnnualBillController
     * @apiName queryAnnualTradeInfo()
     * @apiDescription 查询年度账单的交易记录数据
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Number} data.annualBuyCount 当年买入次数
     * @apiSuccess (响应结果) {Number} data.annualSellCount 当年卖出次数
     * @apiSuccess (响应结果) {String} data.annualBuyTotalAsset 当年买入总资产
     * @apiSuccess (响应结果) {String} data.annualSellTotalAsset 当年卖出总资产
     * @apiSuccess (响应结果) {Number} data.annualAvgHoldDuration 产品平均持有时长
     * @apiSuccess (响应结果) {Number} data.annualLongestHoldProfit 持有最长产品盈利
     * @apiSuccess (响应结果) {String} data.annualLongestHoldProductName 持有最长产品名称
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"sDMQ3","data":{"annualAvgHoldDuration":6988,"annualBuyCount":8657,"annualLongestHoldProfit":949.6576309587201,"annualSellCount":9207,"annualBuyTotalAsset":"ZBNvA3BeY3","annualSellTotalAsset":"70kjd","annualLongestHoldProductName":"uWtvS6bBF8"},"description":"FjBMRA8UZr","timestampServer":"fRV0PY"}
     */
    @PostMapping("/queryannualtradeinfo")
    @ResponseBody
    public CgiResponse<AnnualTradeVO> queryAnnualTradeInfo(@RequestBody HkAnnualBillRequest request) {
        return CgiResponse.ok(annualBillService.queryAnnualTrade(request));
    }

    /**
     * @api {POST} /ext/hkannual/bill/queryannualassetincomeinfo queryAnnualAssetIncomeInfo()
     * @apiVersion 1.0.0
     * @apiGroup AnnualBillController
     * @apiName queryAnnualAssetIncomeInfo()
     * @apiDescription 查询年度账单资产页数据
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Number} data.totalIncome 总持仓
     * @apiSuccess (响应结果) {Number} data.annualIncome 全年收益
     * @apiSuccess (响应结果) {Number} data.annualIncomeRate 全年收益率
     * @apiSuccess (响应结果) {String} data.lastYearIncome 上年收益率
     * @apiSuccess (响应结果) {String} data.isGreaterThanLastYear 当前收益是否大于上年收益 0-否 1-是 当前年收益>0，且当前年收益>上年收益
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"1nV","data":{"totalIncome":6233.839043342815,"isGreaterThanLastYear":"cBxG","annualIncome":9557.914643983966,"annualIncomeRate":1403.0529551474967,"lastYearIncome":"VjYgJNwSC"},"description":"gr56TVU3","timestampServer":"sb17nU"}
     */
    @PostMapping("/queryannualassetincomeinfo")
    @ResponseBody
    public CgiResponse<AnnualAssetIncomeVO> queryAnnualAssetIncomeInfo(@RequestBody HkAnnualBillRequest request) {
        return CgiResponse.ok(annualBillService.queryAnnualAssetIncome(request));
    }
}