package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;
import net.sf.oval.constraint.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 身份证OCR请求
 * @date 2023/11/30 17:15
 * @since JDK 1.8
 */
@Setter
@Getter
public class IdentityCardOcrRequest extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -4278634301535160565L;

    /**
     * 正面图片,用MultipartFile接受,如果不传值，会异常
     */
    @NotBlank(message = "证件信息正面照不能为空")
    private String frontPictureBase64;

    /**
     * 反面图片，用MultipartFile接受,如果不传值，会异常
     */
    @NotBlank(message = "证件信息反面照不能为空")
    private String backPictureBase64;

    @NotBlank(message = "文件类型不能为空")
    private String imageFormat;


    /**
     *是否保存文件 1:是 0:否
     */
    private String saveFile;
}
