package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 赎回方式	 1-按份额赎回；2-按金额赎回
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum RedeemMethodEnum {

    VOL("1", "按份额赎回"),
    AMT("2", "按金额赎回");

    private final String key;
    private final String desc;

    public static RedeemMethodEnum getRedeemMethodEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        RedeemMethodEnum redeemMethodEnum = getRedeemMethodEnum(code);
        return redeemMethodEnum == null ? null : redeemMethodEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    RedeemMethodEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
