package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 个人信息填写页地址信息
 * <AUTHOR>
 * @date 2023/12/13 13:50
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctAddressInfoRequest implements Serializable {

    private static final long serialVersionUID = 5570889284549681403L;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 国家代码名称描述
     */
    private String countryDesc;

    /**
     * 国家英文描述
     */
    private String countryEnglishDesc;

    /**
     * 省份代码
     */
    private String provCode;


    /**
     * 省份代码名称
     */
    private String provDesc;

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 城市代码名称
     */
    private String cityDesc;


    /**
     * 区县代码
     */
    private String countyCode;

    /**
     * 区县代码
     */
    private String countyCodeDesc;

    /**
     * 省代码名称
     */
    private String provEnglishDesc;

    /**
     * 城市英文
     */
    private String cityEnglishDesc;


    /**
     * 区县英文
     */
    private String countyEnglishDesc;

    /**
     * 详细地址中文描述
     */
    private String detailAddrCn;

    /**
     * 详细地址英文描述
     */
    private String detailAddrEn;


    /**
     * 城/镇（英文）
     */
    private String townEn;

    /**
     * 省/州（英文）
     */
    private String stateEn;

    /**
     * 地址证明URL列表
     */
    private List<ImageVO> certUrls;
}
