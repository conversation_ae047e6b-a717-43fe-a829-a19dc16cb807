package com.howbuy.crm.cgi.extservice.common.utils;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @description: 证件信息工具类
 * @date 2023/12/12 14:23
 * @since JDK 1.8
 */
public class IdentityUtils {
    /**
     * @param birthDateStr 出生日期字符串,格式要求: yyyyMMdd
     * @return int
     * @description: 根据出生日期计算年龄
     * @author: jinqing.rao
     * @date: 2023/12/13 9:01
     * @since JDK 1.8
     */
    public static int calculateAge(String birthDateStr) {
        // 将字符串转换为 LocalDate 对象
        LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 计算年龄
        LocalDate currentDate = LocalDate.now();
        long years = ChronoUnit.YEARS.between(birthDate, currentDate);
        return (int) years;
    }
}
