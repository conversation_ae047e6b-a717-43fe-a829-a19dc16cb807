/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.PortraitServiceGuideVO;
import com.howbuy.crm.portrait.client.domain.dto.serviceguide.PortraitServiceGuideDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 客户画像-服务指引数据转换
 * @date 2025-05-13 14:02:00
 */
public class PortraitServiceGuideConvert {

    /**
     * PortraitServiceGuideDTO转换为PortraitServiceGuideVO
     *
     * @param dto PortraitServiceGuideDTO
     * @return PortraitServiceGuideVO
     */
    public static PortraitServiceGuideVO convertToVO(PortraitServiceGuideDTO dto) {
        if (dto == null) {
            return null;
        }
        PortraitServiceGuideVO vo = new PortraitServiceGuideVO();
        vo.setCustCycle(dto.getCustCycle());
        vo.setFwzyTitle(dto.getFwzyTitle());
        vo.setFwzyImgUrl(dto.getFwzyImgUrl());
        vo.setDefaultShowFwJd(dto.getDefaultShowFwJd());
        vo.setDataList(convertServiceStageList(dto.getDataList()));
        return vo;
    }

    /**
     * 转换服务阶段列表
     *
     * @param dtoList 服务阶段DTO列表
     * @return 服务阶段VO列表
     */
    private static List<PortraitServiceGuideVO.ServiceStage> convertServiceStageList(List<PortraitServiceGuideDTO.ServiceStage> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }
        List<PortraitServiceGuideVO.ServiceStage> voList = new ArrayList<>();
        for (PortraitServiceGuideDTO.ServiceStage dto : dtoList) {
            PortraitServiceGuideVO.ServiceStage vo = new PortraitServiceGuideVO.ServiceStage();
            vo.setFwjdName(dto.getFwjdName());
            vo.setFwjdKey(dto.getFwjdKey());
            vo.setCardList(convertServiceCardList(dto.getCardList()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 转换服务卡片列表
     *
     * @param dtoList 服务卡片DTO列表
     * @return 服务卡片VO列表
     */
    private static List<PortraitServiceGuideVO.ServiceStage.ServiceCard> convertServiceCardList(List<PortraitServiceGuideDTO.ServiceStage.ServiceCard> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }
        List<PortraitServiceGuideVO.ServiceStage.ServiceCard> voList = new ArrayList<>();
        for (PortraitServiceGuideDTO.ServiceStage.ServiceCard dto : dtoList) {
            PortraitServiceGuideVO.ServiceStage.ServiceCard vo = new PortraitServiceGuideVO.ServiceStage.ServiceCard();
            vo.setCardKey(dto.getCardKey());
            vo.setCardType(dto.getCardType());
            vo.setCardName(dto.getCardName());
            vo.setCardImgUrl(dto.getCardImgUrl());
            vo.setMaterialList(convertCardMaterialList(dto.getMaterialList()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 转换卡片素材列表
     *
     * @param dtoList 卡片素材DTO列表
     * @return 卡片素材VO列表
     */
    private static List<PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial> convertCardMaterialList(List<PortraitServiceGuideDTO.ServiceStage.ServiceCard.CardMaterial> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }
        List<PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial> voList = new ArrayList<>();
        for (PortraitServiceGuideDTO.ServiceStage.ServiceCard.CardMaterial dto : dtoList) {
            PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial vo = new PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial();
            vo.setMaterialTitle(dto.getMaterialTitle());
            vo.setMaterialType(dto.getMaterialType());
            vo.setSpecialCode(dto.getSpecialCode());
            vo.setCreateTime(dto.getCreateTime());
            vo.setContentList(convertMaterialContentList(dto.getContentList()));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 转换素材内容列表
     *
     * @param dtoList 素材内容DTO列表
     * @return 素材内容VO列表
     */
    private static List<PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial.MaterialContent> convertMaterialContentList(List<PortraitServiceGuideDTO.ServiceStage.ServiceCard.CardMaterial.MaterialContent> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }
        List<PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial.MaterialContent> voList = new ArrayList<>();
        for (PortraitServiceGuideDTO.ServiceStage.ServiceCard.CardMaterial.MaterialContent dto : dtoList) {
            PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial.MaterialContent vo = new PortraitServiceGuideVO.ServiceStage.ServiceCard.CardMaterial.MaterialContent();
            vo.setId(dto.getId());
            vo.setContentType(dto.getContentType());
            vo.setContentTitle(dto.getContentTitle());
            vo.setShareTitle(dto.getShareTitle());
            vo.setLinkUrl(dto.getLinkUrl());
            vo.setSendNum(dto.getSendNum());
            vo.setVisitNum(dto.getVisitNum());
            vo.setContentImg(dto.getContentImg());
            vo.setLabel(dto.getLabel());
            vo.setShareImg(dto.getShareImg());
            vo.setShareDesc(dto.getShareDesc());
            vo.setVbRadarId(dto.getVbRadarId());
            vo.setCreateTime(dto.getCreateTime());
            vo.setMaterialId(dto.getMaterialId());
            vo.setConfigContentType(dto.getConfigContentType());
            voList.add(vo);
        }
        return voList;
    }
} 