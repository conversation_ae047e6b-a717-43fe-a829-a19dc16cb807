/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.portrait;

/**
 * <AUTHOR>
 * @description: 标签客户状态 1-高端存量客户 2-高端0存量客户 3-高端有投顾潜客 4-高端无投顾潜客
 * @date 2024/9/10 10:12
 * @since JDK 1.8
 */
public enum PortraitCustStateEnum {

    HIGH_STOCK("1", "高端存量客户"),
    HIGH_ZERO_STOCK("2", "高端0存量客户"),
    HIGH_INVESTMENT_ADVISER("3", "高端有投顾潜客"),
    HIGH_NO_INVESTMENT_ADVISER("4", "高端无投顾潜客");

    private String code;
    private String desc;

    PortraitCustStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PortraitCustStateEnum getByCode(String code) {
        for (PortraitCustStateEnum custStateEnum : PortraitCustStateEnum.values()) {
            if (custStateEnum.getCode().equals(code)) {
                return custStateEnum;
            }
        }
        return null;
    }

    public static PortraitCustStateEnum getByDesc(String desc) {
        for (PortraitCustStateEnum custStateEnum : PortraitCustStateEnum.values()) {
            if (custStateEnum.getDesc().equals(desc)) {
                return custStateEnum;
            }
        }
        return null;
    }

    /**
     * @param desc
     * @return java.lang.String
     * @description:根据desc获取code
     * <AUTHOR>
     * @date 2024/9/10 14:43
     * @since JDK 1.8
     */
    public static String getCodeByDesc(String desc) {
        PortraitCustStateEnum PortraitCustStateEnum = getByDesc(desc);
        return PortraitCustStateEnum == null ? null : PortraitCustStateEnum.getCode();
    }

    /**
     * @description:根据code获取desc
     * @param code
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/10/11 11:17
     * @since JDK 1.8
     */
    public static String getDescByCode(String code) {
        PortraitCustStateEnum PortraitCustStateEnum = getByCode(code);
        return PortraitCustStateEnum == null ? null : PortraitCustStateEnum.getDesc();
    }
}
