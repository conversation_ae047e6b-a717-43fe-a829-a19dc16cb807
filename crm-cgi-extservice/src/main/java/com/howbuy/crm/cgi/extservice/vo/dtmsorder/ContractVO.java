/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (合同及协议详情)
 * <AUTHOR>
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Data
public class ContractVO implements Serializable {

    /**
     * 文件代码
     */
    private String fileCode;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String filePathUrl;
    /**
     * 协议明细签署状态	 0-未签署；1-已签署
     */
    private String signFlag;
    /**
     * 协议明细签署时间
     */
    private String signDate;
    /**
     * 文件类型名称
     */
    private String fileTypeName;

    /**
     * 文件业务类型, 0 : 默认合同，, 1: 合同文件
     */
    private String fileBizType;
}