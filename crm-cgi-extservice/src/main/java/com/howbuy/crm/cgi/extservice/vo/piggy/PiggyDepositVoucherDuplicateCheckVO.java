/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.piggy;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/9 14:18
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositVoucherDuplicateCheckVO extends Body implements Serializable {


    private static final long serialVersionUID = 2406303770356098762L;
    /**
     *  是否重复 1 是 0 否,默认是 0
     */
    private String duplicate = YesNoEnum.NO.getCode();

    /**
     *  重提打款凭证个数
     */
    private String duplicateNum;


    /**
     * 重复类型
     */
    private List<String> duplicateVoucherTypeDescList;
}
