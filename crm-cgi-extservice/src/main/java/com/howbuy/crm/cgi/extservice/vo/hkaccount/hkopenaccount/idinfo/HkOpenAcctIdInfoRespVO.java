package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/14 9:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctIdInfoRespVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 8369212450867618307L;

    /**
     * 校验信息或者审核不通过的信息,需要提示到具体字段
     */
    private List<HkOpenAcctCheckVO>  checkResult;
}
