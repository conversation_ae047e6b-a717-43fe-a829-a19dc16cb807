package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.pdf;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 海外开户预览Pdf
 * <AUTHOR>
 * @date 2024/1/7 21:51
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctPdfVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -2456273426743309215L;

    /**
     * PDf base64编码 暂定格式
     */
    private String pdfCode;

    /**
     * pdf名称
     */
    private String pdfName;
}
