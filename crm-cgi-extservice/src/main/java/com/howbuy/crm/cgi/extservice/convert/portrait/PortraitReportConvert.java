/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.report.PortraitBalanceReportVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.PortraitProductReportVO;
import com.howbuy.crm.cgi.manager.domain.portrait.report.BalanceReportDTO;
import com.howbuy.crm.cgi.manager.domain.portrait.report.ProductBalanceReportDTO;
import com.howbuy.crm.portrait.client.enums.MaterialSendTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户画像报告转换类
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
public class PortraitReportConvert {
    
    private static final PortraitReportConvert INSTANCE = new PortraitReportConvert();

    private PortraitReportConvert() {}

    public static PortraitReportConvert getInstance() {
        return INSTANCE;
    }

    /**
     * 转换为持仓投后报告VO
     * @param dto 持仓投后报告DTO
     * @return 持仓投后报告VO
     */
    public PortraitBalanceReportVO toPortraitBalanceReportVO(BalanceReportDTO dto) {
        if (dto == null) {
            return null;
        }
        
        PortraitBalanceReportVO vo = new PortraitBalanceReportVO();
        
        // 转换好买基金模块list
        vo.setHmjjList(convertFundReportList(dto.getHmjjList()));
        // 转换好臻投资模块list
        vo.setHztzList(convertFundReportList(dto.getHztzList()));
        // 转换好买香港模块list
        vo.setHmxgList(convertFundReportList(dto.getHmxgList()));
        // 转换用户授权状态
        vo.setIsAuthorization(dto.getIsAuthorization());
        // 转换投顾所属组织架构是否香港
        vo.setTgIsXg(dto.getTgIsXg());
        
        return vo;
    }

    /**
     * 转换基金报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<PortraitBalanceReportVO.FundReportVO> convertFundReportList(List<BalanceReportDTO.FundReportDTO> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<PortraitBalanceReportVO.FundReportVO> targetList = new ArrayList<>();
        for (BalanceReportDTO.FundReportDTO source : sourceList) {
            targetList.add(convertFundReport(source));
        }
        return targetList;
    }

    /**
     * 转换基金报告
     * @param source 源对象
     * @return 目标对象
     */
    private PortraitBalanceReportVO.FundReportVO convertFundReport(BalanceReportDTO.FundReportDTO source) {
        if (source == null) {
            return null;
        }

        PortraitBalanceReportVO.FundReportVO target = new PortraitBalanceReportVO.FundReportVO();
        target.setJjdm(source.getJjdm());
        target.setJjjc(source.getJjjc());
        target.setProductSubTypeName(source.getProductSubTypeName());
        target.setIsMoreReport(source.getIsMoreReport());
        target.setReportList(convertBalanceReportList(source.getReportList()));
        return target;
    }

    /**
     * 转换报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<PortraitBalanceReportVO.ReportVO> convertBalanceReportList(List<BalanceReportDTO.ReportDTO> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<PortraitBalanceReportVO.ReportVO> targetList = new ArrayList<>();
        for (BalanceReportDTO.ReportDTO source : sourceList) {
            targetList.add(convertBalanceReport(source));
        }
        return targetList;
    }

    /**
     * 转换报告
     * @param source 源对象
     * @return 目标对象
     */
    private PortraitBalanceReportVO.ReportVO convertBalanceReport(BalanceReportDTO.ReportDTO source) {
        if (source == null) {
            return null;
        }

        PortraitBalanceReportVO.ReportVO target = new PortraitBalanceReportVO.ReportVO();
        target.setDate(source.getDate());
        target.setTitle(source.getTitle());
        target.setIsNew(source.getIsNew());
        target.setSendNum(source.getSendNum());
        target.setReportId(source.getReportId());
        target.setReportUrl(source.getReportUrl());
        target.setMaterialId(source.getReportId());
        target.setMaterialSendType(MaterialSendTypeEnum.POSITION_REPORT.getCode());
        return target;
    }

    /**
     * 转换为产品持仓投后报告VO
     * @param dto 产品持仓投后报告DTO
     * @return 产品持仓投后报告VO
     */
    public PortraitProductReportVO toPortraitProductReportVO(ProductBalanceReportDTO dto) {
        if (dto == null) {
            return null;
        }
        
        PortraitProductReportVO vo = new PortraitProductReportVO();
        vo.setTotal(dto.getTotal());
        vo.setMinReportYear(dto.getMinReportYear());
        vo.setMaxReportYear(dto.getMaxReportYear());
        vo.setDataList(convertYearReportList(dto.getDataList()));
        return vo;
    }

    /**
     * 转换年度报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<PortraitProductReportVO.YearReportVO> convertYearReportList(List<ProductBalanceReportDTO.YearReportDTO> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<PortraitProductReportVO.YearReportVO> targetList = new ArrayList<>();
        for (ProductBalanceReportDTO.YearReportDTO source : sourceList) {
            targetList.add(convertYearReport(source));
        }
        return targetList;
    }

    /**
     * 转换年度报告
     * @param source 源对象
     * @return 目标对象
     */
    private PortraitProductReportVO.YearReportVO convertYearReport(ProductBalanceReportDTO.YearReportDTO source) {
        if (source == null) {
            return null;
        }

        PortraitProductReportVO.YearReportVO target = new PortraitProductReportVO.YearReportVO();
        target.setYear(source.getYear());
        target.setReportList(convertProductReportList(source.getReportList()));
        return target;
    }

    /**
     * 转换报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<PortraitProductReportVO.ReportVO> convertProductReportList(List<ProductBalanceReportDTO.ReportDTO> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<PortraitProductReportVO.ReportVO> targetList = new ArrayList<>();
        for (ProductBalanceReportDTO.ReportDTO source : sourceList) {
            targetList.add(convertProductReport(source));
        }
        return targetList;
    }

    /**
     * 转换报告
     * @param source 源对象
     * @return 目标对象
     */
    private PortraitProductReportVO.ReportVO convertProductReport(ProductBalanceReportDTO.ReportDTO source) {
        if (source == null) {
            return null;
        }

        PortraitProductReportVO.ReportVO target = new PortraitProductReportVO.ReportVO();
        target.setDate(source.getDate());
        target.setTitle(source.getTitle());
        target.setIsNew(source.getIsNew());
        target.setSendNum(source.getSendNum());
        target.setReportId(source.getReportId());
        target.setReportUrl(source.getReportUrl());
        target.setMaterialId(source.getReportId());
        target.setMaterialSendType(MaterialSendTypeEnum.POSITION_REPORT.getCode());
        return target;
    }
} 