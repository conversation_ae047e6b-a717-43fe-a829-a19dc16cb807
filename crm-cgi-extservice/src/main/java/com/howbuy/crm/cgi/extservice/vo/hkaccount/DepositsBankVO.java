/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/12/14 09:16
 * @since JDK 1.8
 */
@Data
public class DepositsBankVO {

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行卡号摘要
     */
    private String bankAcctDigest;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行logoUrl
     */
    private String bankLogoUrl;

    /**
     * swift编码
     */
    private String swiftCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 币种数据列表
     */
    private List<CurrencyVO> currencyVOList;

    /**
     * 银行卡的展示顺序，按照绑定时间倒排
     */
    private int index;


}