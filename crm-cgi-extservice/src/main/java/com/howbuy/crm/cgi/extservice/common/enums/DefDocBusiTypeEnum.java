package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 产品默认文件业务类型	 1-认申购；2-赎回
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum DefDocBusiTypeEnum {

    BUY("1", "认申购"),
    REDEEM("2", "赎回");

    private final String key;
    private final String desc;

    public static DefDocBusiTypeEnum getDefDocBusiTypeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        DefDocBusiTypeEnum defDocBusiTypeEnum = getDefDocBusiTypeEnum(code);
        return defDocBusiTypeEnum == null ? null : defDocBusiTypeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    DefDocBusiTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
