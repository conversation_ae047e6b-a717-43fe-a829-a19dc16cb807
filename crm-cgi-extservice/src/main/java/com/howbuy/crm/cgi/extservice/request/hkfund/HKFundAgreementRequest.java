/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (合同及协议详情)
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKFundAgreementRequest implements Serializable {

    private static final long serialVersionUID = -3906220905633109402L;
    /**
     * 文件代码
     */
    @NotBlank(message = "合同及列表-文件代码不能为空")
    private String fileCode;
    /**
     * 文件名称
     */
    @NotBlank(message = "合同及列表-文件名称不能为空")
    private String fileName;
    /**
     * 文件路径
     */
    @NotBlank(message = "合同及列表-文件路径不能为空")
    private String filePathUrl;
    
}