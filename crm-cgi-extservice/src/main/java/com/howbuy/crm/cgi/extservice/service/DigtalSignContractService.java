package com.howbuy.crm.cgi.extservice.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.FileTypeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.ContractVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.DigitalSignContractListVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductContractDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductContractFileDTO;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.ProductContractOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 海外产品电子签约合同
 * @date 2023/5/23 14:07
 * @since JDK 1.8
 */
@Slf4j
@Service
public class DigtalSignContractService {

    @Autowired
    private ProductContractOuterService productContractOuterService;

    /**
     * @param fundCode
     * @param busiType
     * @return com.howbuy.crm.cgi.extservice.vo.dtmsorder.DigitalSignContractListVO
     * @description:(查询海外产品电子签约协议信息接口)
     * @author: shuai.zhang
     * @date: 2023/5/23 16:18
     * @since JDK 1.8
     */
    public DigitalSignContractListVO queryList(String fundCode, String busiType) {
        DigitalSignContractListVO retVo = new DigitalSignContractListVO();
        try {
            //调用产品中心获取所有合同   包括配置和默认
            CrmResult<ProductContractDTO> productRes = productContractOuterService.query(fundCode, busiType);
            //将待签约文件信息加上去
            ArrayList<ContractVO> contractVOS = new ArrayList<>();
            if (Objects.nonNull(productRes) && Objects.nonNull(productRes.getData())
                    && CollectionUtils.isNotEmpty(productRes.getData().getFileList())) {
                for (ProductContractFileDTO dto : productRes.getData().getFileList()) {
                    ContractVO vo = new ContractVO();
                    // 根据文件类型处理返回给前端的显示的文件名称
                    vo.setFileCode(dto.getFileType());
                    vo.setFileName(dto.getFileName());
                    vo.setFilePathUrl(dto.getFilePathUrl());
                    vo.setFileTypeName(dto.getFileTypeName());
                    // 特殊文件名称处理
                    getContractFileName(dto, vo);
                    if(StringUtils.isAnyBlank(vo.getFilePathUrl(),vo.getFileName())){
                        log.info("DigtalSignContractService>>>queryList 文件名称/文件地址缺失的不展示,fundCode:{},fileCode:{}",fundCode,vo.getFileCode());
                        continue;
                    }
                    contractVOS.add(vo);
                }
                //根据文件类型排序,默认文件>合同文件,然后细分排序
                contractVOS.sort(Comparator.comparing(ContractVO::getFileBizType)
                        .thenComparing(ContractVO::getFileCode));
            }
            retVo.setContractList(contractVOS);
        } catch (Exception e) {
            log.error("error in DigitalSignContractListVO queryList", e);
        }
        return retVo;
    }

    private void getContractFileName(ProductContractFileDTO dto, ContractVO vo) {
        // 给文件分类, 区分是默认文件还是合同文件,该字段目前只是做分类排序用,前端不用
        if(StringUtils.isNotBlank(dto.getBusiType())){
            vo.setFileBizType(YesNoEnum.NO.getCode());
        }else{
            vo.setFileBizType(YesNoEnum.YES.getCode());
            // 优先获取展示名称
            if(StringUtils.isNotBlank(dto.getDisplayName())){
                vo.setFileName(dto.getDisplayName());
            }else {
                if (dto.getFileType().equals(FileTypeEnum.BCXY1_TYPE.getKey()) || dto.getFileType().equals(FileTypeEnum.BCXY2_TYPE.getKey()) || dto.getFileType().equals(FileTypeEnum.BCXY3_TYPE.getKey())) {
                    String newFileName = convertFileName(dto.getFileName());
                    vo.setFileName(newFileName);
                }
            }
        }
    }

    /**
     * @description: 根据文件名称获取文件类型
     * @param fileName 文件名称
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/8/31 20:21
     * @since JDK 1.8
     */
    private String convertFileName(String fileName){
        try{
            int firstIndex = fileName.indexOf("_");
            if(firstIndex < 0){
                return fileName;
            }
            int secondIndex = fileName.indexOf("_", firstIndex + 1);
            if (secondIndex < 0) {
                return fileName;
            }
            int end = fileName.indexOf(".pdf");
            if (end < 0) {
                return fileName;
            }
            return fileName.substring(secondIndex + 1, end);
        }catch (Exception e) {
            log.error("error in convertFileName", e);
        }
        return fileName;
    }

}
