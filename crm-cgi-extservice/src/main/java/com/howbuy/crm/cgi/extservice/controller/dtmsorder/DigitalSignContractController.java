/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.dtmsorder;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.dtmsorder.QueryDigitalSignContractListRequest;
import com.howbuy.crm.cgi.extservice.service.DigtalSignContractService;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.DigitalSignContractListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: (海外产品电子签约 合同)
 * <AUTHOR>
 * @date 2023/5/17 14:36
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/dtmsorder/digitalsigncontract")
public class DigitalSignContractController {

    @Autowired
    private DigtalSignContractService digtalSignContractService;

    /**
     * @api {POST} /ext/dtmsorder/digitalsigncontract/querylist 查询海外产品电子签约协议信息接口
     * @apiVersion 1.0.0
     * @apiGroup DigitalSignContractController
     * @apiName queryList
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} fundCode 基金代码	必填
     * @apiParam (请求体) {String} tradeMode 交易方式	 1-购买；2-赎回
     * @apiParamExample 请求体示例
     * {"fundCode":"PDbfV1nxT2","tradeMode":"LbE"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.contractList 合同及协议列表
     * @apiSuccess (响应结果) {String} data.contractList.fileCode 文件代码
     * @apiSuccess (响应结果) {String} data.contractList.fileName 文件名称
     * @apiSuccess (响应结果) {String} data.contractList.filePath 文件路径
     * @apiSuccess (响应结果) {String} data.contractList.signFlag 协议明细签署状态	 0-未签署；1-已签署
     * @apiSuccess (响应结果) {String} data.contractList.signDate 协议明细签署时间
     * @apiSuccessExample 响应结果示例
     * {"code":"cuy1kMC","data":{"contractList":[{"signFlag":"B9ln3dDPDD","fileName":"crqC","filePath":"Vt8","fileCode":"SdAFJjPal","signDate":"vynSSh"}]},"description":"gLSf"}
     */
    @PostMapping("/querylist")
    public CgiResponse<DigitalSignContractListVO> queryList(@RequestBody QueryDigitalSignContractListRequest req) {
        return CgiResponse.ok(digtalSignContractService.queryList(req.getFundCode(),req.getTradeMode()));
    }


}