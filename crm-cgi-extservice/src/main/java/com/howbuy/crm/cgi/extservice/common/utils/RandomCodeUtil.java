package com.howbuy.crm.cgi.extservice.common.utils;

/**
 * <AUTHOR>
 * @description: 随机码生成工具类
 * @date 2023/6/8 13:33
 * @since JDK 1.8
 */
public class RandomCodeUtil {
    /**
     * 随机密码长度
     */
    public static final int PASSWORD_CODE_LEN = 6;


    /**
     * 0~9
     */
    private static final String str = "0123456789";

    /**
     * @description: 生成验证码
     * @param: []
     * @return: java.lang.String
     * @author: shaoyang.li
     * @date: 2023/6/16 9:38
     * @since JDK 1.8
     */
    public static String createVerifyCode() {
        return createCode(PASSWORD_CODE_LEN);
    }

    /**
     * 生成指定位随机码
     *
     * @return
     */
    public static String createCode(int len) {
        StringBuilder sb = new StringBuilder();
        while (sb.length() < len) {
            // 0~s.length()-1
            int index = (new java.util.Random()).nextInt(str.length());
            // 处理重复字符：每个新的随机字符在 sb 中使用indexOf()查找下标值，-1为没找到，即不重复
            Character ch = str.charAt(index);
            if (sb.indexOf(ch.toString()) < 0) {
                sb.append(ch);
            }
        }
        return sb.toString();
    }

//    public static void main(String[] args) {
//        for (int i = 0; i < 100; i++) {
//            System.out.println(RandomCodeUtil.createCode(6));
//        }
//    }
}
