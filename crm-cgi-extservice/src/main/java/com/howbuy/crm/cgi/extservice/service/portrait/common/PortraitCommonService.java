/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.common;

import com.howbuy.crm.cgi.extservice.convert.portrait.LabelValueConvert;
import com.howbuy.crm.cgi.extservice.request.portrait.common.*;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.CustLabelValueVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.CustomizeOptionVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.LabelValueDictVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.LabelValueEnumVO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.common.PortraitCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.label.PortraitQueryLabelOuterService;
import com.howbuy.crm.portrait.client.domain.dto.label.CustLabelValueDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.LabelValueEnumDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/18 9:37
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitCommonService {

    @Resource
    private PortraitCommonOuterService portraitCommonOuterService;
    @Resource
    private PortraitQueryLabelOuterService portraitQueryLabelOuterService;

    /**
     * @param request
     * @return void
     * @description:修改标签值
     * <AUTHOR>
     * @date 2024/9/18 10:12
     * @since JDK 1.8
     */
    public void updateLabelValue(UpdateLabelValueRequest request) {
        portraitCommonOuterService.updateLabelValue(request.getConscode(), request.getHboneNo(), request.getLabelId(),
                request.getLabelValues(), request.getLabelCustomizeValues());
    }

    /**
     * @param request
     * @return void
     * @description:添加自定义标签选项
     * <AUTHOR>
     * @date 2024/9/18 10:11
     * @since JDK 1.8
     */
    public void addCustomizeOption(AddCustomizeOptionRequest request) {
        portraitCommonOuterService.addCustomizeOption(request.getConscode(), request.getHboneNo(), request.getLabelId(),
                request.getCustomizeOption());
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.common.CustomizeOptionVO
     * @description:查询自定义标签选项
     * <AUTHOR>
     * @date 2024/9/18 10:11
     * @since JDK 1.8
     */
    public CustomizeOptionVO queryCustomizeOption(QueryCustomizeOptionRequest request) {
        List<String> customizeOptionList =
                portraitCommonOuterService.queryCustomizeOption(request.getHboneNo(), request.getLabelId());
        CustomizeOptionVO customizeOptionVO = new CustomizeOptionVO();
        customizeOptionVO.setCustomizeOptionList(customizeOptionList);
        return customizeOptionVO;
    }

    /**
     * @param request
     * @return void
     * @description:删除自定义标签选项
     * <AUTHOR>
     * @date 2024/9/18 10:11
     * @since JDK 1.8
     */
    public void deleteCustomizeOption(DeleteCustomizeOptionRequest request) {
        portraitCommonOuterService.deleteCustomizeOption(request.getConscode(), request.getHboneNo(),
                request.getLabelId(), request.getDeleteCustomizeOptions());
    }


    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.common.LabelValueDictVO
     * @description:查询标签值枚举
     * <AUTHOR>
     * @date 2024/9/18 10:11
     * @since JDK 1.8
     */
    public LabelValueDictVO queryLabelValueEnum() {
        Map<String, List<LabelValueEnumDTO>> labelMap = portraitCommonOuterService.queryLabelValueEnum();
        if (labelMap == null || labelMap.isEmpty()) {
            return new LabelValueDictVO();
        }

        Map<String, List<LabelValueEnumVO>> labelValueEnumMap = labelMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(dto -> {
                                    LabelValueEnumVO vo = new LabelValueEnumVO();
                                    vo.setEnumKey(dto.getEnumKey());
                                    vo.setEnumValue(dto.getEnumValue());
                                    return vo;
                                })
                                .collect(Collectors.toList())
                ));

        LabelValueDictVO labelValueDictVO = new LabelValueDictVO();
        labelValueDictVO.setLabelValueEnumMap(labelValueEnumMap);
        return labelValueDictVO;
    }

    /**
     * 查询客户标签
     *
     * @param request req
     * @return CustLabelValueVO
     */
    public CustLabelValueVO queryCustLabelValue(QueryCustLabelValueRequest request) {
        CustLabelValueDTO dto = portraitQueryLabelOuterService.queryCustLabelValue(request.getHboneNo(), request.getLabelId());
        if (null == dto) {
            return new CustLabelValueVO();
        }

        return LabelValueConvert.convertCustLabelValueVO(dto);
    }

}
