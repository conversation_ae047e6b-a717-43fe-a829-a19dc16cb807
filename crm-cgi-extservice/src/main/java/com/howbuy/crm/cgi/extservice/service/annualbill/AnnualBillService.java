/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.annualbill;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.HkAnnualBillRequest;
import com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualAssetIncomeVO;
import com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualTradeVO;
import com.howbuy.crm.cgi.extservice.vo.annualbill.BillInfoVO;
import com.howbuy.crm.cgi.manager.outerservice.asset.annualasset.AnnualAssetOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkTradeOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/12 13:30
 * @since JDK 1.8
 */
@Slf4j
@Service
public class AnnualBillService {

    @Autowired
    private AnnualAssetOuterService annualAssetOuterService;

    @Autowired
    private HkTradeOuterService hkTradeOuterService;

    /**
     * @param
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualTradeVO
     * @description:(查询年度交易数据页数据)
     * @author: xufanchao
     * @date: 2024/11/12 13:42
     * @since JDK 1.8
     */
    public AnnualTradeVO queryAnnualTrade(HkAnnualBillRequest request) {
        String loginHkCustNo = "";
        if (StringUtils.isNotEmpty(request.getLoginHkCustNo())) {
            loginHkCustNo = request.getLoginHkCustNo();
        }else {
            loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        }
        // 根据香港客户号查询一账通号
        String hboneNo = hkTradeOuterService.getHboneNo(loginHkCustNo);
        return AnnualTradeVO.transToVO(annualAssetOuterService.queryAnnualTrade(hboneNo));
    }

    /**
     * @param
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualAssetIncomeVO
     * @description:(查询年度资产页数据)
     * @author: xufanchao
     * @date: 2024/11/12 13:42
     * @since JDK 1.8
     */
    public AnnualAssetIncomeVO queryAnnualAssetIncome(HkAnnualBillRequest request) {
        String loginHkCustNo = "";
        if (StringUtils.isNotEmpty(request.getLoginHkCustNo())) {
            loginHkCustNo = request.getLoginHkCustNo();
        }else {
            loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        }
        // 根据香港客户号查询一账通号
        String hboneNo = hkTradeOuterService.getHboneNo(loginHkCustNo);
        return AnnualAssetIncomeVO.transToVO(annualAssetOuterService.queryAnnualAssetIncome(hboneNo));
    }

    /**
     * @description:(查询陪伴以及浏览数据)
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.annualbill.BillInfoVO
     * @author: xufanchao
     * @date: 2024/11/26 13:51
     * @since JDK 1.8
     */
    public BillInfoVO queryBehaviorInfo() {
        String loginHkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        // 根据香港客户号查询一账通号
        String hboneNo = hkTradeOuterService.getHboneNo(loginHkCustNo);
        return BillInfoVO.transToVO(annualAssetOuterService.queryBillInfo(hboneNo));
    }

}