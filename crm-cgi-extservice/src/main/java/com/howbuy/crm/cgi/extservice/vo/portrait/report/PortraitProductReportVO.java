/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.portrait.report;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 客户画像-产品持仓投后报告查询响应
 * <AUTHOR>
 * @date 2025-03-13 13:18:57
 */
@Getter
@Setter
@ToString
public class PortraitProductReportVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 数据总量
     */
    private String total;

    /**
     * 最小报告年份yyyy
     */
    private String minReportYear;

    /**
     * 最大报告年份yyyy
     */
    private String maxReportYear;

    /**
     * 数据列表（按年份分组）
     */
    private List<YearReportVO> dataList;

    /**
     * 年份分组
     */
    @Getter
    @Setter
    public static class YearReportVO extends Body {
        /**
         * 报告年份（格式：yyyy）
         */
        private String year;

        /**
         * 报告列表
         */
        private List<ReportVO> reportList;
    }

    /**
     * 报告项
     */
    @Getter
    @Setter
    @ToString
    public static class ReportVO {

        /**
         * 报告ID
         */
        private String reportId;

        /**
         * 报告日期 (MM月dd日)
         */
        private String date;

        /**
         * 报告标题
         */
        private String title;

        /**
         * 是否new标签
         * 0：否，1：是
         */
        private String isNew;

        /**
         * 发送次数
         */
        private String sendNum;

        /**
         * 报告URL
         */
        private String reportUrl;

        /**
         * 素材发送ID
         */
        private String materialId;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;
    }
} 