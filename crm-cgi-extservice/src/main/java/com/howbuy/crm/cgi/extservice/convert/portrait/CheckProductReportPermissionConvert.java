/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.report.CheckProductReportPermissionVO;
import com.howbuy.crm.portrait.client.domain.response.report.CheckProductReportPermissionResponse;

/**
 * 校验产品报告发送权限转换类
 * <AUTHOR>
 * @date 2025-03-13 13:18:57
 */
public class CheckProductReportPermissionConvert {

    /**
     * 转换为产品报告发送权限VO
     * @description 将外部服务返回的Response转换为前端展示的VO
     * @param response 外部服务响应对象
     * @return CheckProductReportPermissionVO 产品报告发送权限VO
     * <AUTHOR>
     * @date 2025-03-13 13:18:57
     */
    public static CheckProductReportPermissionVO convertToVO(CheckProductReportPermissionResponse response) {
        CheckProductReportPermissionVO vo = new CheckProductReportPermissionVO();
        if (response == null) {
            return vo;
        }

        vo.setSendAble(response.getSendAble());
        vo.setSendAbleReason(response.getSendAbleReason());

        return vo;
    }
} 