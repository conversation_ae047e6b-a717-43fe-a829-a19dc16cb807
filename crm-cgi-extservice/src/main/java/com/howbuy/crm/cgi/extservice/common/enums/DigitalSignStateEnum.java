package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 签约状态	 0-成功；1-失败；
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum DigitalSignStateEnum {

    SUCCESS("0", "成功"),
    FAIL("1", "失败");

    private final String key;
    private final String desc;

    public static DigitalSignStateEnum getDigitalSignStateEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        DigitalSignStateEnum signStateEnum = getDigitalSignStateEnum(code);
        return signStateEnum == null ? null : signStateEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    DigitalSignStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
