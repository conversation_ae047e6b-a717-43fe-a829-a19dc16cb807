package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 证件信息
 * @date 2023/11/30 17:19
 * @since JDK 1.8
 */
@Getter
@Builder
public class IdentityCardVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -2820081845015535724L;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 证件有效期
     */
    private String expireTime;

    /**
     * 是否是长期有效 1:是 0：否
     */
    private String idAlwaysValidFlag;

    /**
     * 证件地址
     */
    private String idAddress;


    /**
     * 正面照片的图片路径和缩略图路径
     */
    private ImageVO frontImage;

    /**
     * 反面照片的图片路径和缩略图路径
     */
    private ImageVO backImage;

    /**
     * 海外开户个人信息审核结果信息,如果审核通过则为空。错误会具体到哪个字段
     */
    private List<HkOpenAcctCheckVO> checkResult;

}
