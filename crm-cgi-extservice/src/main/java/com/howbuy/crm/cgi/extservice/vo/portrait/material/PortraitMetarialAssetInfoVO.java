/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 资配报告信息VO
 * @Date 2024/9/11 11:13
 */
public class PortraitMetarialAssetInfoVO extends Body implements Serializable {

    private static final long serialVersionUID = 96714569999187524L;

    /**
     * 报告id
     */
    private String id;

    /**
     * pdf文件名称
     */
    private String fileName;

    /**
     * 文件字节base64字符串
     */
    private String filebase64;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilebase64() {
        return filebase64;
    }

    public void setFilebase64(String filebase64) {
        this.filebase64 = filebase64;
    }
}
