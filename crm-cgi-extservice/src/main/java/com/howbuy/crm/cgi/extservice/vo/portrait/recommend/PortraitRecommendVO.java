package com.howbuy.crm.cgi.extservice.vo.portrait.recommend;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 客户画像-首页为您推荐查询响应
 * <AUTHOR>
 * @date 2024-03-19 14:45:00
 */
@Getter
@Setter
public class PortraitRecommendVO extends Body {
    
    /**
     * 是否展示持仓产品报告入口
     * 1-是 0-否
     */
    private String showBalanceReport;

    /**
     * 是否展示产品三件套入口
     * 1-是 0-否
     */
    private String showProductReport;

    /**
     * 是否显示小红点
     * 1-是 0-否
     */
    private String redPoint;

    /**
     * 总数量
     */
    private String total;

    /**
     * 推荐列表
     */
    private List<RecommendItem> dataList;

    @Getter
    @Setter
    public static class RecommendItem extends Body {
        /**
         * 素材id
         */
        private String materialId;

        /**
         * 素材标题
         */
        private String title;

        /**
         * 素材链接
         */
        private String link;

        /**
         * 素材分类标签
         * A-A基础 B-B投教 C-C市场资讯 D-D金融产品
         */
        private String label;

        /**
         * 专栏名称
         */
        private String column;

        /**
         * 最新标签
         * 1-是 0-否
         */
        private String newestTag;

        /**
         * 产品标签列表
         */
        private List<String> productTag;

        /**
         * 关键词列表
         */
        private List<String> keywordList;

        /**
         * 素材发送次数
         */
        private String sendNum;


        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;

        /**
         * 产品代码
         */
        private String fundCode;
    }
} 