/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * @description: 海外开户(香港)小程序客户展示状态枚举
 * <AUTHOR>
 * @date 2023/12/4 13:07
 * @since JDK 1.8
 */
public enum HkOpenCustomerStatusEnum {

    OPEN_ACCOUNT("01", "去开户"),
    CONTINUE_ACCOUNT("02", "继续开户"),
    VIEW_ACCOUNT_PROGRESS("03", "查看开户进度"),
    MODIFY_ACCOUNT_INFO("04", "修改开户资料"),
    DEPOSIT("05", "去入金"),
    VIEW_DEPOSIT_PROGRESS("06", "查看入金进度"),
    MODIFY_DEPOSIT_INFO("07", "修改入金资料"),
    HIDE_ACCOUNT_DEPOSIT_AREA("08", "隐藏开户入金区域");

    /**
     *枚举编码
     */
    private final String code;

    /**
     *枚举描述
     */
    private final String desc;

    /**
     * 构造函数
     * @param code 枚举编码
     * @param desc 枚举描述
     */
    HkOpenCustomerStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getNumberTypeCode() {
        return Integer.valueOf(code).toString();
    }
}

