/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (邮箱摘要验证码请求实体类)
 * @date 2023/11/29 18:58
 * @since JDK 1.8
 */
@Data
public class GetEmailDigestCodeByTypeRequest extends AccountBaseRequest {


    /**
     *  邮箱摘要
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "邮箱摘要", isRequired = true)
    private String emailDigest;


    /**
     * 验证码类型 56-绑定一账通香港邮箱邮箱验证码；57-解绑一账通香港邮箱邮箱验证码；58-交易账户激活邮箱验证码；59-登录账户激活邮箱验证码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码类型", isRequired = true)
    private String verifyCodeType;

}