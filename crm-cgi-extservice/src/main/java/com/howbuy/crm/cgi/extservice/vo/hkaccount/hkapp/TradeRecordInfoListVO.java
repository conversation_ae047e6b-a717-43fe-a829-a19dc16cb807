/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (交易记录的列表VO)
 * <AUTHOR>
 * @date 2024/2/22 18:42
 * @since JDK 1.8
 */
@Data
public class TradeRecordInfoListVO extends AccountBase<PERSON> implements Serializable {

    /**
     * 交易记录列表
     */
    private List<TradeRecordInfoVO> tradeRecordInfoList;

}