package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * @description:(返回给前端的香港客户状态)
 * @return
 * @author: xufanchao
 * @date: 2023/12/25 11:26
 * @since JDK 1.8
 */
public enum HkOpenCustStatusEnum {
    /**
     * 正常
     */
    NORMAL("0", "正常"),
    /**
     * 注册成功
     */
    SIGNOFF("1", "注册成功"),
    /**
     * 未开户
     */
    SLEEP("2", "未开户");

    private String code;
    private String desc;

    HkOpenCustStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static HkOpenCustStatusEnum getEnumByCode(String code) {
        for (HkOpenCustStatusEnum e : HkOpenCustStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
