/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsproduct;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Data;

import java.util.List;

/**
 * @description: (财富配置报告及预约日历 vo)
 * <AUTHOR>
 * @date 2023/12/21 14:39
 * @since JDK 1.8
 */
@Data
public class ProductWealthAppointListVO extends Body {
    /**
     * 总记录数
     */

    private long total;

    /**
     * 总页数
     */
    private int pages;

    /**
     * 财富配置报告及预约日历列表数据
     */
    private List<ProductWealthAppointVO> configVOList;
}