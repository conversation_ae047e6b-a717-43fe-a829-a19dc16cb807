package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 交易记录详情关联订单列表查询请求参数
 * @author: jinqing.rao
 * @date: 2024/3/11 11:30
 * @since JDK 1.8
 */
@Setter
@Getter
public class RelationOrderListRequest implements Serializable {

    private static final long serialVersionUID = 4870646461781271731L;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 关联订单号列表
     */
    private List<String> relationOrderList;
} 