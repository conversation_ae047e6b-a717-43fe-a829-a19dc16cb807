/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.common;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.cgi.extservice.common.utils.LabelUtils;
import com.howbuy.crm.cgi.manager.outerservice.crmwechat.WechatCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.DsLabelValueDTO;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.QueryCustLabelOuterService;
import com.howbuy.crm.portrait.client.enums.LabelEnum;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: (客户的公共处理类)
 * <AUTHOR>
 * @date 2025/3/21 15:13
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitCustCommonService {

    @Resource
    private QueryCustLabelOuterService queryCustLabelOuterService;

    @Resource
    private WechatCustInfoOuterService wechatCustInfoOuterService;



    /**
     * @description:(优先获取BDP-用户标签表-姓名，再次获取用户微信昵称)
     * @param hboneNo
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2025/3/21 15:15
     * @since JDK 1.8
     */
    public String  getCustName(String hboneNo){
        if(StringUtil.isEmpty(hboneNo)){
            return "";
        }
        List<DsLabelValueDTO> dsLabelValueDTOList = queryCustLabelOuterService.queryCustMultiLabel(hboneNo, LabelUtils.BASE_INFO_LABEL_LIST);
        Map<String, String> labelValueMap = dsLabelValueDTOList.stream()
                .collect(Collectors.toMap(DsLabelValueDTO::getLabelId, DsLabelValueDTO::getLabelValue, (v1, v2) -> v2));

        // 姓名 -BDP-用户标签表-姓名
        String name = labelValueMap.get(LabelEnum.LABEL_010101.getId());
        if(StringUtil.isNotBlank(name)){
            return name;
        }

        // 姓名 -查询微信客户信息
        WechatCustInfoVO wechatCustInfoVO = wechatCustInfoOuterService.queryWechatCustInfo(null, hboneNo);
        if(Objects.nonNull(wechatCustInfoVO)) {
          return wechatCustInfoVO.getNickName();
        }
        return "";
    }

}