/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.piggy;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/16 11:18
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggySignSubmitRequest implements Serializable {

    private static final long serialVersionUID = -242662443924204104L;

    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String txPassword;

    /**
     * 签名文件路径列表
     */
    @Valid
    private List<PiggySignSubmitFile> signFilePathList;

    @NotBlank(message = "防重标识不能为空")
    private String hbSceneId;

    /**
     * 是否同意协议变更 1 是 0 否
     */
    private String agreed;

    @Setter
    @Getter
    public static class PiggySignSubmitFile implements Serializable{

        private static final long serialVersionUID = -4730549676214664321L;

        /**
         * 文件名称
         */
        @NotBlank(message = "文件名称不能为空")
        private String fileName;

        /**
         * 文件路径
         */
        @NotBlank(message = "文件路径不能为空")
        private String filePathUrl;

        /**
         * 文件代码
         */
        @NotBlank(message = "文件代码不能为空")
        private String fileCode;

        /**
         * 文件类型名称
         */
        private String fileTypeName;
    }
}
