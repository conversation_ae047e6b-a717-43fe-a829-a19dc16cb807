package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.signnature;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctMaterialVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 电子签名开户查询
 * <AUTHOR>
 * @date 2023/11/30 14:11
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctESignatureVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -8374815371224453468L;

    /**
    *电子签名图片URL
    */
    private List<ImageVO> signatureImages;


    /**
     * 材料表展示信息
     */
    private List<HkOpenAcctMaterialVO>  materialList;

    /**
     * 用户名称
     */
    private String userName;

    /**
     *  审核错误的提示信息
     */
    private List<HkOpenAcctCheckVO> checkResult;

}
