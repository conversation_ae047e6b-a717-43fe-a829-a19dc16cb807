package com.howbuy.crm.cgi.extservice.request.agreement;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 补充协议提交请求参数
 * @author: jinqing.rao
 * @date: 2024/3/6 18:20
 * @since JDK 1.8
 */
@Setter
@Getter
public class SupplementalAgreementSubmitRequest {
    
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金编码", isRequired = true)
    private String fundCode;

    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "补充协议ID", isRequired = true)
    private List<String> agreementIdList;

    /**
     * 交易密码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易密码", isRequired = true)
    private String txPassword;

} 