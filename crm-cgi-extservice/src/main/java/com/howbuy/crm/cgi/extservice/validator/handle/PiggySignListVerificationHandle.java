/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.handle;

import com.howbuy.crm.cgi.extservice.validator.CustExitFundHoldFundValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggySignCustStatusValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggyValidator;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 储蓄罐关闭校验器
 * @date 2024/7/22 16:07
 * @since JDK 1.8
 */
public class PiggySignListVerificationHandle implements VerificationHandle<PiggyBankVerificationVO> {

    private final List<PiggyValidator<PiggyBankVerificationVO>> validators;

    public PiggySignListVerificationHandle(PiggyVerificationContext context) {
        this.validators = buildHkFundValidators(context.getHkCustInfoDTO(), context.getFundBasicInfoDTO());
    }

    public PiggyBankVerificationVO verification() {
        //添加过滤器
        PiggyBankVerificationVO verificationVO = new PiggyBankVerificationVO();
        for (PiggyValidator<PiggyBankVerificationVO> validator : validators) {
            verificationVO = validator.verification();
            //校验不通过,返回错误信息
            if (null != verificationVO && StringUtils.isNotBlank(verificationVO.getVerfiyState())) {
                return verificationVO;
            }
        }
        return verificationVO;
    }

    protected List<PiggyValidator<PiggyBankVerificationVO>> buildHkFundValidators(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO) {
        return new ArrayList<PiggyValidator<PiggyBankVerificationVO>>() {
            private static final long serialVersionUID = -5640545335979781797L;

            {
                //客户状态校验
                add(new PiggySignCustStatusValidator(hkCustInfo));
                //持仓或者在途校验
                add(new CustExitFundHoldFundValidator(hkCustInfo, fundBasicInfoDTO));
            }
        };
    }
}
