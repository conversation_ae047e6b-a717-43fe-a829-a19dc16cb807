package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/6/12 16:39
 * @since JDK 1.8
 */
@Data
public class SetLoginPasswordRequest extends AccountBaseRequest {
    /**
     * 新登录密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "新登录密码", isRequired = true)
    private String newLoginPassword;
}
