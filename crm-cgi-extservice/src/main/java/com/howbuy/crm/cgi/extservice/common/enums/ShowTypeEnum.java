package com.howbuy.crm.cgi.extservice.common.enums;


/**
 * @description:(展示类型枚举)
 * @author: xuf<PERSON><PERSON><PERSON>
 * @date: 2024/3/12 17:47
 * @since JDK 1.8
 */
public enum ShowTypeEnum {
    //1.非分期成立 2.分起成立 3.千禧年产品
    NON_STAGING("1", "非分期成立"),
    STAGING("2", "分期成立"),
    MILLENNIUM("3", "千禧年产品");

    private String code;
    private String description;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    ShowTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }



}
