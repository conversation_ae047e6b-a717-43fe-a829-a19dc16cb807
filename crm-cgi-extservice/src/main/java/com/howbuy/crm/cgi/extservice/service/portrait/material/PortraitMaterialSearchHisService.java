/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.material;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitMaterialHistoryAddRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitMaterialHistoryDeleteRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitMaterialHistoryQueryRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialHistoryAddVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialHistoryDeleteVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialHistoryQueryVO;
import com.howbuy.crm.cgi.manager.domain.portrait.material.AddMetarialSearchHisDTO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.material.PortraitMaterialSearchHisOuterService;
import com.howbuy.crm.portrait.client.domain.dto.common.OptResultDTO;
import com.howbuy.crm.portrait.client.domain.dto.material.SearchHistoryItemDTO;
import com.howbuy.crm.portrait.client.domain.response.material.QueryMaterialSearchHisResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: (话术回复检索历史service)
 * <AUTHOR>
 * @date 2025/3/17 15:09
 * @since JDK 1.8
 */
@Slf4j
@Service("portraitMaterialSearchHisService")
public class PortraitMaterialSearchHisService {

    @Resource
    private PortraitMaterialSearchHisOuterService portraitMaterialSearchHisOuterService;


    /**
     * @description:(话术回复检索历史-删除)
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialHistoryDeleteVO
     * @author: haoran.zhang
     * @date: 2025/3/17 15:11
     * @since JDK 1.8
     */
    public PortraitMaterialHistoryDeleteVO deleteHistory(PortraitMaterialHistoryDeleteRequest request){
        OptResultDTO optDto=portraitMaterialSearchHisOuterService
                .deleteMaterialSearchHis(request.getConscode(), request.getHboneNo());
        log.info("话术回复检索历史-删除参数:{},删除结果：{}", JSON.toJSONString(request),optDto);
        return  new PortraitMaterialHistoryDeleteVO();
    }



    public PortraitMaterialHistoryAddVO addHistory(PortraitMaterialHistoryAddRequest request){
        AddMetarialSearchHisDTO addDto = new AddMetarialSearchHisDTO();
        addDto.setConscode(request.getConscode());
        addDto.setHboneNo(request.getHboneNo());
        addDto.setContent(request.getContent());
        addDto.setSearchParam(request.getSearchParam());
        OptResultDTO optDto=portraitMaterialSearchHisOuterService.addMaterialSearchHis(addDto);
        log.info("话术回复检索历史-添加参数:{},添加结果：{}", JSON.toJSONString(request),optDto);
        return new PortraitMaterialHistoryAddVO();
    }


    /**
     * @description:(分页查询素材搜索历史记录)
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMaterialHistoryQueryVO
     * @author: haoran.zhang
     * @date: 2025/3/18 16:06
     * @since JDK 1.8
     */
    public PortraitMaterialHistoryQueryVO queryHistory(PortraitMaterialHistoryQueryRequest request){
        // 获取三个月前的时间字符串
        String startTime = DateUtil.date2String(DateUtil.getDateBeforeOfMonth(3), DateUtil.DEFAULT_DATESFM);
        QueryMaterialSearchHisResponse response=
                portraitMaterialSearchHisOuterService.queryMaterialSearchHis
                        (request.getHboneNo(), request.getConscode(), startTime, request.getPage(), request.getSize());
        log.info("话术回复检索历史-查询参数:{},查询结果：{}", JSON.toJSONString(request),response);
        List<SearchHistoryItemDTO> dataList = response.getDataList();
        PortraitMaterialHistoryQueryVO vo=new PortraitMaterialHistoryQueryVO();
        vo.setTotal(String.valueOf(response.getTotal()));

        List<PortraitMaterialHistoryQueryVO.HistoryItem> itemList= Lists.newArrayList();
        for (SearchHistoryItemDTO dto : dataList) {
            PortraitMaterialHistoryQueryVO.HistoryItem item=new PortraitMaterialHistoryQueryVO.HistoryItem();
            item.setContent(dto.getContent());
            item.setId(dto.getId());
            item.setSearchParam(dto.getSearchParam());
            itemList.add(item);
        }
        vo.setDataList(itemList);
        return vo;
    }


}