/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsproduct;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: (财富配置报告及预约日历 vo对象)
 * @date 2023/12/19 15:09
 * @since JDK 1.8
 */
@Data
public class ProductWealthAppointVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 投资者类型
     */
    private String investType;

    /**
     * 具体年月份
     */
    private String yearMonth;

    /**
     * 财富配置报告
     */
    private String wealthAllocationReport;

    /**
     * 财富配置报告的地址
     */
    private String wealthAllocationReportPath;

    /**
     * 投资者预约日历汇总
     */
    private String appointmentCalendarSummary;

    /**
     * 投资者预约日历汇总地址
     */
    private String appointmentCalendarSummaryPath;


}