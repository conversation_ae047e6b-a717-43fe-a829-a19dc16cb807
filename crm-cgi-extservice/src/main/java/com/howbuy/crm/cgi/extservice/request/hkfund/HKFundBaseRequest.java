/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 海外基金基础实体类
 * <AUTHOR>
 * @date 2024/4/17 15:14
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKFundBaseRequest extends BodyRequest {

    /**
     * tokenId 用于重复提交
     */
    private String tokenId;
}
