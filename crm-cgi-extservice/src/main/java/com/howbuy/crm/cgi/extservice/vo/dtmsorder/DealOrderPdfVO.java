/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import com.howbuy.crm.cgi.extservice.vo.DtmsBaseVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/9 16:03
 * @since JDK 1.8
 */
@Data
public class DealOrderPdfVO extends DtmsBaseVO {


    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * ebrokerId
     */
    private String ebrokerId;

    /**
     * 电话号码区号
     */
    private String areaCode;

    /**
     * 电话号码明文
     */
    private String mobile;

    /**
     * 邮箱地址
     */
    private String email;


    /**
     * 是否首次购入 1-首次购买；2-追加购买
     */
    private String firstBuyFlag;


    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 证件明文
     */
    private String idNo;

    /**
     * 基金名称(全称)
     */
    private String fundName;

    /**
     *  类别: 是否复杂产品 0-否、1-是
     */
    private String isComplexProduct;


    /**
     * 基金风险等级 1-低风险、2-低中风险、3-中风险、4-高中风险、5-高风险
     */
    private String fundRiskLevel;

    /**
     * 币种
     */
    private String currency;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 费用
     */
    private BigDecimal fee;

    /**
     * 分红方式 0-红利再投；1-现金红利
     */
    private String fundDivMode;

    /**
     * 开放日
     */
    private String openDt;

    /**
     * 支付方式 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
     */
    private String paymentType;

    /**
     * 申请份额
     */
    private BigDecimal appVol;


    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 赎回方式  1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：支票
     */
    private String redeemDirection;

    /**
     * 持牌代表姓名
     */
    private String licensePersonName;

    /**
     * 首次收费
     */
    private String firstCharge;

    /**
     * 香港资金账号
     */
    private String cpAcctNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行账户名称
     */
    private String bankAcctName;

    /**
     * 银行卡号
     */
    private String bankAcctNo;

    /**
     * 申请日期
     */
    private String appDt;

    private String redeemType;
}