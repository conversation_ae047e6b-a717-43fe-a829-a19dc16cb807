package com.howbuy.crm.cgi.extservice.common.utils;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctCheckInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 反射工具类
 * @date 2023/12/13 18:38
 * @since JDK 1.8
 */
public class ReflectUtils {
    private static final Logger log = LoggerFactory.getLogger(ReflectUtils.class);

    /**
     * @description: 反射获取类中的字段名称和字段值
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO>
     * @author: jinqing.rao
     * @date: 2023/12/13 19:33
     * @since JDK 1.8
     */
    public static List<HkOpenAcctCheckVO> extractFieldInfo(Object object) {
        List<HkOpenAcctCheckVO> fileInfoList = new ArrayList<>();
        try {
            // 获取所有字段
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                // 设置可访问私有字段
                field.setAccessible(true);
                // 获取字段名称
                String fieldName = field.getName();
                // 获取字段值
                Object fieldValue = field.get(object);
                if(null == fieldValue){
                    continue;
                }
                // 创建 FileInfo 对象并添加到集合中
                HkOpenAcctCheckVO fileInfo = new HkOpenAcctCheckVO(fieldName, fieldValue);
                fileInfoList.add(fileInfo);
            }
        } catch (Exception e) {
            log.error("ReflectUtils>>>反射工具类异常", e);
        }
        return fileInfoList;
    }
}
