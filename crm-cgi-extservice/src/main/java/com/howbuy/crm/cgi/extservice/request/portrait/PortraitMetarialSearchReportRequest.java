package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户画像-素材产品报告搜索req
 * @Date 2024/9/2 18:31
 */
public class PortraitMetarialSearchReportRequest implements Serializable {

    private static final long serialVersionUID = 1809314055045508313L;
    
    /**
     * 产品代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 投顾号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }
}
