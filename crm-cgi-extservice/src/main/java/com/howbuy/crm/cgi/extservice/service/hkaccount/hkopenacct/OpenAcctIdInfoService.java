package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.ImageUtils;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctInvstTypeEnum;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.IdentityCardOcrRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.idInfo.HkAcctOpenIdInfoRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.IdentityCardVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkAcctOpenIdDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.idinfo.HkOpenAcctIdInfoRespVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctIdInfoDTO;
import com.howbuy.crm.cgi.manager.domain.ocr.IdentityCardOcrDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import com.howbuy.crm.cgi.manager.outerservice.hkopen.OpenAcctCatchService;
import com.howbuy.crm.cgi.manager.outerservice.ocr.IdentityCardOcrOuterService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @description: 香港开户步骤一,证件信息页服务类
 * <AUTHOR>
 * @date 2024/1/22 18:37
 * @since JDK 1.8
 */
@Service
public class OpenAcctIdInfoService extends OpenAcctAbstractService{

    private static final Logger log = LoggerFactory.getLogger(OpenAcctAbstractService.class);

    @Autowired
    private OpenAcctCatchService openAcctCatchService;

    @Resource
    private IdentityCardOcrOuterService identityCardOcrOuterService;

    public IdentityCardVO queryIdentityCardOcrDetail(IdentityCardOcrRequest request) {
        String hkCusNo = getHkCusNo();
        //直接清除以后得缓存,因为这个缓存是在开户第一步的时候就缓存了,如果用户修改了证件信息,那么第一步的缓存就要清除
        openAcctCatchService.deletedHkOpenAcctIdOcrDTO(hkCusNo);
        //数据校验
        byte[] frontImageBytes = ImageUtils.base64ToBytes(request.getFrontPictureBase64());
        byte[] backImageBytes = ImageUtils.base64ToBytes(request.getBackPictureBase64());
        return getIdentityCardVO(frontImageBytes,backImageBytes,request.getImageFormat(),request.getImageFormat(), hkCusNo);
    }

    /**
     * @description: 身份证信息解析并且上传
     * @param frontImageBytes	 正面图片
     * @param backImageBytes	 反面图片
     * @param frontImageFormat	 正面图片格式
     * @param backImageFormat	 反面图片格式
     * @param hkCusNo   香港客户号
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.IdentityCardVO
     * @author: jinqing.rao
     * @date: 2024/3/8 16:50
     * @since JDK 1.8
     */
    private IdentityCardVO getIdentityCardVO(byte[] frontImageBytes,byte[] backImageBytes,String frontImageFormat,String backImageFormat, String hkCusNo) {

        IdentityCardOcrDTO identityCardOcr = null;
        //证件解析异常产品要求特殊处理,直接上传，等待人工审核。OCR有值就获取信息,没有就跳过
        try {
            //调用网关腾讯实名认证接口,获取身份证OCR详情,封装返回结果
            identityCardOcr = identityCardOcrOuterService.getIdentityCardOcr(hkCusNo, frontImageBytes, backImageBytes);
        } catch (Exception e) {
            log.info("queryIdentityCardOcrDetail>>>证件解析失败,hkCusNo:{},result:{}", hkCusNo, JSON.toJSONString(identityCardOcr), e);
        }
        log.info("queryIdentityCardOcrDetail>>>正面图片上传webDav,hkCusNo:{}", hkCusNo);
        ImageVO frontPictureUrl = getUploadImageUrl("00", hkCusNo, frontImageBytes, frontImageFormat, "frontImage");
        log.info("queryIdentityCardOcrDetail>>>反面图片上传webDav,hkCusNo:{}", hkCusNo);
        ImageVO backPictureUrl = getUploadImageUrl("00", hkCusNo, backImageBytes, backImageFormat, "backImage");
        log.info("queryIdentityCardOcrDetail>>>图片上传webDav结束,hkCusNo:{},frontPictureUrl :{},backPictureUrl:{}", hkCusNo,JSON.toJSONString(frontPictureUrl),JSON.toJSONString(backPictureUrl));
        //缓存用户解析的证件信息,在个人信息页需要带出用户的证件信息
        if (null != identityCardOcr) {
            openAcctCatchService.saveHkOpenAcctIdOcrDTO(hkCusNo, identityCardOcr);
        }
        return OpenAcctConvert.toIdentityCardVO(identityCardOcr, frontPictureUrl, backPictureUrl);
    }

    public CgiResponse<HkOpenAcctIdInfoRespVO> saveOpenIdInfo(HkAcctOpenIdInfoRequest request) {
        HkOpenAcctIdInfoRespVO acctIdInfoRespVO = new HkOpenAcctIdInfoRespVO();
        List<HkOpenAcctCheckVO> checkVOList = new ArrayList<>();
        //获取香港客户号
        String hkCustNo = getHkCusNo();
        //校验证件的基本格式类型,长度,合法字符等等
        HkOpenAcctValidator.validatorOpenIdBaseFormat(request.getIdType(), request.getIdNo());
        //证件是否已过期：校验【证件有效期截止日】是否早于当前日期,需要有特定的提示要求
        if (YesNoEnum.NO.getCode().equals(request.getIdAlwaysValidFlag())) {
            boolean beforeOrEqualToCurrentDate = DateUtils.isBeforeOrEqualToCurrentDate(request.getIdExpireTime());
            if (beforeOrEqualToCurrentDate) {
                checkVOList.add(new HkOpenAcctCheckVO("idExpireTime", "证件有效期截止日小于当前日期，证件已过期"));
                acctIdInfoRespVO.setCheckResult(checkVOList);
                return CgiResponse.error(ExceptionCodeEnum.HK_OPEN_ACCOUNT_ID_IS_EXIST.getCode(), "您的证件已过期", acctIdInfoRespVO);
            }
        }
        // 调香港账户中心接口，判断证件号码是否已经开户,香港客户状态是开户或者休眠状态认为是开户状态 需要有特定的提示要求
        HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfoByIdNo(request.getIdAreaCode(), request.getIdType(), DigestUtil.digest(request.getIdNo()), HkOpenAcctInvstTypeEnum.INDIVIDUAL.getValue());
        if (null != hkCustInfoDTO && StringUtils.isNotBlank(hkCustInfoDTO.getCustState())) {
            checkVOList.add(new HkOpenAcctCheckVO("idNo", "证件号码已开户"));
            acctIdInfoRespVO.setCheckResult(checkVOList);
            return CgiResponse.error(ExceptionCodeEnum.HK_OPEN_ACCOUNT_ID_IS_EXIST.getCode(), ExceptionCodeEnum.HK_OPEN_ACCOUNT_ID_IS_EXIST.getDescription(), acctIdInfoRespVO);
        }
        //保存redis缓存
        openAcctCatchService.saveHkOpenAcctIdInfoDTO(hkCustNo, OpenAcctConvert.toHkOpenAcctIdInfoDTO(request));
        //删除审核不通过的证件信息原因
        openAcctCatchService.removeHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.IDENTITY_INFORMATION_CHECK_RESULT,hkCustNo);
        return CgiResponse.ok(acctIdInfoRespVO);
    }

    public HkOpenAcctIdInfoRespVO temporaryStorageOpenId(HkAcctOpenIdInfoRequest request) {
        HkOpenAcctIdInfoRespVO acctIdInfoRespVO = new HkOpenAcctIdInfoRespVO();
        //获取香港客户号
        String hkCustNo = getHkCusNo();
        //保存redis缓存
        openAcctCatchService.saveHkOpenAcctIdInfoDTO(hkCustNo, OpenAcctConvert.toHkOpenAcctIdInfoDTO(request));
        return acctIdInfoRespVO;
    }

    public HkAcctOpenIdDetailVO queryOpenIdDetail() {
        String hkCustNo = getHkCusNo();
        //获取省市区信息
        Map<String, String> countryInfoMap = hkAccCommonOuterService.getCountryInfoMap();
        //从缓存获取
        HkOpenAcctIdInfoDTO hkOpenAcctIdInfoDTO = openAcctCatchService.getHkOpenAcctIdInfoDTO(hkCustNo);
        //从缓存获取开户订单审核退回修改状态的错误信息,这里的信息是在 com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService.querySubmitResult 回写到缓存的。因为没有业务数据表
        String orderCheckReason = openAcctCatchService.getHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.IDENTITY_INFORMATION_CHECK_RESULT, hkCustNo);
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = JSON.parseArray(orderCheckReason, HkOpenAcctCheckVO.class);
        if (null != hkOpenAcctIdInfoDTO) {
            return OpenAcctConvert.toOpenIdVO(hkOpenAcctIdInfoDTO, countryInfoMap, new HashMap<>(), hkOpenAcctCheckVOS);
        }
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getHkOpenAcctIdInfoDTO() && StringUtils.isNotBlank(orderInfoDTO.getHkOpenAcctIdInfoDTO().getIdNo())) {
            //根据国家获取对应的证件类型
            Map<String, String> idTypeListMap = getIdTypeVOListByCountryCode(orderInfoDTO.getHkOpenAcctIdInfoDTO().getIdAreaCode());
            return OpenAcctConvert.toOpenIdVO(orderInfoDTO.getHkOpenAcctIdInfoDTO(), countryInfoMap, idTypeListMap, hkOpenAcctCheckVOS);
        }
        //没有数据,返回空对象
        return HkAcctOpenIdDetailVO.builder().build();
    }

    /**
     * @description: OCR文件流解析身份证信息
     * @param frontFileStream	 正面文件流
     * @param backFileStream	 反面文件流
     * @return void
     * @author: jinqing.rao
     * @date: 2024/3/8 16:26
     * @since JDK 1.8
     */
    public IdentityCardVO queryIdentityOcrDetailByFileStream(MultipartFile frontFileStream, MultipartFile backFileStream,String frontImageFormat,String backImageFormat) {
        String hkCusNo = getHkCusNo();
        //直接清除以后得缓存,因为这个缓存是在开户第一步的时候就缓存了,如果用户修改了证件信息,那么第一步的缓存就要清除
        openAcctCatchService.deletedHkOpenAcctIdOcrDTO(hkCusNo);
        try {
            byte[] frontBytes = frontFileStream.getBytes();
            byte[] backBytes = backFileStream.getBytes();
            return getIdentityCardVO(frontBytes, backBytes, frontImageFormat, backImageFormat, hkCusNo);
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_APP_FILE_UPLOAD_ERROR);
        }
    }
}
