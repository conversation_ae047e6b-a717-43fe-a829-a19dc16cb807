/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (交易信息详情)
 * <AUTHOR>
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Data
public class TradeInfoVO implements Serializable {

    /**
     * 支付方式列表
     */
    private List<String> paymentTypeList;
    /**
     * 付款金额 保留两位小数
     */
    private String payAmt;
    /**
     * 回款去向列表
     */
    private List<String> redeemDirectionList;
}