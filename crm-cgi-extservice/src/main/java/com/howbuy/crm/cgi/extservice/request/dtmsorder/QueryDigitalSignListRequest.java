/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.dtmsorder;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.TimeTypeEnum;
import com.howbuy.crm.cgi.extservice.request.DtmsBaseRequest;
import lombok.Data;

/**
 * @description: (查询海外产品电子签约列表接口)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class QueryDigitalSignListRequest extends DtmsBaseRequest {
    /**
     * 交易方式	必填，默认全部，0-全部；1-购买；2-赎回
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易方式", isRequired = true)
    private String tradeMode;
    /**
     * 交易状态  必填，默认全部，0-全部；1-待签约；2-交易中；3-交易成功；4-交易失败；
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易状态", isRequired = true)
    private String tradeState;
    /**
     * 时间类型 必填，默认近一年；0-全部；1-近一月；2-近三月；3-近六月；4-近一年
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "时间类型", isRequired = true)
    private String timeType = TimeTypeEnum.ONEYEAR.getKey();
    /**
     * 页码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Digit, fieldName = "页码", isRequired = true)
    private int pageNo = 1;
    /**
     * pageSize
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Digit, fieldName = "pageSize", isRequired = true)
    private int pageSize = 50;
}