/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.piggy;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 海外储蓄罐签约合同请求
 * <AUTHOR>
 * @date 2024/7/22 10:08
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggySignContractRequest implements Serializable {

    private static final long serialVersionUID = 3738083901423705673L;
    /**
     * 海外客户编号
     */
    @NotBlank(message = "海外客户编号不能为空")
    private String hkCustNo;
    /**
     * 合同业务类型 1 海外储蓄罐签约 2.海外储蓄罐变更
     */
    @NotBlank(message = "合同业务类型不能为空")
    private String bizType;
}
