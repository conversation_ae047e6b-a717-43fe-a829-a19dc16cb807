package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 素材库对话检索历史删除请求
 * @Date 2024-09-06 10:00:35
 */
@Getter
@Setter
@ToString
public class PortraitMaterialHistoryDeleteRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 一账通号
     */
    private String hboneNo;

} 