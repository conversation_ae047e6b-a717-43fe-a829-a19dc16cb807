package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * @description: 风险等级枚举,主要服务于香港开户的风险测评 1-低风险等级, 2-中低风险等级, 3-中风险等级, 4-中高风险等级, 5-高风险等级
 * @author: jinqing.rao
 * @date: 2023/12/13 9:02
 * @since JDK 1.8
 */
public enum RiskLevelEnum {
    LOW_RISK("1", "低风险等级"),
    MEDIUM_LOW_RISK("2", "中低风险等级"),
    MEDIUM_RISK("3", "中风险等级"),
    MEDIUM_HIGH_RISK("4", "中高风险等级"),
    HIGH_RISK("5", "高风险等级");

    private String code;
    private String desc;

    RiskLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        for (RiskLevelEnum level : RiskLevelEnum.values()) {
            if (level.getCode().equals(code)) {
                return level.getDesc();
            }
        }
        return null;
    }
}