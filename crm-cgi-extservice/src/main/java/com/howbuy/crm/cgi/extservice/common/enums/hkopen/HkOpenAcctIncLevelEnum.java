package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * 年收入枚举
 */
public enum HkOpenAcctIncLevelEnum {

    /**
     * ≦HK$500,000
     */
    ZERO_ZERO_HK_1("01", "<=HK$200,000"),
    /**
     * HK$500,001 - HK$1,000,000
     */
    ZERO_ZERO_HK_2("02", "HK$200,001 - HK$500,000"),
    /**
     * HK$1,000,001 - HK$2,000,000
     */
    ZERO_ZERO_HK_3("03", "HK$500,001 - HK$1,000,000"),
    /**
     * HK$2,000,001 - HK$5,000,000
     */
    ZERO_ZERO_HK_4("04", "HK$1,000,001 - HK$5,000,000"),
    /**
     * >HK$5,000,000
     */
    ZERO_ZERO_HK_5("05", ">HK$5,000,000");
    /**
     * 枚举
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    HkOpenAcctIncLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HkOpenAcctIncLevelEnum getHkOpenAcctIncLevelEnumByCode(String code) {
        for (HkOpenAcctIncLevelEnum e : HkOpenAcctIncLevelEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
