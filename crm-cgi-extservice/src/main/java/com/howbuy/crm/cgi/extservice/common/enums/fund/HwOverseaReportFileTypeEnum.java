/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.fund;

import org.apache.commons.lang3.StringUtils;

/**
 * @description: 海外报告文件格式枚举
 * <AUTHOR>
 * @date 2025/3/28 14:30
 * @since JDK 1.8
 */
public enum HwOverseaReportFileTypeEnum {
    
    PDF("pdf", "PDF文档"),
    PNG("png", "PNG图片"),
    JPEG("jpeg", "JPEG图片");
    
    private final String code;
    private final String desc;
    
    /**
     * PDF文件类型代码
     */
    public static final String PDF_TYPE_CODE = "1";
    
    /**
     * 图片文件类型代码
     */
    public static final String IMAGE_TYPE_CODE = "2";
    
    HwOverseaReportFileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * @description: 根据文件后缀判断文件类型
     * @param fileSuffix 文件后缀
     * @return String 文件类型代码，PDF返回1，图片(PNG/JPEG)返回2，不匹配返回null
     * @author: jinqing.rao
     * @date: 2025/3/28 15:10
     * @since JDK 1.8
     */
    public static String getFileTypeByFileSuffix(String fileSuffix) {
        if (StringUtils.isBlank(fileSuffix)) {
            return null;
        }
        
        String lowerSuffix = fileSuffix.toLowerCase();
        if (PDF.getCode().equals(lowerSuffix)) {
            return PDF_TYPE_CODE;
        } else if (PNG.getCode().equals(lowerSuffix) || JPEG.getCode().equals(lowerSuffix)) {
            return IMAGE_TYPE_CODE;
        }
        
        return null;
    }
    
    /**
     * @description: 判断文件类型是否为PDF
     * @param fileTypeCode 文件类型代码
     * @return boolean 是否为PDF类型
     * @author: jinqing.rao
     * @date: 2025/3/28 14:38
     * @since JDK 1.8
     */
    public static boolean isPdfType(String fileTypeCode) {
        return PDF_TYPE_CODE.equals(fileTypeCode);
    }
    
    /**
     * @description: 判断文件类型是否为图片(PNG/JPEG)
     * @param fileTypeCode 文件类型代码
     * @return boolean 是否为图片类型
     * @author: jinqing.rao
     * @date: 2025/3/28 14:40
     * @since JDK 1.8
     */
    public static boolean isImageType(String fileTypeCode) {
        return IMAGE_TYPE_CODE.equals(fileTypeCode);
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}