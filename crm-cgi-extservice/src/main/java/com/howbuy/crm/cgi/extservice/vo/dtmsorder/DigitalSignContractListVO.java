/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import com.howbuy.crm.cgi.extservice.vo.DtmsBaseVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.ContractVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description: (查询海外产品电子签约协议信息接口)
 * <AUTHOR>
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
@Data
public class DigitalSignContractListVO extends DtmsBaseVO {
    /**
     *合同及协议列表
     */
    private List<ContractVO> contractList;

}