/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.portrait;

/**
 * <AUTHOR>
 * @description: 标签展示tab 1-投资属性tab 2-行为属性tab 3-基础属性tab
 * @date 2024/9/24 15:06
 * @since JDK 1.8
 */
public enum LabelShowTabEnum {

    INVESTMENT("1", "投资属性"),
    BEHAVIOR("2", "行为属性"),
    BASIC("3", "基础属性");

    private String code;
    private String desc;

    LabelShowTabEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
