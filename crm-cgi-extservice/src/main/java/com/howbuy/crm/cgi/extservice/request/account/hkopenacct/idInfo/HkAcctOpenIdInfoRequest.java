package com.howbuy.crm.cgi.extservice.request.account.hkopenacct.idInfo;

/**
 * Copyright (c) 2023, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 开户暂存接口  对应页面：线上开户-填写资料1
 * @date 2023/11/29 16:49
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAcctOpenIdInfoRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1042915384078377283L;

    /**
     * 证件所属地区编码
     */
    @NotBlank(message = "证件所属地区编码不能为空")
    private String idAreaCode;

    /**
     * 证件所属地区名称
     */
    @NotBlank(message = "证件所属地区名称不能为空")
    private String idAreaCodeDesc;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空")
    private String idType;

    /**
     * 证件类型名称
     */
    @NotBlank(message = "证件类型名称不能为空")
    private String idTypeDesc;

    /**
     * 证件类型名称
     */
    @NotBlank(message = "身份证正面图片不能为空")
    private String frontPictureUrl;

    /**
     * 正面缩略图URL
     */
    @NotBlank(message = "正面缩略图URL不能为空")
    private String frontThumbnailUrl;

    /**
     * 反面图片URL
     */
    @NotBlank(message = "反面图片URL不能为空")
    private String backPictureUrl;

    /**
     * 反面缩略图URL
     */
    @NotBlank(message = "反面缩略图URL不能为空")
    private String backThumbnailUrl;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String idNo;

    /**
     * 证件是否永久有效
     */
    @NotBlank(message = "证件是否永久有效不能为空")
    private String idAlwaysValidFlag;

    /**
     * 证件有效期
     */
    private String idExpireTime;

    /**
     * 扩展字段,主要服务前端交互的字段,由前端控制,不做校验。json格式
     */
    private String extendFileJson;

    /**
     * 开户保存类型 01-保存，02-退出
     */
    @NotBlank(message = "开户保存类型不能为空")
    private String openSaveType;


}
