package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.hkopenacct.declare.HkOpenAcctDeclareRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.declare.HkOpenAcctDeclareVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctDeclarationInfoDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 海外小程序步骤四,开户声明信息服务类
 * <AUTHOR>
 * @date 2024/1/22 19:10
 * @since JDK 1.8
 */
@Service
public class OpenAcctDeclareInfoService extends OpenAcctAbstractService{

    public void temporaryStorageOpenDeclareInfo(HkOpenAcctDeclareRequest request) {
        //保存redis缓存
        openAcctCatchService.saveHkOpenAcctDeclarationInfoDTO(getHkCusNo(), OpenAcctConvert.toHkOpenAcctDeclarationInfoDTO(request));
    }

    public HkOpenAcctDeclareVO queryOpenDeclareInfoDetail() {
        String hkCustNo = getHkCusNo();
        //查询缓存获取填写信息
        HkOpenAcctDeclarationInfoDTO hkOpenAcctDeclarationInfoDTO = openAcctCatchService.getHkOpenAcctDeclarationInfo(hkCustNo);
        //从缓存获取开户订单审核退回修改状态的错误信息,这里的信息是在 com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService.querySubmitResult 回写到缓存的。因为没有业务数据表
        String orderCheckReason = openAcctCatchService.getHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.DECLARATION_INFORMATION_CHECK_RESULT, hkCustNo);
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = JSON.parseArray(orderCheckReason, HkOpenAcctCheckVO.class);
        if (null != hkOpenAcctDeclarationInfoDTO) {
            return OpenAcctConvert.toOpenDeclareVO(hkOpenAcctDeclarationInfoDTO, hkOpenAcctCheckVOS);
        }
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getAccountDeclarationInfoDTO() && StringUtils.isNotBlank(orderInfoDTO.getDealNo())) {
            return OpenAcctConvert.toOpenDeclareVO(orderInfoDTO.getAccountDeclarationInfoDTO(), hkOpenAcctCheckVOS);
        }
        //添加默认值
        return HkOpenAcctDeclareVO.builder().openDeclareValueVO(HkOpenAcctDeclareVO.OpenDeclareValueVO.builder()
                .declareOne(YesNoEnum.NO.getCode())
                .declareTwo(YesNoEnum.NO.getCode())
                .declareThree(YesNoEnum.NO.getCode())
                .declareFour(YesNoEnum.NO.getCode())
                .build()).build();
    }

    public void saveOpenDeclareInfo(HkOpenAcctDeclareRequest request) {
        String hkCusNo = getHkCusNo();
        //保存redis缓存
        openAcctCatchService.saveHkOpenAcctDeclarationInfoDTO(getHkCusNo(), OpenAcctConvert.toHkOpenAcctDeclarationInfoDTO(request));
        //删除审核不通过的证件信息原因
        openAcctCatchService.removeHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.DECLARATION_INFORMATION_CHECK_RESULT, hkCusNo);
    }
}
