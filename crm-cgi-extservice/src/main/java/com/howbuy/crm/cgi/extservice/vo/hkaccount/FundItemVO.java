/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/23 22:10
 * @since JDK 1.8
 */
@Data
public class FundItemVO {
    /**
     * 参考市值
     */
    private String currencyMarketValue;

    /**
     * 参考净值
     */
    private String nav;

    /**
     * 净值日期 YYYY-MM-DD或者MM-DD
     */
    private String navDate;

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 当前收益（当前币种）
     */
    private String currentAssetCurrency;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 当前收益率
     */
    private String yieldRate;

    /**
     * 分红标志
     */
    private String navDivFlag;

    /**
     * 费后市值
     */
    private String currencyMarketValueExFee;

    /**
     * 累计应收管理费
     */
    private String receivManageFee;


    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFee;

    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;

    private String incomeCalStat;
    /**
     * 平衡因子转换完成 1-是 0-否
     */
    private String convertFinish;
    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;
}