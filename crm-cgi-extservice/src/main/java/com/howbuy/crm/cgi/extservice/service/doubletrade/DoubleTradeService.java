package com.howbuy.crm.cgi.extservice.service.doubletrade;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.doubletrade.service.VideoDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 双录公共服务
 * @Date 2024/6/19 18:36
 */
@Slf4j
@Service("doubleTradeService")
public class DoubleTradeService {

    @DubboReference(registry = "crm-td-server", check = false)
    private VideoDownloadService videoDownloadService;

    /**
     * 甜新通知回调，下载双录视频
     * @param txBizId
     * @return
     */
    public CgiResponse<Body> notice(String txBizId) {
        String[] arr = txBizId.split("-");
        if (arr.length < 2) {
            log.warn("DoubleTradeService notice bizId error, bizId={}", txBizId);
            return CgiResponse.error(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), ExceptionCodeEnum.REQUEST_PARAM_ERROR.getDescription(),null);
        }
        String appid = arr[0];
        String bizId = arr[1];
        log.info("DoubleTradeService notice param, appid={}, bizId={}", appid, bizId);
        int result = videoDownloadService.uploadVerifyVideo(bizId);
        if (result < 1) {
            log.error("DoubleTradeService notice uploadVerifyVideo error, bizId={}", txBizId);
        }
        Body body = new Body();
        body.setReturnCode(ExceptionCodeEnum.SUCCESS.getCode());
        body.setDescription(ExceptionCodeEnum.SUCCESS.getCode());
        return CgiResponse.ok(body);
    }

}
