
package com.howbuy.crm.cgi.extservice.common.enums.fund;

public enum FundStatEnum {
    PURCHASE_REDEMPTION("0", "可申购赎回"),
    ISSUE("1", "发行"),
    STOP_TRADE("4", "停止交易"),
    STOP_PURCHASE("5", "停止申购"),
    STOP_REDEMPTION("6", "停止赎回"),
    FUND_TERMINATION("8", "基金终止"),
    FUND_CLOSED("9", "基金封闭");

    private final String code;
    private final String desc;

    private FundStatEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
