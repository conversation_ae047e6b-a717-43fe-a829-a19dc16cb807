/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.annualbill;

import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.manager.domain.asset.annualasset.AnnualAssetIncomeDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/12 10:25
 * @since JDK 1.8
 */
@Data
public class AnnualAssetIncomeVO extends AccountBaseVO implements Serializable {

    /**
     * 总持仓
     */
    private String totalIncome;

    /**
     * 全年收益
     */
    private String annualIncome;

    /**
     * 全年收益率
     */
    private String annualIncomeRate;

    /**
     * 上年收益率
     */
    private String lastYearIncome;

    /**
     * 当前收益是否大于上年收益 0-否 1-是 当前年收益>0，且当前年收益>上年收益
     */
    private String isGreaterThanLastYear;


    public static AnnualAssetIncomeVO transToVO(AnnualAssetIncomeDTO annualAssetIncomeDTO) {
        AnnualAssetIncomeVO annualAssetIncomeVO = new AnnualAssetIncomeVO();
        annualAssetIncomeVO.setAnnualIncome(annualAssetIncomeDTO.getAnnualIncome() == null ? "0.00" : BigDecimalUtils.formatTh(annualAssetIncomeDTO.getAnnualIncome().setScale(2, RoundingMode.HALF_UP)));
        annualAssetIncomeVO.setAnnualIncomeRate(annualAssetIncomeDTO.getAnnualIncomeRate() == null ? "0" : BigDecimalUtils.formatTh(annualAssetIncomeDTO.getAnnualIncomeRate().setScale(2, RoundingMode.HALF_UP)));
        annualAssetIncomeVO.setLastYearIncome(annualAssetIncomeDTO.getLastYearIncome() == null ? "0.00" : BigDecimalUtils.formatTh(annualAssetIncomeDTO.getLastYearIncome().setScale(2, RoundingMode.HALF_UP)));
        annualAssetIncomeVO.setIsGreaterThanLastYear(annualAssetIncomeDTO.getIsGreaterThanLastYear());
        annualAssetIncomeVO.setTotalIncome(annualAssetIncomeDTO.getTotalIncome() != null ? BigDecimalUtils.formatTh(annualAssetIncomeDTO.getTotalIncome().setScale(2, RoundingMode.HALF_UP)) : "0.00");
        return annualAssetIncomeVO;

    }
}