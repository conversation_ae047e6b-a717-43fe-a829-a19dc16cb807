/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.security.Base64;
import com.howbuy.security.des.DESedeUtil;
import lombok.extern.slf4j.Slf4j;

import java.security.Key;

/**
 * @description: (参数工具类(加密解密等)
 * <AUTHOR>
 * @date 2024/3/12 10:32
 * @since JDK 1.8
 */
@Slf4j
public class ParamsUtils {

    public static String getEncValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        } else {
            try {
                Key key = DESedeUtil.getKey("1234567890__howbuy__abcdefghij".getBytes());
                return Base64.encode(DESedeUtil.encrypt(value.getBytes("UTF-8"), key).getBytes());
            } catch (Exception var2) {
                var2.printStackTrace();
                log.error(var2.getMessage(), var2);
                return null;
            }
        }
    }
}