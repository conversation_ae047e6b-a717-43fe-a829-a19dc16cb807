/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy.factory;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.FundValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.validator.handle.*;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 根据不同的业务类型,返回对应的检验处理器
 * @date 2024/7/19 14:44
 * @since JDK 1.8
 */
@Component
public class PiggyValidatorFactory {

    /**
     * @description: 根据不同的业务检验枚举,返回对应的检验处理器
     * @param context 上下文信息
     * @return com.howbuy.crm.cgi.extservice.validator.PiggyVerificationHandle
     * @author: jinqing.rao
     * @date: 2024/7/19 17:46
     * @since JDK 1.8
     */
    public VerificationHandle<PiggyBankVerificationVO> createPiggyVerificationDirector(PiggyVerificationContext context) {
        // 海外储蓄罐签约
        if (FundValidatorTypeEnum.PIGGY_SIGN_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            return new PiggySignVerificationHandle(context);
        }
        //海外储蓄罐打款凭证
        if (FundValidatorTypeEnum.PIGGY_DEPOSIT_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            return new PiggyVoucherVerificationHandle(context);
        }
        //海外储蓄罐变更
        if (FundValidatorTypeEnum.PIGGY_CHANGE_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            return new PiggyChangeVerificationHandle(context);
        }
        //海外储蓄罐变更关闭
        if (FundValidatorTypeEnum.PIGGY_CHANGE_CLOSE_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            return new PiggyChangeCloseVerificationHandle(context);
        }
        //储蓄罐线上关闭
        if (FundValidatorTypeEnum.PIGGY_SIGN_LIST_CLOSE_VALIDATOR.equals(context.getValidatorTypeEnum())) {
            return new PiggySignListVerificationHandle(context);
        }
        throw new BusinessException(ExceptionCodeEnum.FUND_VALIDATOR_TYPE_ERROR);
    }
}
