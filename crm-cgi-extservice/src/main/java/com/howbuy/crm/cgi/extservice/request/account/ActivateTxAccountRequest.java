/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * @description: (交易账号激活接口)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class ActivateTxAccountRequest extends AccountBaseRequest {
    /**
     * 手机号摘要  二选一
     */
    private String mobileDigest;
    /**
     * 邮箱地址摘要  二选一
     */
    private String emailDigest;
    /**
     * 证件类型  必须
     */
    private String idType;
    /**
     * 证件号码  必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "证件号码", isRequired = true)
    private String idNo;
    /**
     * 验证码  必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码", isRequired = true)
    private String verifyCode;
    /**
     * 交易密码  必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易密码", isRequired = true)
    private String txPassword;
}