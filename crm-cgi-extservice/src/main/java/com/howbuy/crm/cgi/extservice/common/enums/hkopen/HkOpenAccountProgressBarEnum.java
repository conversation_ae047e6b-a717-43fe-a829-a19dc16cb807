/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: App开户进度枚举类
 *
 * 填写资料(1/6)：选择地区&证件类型   开户资料填写进度：1%
 * 填写资料(2/6)：填写个人信息-交互明细   开户资料填写进度：13%
 * 填写资料(3/6)：填写职业及税务信息   开户资料填写进度：25%
 * 填写资料(4/5)：填写个人情况声明信息  开户资料填写进度：38%
 * 填写资料(5/5)：投资经验及目的(含风测)  开户资料填写进度：50%
 * 填写资料(5/5)绑定银行卡：填写银行账户信息
 * 开户资料填写进度：63%
 * 文件签署(1/2)：风险披露
 * 开户资料填写进度：75%
 * 文件签署(2/2)：电子签名
 * 开户资料填写进度：88%
 * <AUTHOR>
 * @date 2024/2/29 11:15
 * @since JDK 1.8
 */
public enum HkOpenAccountProgressBarEnum {

    ONE_PERCENT(HkOpenAccountStepEnum.IDENTITY_INFORMATION,"1"),

    THIRTEEN_PERCENT(HkOpenAccountStepEnum.PERSONAL_INFORMATION,"13"),

    TWENTY_FIVE_PERCENT(HkOpenAccountStepEnum.PROFESSIONAL_INFORMATION,"25"),

    THIRTY_EIGHT_PERCENT(HkOpenAccountStepEnum.DECLARATION_INFORMATION,"38"),

    FIFTY_PERCENT(HkOpenAccountStepEnum.INVESTMENT_EXPERIENCE,"50"),

    SIXTY_THREE_PERCENT(HkOpenAccountStepEnum.BANK_CARD,"63"),

    SEVENTY_FIVE_PERCENT(HkOpenAccountStepEnum.RISK_DISCLOSURE,"75"),

    EIGHTY_EIGHT_PERCENT(HkOpenAccountStepEnum.ELECTRONIC_SIGNATURE,"88%")
    ;


    private final HkOpenAccountStepEnum hkOpenAccountStepEnum;

    private final String progressBarName;

    /**
     * 构造函数
     */
     HkOpenAccountProgressBarEnum(HkOpenAccountStepEnum hkOpenAccountStepEnum, String progressBarName) {
        this.hkOpenAccountStepEnum = hkOpenAccountStepEnum;
        this.progressBarName = progressBarName;
    }
    
    /**
     * @description: 通过HkOpenAccountStepEnum的code获取对应的progressBarName
     * @param hkOpenAccountStepEnum
     * @return  String
     * @author: jinqing.rao
     * @date: 2024/2/29 11:27
     * @since JDK 1.8
     */
    public static String getProgressBarNameByName(HkOpenAccountStepEnum hkOpenAccountStepEnum) {
        if(null == hkOpenAccountStepEnum){
            return null;
        }
        for (HkOpenAccountProgressBarEnum hkOpenAccountProgressBarEnum : HkOpenAccountProgressBarEnum.values()) {
            //优先匹配名称
            if (hkOpenAccountProgressBarEnum.hkOpenAccountStepEnum.name().equals(hkOpenAccountStepEnum.name())) {
                //子功能判断
                return hkOpenAccountProgressBarEnum.progressBarName;
            }
            //匹配code
            if (hkOpenAccountProgressBarEnum.hkOpenAccountStepEnum.getCode().equals(hkOpenAccountStepEnum.getCode())) {
                //子功能判断
                return hkOpenAccountProgressBarEnum.progressBarName;
            }
        }
        return null;
    }
}

