/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsorder;

import com.howbuy.crm.cgi.extservice.vo.DtmsBaseVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.PreBookInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (查询海外产品电子签约列表)
 * <AUTHOR>
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
@Data
public class DigitalSignListVO extends DtmsBaseVO implements Serializable {
    /**
     *待签约记录数
     */
    private String needSignCount;
    /**
     * 总数
     */
    private String total;
    /**
     *签约订单列表
     */
    private List<PreBookInfoVO> orderList;

}