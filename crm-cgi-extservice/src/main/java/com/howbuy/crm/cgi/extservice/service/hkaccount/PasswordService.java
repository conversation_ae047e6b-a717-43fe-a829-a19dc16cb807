package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.cachemanagement.util.EncryptionUtils;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.MessageTempleteEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.extservice.common.enums.PasswordTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.ExceptionUtil;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.common.utils.PasswordValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCommonDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPasswordOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 密码服务
 * @date 2023/6/6 14:41
 * @since JDK 1.8
 */
@Slf4j
@Service("passwordService")
public class PasswordService {

    @Autowired
    private HkPasswordOuterService hkPasswordOuterService;

    @Autowired
    private VerifyCodeService verifyCodeService;

    @Autowired
    private CustInfoService custInfoService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    /**
     * @description: 修改登录密码
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 15:35
     * @since JDK 1.8
     */
    public void changeLoginPassword(ChangeLoginPasswordRequest request) {
        PasswordValidator.validateOldNewPwdEqual(request.getOldLoginPassword(), request.getNewLoginPassword());
        PasswordValidator.validateLoginPassword(request.getNewLoginPassword());
        String hkCustNo = getHkCustNo();
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.changeLoginPassword(hkCustNo, PasswordTypeEnum.LOGIN_PASSWORD.getKey(),
                PassWordUtil.encrypt(request.getOldLoginPassword()), PassWordUtil.encrypt(request.getNewLoginPassword()));
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);
    }

    /**
     * @description: 修改交易密码
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 15:35
     * @since JDK 1.8
     */
    public void changeTradePassword(ChangeTradePasswordRequest request) {
        PasswordValidator.validateOldNewPwdEqual(request.getOldTradePassword(), request.getNewTradePassword());
        String hkCustNo = getHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(hkCustNo);
        PasswordValidator.validateTxPassword(request.getNewTradePassword(), hkCustInfoDTO.getBirthday());

        HkCommonDTO hkCommonDTO = hkPasswordOuterService.changeTradePassword(hkCustNo, PasswordTypeEnum.TRADE_PASSWORD.getKey(),
                PassWordUtil.encrypt(request.getOldTradePassword()), PassWordUtil.encrypt(request.getNewTradePassword()));
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);
    }

    private static String getHkCustNo() {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        return hkCustNo;
    }

    /**
     * @description: 重置登录密码
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 15:36
     * @since JDK 1.8
     */
    public void resetLoginPassword(ResetLoginPasswordRequest request) {
        ParamsValidator.validateParams(request, "mobile#email");
        PasswordValidator.validateLoginPassword(request.getNewLoginPassword());
        checkResetLoginPasswordVerifyCode(request.getMobile(), request.getEmail(), request.getVerifyCode());
        String mobileDigest = DigestUtil.digest(request.getMobile());
        String emailDigest = DigestUtil.digest(request.getEmail());
        // 未登录
        HkCustInfoDTO hkCustInfoDTO;
        if (StringUtils.isNotEmpty(request.getMobile())) {
            hkCustInfoDTO = custInfoService.getHkCustInfoByMobile(Constants.DEFAULT_MOBILE_AREA_CODE, mobileDigest);
        } else {
            hkCustInfoDTO = custInfoService.getHkCustInfoByEmail(emailDigest);
        }
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.resetPassword(hkCustInfoDTO.getHkCustNo(), mobileDigest, emailDigest,
                PasswordTypeEnum.LOGIN_PASSWORD.getKey(), PassWordUtil.encrypt(request.getNewLoginPassword()), null);
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);
    }

    /**
     * @description: 重置交易密码
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 15:36
     * @since JDK 1.8
     */
    public void resetTradePassword(ResetTradePasswordRequest request) {
        ParamsValidator.validateParams(request, "mobile#email");
        checkResetTradePasswordVerifyCode(request.getMobile(), request.getEmail(), request.getVerifyCode());
        // 已登录
        String hkCustNo = getHkCustNo();
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(hkCustNo);
        PasswordValidator.validateTxPassword(request.getNewTradePassword(), hkCustInfoDTO.getBirthday());

        String mobileDigest = DigestUtil.digest(request.getMobile());
        String emailDigest = DigestUtil.digest(request.getEmail());
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.resetPassword(hkCustNo, mobileDigest, emailDigest,
                PasswordTypeEnum.TRADE_PASSWORD.getKey(), null, PassWordUtil.encrypt(request.getNewTradePassword()));
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);
    }

    /**
     * @description: 设置登录密码
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 15:36
     * @since JDK 1.8
     */
    public void setLoginPassword(SetLoginPasswordRequest request) {
        PasswordValidator.validateLoginPassword(request.getNewLoginPassword());
        String hkCustNo = getHkCustNo();
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.resetPassword(hkCustNo, null, null,
                PasswordTypeEnum.LOGIN_PASSWORD.getKey(), PassWordUtil.encrypt(request.getNewLoginPassword()), null);
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);
    }

    /**
     * @description: 校验重置登录密码验证码
     * @param: [mobile, email, verifyCode]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 15:36
     * @since JDK 1.8
     */
    private void checkResetLoginPasswordVerifyCode(String mobile, String email, String verifyCode) {
        if (StringUtils.isNotEmpty(mobile)) {
            verifyCodeService.checkSmsVerifyCode(mobile, verifyCode, MessageTempleteEnum.RESET_LOGIN_PASSWORD_MOBILE_VERIFYCODE);
        } else {
            verifyCodeService.checkEmailVerifyCode(email, verifyCode, MessageTempleteEnum.RESET_LOGIN_PASSWORD_EMAIL_VERIFYCODE);
        }
    }

    /**
     * @description: 校验重置交易密码验证码
     * @param: [mobile, email, verifyCode]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 19:32
     * @since JDK 1.8
     */
    private void checkResetTradePasswordVerifyCode(String mobile, String email, String verifyCode) {
        if (StringUtils.isNotEmpty(mobile)) {
            verifyCodeService.checkSmsVerifyCode(mobile, verifyCode, MessageTempleteEnum.RESET_TRADE_PASSWORD_MOBILE_VERIFYCODE);
        } else {
            verifyCodeService.checkEmailVerifyCode(email, verifyCode, MessageTempleteEnum.RESET_TRADE_PASSWORD_EMAIL_VERIFYCODE);
        }
    }

    /**
     * @description: 验证码校验接口
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.AccountBaseVO
     * @author: jinqing.rao
     * @date: 2024/4/12 10:23
     * @since JDK 1.8
     */
    public void checkTradePassword(CheckTradePasswordRequest request) {
        //校验次数拦截  产品本次需求不加
        //校验交易密码
        try {
            hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        } catch (Exception e) {
            // 统一拦截 校验错误信息
            throw new BusinessException(ExceptionCodeEnum.TXPASSWORD_ERROR.getCode(),
                    ExceptionCodeEnum.TXPASSWORD_ERROR.getDescription());
        }
    }

    /**
     * 校验登录密码
     * @param request
     */
    public void checkLoginPassword(CheckLoginPasswordRequest request) {
        try {
            //校验登录密码
            hkCustInfoOuterService.validateHkLoginPassword(request.getHkCustNo(), request.getPassword());
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.LOGIN_PASSWORD_ERROR.getCode(),
                    ExceptionCodeEnum.LOGIN_PASSWORD_ERROR.getDescription());
        }
    }
}
