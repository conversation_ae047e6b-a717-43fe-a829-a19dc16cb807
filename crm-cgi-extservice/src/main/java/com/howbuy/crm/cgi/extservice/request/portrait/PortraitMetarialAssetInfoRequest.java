package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户画像-资配报告信息req
 * @Date 2024/9/11 10:31
 */
public class PortraitMetarialAssetInfoRequest implements Serializable {

    private static final long serialVersionUID = -2693144503501867632L;

    /**
     * 报告id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "报告id", isRequired = true)
    private String assetId;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }
}
