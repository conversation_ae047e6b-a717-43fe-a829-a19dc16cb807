package com.howbuy.crm.cgi.extservice.request.account.hkapp;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 海外APP小程序广告位请求对象
 * <AUTHOR>
 * @date 2024/2/29 13:55
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAcctAppAdvertisRequest extends AccountBaseRequest implements Serializable {


    private static final long serialVersionUID = -1812972294186894587L;

    /**
     * 香港客户号 加密
     */
    @NotBlank(message = "香港客户号参数错误")
    private String hkCustNo;
    /**
     * 广告的信息位置 APP个人中心广告位 ： 1
     */
    @NotBlank(message = "广告位置参数错误")
    private String position;

    /**
     * 图片的长度
     */
    private String length;

    /**
     * 图片的宽度
     */
    private String width;
}
