/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (省市区对象)
 * <AUTHOR>
 * @date 2023/12/14 16:55
 * @since JDK 1.8
 */
@Data
public class ProvinceCityCoutryVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 具体的数据列表
     */
    private List<CityListVO> cityListVO;

}