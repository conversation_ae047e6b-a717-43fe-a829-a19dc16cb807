/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * @description: 海外开户(香港), 开户状态枚举
 * <AUTHOR>
 * @date 2023/12/4 15:25
 * @since JDK 1.8
 */
public enum HkOpenAcctStatusEnum {

    NORMAL("0", "正常"),
    CANCELLED("1", "注销"),
    DORMANT("2", "休眠"),
    REGISTERED("3", "注册"),
    APPLICATION_SUCCESS("4", "开户申请成功");

    /**
     *枚举编码
     */
    private final String code;
    /**
     *枚举描述
     */
    private final String desc;

    HkOpenAcctStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

