package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 赎回去向 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：支票
 * @author: shuai.zhang
 * @date: 2023/5/29
 * @since JDK 1.8
 * @version: 1.0
 */
public enum RedeemDirectionEnum {

    TELEGRAPHICTRANSFER("1", "电汇"),
    KEEPACCOUNT("2", "留账"),
    OVERSEASPIGGY("3", "海外储蓄罐"),
    CHECK("4", "支票");

    private final String key;
    private final String desc;

    public static RedeemDirectionEnum getRedeemDirectionEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        RedeemDirectionEnum redeemDirectionEnum = getRedeemDirectionEnum(code);
        return redeemDirectionEnum == null ? null : redeemDirectionEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    RedeemDirectionEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
