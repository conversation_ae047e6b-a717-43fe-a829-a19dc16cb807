/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.GueryConsCodeByUserIdRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBaseRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.entrance.EntranceValidateRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.entrance.QueryCustListRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.entrance.PortraitEntranceService;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.QueryConsCodeByUserIdVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.entrance.CustLessListVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.entrance.EntranceValidateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 入口控制器
 * @date 2024/9/3 9:49
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/portrait/entrance")
public class PortraitEntranceController {

    @Resource
    private PortraitEntranceService portraitEntranceService;


    /**
     * @api {POST} /ext/portrait/entrance/validatewechatbind validateWechatBind()
     * @apiVersion 1.0.0
     * @apiGroup PortraitEntranceController
     * @apiName validateWechatBind()
     * @apiDescription 校验微信绑定
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"Z7aUS7h36F"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.conscode 投顾编号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"sxEHkjWws","description":"GElC","timestampServer":"fqFc"}
     */
    @PostMapping("/validatewechatbind")
    @ResponseBody
    public CgiResponse<EntranceValidateVO> validateWechatBind(@RequestBody PortraitBaseRequest request) {
        return CgiResponse.appOk(portraitEntranceService.validateWechatBind(request));
    }


    /**
     * @api {POST} /ext/portrait/entrance/validatewechatbindandconsrel validateWechatBindAndConsRel()
     * @apiVersion 1.0.0
     * @apiGroup PortraitEntranceController
     * @apiName validateWechatBindAndConsRel()
     * @apiDescription 校验微信绑定和投顾关系
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} userId 用户id
     * @apiParam (请求体) {String} externalUserId 外部联系人id
     * @apiParamExample 请求体示例
     * {"externalUserId":"Gh805wU9","userId":"rRnvunDo"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.conscode 投顾编号
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"hNT7SF0hNN","description":"5zo7Qx","timestampServer":"QogH"}
     */
    @PostMapping("/validatewechatbindandconsrel")
    @ResponseBody
    public CgiResponse<EntranceValidateVO> validateWechatBindAndConsRel(@RequestBody EntranceValidateRequest request) {
        return CgiResponse.appOk(portraitEntranceService.validateWechatBindAndConsRel(request));
    }

    /**
     * @api {POST} /ext/portrait/entrance/querycustlistbyuserid queryCustListByUserId()
     * @apiVersion 1.0.0
     * @apiGroup PortraitEntranceController
     * @apiName queryCustListByUserId()
     * @apiDescription 入口查询客户列表
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} userId 用户Id
     * @apiParam (请求体) {String} searchContent 查询内容
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"pageNo":2011,"pageSize":125,"userId":"Xk1","searchContent":"UhSjxMOw"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.custLessInfoList 客户较少信息列表
     * @apiSuccess (响应结果) {String} data.custLessInfoList.custName 客户姓名
     * @apiSuccess (响应结果) {String} data.custLessInfoList.conscustNo 投顾客户号
     * @apiSuccess (响应结果) {String} data.custLessInfoList.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} data.custLessInfoList.mobileMask 手机号掩码
     * @apiSuccess (响应结果) {String} data.custLessInfoList.labelCustStateEnumKey 标签-客户状态
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Hm20vVJVYm","data":{"custInfoList":[{"conscustNo":"VUtf","custName":"wLg4LX","labelCustStateDesc":"zEbtKUf","hboneNo":"Eiu0GEe6Z","mobileMask":"MOJd4"}]},"description":"s","timestampServer":"kGca3Ryenb"}
     */
    @PostMapping("/querycustlistbyuserid")
    @ResponseBody
    public CgiResponse<CustLessListVO> queryCustListByUserId(@RequestBody QueryCustListRequest request) {
        return CgiResponse.appOk(portraitEntranceService.queryCustListByUserId(request));
    }

    /**
     * @api {POST} /ext/portrait/entrance/getconscodebyuserid getConsCodeByUserId()
     * @apiVersion 1.0.0
     * @apiGroup PortraitEntranceController
     * @apiName getConsCodeByUserId()
     * @apiDescription 根据用户ID获取投顾编号接口
     *
     * @apiParam (请求体) {String} userId 用户ID
     *
     * @apiParamExample 请求参数示例:
     * {
     *   "userId": "user123"
     * }
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.conscode 投顾编号
     * @apiSuccess (响应结果) {String} timestampServer
     *
     * @apiSuccessExample 响应结果示例:
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "conscode": "TA123456"
     *   },
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/getconscodebyuserid")
    @ResponseBody
    public CgiResponse<QueryConsCodeByUserIdVO> getConsCodeByUserId(@RequestBody GueryConsCodeByUserIdRequest request) {
        QueryConsCodeByUserIdVO responseVO = portraitEntranceService.getConsCodeByUserId(request);
        return CgiResponse.appOk(responseVO);
    }
}
