/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.AssetsDetailVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.AssetFundTypeVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/23 23:53
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TotalAssetDetailInfoVO extends AccountBaseVO implements Serializable {

    /**
     * 总资产
     */
    private String totalAsset;

    /**
     * 是否显示资产 [资产小眼睛] (0:显示, 1: 隐藏)
     */
    private String showAsset;

    /**
     * 总资产明细弹窗内容列表
     */
    private List<AssetsDetailVO> totalAssetsDetail;

    /**
     * 在途资产总额
     */
    private String inTransitTradeAmt;

    /**
     * 在途资产总额是否显示标志
     */
    private String inTransitTradeAmtFlag;

    /**
     * 类型基金持仓列表
     */
    private List<AssetFundTypeVO> assetFundTypeList;

    /**
     * 现金余额
     */
    private String cashBalanceAsset;

    /**
     * 现金余额是否显示标志 1 是 0 否
     */
    private String cashBalanceAssetFlag;


}