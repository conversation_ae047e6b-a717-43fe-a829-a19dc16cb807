/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.common.base.PageRequest;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 查询线下活动请求
 * @Date 2024/10/21 10:11
 */
public class QueryOfflineActivityRequest extends PageRe<PERSON> implements Serializable {

    private static final long serialVersionUID = -6517102127317222926L;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }
}
