/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.financialnine;

import com.google.common.collect.Lists;
import com.howbuy.cms.dto.base.yxs.SmYxsCoursePlanDTO;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryFinancialNineLessonsLabelRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryFinancialNineProgressRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryFinancialNineStudyListRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.QueryOfflineActivityRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.FinancialNineProgressVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.FinancialNineStudyVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.NineLessonsLabelVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.financialnine.OfflineActivityVO;
import com.howbuy.crm.cgi.manager.outerservice.cms.CmsSmYxsCourseOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmcore.QueryHbconstantOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmnt.CmConferenceOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.DsLabelValueDTO;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.QueryCustLabelOuterService;
import com.howbuy.crm.nt.conference.dto.CmPortraitStudyDetailDTO;
import com.howbuy.crm.nt.conference.dto.CmPortraitStudyProgressDTO;
import com.howbuy.crm.nt.conference.response.QueryCmConferenceStudyResponse;
import com.howbuy.crm.nt.conference.response.QueryCmOfflineActivityResponse;
import com.howbuy.crm.portrait.client.enums.LabelEnum;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.utils.ParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 客户画像理财九章服务
 * @Date 2024/10/21 13:30
 */
@Slf4j
@Service
public class PortraitFinancialNineService {

    @Resource
    private QueryCustLabelOuterService queryCustLabelOuterService;

    @Resource
    private CmConferenceOuterService cmConferenceOuterService;

    @Resource
    private QueryHbconstantOuterService queryHbconstantOuterService;

    @Resource
    private CmsSmYxsCourseOuterService cmsSmYxsCourseOuterService;

    // 理财九章标签 [050206-上次学习时间,050207-讲师偏好,050208-报名次数,050209-参会次数,050210-参会率]
    private final List<String> FINANCIAL_NINE_LABEL_LIST = Lists.newArrayList(LabelEnum.LABEL_050206.getId(), LabelEnum.LABEL_050207.getId(),
            LabelEnum.LABEL_050208.getId(), LabelEnum.LABEL_050209.getId(), LabelEnum.LABEL_050210.getId());

    // 线下活动标签 [050301-线下活动参加次数,050302-线下活动偏好]
    private final List<String> OFFLINE_ACTIVITY_LABEL_LIST = Lists.newArrayList(LabelEnum.LABEL_050301.getId(), LabelEnum.LABEL_050302.getId());

    /**
     * 全量的 会议类型  typeCode=conferenceType
     */
    public static final String FULL_CONFERENCE_TYPE = "conferenceType";

    /**
     * 理财九章 的 会议类型  typeCode=newconferenceOptType
     */
    public static final String LCJZ_CONFERENCE_TYPE = "newconferenceOptType";

    /**
     * 理财九章的大类类型-9
     */
    public static final String FINANCIAL_NINE_TYPE = "9";

    /**
     * 查询客户理财九章标签信息
     *
     * @param request req
     * @return NineLessonsLabelVO
     */
    public NineLessonsLabelVO queryFinancialNineLabel(QueryFinancialNineLessonsLabelRequest request) {
        NineLessonsLabelVO vo = new NineLessonsLabelVO();
        // 查询客户理财九章标签
        List<DsLabelValueDTO> dsLabelValueDTOList = queryCustLabelOuterService.queryCustMultiLabel(request.getHboneNo(), FINANCIAL_NINE_LABEL_LIST);
        Map<String, String> labelValueMap = dsLabelValueDTOList.stream()
                .collect(Collectors.toMap(DsLabelValueDTO::getLabelId, DsLabelValueDTO::getLabelValue, (v1, v2) -> v2));

        // 上次学习时间
        String lastStudyTime = labelValueMap.get(LabelEnum.LABEL_050206.getId());
        lastStudyTime = getLastStudyTime(lastStudyTime);
        vo.setLastStudyTime(lastStudyTime);

        // 讲师偏好
        vo.setTeacherPreference(labelValueMap.get(LabelEnum.LABEL_050207.getId()));
        // 报名次数
        vo.setRegisterCount(labelValueMap.get(LabelEnum.LABEL_050208.getId()));
        // 参会次数
        vo.setAttendCount(labelValueMap.get(LabelEnum.LABEL_050209.getId()));
        // 参会率
        vo.setAttendRatio(labelValueMap.get(LabelEnum.LABEL_050210.getId()));
        return vo;
    }

    /**
     * 查询客户理财九章学习进度
     *
     * @param request req
     * @return FinancialNineProgressVO
     */
    public FinancialNineProgressVO queryFinancialNineProgress(QueryFinancialNineProgressRequest request) {
        FinancialNineProgressVO vo = new FinancialNineProgressVO();

        // 查询全量的会议类型Map
        Map<String, String> lcjzConferenceMap = queryHbconstantOuterService.queryHbconstantMap(LCJZ_CONFERENCE_TYPE);
        Map<String, String> fullConferenceMap = queryHbconstantOuterService.queryHbconstantMap(FULL_CONFERENCE_TYPE);

        if (MapUtils.isEmpty(lcjzConferenceMap)) {
            log.error("获取理财九章会议类型Map为空！hboneNo={}", request.getHboneNo());
            return vo;
        }

        // 获取理财九章的会议类型Map
        Map<String, String> financialNineTypeMap = lcjzConferenceMap.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(FINANCIAL_NINE_TYPE) && !FINANCIAL_NINE_TYPE.equals(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        // 按照子类型升序排序
        List<String> financialNineTypeList = financialNineTypeMap.keySet().stream()
                .sorted(Comparator.comparing(String::valueOf))
                .collect(Collectors.toList());

        List<CmPortraitStudyProgressDTO> progressDTOList = cmConferenceOuterService.queryCmConferenceList(request.getHboneNo());
        if (CollectionUtils.isEmpty(progressDTOList)) {
            log.info("查询客户画像-理财九章用户参会数据为空! 一账通号:{}", request.getHboneNo());
            return vo;
        }

        // 理财九章会议类型学习次数map
        Map<String, Integer> map = new HashMap<>();
        for (CmPortraitStudyProgressDTO progressDTO : progressDTOList) {
            if (progressDTO.getActualnub() <= 0) {
                continue;
            }
            List<String> translateTypeDesc = this.translateTypeDescList(progressDTO.getConferenceType(), progressDTO.getIslcjz(), lcjzConferenceMap, fullConferenceMap);
            if (CollectionUtils.isEmpty(translateTypeDesc)) {
                continue;
            }

            for (String conferenceName : translateTypeDesc) {
                String name = handleConferenceName(conferenceName);
                Integer num = map.getOrDefault(name, 0);
                map.put(name, num + 1);
            }
        }

        List<FinancialNineProgressVO.FinancialNineLessonsProgress> list = new ArrayList<>();
        for (int i = 0; i < financialNineTypeList.size(); i++) {
            FinancialNineProgressVO.FinancialNineLessonsProgress progress = new FinancialNineProgressVO.FinancialNineLessonsProgress();
            // 序号
            progress.setLessonsSerialNo(String.valueOf(i + 1));
            // 会议类型
            String financialNineType = financialNineTypeList.get(i);
            // 获取会议类型名称
            String financialNineName = financialNineTypeMap.get(financialNineType);
            String showConferenceName = getShowConferenceName(financialNineName);
            progress.setLessonsName(showConferenceName);
            // 学习次数
            Integer count = map.getOrDefault(handleConferenceName(financialNineName), 0);
            progress.setStudyCount(String.valueOf(count));
            // 学习状态
            progress.setStudyState(count > 0 ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            list.add(progress);
        }
        vo.setTotal(String.valueOf(list.size()));
        vo.setLessonsProgressList(list);
        return vo;
    }

    /**
     * 分页查询客户查询理财九章学习明细接口
     *
     * @param request req
     * @return FinancialNineStudyVO
     */
    public FinancialNineStudyVO queryFinancialNineStudyList(QueryFinancialNineStudyListRequest request) {
        FinancialNineStudyVO vo = new FinancialNineStudyVO();
        QueryCmConferenceStudyResponse response = cmConferenceOuterService.queryCmConferenceStudyPageList(request.getHboneNo(), request.getPage(), request.getSize());
        if (null == response || CollectionUtils.isEmpty(response.getDataList())) {
            log.info("查询客户画像-理财九章学习明细数据为空! 一账通号:{}, page:{}, size:{}", request.getHboneNo(), request.getPage(), request.getSize());
            return vo;
        }

        vo.setTotal(String.valueOf(response.getTotal()));
        List<CmPortraitStudyDetailDTO> dataList = response.getDataList();
        List<FinancialNineStudyVO.FinancialNineStudy> list = new ArrayList<>();
        vo.setStudyList(list);

        // 查询全量的会议类型Map
        Map<String, String> lcjzConferenceMap = queryHbconstantOuterService.queryHbconstantMap(LCJZ_CONFERENCE_TYPE);
        Map<String, String> fullConferenceMap = queryHbconstantOuterService.queryHbconstantMap(FULL_CONFERENCE_TYPE);

        // 获取会议课程id列表
        List<String> courseIds = dataList.stream()
                .map(CmPortraitStudyDetailDTO::getCourseId)
                .filter(StringUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        // 课程id查询课程信息
        Map<String, SmYxsCoursePlanDTO> courseMap = cmsSmYxsCourseOuterService.queryCourseMap(courseIds);

        for (CmPortraitStudyDetailDTO detailDTO : dataList) {
            FinancialNineStudyVO.FinancialNineStudy study = new FinancialNineStudyVO.FinancialNineStudy();
            String lessonsName = this.getConferenceNameStr(detailDTO, lcjzConferenceMap, fullConferenceMap);
            // 课程名称 （取会议类型，多个、拼接）
            study.setLessonsName(getShowConferenceName(lessonsName));
            // 会议地点
            study.setMeetingAdress(StringUtil.null2String(detailDTO.getCityName()));
            // 会议时间
            String conferenceDt = detailDTO.getConferenceDt();
            String date = DateUtil.format(conferenceDt, DateUtil.STR_PATTERN, DateUtil.DEFAULT_MINUS);
            study.setMeetingTime(StringUtil.null2String(date));
            // 讲师
            SmYxsCoursePlanDTO course = courseMap.getOrDefault(detailDTO.getCourseId(), new SmYxsCoursePlanDTO());
            study.setTeacher(StringUtil.null2String(course.getLecturer()));
            list.add(study);
        }

        return vo;
    }

    /**
     * 查询线下活动标签
     *
     * @param request req
     * @return OfflineActivityVO
     */
    public OfflineActivityVO queryOfflineActivityLabel(QueryOfflineActivityRequest request) {
        OfflineActivityVO vo = new OfflineActivityVO();
        // 查询客户线下活动标签
        List<DsLabelValueDTO> dsLabelValueDTOList = queryCustLabelOuterService.queryCustMultiLabel(request.getHboneNo(), OFFLINE_ACTIVITY_LABEL_LIST);
        Map<String, String> labelValueMap = dsLabelValueDTOList.stream()
                .collect(Collectors.toMap(DsLabelValueDTO::getLabelId, DsLabelValueDTO::getLabelValue, (v1, v2) -> v2));

        // 参会次数
        vo.setAttendCount(labelValueMap.get(LabelEnum.LABEL_050301.getId()));
        // 活动偏好
        vo.setActivityPreference(labelValueMap.get(LabelEnum.LABEL_050302.getId()));

        QueryCmOfflineActivityResponse response = cmConferenceOuterService.queryOfflineActivityPageList(request.getHboneNo(), request.getPage(), request.getSize());
        if (null == response || CollectionUtils.isEmpty(response.getDataList())) {
            log.info("查询客户画像-理财九章线下活动列表数据为空! 一账通号:{}, page:{}, size:{}", request.getHboneNo(), request.getPage(), request.getSize());
            return vo;
        }

        List<OfflineActivityVO.AttendMeeting> list = new ArrayList<>();
        for (CmPortraitStudyDetailDTO detailDTO : response.getDataList()) {
            OfflineActivityVO.AttendMeeting attendMeeting = new OfflineActivityVO.AttendMeeting();
            // 会议名称
            attendMeeting.setMeetingName(getShowConferenceName(detailDTO.getConferenceName()));
            // 会议地点
            attendMeeting.setMeetingAdress(StringUtil.null2String(detailDTO.getCityName()));
            // 会议时间
            String conferenceDt = detailDTO.getConferenceDt();
            String date = DateUtil.format(conferenceDt, DateUtil.STR_PATTERN, DateUtil.DEFAULT_MINUS);
            attendMeeting.setMeetingTime(StringUtil.null2String(date));
            list.add(attendMeeting);
        }

        vo.setTotal(String.valueOf(response.getTotal()));
        vo.setAttendMeetingList(list);
        return vo;
    }

    /**
     * 翻译多个会议类型
     *
     * @param conferenceType
     * @param isLcjz
     * @param lcjzTypeMap
     * @param nonLcjzTypeMap
     * @return
     */
    private List<String> translateTypeDescList(String conferenceType,
                                               String isLcjz,
                                               Map<String, String> lcjzTypeMap,
                                               Map<String, String> nonLcjzTypeMap) {
        Map<String, String> usedTypeMap = YesOrNoEnum.YES.getCode().equals(isLcjz) ? lcjzTypeMap : nonLcjzTypeMap;
        if (crm.howbuy.base.utils.StringUtil.isNotNullStr(conferenceType)) {
            List<String> typeCodeList = ParamUtil.getParamList(conferenceType, Lists.newArrayList());

            //类型 描述 翻译
            return typeCodeList.stream()
                    .map(code -> handleConferenceName(usedTypeMap.getOrDefault(code, code)))
                    .distinct()
                    .collect(Collectors.toList());

        }
        return null;
    }

    /**
     * 翻译单个会议类型
     *
     * @param conferenceType
     * @param isLcjz
     * @param lcjzTypeMap
     * @param nonLcjzTypeMap
     * @return
     */
    private String translateTypeDesc(String conferenceType,
                                     String isLcjz,
                                     Map<String, String> lcjzTypeMap,
                                     Map<String, String> nonLcjzTypeMap) {
        Map<String, String> usedTypeMap = YesOrNoEnum.YES.getCode().equals(isLcjz) ? lcjzTypeMap : nonLcjzTypeMap;
        if (crm.howbuy.base.utils.StringUtil.isNotNullStr(conferenceType)) {
            //类型 描述 翻译
            String name = usedTypeMap.get(conferenceType);
            return getShowConferenceName(name);
        }
        return null;
    }

    /**
     * 获取课程名称（会议类型名称按、拼接）
     *
     * @param detailDTO      会议类型code列表
     * @param lcjzTypeMap    会议类型全量Map
     * @param nonLcjzTypeMap 会议类型全量Map
     * @return String
     */
    private String getConferenceNameStr(CmPortraitStudyDetailDTO detailDTO,
                                        Map<String, String> lcjzTypeMap,
                                        Map<String, String> nonLcjzTypeMap) {
        if (null == detailDTO || CollectionUtils.isEmpty(detailDTO.getTypeCodeList())) {
            return Strings.EMPTY;
        }

        List<String> conferenceNameList = this.translateTypeDescList(detailDTO.getConferenceType(), detailDTO.getIslcjz(), lcjzTypeMap, nonLcjzTypeMap);
        if (CollectionUtils.isEmpty(conferenceNameList)) {
            return Strings.EMPTY;
        }

        // typeCodeList中的元素作为key从conferenceMap中获取到对应值后按照、拼接成字符串
        return conferenceNameList.stream()
                .map(this::handleConferenceName)
                .filter(StringUtil::isNotBlank)
                .collect(Collectors.joining("、"));
    }

    /**
     * 处理会议名称展示，增加书名号
     *
     * @param name 名称
     * @return String
     */
    private String getShowConferenceName(String name) {
        if (StringUtil.isEmpty(name)) {
            return Strings.EMPTY;
        }

        if (name.startsWith("--")) {
            name = name.substring(2);
        }
        return StringUtils.join("《", name, "》");
    }

    /**
     * 处理会议名称展示，去除前面 --
     *
     * @param name 名称
     * @return String
     */
    private String handleConferenceName(String name) {
        if (StringUtil.isEmpty(name)) {
            return Strings.EMPTY;
        }

        if (name.startsWith("--")) {
            name = name.substring(2);
        }
        return name;
    }

    /**
     * 格式化 获取上次学习时间
     * BDP数据格式：yyyy-MM-dd HH:mm:ss
     * yyyy/MM/dd HH:mm:ss（N天前）
     *
     * @param lastStudyTime BDP学习时间
     * @return String
     */
    private String getLastStudyTime(String lastStudyTime) {
        if (StringUtil.isEmpty(lastStudyTime)) {
            return null;
        }

        Date studyDate = DateUtil.string2Date(lastStudyTime, DateUtil.DEFAULT_DATESFM);
        String studyTime = DateUtil.date2String(studyDate, "yyyy/MM/dd HH:mm:ss");
        int elapsedDay = DateUtil.elapsedDay(studyDate, new Date());
        if (elapsedDay <= 0) {
            return studyTime;
        }
        return StringUtils.join(studyTime, "（", elapsedDay, "天前）");
    }
}
