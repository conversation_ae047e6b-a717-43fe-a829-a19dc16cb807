/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.piggy;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/16 13:32
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositVoucherDuplicateCheckRequest implements Serializable {


    private static final long serialVersionUID = -7019129325114055288L;

    /**
     * 打款凭证号,新增不传值,编辑传值
     */
    private String voucherNo;
    /**
     * 香港资金账号
     */
    @NotBlank(message = "香港资金账号不能为空")
    private String cpAcctNo;
    /**
     * 香港客户号
     */
    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 银行swiftCode码值
     */
    @NotBlank(message = "银行swiftCode码值不能为空")
    private String swiftCode;

    /**
     * 汇款账户币种
     */
    @NotBlank(message = "汇款账户币种不能为空")
    private String remitCurrency;

    /**
     * 汇款金额
     */
    @NotBlank(message = "汇款币种不能为空")
    private String remitAmt;
}
