/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.comm;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.response.commvisit.CsCommVisitVO;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustHisConsultant;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO;
import com.howbuy.crm.base.model.CustVisitTypeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.portrait.CommSourceEnum;
import com.howbuy.crm.cgi.extservice.request.portrait.comm.AddCommRecordRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.comm.QueryCommRecordRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordGroupVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordListVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordVO;
import com.howbuy.crm.cgi.manager.domain.portrait.comm.AddCommRecordDTO;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConscustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConsultantInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.CsCommVisitOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.QueryWeChatCommOuterService;
import com.howbuy.crm.cgi.manager.outerservice.portrait.comm.PortraitCommRecordOuterService;
import com.howbuy.crm.portrait.client.domain.dto.comm.CommRecordDTO;
import com.howbuy.crm.portrait.client.enums.CommModeEnum;
import com.howbuy.ds.label.client.domain.response.customerprofile.QueryConsCustWecomChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 画像沟通记录
 * @date 2024/10/8 16:27
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitCommRecordService {

    @Resource
    private PortraitCommRecordOuterService portraitCommRecordOuterService;

    @Resource
    private ConscustInfoOuterService conscustInfoOuterService;

    @Resource
    private QueryWeChatCommOuterService queryWeChatCommOuterService;

    @Resource
    private CsCommVisitOuterService csCommVisitOuterService;

    @Resource
    private ConsultantInfoOuterService consultantInfoOuterService;

    private static List<String> visitTypeList = Arrays.asList(
            CustVisitTypeEnum.TELEPHONE.getCode(),
            CustVisitTypeEnum.FACE2FACE.getCode(),
            CustVisitTypeEnum.CONFERENCE.getCode(),
            CustVisitTypeEnum.TEXT_MESSAGE.getCode(),
            CustVisitTypeEnum.EMAIL.getCode(),
            CustVisitTypeEnum.WECHAT.getCode(),
            CustVisitTypeEnum.EXPRESS_DELIVERY.getCode());

    /**
     * @param request
     * @return void
     * @description:添加沟通记录
     * <AUTHOR>
     * @date 2024/10/8 17:42
     * @since JDK 1.8
     */
    public void addCommRecord(AddCommRecordRequest request) {
        AddCommRecordDTO addCommRecordDTO = buildAddCommRecordDTO(request);
        portraitCommRecordOuterService.addCommRecord(addCommRecordDTO);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.manager.domain.portrait.comm.AddCommRecordDTO
     * @description:构建添加沟通记录DTO
     * <AUTHOR>
     * @date 2024/10/8 17:42
     * @since JDK 1.8
     */
    private AddCommRecordDTO buildAddCommRecordDTO(AddCommRecordRequest request) {
        AddCommRecordDTO addCommRecordDTO = new AddCommRecordDTO();
        addCommRecordDTO.setConscode(request.getConscode());
        addCommRecordDTO.setHboneNo(request.getHboneNo());
        addCommRecordDTO.setCommDate(request.getCommDate());
        addCommRecordDTO.setCommTime(request.getCommTime());
        addCommRecordDTO.setCommMode(request.getCommMode());
        addCommRecordDTO.setCommContext(request.getCommContext());
        return addCommRecordDTO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordListVO
     * @description:查询沟通记录
     * <AUTHOR>
     * @date 2024/10/8 17:46
     * @since JDK 1.8
     */
    public CommRecordListVO queryCommRecord(QueryCommRecordRequest request) {
        CommRecordListVO commRecordListVO = new CommRecordListVO();
        // 根据一账通号查询投顾客户简单信息 获取投顾客户号
        List<CmConsCustSimpleVO> consCustSimpleVOList =
                conscustInfoOuterService.queryCustconstantByHboneNo(request.getHboneNo());
        if (CollectionUtils.isEmpty(consCustSimpleVOList) ||
                StringUtils.isEmpty(consCustSimpleVOList.get(0).getConscustno())) {
            return commRecordListVO;
        }
        String conscustno = consCustSimpleVOList.get(0).getConscustno();

        // 查询画像沟通记录
        List<CommRecordDTO> portraitCommRecordList = portraitCommRecordOuterService.queryCommRecord(request.getHboneNo());
        // 查询历史投顾
        List<CmCustHisConsultant> hisConsultantList = conscustInfoOuterService.queryCustHisConsultantList(conscustno);
        if (CollectionUtils.isEmpty(hisConsultantList)) {
            return commRecordListVO;
        }
        List<String> hisConsCodeList = hisConsultantList.stream().map(CmCustHisConsultant::getConscode).collect(Collectors.toList());

        // 查询企微沟通记录
        List<QueryConsCustWecomChatResponse.WecomChat> wechatCommList =
                queryWeChatCommOuterService.queryWeChatCommList(Arrays.asList(request.getHboneNo()), hisConsCodeList);

        // 查询crm沟通记录
        List<CsCommVisitVO> csCommVisitVOList = csCommVisitOuterService.queryCsCommVisit(conscustno, visitTypeList);

        // 合并沟通记录列表
        List<CommRecordVO> commRecordVOList = mergeCommRecord(portraitCommRecordList, wechatCommList, csCommVisitVOList);

        // 日期列表
        commRecordListVO.setDateList(getDateList(commRecordVOList));
        // 内存分页
        memoryPage(commRecordListVO, request, commRecordVOList);

        return commRecordListVO;
    }

    /**
     * 查询客户最新沟通记录
     * @param hboneNo 一账通号
     * @return
     */
    public String queryNewestCommRecord(String hboneNo) {

        List<CmConsCustSimpleVO> consCustSimpleVOList = conscustInfoOuterService.queryCustconstantByHboneNo(hboneNo);
        if (CollectionUtils.isEmpty(consCustSimpleVOList) || StringUtils.isEmpty(consCustSimpleVOList.get(0).getConscustno())) {
            return null;
        }
        String conscustno = consCustSimpleVOList.get(0).getConscustno();

        // 不同渠道最新沟通时间列表
        List<String> comDateTimeList = new ArrayList<>();
        // 查询画像沟通记录
        CommRecordDTO commRecordDTO = portraitCommRecordOuterService.queryNewestCommRecord(hboneNo);

        // 查询历史投顾
        List<CmCustHisConsultant> hisConsultantList = conscustInfoOuterService.queryCustHisConsultantList(conscustno);
        if (CollectionUtils.isEmpty(hisConsultantList)) {
            return null;
        }
        List<String> hisConsCodeList = hisConsultantList.stream().map(CmCustHisConsultant::getConscode).collect(Collectors.toList());
        // 查询企微沟通记录
        List<QueryConsCustWecomChatResponse.WecomChat> wechatCommList = queryWeChatCommOuterService.queryWeChatCommList(Collections.singletonList(hboneNo), hisConsCodeList);
        QueryConsCustWecomChatResponse.WecomChat wecomChat = null;
        if (CollectionUtils.isNotEmpty(wechatCommList)) {
            // 根据 comDt 和 comTime 降序排取第一条
            wecomChat = wechatCommList.stream()
                    .min(
                            Comparator.comparing(QueryConsCustWecomChatResponse.WecomChat::getComDt, Comparator.reverseOrder())
                                    .thenComparing(QueryConsCustWecomChatResponse.WecomChat::getComTime, Comparator.reverseOrder())
                    ).orElse(null);
        }

        // 查询crm沟通记录
        CsCommVisitVO csCommVisitVO = csCommVisitOuterService.queryNewsestCsCommVisit(conscustno, visitTypeList);

        if (commRecordDTO != null) {
            comDateTimeList.add(commRecordDTO.getCommDate() + commRecordDTO.getCommTime());
        }
        if (wecomChat != null && StringUtils.isNotBlank(wecomChat.getComTime())) {
            String wechatComTime = DateUtil.format(wecomChat.getComTime(), DateUtil.DEFAULT_DATESFM, DateUtil.STR_PATTERN);
            comDateTimeList.add(wechatComTime);
        }
        if (csCommVisitVO != null) {
            comDateTimeList.add(csCommVisitVO.getCreDtStr());
        }
        if (CollectionUtils.isEmpty(comDateTimeList)) {
            return null;
        }
        return comDateTimeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0);
    }

    /**
     * @param commRecordVOList
     * @return void
     * @description:设置沟通人
     * <AUTHOR>
     * @date 2024/10/25 10:09
     * @since JDK 1.8
     */
    private void setCommPerson(List<CommRecordVO> commRecordVOList) {
        List<String> consCodeList = commRecordVOList.stream()
                .map(CommRecordVO::getCommPersonCode)
                .distinct()
                .collect(Collectors.toList());
        // cons
        List<CmConsultantInfo> consultantInfoList = consultantInfoOuterService.queryConsultantInfo(null, YesNoEnum.NO.getCode(), consCodeList);
        if (CollectionUtils.isEmpty(consultantInfoList)) {
            return;
        }
        Map<String, String> consCodeMap = consultantInfoList.stream()
                .filter(info -> StringUtils.isNotEmpty(info.getConsname()))
                .collect(Collectors.toMap(CmConsultantInfo::getConscode, CmConsultantInfo::getConsname, (v1, v2) -> v1));
        for (CommRecordVO commRecordVO : commRecordVOList) {
            String consName = consCodeMap.get(commRecordVO.getCommPersonCode());
            commRecordVO.setCommPerson(StringUtils.isNotEmpty(consName) ? consName : commRecordVO.getCommPersonCode());
        }
    }

    /**
     * @param commRecordVOList
     * @return java.util.List<java.lang.String>
     * @description:获取日期列表
     * <AUTHOR>
     * @date 2024/10/24 20:20
     * @since JDK 1.8
     */
    private static List<String> getDateList(List<CommRecordVO> commRecordVOList) {
        HashSet dateSet = new HashSet();
        for (CommRecordVO commRecordVO : commRecordVOList) {
            dateSet.add(commRecordVO.getCommMonthDate());
        }
        // 排倒序
        List<String> dateList = Lists.newArrayList(dateSet);
        dateList.sort(Comparator.reverseOrder());
        return dateList;
    }

    /**
     * @param commRecordListVO
     * @param request
     * @param commRecordVOList
     * @return void
     * @description:内存分页
     * <AUTHOR>
     * @date 2024/10/25 9:07
     * @since JDK 1.8
     */
    private void memoryPage(CommRecordListVO commRecordListVO, QueryCommRecordRequest request, List<CommRecordVO> commRecordVOList) {
        // 过滤日期
        if (StringUtils.isNotEmpty(request.getQueryDate())) {
            commRecordVOList = commRecordVOList.stream()
                    .filter(commRecordVO -> request.getQueryDate().compareTo(commRecordVO.getCommMonthDate()) >= 0)
                    .collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(commRecordVOList)) {
            return;
        }
        // 排倒序
        commRecordVOList.sort(Comparator.comparing(CommRecordVO::getCommDate).reversed());
        // 设置总条数
        commRecordListVO.setTotalCount(commRecordVOList.size());
        // 内存分页
        int totalPage = (int) Math.ceil((double) commRecordVOList.size() / (double) request.getSize());
        if (request.getPage() > totalPage) {
            request.setPage(totalPage);
        }
        int start = (request.getPage() - 1) * request.getSize();
        int end = start + request.getSize();
        if (end > commRecordVOList.size()) {
            end = commRecordVOList.size();
        }
        // 获取当前分页数据
        List<CommRecordVO> currentPageList = commRecordVOList.subList(start, end);
        // 设置沟通人
        setCommPerson(currentPageList);
        // 按照CommMonthDate分组构建CommRecordGroupVO列表
        List<CommRecordGroupVO> commGroupList = currentPageList.stream()
                .collect(Collectors.groupingBy(CommRecordVO::getCommMonthDate))
                .entrySet().stream()
                .map(entry -> {
                    CommRecordGroupVO groupVO = new CommRecordGroupVO();
                    groupVO.setDate(entry.getKey());
                    groupVO.setCommList(entry.getValue());
                    return groupVO;
                })
                .collect(Collectors.toList());
        // 根据日期排序
        commGroupList.sort(Comparator.comparing(CommRecordGroupVO::getDate).reversed());
        commRecordListVO.setCommGroupList(commGroupList);
    }

    /**
     * @param portraitCommRecordList
     * @param wechatCommList
     * @param csCommVisitVOList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordVO>
     * @description:合并CommRecordVO
     * <AUTHOR>
     * @date 2024/10/24 20:00
     * @since JDK 1.8
     */
    private List<CommRecordVO> mergeCommRecord(List<CommRecordDTO> portraitCommRecordList,
                                               List<QueryConsCustWecomChatResponse.WecomChat> wechatCommList,
                                               List<CsCommVisitVO> csCommVisitVOList) {
        List<CommRecordVO> commRecordVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(portraitCommRecordList)) {
            for (CommRecordDTO commRecordDTO : portraitCommRecordList) {
                commRecordVOList.add(buildCommRecordVO(commRecordDTO));
            }
        }
        if (CollectionUtils.isNotEmpty(wechatCommList)) {
            for (QueryConsCustWecomChatResponse.WecomChat wecomChat : wechatCommList) {
                commRecordVOList.add(buildCommRecordVO(wecomChat));
            }
        }
        if (CollectionUtils.isNotEmpty(csCommVisitVOList)) {
            for (CsCommVisitVO csCommVisitVO : csCommVisitVOList) {
                commRecordVOList.add(buildCommRecordVO(csCommVisitVO));
            }
        }
        // 过滤日期和月日期为空的记录
        return commRecordVOList.stream()
                .filter(o -> StringUtils.isNotEmpty(o.getCommDate()) && StringUtils.isNotEmpty(o.getCommMonthDate()))
                .collect(Collectors.toList());
    }

    /**
     * @param commRecordDTO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordVO
     * @description:构建CommRecordVO
     * <AUTHOR>
     * @date 2024/10/24 19:59
     * @since JDK 1.8
     */
    private CommRecordVO buildCommRecordVO(CommRecordDTO commRecordDTO) {
        CommRecordVO commRecordVO = new CommRecordVO();
        commRecordVO.setCommPersonCode(commRecordDTO.getConscode());
        String monthDate = DateUtils.formatDateTimeStr(commRecordDTO.getCommDate() + commRecordDTO.getCommTime(), DateUtils.YYYYMMDDHHMMSS, DateUtils.YYYY_MM);
        commRecordVO.setCommMonthDate(monthDate);
        String commDate = DateUtils.formatDateTimeStr(commRecordDTO.getCommDate() + commRecordDTO.getCommTime(), DateUtils.YYYYMMDDHHMMSS, DateUtils.YYYY_MM_DD_HH_MM);
        commRecordVO.setCommDate(commDate);
        commRecordVO.setCommSource(CommSourceEnum.MANUAL.getCode());
        commRecordVO.setCommSourceDesc(CommSourceEnum.MANUAL.getDesc());
        commRecordVO.setCommMode(commRecordDTO.getCommMode());
        commRecordVO.setCommModeDesc(CommModeEnum.getDesc(commRecordDTO.getCommMode()));
        commRecordVO.setCommContext(commRecordDTO.getCommContext());
        return commRecordVO;
    }

    /**
     * @param wecomChat
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordVO
     * @description:构建CommRecordVO
     * <AUTHOR>
     * @date 2024/10/24 19:59
     * @since JDK 1.8
     */
    private CommRecordVO buildCommRecordVO(QueryConsCustWecomChatResponse.WecomChat wecomChat) {
        CommRecordVO commRecordVO = new CommRecordVO();
        commRecordVO.setCommPersonCode(wecomChat.getConsCode());
        String monthDate = DateUtils.formatDateTimeStr(wecomChat.getComTime(), DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.YYYY_MM);
        commRecordVO.setCommMonthDate(monthDate);
        String commDate = DateUtils.formatDateTimeStr(wecomChat.getComTime(), DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.YYYY_MM_DD_HH_MM);
        commRecordVO.setCommDate(commDate);
        commRecordVO.setCommSource(CommSourceEnum.WECHAT.getCode());
        commRecordVO.setCommSourceDesc(CommSourceEnum.WECHAT.getDesc());
        return commRecordVO;
    }

    /**
     * @param csCommVisitVO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordVO
     * @description:构建CommRecordVO
     * <AUTHOR>
     * @date 2024/10/24 19:58
     * @since JDK 1.8
     */
    private CommRecordVO buildCommRecordVO(CsCommVisitVO csCommVisitVO) {
        CommRecordVO commRecordVO = new CommRecordVO();
        commRecordVO.setCommPersonCode(csCommVisitVO.getCreator());
        String monthDate = DateUtils.formatDateTimeStr(csCommVisitVO.getCreDtStr(), DateUtils.YYYYMMDDHHMMSS, DateUtils.YYYY_MM);
        commRecordVO.setCommMonthDate(monthDate);
        String commDate = DateUtils.formatDateTimeStr(csCommVisitVO.getCreDtStr(), DateUtils.YYYYMMDDHHMMSS, DateUtils.YYYY_MM_DD_HH_MM);
        commRecordVO.setCommDate(commDate);
        commRecordVO.setCommSource(CommSourceEnum.CRM.getCode());
        commRecordVO.setCommSourceDesc(CommSourceEnum.CRM.getDesc());
        commRecordVO.setCommMode(commModeConvert(csCommVisitVO.getVisittype()));
        commRecordVO.setCommModeDesc(CommModeEnum.getDesc(commModeConvert(csCommVisitVO.getVisittype())));
        commRecordVO.setCommContext(csCommVisitVO.getCommcontent());
        return commRecordVO;

    }

    /**
     * @param visitType
     * @return com.howbuy.crm.portrait.client.enums.CommModeEnum
     * @description:(沟通模式枚举转换)
     * <AUTHOR>
     * @date 2024/11/27 14:57
     * @since JDK 1.8
     */
    private String commModeConvert(String visitType) {
        CustVisitTypeEnum visitTypeEnum = CustVisitTypeEnum.getEnum(visitType);
        // 与crm保持一致，为空默认为电话
        if (Objects.isNull(visitTypeEnum)) {
            return CommModeEnum.PHONE.getCode();
        }
        if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.TELEPHONE.getCode())) {
            return CommModeEnum.PHONE.getCode();
        } else if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.FACE2FACE.getCode())) {
            return CommModeEnum.MEET.getCode();
        } else if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.CONFERENCE.getCode())) {
            return CommModeEnum.MEETING.getCode();
        } else if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.TEXT_MESSAGE.getCode())) {
            return CommModeEnum.SMS.getCode();
        } else if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.EMAIL.getCode())) {
            return CommModeEnum.EMAIL.getCode();
        } else if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.WECHAT.getCode())) {
            return CommModeEnum.WECHAT.getCode();
        } else if (visitTypeEnum.getCode().equals(CustVisitTypeEnum.EXPRESS_DELIVERY.getCode())) {
            return CommModeEnum.EXPRESS_DELIVERY.getCode();
        }
        return null;
    }

}
