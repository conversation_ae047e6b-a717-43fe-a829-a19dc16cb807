/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.AssetsDetailVO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (持仓总资产信息VO)
 * @date 2024/2/23 08:58
 * @since JDK 1.8
 */
@Data
public class TotalAssetInfoVO extends AccountBaseVO implements Serializable {
    /**
     * 总资产
     */
    private String totalAsset;

    /**
     * 总资产换算的币种汇率
     */
    private List<BalCurrencyRateVO> totalBalanceRateList;

    /**
     * 是否显示资产 [资产小眼睛] (0:显示, 1: 隐藏)
     */
    private String showAsset;

    /**
     * 总资产明细弹窗内容列表
     */
    private List<AssetsDetailVO> totalAssetsDetail;

    /**
     * 在途交易笔数
     */
    private String inTransitTradeNums;

    /**
     * 在途资产总额
     */
    private String inTransitTradeAmt;

    /**
     * 在途交易资产换算的币种汇率
     */
    private List<BalCurrencyRateVO> inTransitBalanceRateList;

    /**
     * 交易单号 (在途交易笔数 == 1 时的交易单号)
     */
    private String inTransitDealNo;

    /**
     * 待签约交易笔数
     */
    private String waitSignNums;

    /**
     * 资金到账笔数
     */
    private String fundReceivedNums;

    /**
     * 打款凭证审核中的数量
     */
    private String underReviewCount;

    /**
     * 打款凭证审核不通过的数量
     */
    private String notPassCount;

    /**
     * 打款凭证只有一个的时候的打款凭证编号,APP需要直接跳转到详情页
     */
    private String voucherNo;


    /**
     * 基金持仓总资产
     */
    private String fundAsset;

    /**
     * 基金持仓总资产换算的币种汇率
     */
    private List<BalCurrencyRateVO> fundBalanceRateList;


    /**
     * 存钱罐资产
     */
    private String piggyBankAsset;

    /**
     * 基金持仓总资产换算的币种汇率
     */
    private List<BalCurrencyRateVO> piggyBalanceRateList;

    /**
     * 现金余额
     */
    private String cashBalanceAsset;

    /**
     * 现金余额日期
     */
    private String cashBalanceAssetDate;

    /**
     * 现金凭证是否已达上限 1-是,0-否
     */
    private String cashBalanceAssetLimit;

    /**
     * 补充协议个数
     */
    private String agencyAgreementCount;


    /**
     * 补充协议信息
     */
    private SupplementalAgreementVO supplementalAgreementInfo;

    /**
     * @description: 资产币种换算的币种汇率
     * @return
     * @author: jinqing.rao
     * @date: 2025/4/3 14:01
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class BalCurrencyRateVO implements Serializable {

        private static final long serialVersionUID = -607941336351198112L;

        /**
         * 币种
         */
        private String currency;

        /**
         * 币种描述
         */
        private String currencyDesc;

        /**
         * 汇率
         */
        private String excRate;

        /**
         * 汇率日期
         */
        private String excRateDate;
    }

    @Setter
    @Getter
    public static class SupplementalAgreementVO {
        /**
         * 协议ID
         */
        private String agreementId;

        /**
         * 基金代码
         */
        private String fundCode;
    }
}