package com.howbuy.crm.cgi.extservice.request.hkfund;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 海外基金申请结果页查询请求参数
 * <AUTHOR>
 * @date 2024/4/9 17:54
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKPurchasePendingMessageDetailRequest extends BodyRequest {

    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 香港客户号
     */
    private String hkCustNo;
}
