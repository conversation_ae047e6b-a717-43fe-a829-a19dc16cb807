package com.howbuy.crm.cgi.extservice.request.portrait;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户画像-消息提醒事件req
 * @Date 2024/9/2 18:31
 */
public class PortraitMsgRemindRequest implements Serializable {

    private static final long serialVersionUID = 1809314055045508313L;

    /**
     * 一账通号
     */
    @NotBlank(message = "一账通号不能为空!")
    private String hboneNo;

    /**
     * 投顾号
     */
    @NotBlank(message = "投顾编号不能为空!")
    private String conscode;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }
}
