package com.howbuy.crm.cgi.extservice.common.enums.fund;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.utils.DateUtils;

import java.util.Date;

public enum HKFundTimePeriodEnum {

    // 1-近一个月、2-近半年、3-近一年
    ONE_MONTH("1", "近一个月",-1),
    THREE_MONTH("2","近三个月",-3),

    HALF_YEAR("3", "近半年",-6),
    ONE_YEAR("4", "近一年",-12);

    private final String code;

    private final String desc;

    private final int month;

    HKFundTimePeriodEnum(String code, String value, int month) {
        this.code = code;
        this.desc = value;
        this.month = month;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }

    public int getMonth() {
        return month;
    }
    /**
     * @description: 根据Code获取对应的枚举
     * @param timePeriod
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/4/1 15:04
     * @since JDK 1.8
     */
    public static HKFundTimePeriodEnum getEnumByCode(String timePeriod) {
        for (HKFundTimePeriodEnum periodEnum : HKFundTimePeriodEnum.values()) {
            if (periodEnum.getCode().equals(timePeriod)) {
                return periodEnum;
            }
        }
        return null;
    }

    // 根据枚举的时间范围，计算出来对应的开始起始时间
    public static String getStartDt(String timePeriod) {
        HKFundTimePeriodEnum enumByCode = getEnumByCode(timePeriod);
        if (enumByCode == null) {
           throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "时间范围枚举传值错误");
        }
        return DateUtils.getStrNextMonthByDate(new Date(),enumByCode.getMonth());
    }

    /**
     * @description: 获取结束时间
     * @param
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2025/4/1 15:14
     * @since JDK 1.8
     */
    public static String getEndDt() {
        return DateUtils.getCurrentDate(DateUtils.YYYYMMDD);
    }
}
