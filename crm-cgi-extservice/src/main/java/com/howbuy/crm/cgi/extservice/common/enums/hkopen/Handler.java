package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctDepositInfoDTO;

/**
 * @description: 用户客户中心状态枚举处理器
 * @author: jinqing.rao
 * @date: 2023/12/4 13:55
 * @since JDK 1.8
 */
interface Handler {
    HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO);
}