package com.howbuy.crm.cgi.extservice.request.account.hkopenacct;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * @description: 签名页签名图片解析
 * <AUTHOR>
 * @date 2023/12/28 14:03
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAccESignatureImageRequest {
    /**
     * 签名图片base字符串
     */
    @NotBlank(message = "签名图片base字符串不能为空")
    private String signatureImageBase64;

    @NotBlank(message = "图片格式类型不能为空")
    private String imageFormat;

}
