/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;
/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/11/29 19:43
 * @since JDK 1.8
 */
@Data
public class UnBindHboneInfoRequest extends AccountBaseRequest {

    /**
     * 验证码
     */
    private String verifyCode;

    /**
     * 验证码类型 07-解绑一账通香港手机号短信验证码|08-解绑一账通好买手机号短信验证码|57-解绑一账通香港邮箱邮箱验证码
     */
    private String verifyCodeType;

}