/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.validator.BasicDataTypeValidator;
import com.howbuy.crm.cgi.extservice.request.hkfund.HkRevokeFundOrderRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HkRevokeFundVerRequest;
import com.howbuy.crm.cgi.extservice.service.hkfund.RevokeFundService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/18 16:19
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkfund/revoke/")
public class RevokeFundController {

    @Resource
    private RevokeFundService revokeFundService;

    /**
     * @api {POST} /ext/hkfund/revoke/verification verification()
     * @apiVersion 1.0.0
     * @apiGroup RevokeFundController
     * @apiName verification()
     * @apiDescription 交易详情页-撤回订单校验
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} orderNo 订单号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} orderType 订单类型 1 : 申购  2 : 赎回
     * @apiParamExample 请求体示例
     * {"orderNo":"bT","hkCustNo":"snqM959GrH"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"2OBtLx","description":"GFyrRhwrd7","timestampServer":"T3p"}
     */
    @PostMapping("verification")
    public CgiResponse<Body> verification( @RequestBody HkRevokeFundVerRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        revokeFundService.verification(request);
        return CgiResponse.ok(new Body());
    }

    /**
     * @api {POST} /ext/hkfund/revoke/revokefundorder redeemFundVerification()
     * @apiVersion 1.0.0
     * @apiGroup RevokeFundController
     * @apiName redeemFundVerification()
     * @apiHeader (header-param) {String} noLogin = 1 支持自动化平台免登录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiDescription 交易详情页-撤销申购/赎回订单
     * @apiParam (请求体) {String} dealNo 订单号
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} hbSceneId
     * @apiParamExample 请求体示例
     * {"txPassword":"cRH","hbSceneId":"bnyV","hkCustNo":"TNEr6","dealNo":"AcMx4XUkL"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"vqCmWSKB","description":"wB","timestampServer":"X0W6PlFs7"}
     */
    @PostMapping("revokefundorder")
    public CgiResponse<Body> redeemFundOrder(@RequestBody HkRevokeFundOrderRequest request) {
        //参数校验
        BasicDataTypeValidator.validator(request);
        revokeFundService.redeemFundOrder(request);
        return CgiResponse.ok(null);
    }
}
