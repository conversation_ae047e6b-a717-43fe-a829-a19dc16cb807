package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.convert.hkopen.OpenAcctConvert;
import com.howbuy.crm.cgi.extservice.request.account.OpenInvestRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.investinfo.HkOpenAcctInvestVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctInvestExpDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctRiskCalculateResultDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctRiskQuestionDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 开户步骤五,投资信息服务类
 * <AUTHOR>
 * @date 2024/1/22 19:14
 * @since JDK 1.8
 */
@Service
public class OpenAcctInvestInfoService extends OpenAcctAbstractService{

    public void temporaryStorageOpenInvestInfo(OpenInvestRequest request) {
        String hkCustNo = getHkCusNo();
        HkOpenAcctInvestExpDTO openAccInvestExpDTO = OpenAcctConvert.toHkOpenAccInvestExpDTO(request, null, null);
        openAcctCatchService.saveOpenInvestInfo(hkCustNo, openAccInvestExpDTO);
    }

    public HkOpenAcctInvestVO queryOpenInvestInfoDetail() {
        String hkCustNo = getHkCusNo();
        //获取用户风测结果信息
        HkOpenAcctRiskCalculateResultDTO acctRiskCalculateResultDTO = openAcctCatchService.getHkOpenAcctRiskAssessmentRiskResult(hkCustNo);
        //查询缓存获取开户订单信息
        HkOpenAcctInvestExpDTO hkOpenAcctInvestExpDTO = openAcctCatchService.getHkOpenAcctInvestExpDTO(hkCustNo);
        //从缓存获取开户订单审核退回修改状态的错误信息,这里的信息是在 com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.OpenAcctService.querySubmitResult 回写到缓存的。因为没有业务数据表
        String orderCheckReason = openAcctCatchService.getHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.INVESTMENT_EXPERIENCE_RISK_CHECK_RESULT, hkCustNo);
        List<HkOpenAcctCheckVO> hkOpenAcctCheckVOS = JSON.parseArray(orderCheckReason, HkOpenAcctCheckVO.class);
        if (null != hkOpenAcctInvestExpDTO) {
            return OpenAcctConvert.toOpenInvestVO(hkOpenAcctInvestExpDTO, OpenAcctConvert.toRiskToleranceLevelVO(acctRiskCalculateResultDTO), hkOpenAcctCheckVOS);
        }
        //查询账户中心获取用户的开户订单
        HkOpenAccOrderInfoDTO orderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustNo);
        //存在开户订单,直接返回开户信息
        if (null != orderInfoDTO.getAccountInvestExpDTO() && StringUtils.isNotBlank(orderInfoDTO.getDealNo())) {
            //回写用户的风险测评信息到缓存,不然在提交投资经验页面获取不到分险测评的信息
            HkOpenAcctRiskQuestionDTO hkOpenAcctRiskQuestionDTO = new HkOpenAcctRiskQuestionDTO();
            HkOpenAcctInvestExpDTO accountInvestExpDTO = orderInfoDTO.getAccountInvestExpDTO();
            hkOpenAcctRiskQuestionDTO.setAnswerDTOList(accountInvestExpDTO.getAnswerDTOList());
            hkOpenAcctRiskQuestionDTO.setExamId(accountInvestExpDTO.getExamId());
            hkOpenAcctRiskQuestionDTO.setHkCustNo(hkCustNo);
            if (null != orderInfoDTO.getAccountCustInfo()) {
                hkOpenAcctRiskQuestionDTO.setBirthday(orderInfoDTO.getAccountCustInfo().getBirthday());
            }
            openAcctCatchService.saveHkOpenAcctRiskAssessmentRiskAnswer(hkCustNo, hkOpenAcctRiskQuestionDTO);
            //回写用户的风险测评答案的缓存
            HkOpenAcctRiskCalculateResultDTO acctRiskCalculateResult = new HkOpenAcctRiskCalculateResultDTO();
            acctRiskCalculateResult.setLevelValue(accountInvestExpDTO.getRiskToleranceLevel());
            acctRiskCalculateResult.setScore(accountInvestExpDTO.getScore());
            acctRiskCalculateResult.setExamId(accountInvestExpDTO.getExamId());
            acctRiskCalculateResult.setDerivativeKnowledge(accountInvestExpDTO.getDerivativeKnowledge());
            acctRiskCalculateResult.setRiskToleranceDate(accountInvestExpDTO.getRiskToleranceDate());
            acctRiskCalculateResult.setRiskToleranceTerm(accountInvestExpDTO.getRiskToleranceTerm());
            openAcctCatchService.saveHkOpenAcctRiskAssessmentRisk(hkCustNo, acctRiskCalculateResult);
            return OpenAcctConvert.toOpenInvestVO(orderInfoDTO.getAccountInvestExpDTO(), OpenAcctConvert.toRiskToleranceLevelVO(orderInfoDTO.getRiskCalculateResultDTO()), hkOpenAcctCheckVOS);
        }
        return HkOpenAcctInvestVO.builder().build();
    }

    public void saveOpenInvestInfo(OpenInvestRequest request) {
        String hkCustNo = getHkCusNo();
        //参数校验
        HkOpenAcctValidator.validatorOpenInvestRequest(request);
        //缓存获取投资经验中的风险测评认证和结果信息
        HkOpenAcctRiskQuestionDTO riskHistoryAnswer = openAcctCatchService.getHkOpenAcctRiskAssessmentRiskHistoryAnswer(hkCustNo);
        if (null == riskHistoryAnswer) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_RISK_INFO_ERROR);
        }
        //获取测评的结果信息
        HkOpenAcctRiskCalculateResultDTO hkOpenAcctRiskAssessmentRiskResult = openAcctCatchService.getHkOpenAcctRiskAssessmentRiskResult(hkCustNo);
        if (null == hkOpenAcctRiskAssessmentRiskResult) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_RISK_INFO_ERROR);
        }
        HkOpenAcctInvestExpDTO openAccInvestExpDTO = OpenAcctConvert.toHkOpenAccInvestExpDTO(request, riskHistoryAnswer.getAnswerDTOList(), hkOpenAcctRiskAssessmentRiskResult);
        openAcctCatchService.saveOpenInvestInfo(hkCustNo, openAccInvestExpDTO);
        //删除审核不通过的投资经验原因
        openAcctCatchService.removeHkOpenAcctOrderCheckReason(HkOpenAccountStepEnum.INVESTMENT_EXPERIENCE_RISK_CHECK_RESULT, hkCustNo);
    }
}
