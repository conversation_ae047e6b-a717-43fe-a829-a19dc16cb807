/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.annualbill;

import com.howbuy.crm.cgi.manager.domain.asset.annualasset.BrowseInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/22 08:58
 * @since JDK 1.8
 */
@Data
public class BrowseInfoVO {

    /**
     * 浏览文章总次数
     */
    private String browseNewsCount;

    /**
     * 浏览产品总次数
     */
    private String browseProductCount;

    /**
     * 浏览最多文章名称
     */
    private String browseNewsName;

    /**
     * 浏览最多产品名称
     */
    private String browseProductName;

    /**
     * 浏览最多文章浏览次数
     */
    private String browseNewsMaxCount;

    /**
     * 浏览最多产品浏览次数
     */
    private String browseProductMaxCount;

    /**
     * 浏览最多文章标题
     */
    private String browseMaxNewsTitle;

    /**
     * 数据格式转换
     * @param browseInfoDTO
     * @return
     */
    public static BrowseInfoVO transToVO(BrowseInfoDTO browseInfoDTO) {
        BrowseInfoVO browseInfoVO = new BrowseInfoVO();
        browseInfoVO.setBrowseNewsCount(browseInfoDTO.getBrowseNewsCount());
        browseInfoVO.setBrowseProductCount(browseInfoDTO.getBrowseProductCount());
        browseInfoVO.setBrowseNewsName(browseInfoDTO.getBrowseNewsName());
        browseInfoVO.setBrowseProductName(browseInfoDTO.getBrowseProductName());
        browseInfoVO.setBrowseNewsMaxCount(browseInfoDTO.getBrowseNewsMaxCount());
        browseInfoVO.setBrowseProductMaxCount(browseInfoDTO.getBrowseProductMaxCount());
        browseInfoVO.setBrowseMaxNewsTitle(browseInfoDTO.getBrowseMaxNewsTitle());
        return browseInfoVO;
    }
}