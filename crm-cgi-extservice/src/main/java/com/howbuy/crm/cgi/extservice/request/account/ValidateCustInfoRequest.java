package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/6/9 14:28
 * @since JDK 1.8
 */
@Data
public class ValidateCustInfoRequest extends AccountBaseRequest {

    /**
     * 地区码 手机号非空时必须
     */
    private String areaCode;
    /**
     * 手机号 手机号,邮箱,证件号 三选一必须
     */
    private String mobile;
    /**
     * 邮箱地址 手机号,邮箱,证件号 三选一必须
     */
    private String email;
    /**
     * 证件类型  证件号非空时必须
     */
    private String idType;
    /**
     * 证件号码 手机号,邮箱,证件号 三选一必须
     */
    private String idNo;


}
