package com.howbuy.crm.cgi.extservice.common.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

/**
 * @description: 香港客户状态 0-正常 1-注销 2-休眠
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @version: 1.0
 * @since JDK 1.8
 */
public enum CustStateEnum {

    NORMAL("0", "正常"),
    SIGNOFF("1", "注销"),
    SLEEP("2", "休眠"),
    /**
     * 注册
     */
    REGISTER("3", "注册"),
    /**
     * 开户申请成功
     */
    OPEN_ACCOUNT_SUCCESS("4", "开户申请成功")
    ;

    private final String key;
    private final String desc;

    public static CustStateEnum getCustStateEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        CustStateEnum custStateEnum = getCustStateEnum(code);
        return custStateEnum == null ? null : custStateEnum.getDesc();
    }

    /**
     * @description: 判断用户的状态是否是正常或者休眠
     * @param code	状态枚举编码
     * @return boolean
     * @author: jinqing.rao
     * @date: 2023/12/7 13:22
     * @since JDK 1.8
     */
    public static boolean isNormalOrSleep(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }
        return Stream.of(values()).anyMatch(tmp -> tmp.key.equals(code) && (tmp.key.equals(NORMAL.getKey()) || tmp.key.equals(SLEEP.getKey())));
    }

    public static void main(String[] args) {
        System.out.println(isNormalOrSleep("1"));
    }
    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    CustStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
