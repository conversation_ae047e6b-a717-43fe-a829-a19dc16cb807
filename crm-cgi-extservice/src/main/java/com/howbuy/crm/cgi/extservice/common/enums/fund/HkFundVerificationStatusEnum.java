package com.howbuy.crm.cgi.extservice.common.enums.fund;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;

public enum HkFundVerificationStatusEnum {

    NORMAL("0", "校验通过",null),
    TO_OPEN_ACCOUNT("1", "去开户",null),
    CONTINUE_PROCESS("2", "继续处理",null),
    VIEW_OPEN_ACCOUNT_PROGRESS("3", "查看开户进度",null),
    MODIFY_OPEN_ACCOUNT_INFO("4", "修改开户资料",null),
    TO_DEPOSIT("5", "去入金",null),
    VIEW_DEPOSIT_PROGRESS("6", "查看入金进度",null),
    MODIFY_DEPOSIT_INFO("7", "修改入金资料",null),
    CUSTOMER_STATUS_EXCEPTION("8", "客户状态异常",ExceptionCodeEnum.HK_CUST_INFO_NOT_EXIST),
    OPEN_ACCOUNT_STATUS_DORMANT("18", "开户状态=“休眠",ExceptionCodeEnum.HK_CUST_INFO_NOT_EXIST),
    TRADE_ACCOUNT_NOT_ACTIVATED("19", "交易账号未激活",ExceptionCodeEnum.TRADE_ACCOUNT_NOT_ACTIVATED_ERROR),
    NOT_BIND_BANK_CARD("20", "未绑定银行卡",ExceptionCodeEnum.NOT_BIND_BANK_CARD_ERROR),
    RISK_LEVEL_NOT_MATCH("21", "风险等级不匹配",ExceptionCodeEnum.RISK_LEVEL_NOT_MATCH_ERROR),
    CUSTOMER_DERIVATIVE_EXPERIENCE_NO("22", "客户衍生品经验为否",ExceptionCodeEnum.CUSTOMER_DERIVATIVE_EXPERIENCE_NO_ERROR),
    CUSTOMER_NOT_PROFESSIONAL_INVESTOR("23", "客户非专业投资者",ExceptionCodeEnum.CUSTOMER_NOT_PROFESSIONAL_INVESTOR_ERROR),
    CUSTOMER_ASSET_PROOF_EXPIRED("24", "客户资产证明有效期已过期",ExceptionCodeEnum.CUSTOMER_ASSET_PROOF_EXPIRED_ERROR),
    // 用户年纪超过65岁
    CUSTOMER_AGE_GREATER_THAN_65_LIMIT("28", "用户年纪超过65岁", ExceptionCodeEnum.CUSTOMER_AGE_GREATER_THAN_65_LIMIT_ERROR),
    //是否有海外储蓄罐在途交易
    CUSTOMER_HAS_PIGGY_IN_TRANSIT("29", "海外储蓄罐产品存在在途交易",ExceptionCodeEnum.CUSTOMER_HAS_PIGGY_IN_TRANSIT_ERROR),
    //有持仓或者有在途交易
    CUSTOMER_HAS_POSITION_OR_IN_TRANSIT("30", "有持仓或者有在途交易",ExceptionCodeEnum.CUSTOMER_HAS_POSITION_OR_IN_TRANSIT_ERROR),
    ;
    private final String code;
    private final String desc;

    private final ExceptionCodeEnum exceptionCodeEnum;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public ExceptionCodeEnum getExceptionCodeEnum() {
        return exceptionCodeEnum;
    }

    HkFundVerificationStatusEnum(String code, String desc,ExceptionCodeEnum exceptionCodeEnum) {
        this.code = code;
        this.desc = desc;
        this.exceptionCodeEnum = exceptionCodeEnum;
    }

    public static HkFundVerificationStatusEnum getEnumByCode(String code) {
        for (HkFundVerificationStatusEnum statusEnum : HkFundVerificationStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * @description: 根据Code获取描述信息
     * @param code 状态Code
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/7/26 19:43
     * @since JDK 1.8
     */
    public static ExceptionCodeEnum getEnumDisPlayByCode(String code) {
        for (HkFundVerificationStatusEnum statusEnum : HkFundVerificationStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getExceptionCodeEnum();
            }
        }
        return null;
    }
}
