/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/11/29 19:27
 * @since JDK 1.8
 */
@Data
public class HboneInfoRequest extends AccountBaseRequest {


    /**
     * 好买姓名 前端加密
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "好买姓名", isRequired = true)
    private String hbCustName;

    /**
     * 好买证件号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "好买证件号", isRequired = true)
    private String hbIdNo;

    /**
     * 好买手机号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "好买手机号", isRequired = true)
    private String hbMobile;

    /**
     * 验证码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码", isRequired = true)
    private String verifyCode;

    /**
     * 验证码类型  06-绑定一账通香港手机号短信验证码 11-绑定一账通好买手机号短信验证码 56-绑定一账通香港邮箱邮箱验证码；
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码类型", isRequired = true)
    private String verifyCodeType;
}