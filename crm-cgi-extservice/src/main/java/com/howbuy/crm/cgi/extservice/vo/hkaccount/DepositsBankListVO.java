/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

import java.util.List;

/**
 * @description: (入金银行卡)
 * <AUTHOR>
 * @date 2023/11/30 17:14
 * @since JDK 1.8
 */
@Data
public class DepositsBankListVO extends AccountBaseVO {

    /**
     * 银行卡列表数据
     */
    private List<DepositsBankVO> depositBankVOList;

    /**
     * 好买银行的配图
     */
    private String defaultLogo;

}