package com.howbuy.crm.cgi.extservice.common.enums.piggy;

import com.howbuy.crm.cgi.extservice.common.enums.ContractFileFormDocCfgEnum;

public enum PiggyContractBizTypeEnum {
   // 1 海外储蓄罐签约 2.海外储蓄罐关闭 3.海外储蓄罐变更
    PIGGY_SIGN("1","海外储蓄罐签约"),
    PIGGY_CHANGE("2","海外储蓄罐变更");

    private final String code;

    private final String desc;

    PiggyContractBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * @description: 获取海外签约的表单表单合同
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/7/22 10:17
     * @since JDK 1.8
     */
    public static String getPiggySignFormContract(){
        //多个文件用, 隔离
        return ContractFileFormDocCfgEnum.F006.getCode()+","+ContractFileFormDocCfgEnum.F007.getCode();
    }

    public static String getPiggyChangeFormContract() {
        //多个文件用, 隔离
        return ContractFileFormDocCfgEnum.F008.getCode()+","+ContractFileFormDocCfgEnum.F009.getCode();
    }
}
