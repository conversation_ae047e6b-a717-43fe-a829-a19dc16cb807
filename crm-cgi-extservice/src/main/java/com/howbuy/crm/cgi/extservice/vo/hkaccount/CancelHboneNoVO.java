/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (取消绑定一账通vo)
 * @date 2023/11/29 20:18
 * @since JDK 1.8
 */
@Data
public class CancelHboneNoVO extends AccountBaseVO {
    /**
     * 弹窗提示信息
     */
    private WarnVo warnVo;


    @Data
    public static class WarnVo {
        /**
         * 标题
         */
        private String title;

        /**
         * 提示
         */
        private List<String> tips;

        /**
         * 提示
         */
        private String notice;

        /**
         * 联系方式
         */
        private String phone;
    }
}