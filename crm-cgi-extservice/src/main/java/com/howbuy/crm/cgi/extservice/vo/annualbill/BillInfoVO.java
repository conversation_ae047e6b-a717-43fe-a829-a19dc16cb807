/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.annualbill;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.manager.domain.asset.annualasset.BillInfoDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/22 09:02
 * @since JDK 1.8
 */
@Data
public class BillInfoVO extends AccountBaseVO implements Serializable {

    /**
     * 陪伴时间对象
     */
    private AccompanyInfoVO accompanyInfoVO;

    /**
     * 浏览行为对象
     */
    private BrowseInfoVO browseInfoVO;

    public static BillInfoVO transToVO(BillInfoDTO billInfoDTO) {
        BillInfoVO billInfoVO = new BillInfoVO();
        billInfoVO.setAccompanyInfoVO(AccompanyInfoVO.transToVO(billInfoDTO.getAccompanyInfoDTO()));
        billInfoVO.setBrowseInfoVO(BrowseInfoVO.transToVO(billInfoDTO.getBrowseInfoDTO()));
        return billInfoVO;
    }
}