package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctOrderStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.app.HkOpenAcctInvestAuditEnum;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.hkopen.HkOpenImageUrlUtils;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountBaseRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkOpenAcctInvestAuditRequest;
import com.howbuy.crm.cgi.extservice.validator.hkopen.HkOpenAcctValidator;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkOpenAcctInvestDetailVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkChangeInvestorQualsResultDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctInvestExpDTO;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkChangeInvestorOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 海外App专业资质信息审核详情服务类
 * <AUTHOR>
 * @date 2024/3/5 8:55
 * @since JDK 1.8
 */
@Service
public class HkOpenAcctInvestAuditService {

    private static final Logger log = LoggerFactory.getLogger(HkOpenAcctInvestAuditService.class);

    @Resource
    private HkChangeInvestorOuterService hkChangeInvestorOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    /**
     * @description: 获取专业投资者审核详情
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkOpenAcctInvestDetailVO
     * @author: jinqing.rao
     * @date: 2024/3/5 11:06
     * @since JDK 1.8
     */
    public HkOpenAcctInvestDetailVO getInvestAuditDetail(HkAppAccountBaseRequest request) {
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(request.getHkCustNo());
        HkChangeInvestorQualsResultDTO hkChangeInvestorQualsResultDTO = hkChangeInvestorOuterService.getInvestAuditDetail(request.getHkCustNo());
        HkOpenAcctInvestDetailVO hkOpenAcctInvestDetailVO = new HkOpenAcctInvestDetailVO();
        hkOpenAcctInvestDetailVO.setRefuseReason(hkChangeInvestorQualsResultDTO.getInvestorQualificationRefuseReason());
        if(!CollectionUtils.isEmpty(hkChangeInvestorQualsResultDTO.getPropertyImages())){
           List<ImageVO> imageUrl =  hkChangeInvestorQualsResultDTO.getPropertyImages().stream().map(m -> new ImageVO(m,HkOpenImageUrlUtils.getThumbnailUrlByBaseFileUrl(m))).collect(Collectors.toList());
            hkOpenAcctInvestDetailVO.setPropertyImages(imageUrl);
        }
        hkOpenAcctInvestDetailVO.setInvestorType(hkChangeInvestorQualsResultDTO.getInvestorQualification());
        hkOpenAcctInvestDetailVO.setAssetEffectiveTime(DateUtils.formatDateStr(hkCustInfo.getAssetCertExpiredDate(),DateUtils.YYYY_MM_DD,DateUtils.YYYYMMDD));
        //初始化审核信息
        initAuditInfo(hkChangeInvestorQualsResultDTO, hkCustInfo, hkOpenAcctInvestDetailVO);
        return hkOpenAcctInvestDetailVO;
    }

    /**
     * @description: 初始化打款凭证状态
     * @param hkChangeInvestorQualsResultDTO	
     * @param hkCustInfo	
     * @param hkOpenAcctInvestDetailVO
     * @return void
     * @author: jinqing.rao
     * @date: 2024/8/13 19:01
     * @since JDK 1.8
     */
    private void initAuditInfo(HkChangeInvestorQualsResultDTO hkChangeInvestorQualsResultDTO, HkCustInfoDTO hkCustInfo, HkOpenAcctInvestDetailVO hkOpenAcctInvestDetailVO) {
        //没有提交订单
        if(StringUtils.isEmpty(hkChangeInvestorQualsResultDTO.getDealNo())){
            // 特殊情况,专业投资者,没有订单
            initNotUploadInvestOrderCase(hkCustInfo, hkOpenAcctInvestDetailVO);
        }
        // 审核中
        if(HkOpenAcctOrderStatusEnum.PENDING_REVIEW.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
        || HkOpenAcctOrderStatusEnum.PENDING_RECHECK.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())||
                HkOpenAcctOrderStatusEnum.REJECTED_TO_INITIAL.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())){
            hkOpenAcctInvestDetailVO.setAuditStatus(HkOpenAcctInvestAuditEnum.AUDITING.getCode());
        }
        // 审核通过
        if(HkOpenAcctOrderStatusEnum.APPROVED.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())){
            hkOpenAcctInvestDetailVO.setAuditStatus(HkOpenAcctInvestAuditEnum.PASS.getCode());
            //调用客户的基本信息接口,获取用户的资产有效时间是否过期
            checkAssetCerExpiredDate(hkOpenAcctInvestDetailVO, hkCustInfo);
        }
        // 审核不通过
        if(HkOpenAcctOrderStatusEnum.REJECTED.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
                || HkOpenAcctOrderStatusEnum.INVALID.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
        || HkOpenAcctOrderStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())){
            hkOpenAcctInvestDetailVO.setAuditStatus(HkOpenAcctInvestAuditEnum.REJECT.getCode());
        }
    }

    /**
     * @description: 没有更新过打款凭证的场景,在开户的过程中直接上传了打款凭证
     * @param hkCustInfo	
     * @param hkOpenAcctInvestDetailVO
     * @return void
     * @author: jinqing.rao
     * @date: 2024/8/22 18:25
     * @since JDK 1.8
     */
    private void initNotUploadInvestOrderCase(HkCustInfoDTO hkCustInfo, HkOpenAcctInvestDetailVO hkOpenAcctInvestDetailVO) {
        if(Constants.INVESTOR_QUALIFICATION_PRO.equals(hkCustInfo.getInvestorQualification())){
            checkAssetCerExpiredDate(hkOpenAcctInvestDetailVO, hkCustInfo);
            //查询开户订单,查看用户是否有开户过程中的资产证明材料
            HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO = hkCustInfoOuterService.queryHkOpenAccOrderInfo(hkCustInfo.getHkCustNo());
            HkOpenAcctInvestExpDTO accountInvestExpDTO = hkOpenAccOrderInfoDTO.getAccountInvestExpDTO();
            if(null != accountInvestExpDTO && !StringUtils.isEmpty(accountInvestExpDTO.getAssetCertUrlList())){
                List<ImageVO> imageUrl =  accountInvestExpDTO.getAssetCertUrlList().stream().map(m -> new ImageVO(m,HkOpenImageUrlUtils.getThumbnailUrlByBaseFileUrl(m))).collect(Collectors.toList());
                hkOpenAcctInvestDetailVO.setPropertyImages(imageUrl);
            }
        }else{
            hkOpenAcctInvestDetailVO.setAuditStatus(HkOpenAcctInvestAuditEnum.UNCOMMITTED.getCode());
        }
    }

    /**
     * @description: 校验资产有效期, 字段值是空,或者资产有效时间小于当前时间,表示已过期
     * @param hkCustInfo 香港客户号信息
     * @param hkOpenAcctInvestDetailVO	
     * @return void
     * @author: jinqing.rao
     * @date: 2024/5/15 10:06
     * @since JDK 1.8
     */
    private void checkAssetCerExpiredDate(HkOpenAcctInvestDetailVO hkOpenAcctInvestDetailVO,HkCustInfoDTO hkCustInfo) {
        if(null != hkCustInfo){
            if(StringUtils.isEmpty(hkCustInfo.getAssetCertExpiredDate())){
                hkOpenAcctInvestDetailVO.setAuditStatus(HkOpenAcctInvestAuditEnum.PASS_EXPIRE.getCode());
            }else{
                boolean beforeCurrentDate = DateUtils.isBeforeCurrentDate(hkCustInfo.getAssetCertExpiredDate(), DateUtils.YYYYMMDD);
                String auditStatus = beforeCurrentDate ? HkOpenAcctInvestAuditEnum.PASS_EXPIRE.getCode() : HkOpenAcctInvestAuditEnum.PASS.getCode();
                hkOpenAcctInvestDetailVO.setAuditStatus(auditStatus);
            }
        }
    }
    /**
     * @description: 提交专业投资者审核
     * @param request	请求参数
     * @return void
     * @author: jinqing.rao
     * @date: 2024/3/5 11:06
     * @since JDK 1.8
     */
    public void submitInvestAudit(HkOpenAcctInvestAuditRequest request) {
        HkOpenAcctValidator.validator(request);
        String ip = RequestUtil.getParameter(Constants.CUST_IP);
        List<ImageVO> propertyImages = request.getPropertyImages();
        List<String> list = propertyImages.stream().map(ImageVO::getUrl).collect(Collectors.toList());

        //因为历史原因，渠道号和网点号是放在链接上的,所以需要从链接上获取
        String coopId = RequestUtil.getHttpParameter(Constants.COOPID);
        String corpId = RequestUtil.getHttpParameter(Constants.CORPID);
        hkChangeInvestorOuterService.submitInvestAudit(request.getInvestorType(), request.getHkCustNo(),list,
                coopId,corpId,ip,request.getDeviceId(),"01", request.getVersion());
    }
}
