package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description 客户简要信息
 * @Date 2024/8/6 16:16
 */
@Getter
@Setter
public class PersonalSimpleInfoVO extends AccountBaseVO {


    private static final long serialVersionUID = -903021084127008666L;

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 客户类型，0-机构,1-个人,2-产品户
     */
    private String invstType;
    /**
     * 客户姓名(中文)
     */
    private String custName;
    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号摘要
     */
    private String mobileDigest;
    /**
     * 手机号地区码
     */
    private String mobileAreaCode;
    /**
     * 邮箱地址掩码
     */
    private String emailMask;
    /**
     * 邮箱地址密文
     */
    private String emailDigest;

}
