/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/7 17:02
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkCustFundHoldRiskLevelVO extends Body implements Serializable {

    private static final long serialVersionUID = -4383026520329571316L;

    /**
     * 客户的分险等级
     */
    private String custRiskLevel;

    /**
     * 客户持仓的基金风险等级
     */
    private List<FundHoldRiskLevelVO> fundRiskLevelInfoList;

    @Setter
    @Getter
    public static class FundHoldRiskLevelVO implements Serializable {
        private static final long serialVersionUID = 8121996891380300980L;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 基金风险等级
         */
        private String fundRiskLevel;

        /**
         * 基金名称
         */
        private String fundName;
    }
}
