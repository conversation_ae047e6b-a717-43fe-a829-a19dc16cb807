/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (好买开户账户信息vo)
 * @date 2023/11/29 20:35
 * @since JDK 1.8
 */
@Data
public class HbHkBankAcctInfoVO {

    /**
     * 账户名称
     */
    private String bankAcctName;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 默认logo
     */
    private String defaultLogo;

    /**
     * 银行账号列表
     */
    private List<String> bankAcctList;

    /**
     * 国际汇款识别码
     */
    private String swiftCode;

    /**
     * 银行代码
     */
    private String bankCode;

}