/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.piggy;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.FileTypeEnum;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.extservice.common.enums.RedeemDirectionEnum;
import com.howbuy.crm.cgi.extservice.common.enums.RedeemMethodEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolCancelTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolSignTypeEnum;
import com.howbuy.crm.cgi.extservice.request.piggy.PiggySignSubmitRequest;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignContractRecordVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignContractVO;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignResultVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.AgreementDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.DigitalSignSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkRedeemFundSubmitInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductContractDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductContractFileDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggySignSubmitRequestDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 海外储蓄罐罐DTO转换类
 * @date 2024/7/18 9:24
 * @since JDK 1.8
 */
public class PiggySignConvert {

    private static final Logger log = LoggerFactory.getLogger(PiggySignConvert.class);

    public static PiggySignContractVO getPiggySignContractVO(ProductContractDTO productContractDTO, String fundCode, List<ProductContractFileDTO> productContractFileDTOS) {
        List<PiggySignContractVO.PiggySignContract> contractVOS = new ArrayList<>();
        // 产品合同处理
        productContractConvert(productContractDTO, fundCode, contractVOS);
        // 产品指定表单合同处理
        productFormContractConvert(fundCode, productContractFileDTOS, contractVOS);
        //根据文件类型排序,根据fileBizType字段处理的，该字段的赋值是在封装参数的时候,根据展示的顺序，赋值 1 2 3
        contractVOS.sort(Comparator.comparing(PiggySignContractVO.PiggySignContract::getFileBizType)
                .thenComparing(PiggySignContractVO.PiggySignContract::getFileCode));
        PiggySignContractVO piggySignContractVO = new PiggySignContractVO();
        piggySignContractVO.setContractList(contractVOS);
        piggySignContractVO.setFundCode(fundCode);
        return piggySignContractVO;
    }

    private static void productFormContractConvert(String fundCode, List<ProductContractFileDTO> productContractFileDTOS, List<PiggySignContractVO.PiggySignContract> contractVOS) {
        if (CollectionUtils.isNotEmpty(productContractFileDTOS)) {
            for (ProductContractFileDTO dto : productContractFileDTOS) {
                PiggySignContractVO.PiggySignContract vo = new PiggySignContractVO.PiggySignContract();
                // 根据文件类型处理返回给前端的显示的文件名称
                vo.setFileCode(dto.getFileType());
                String displayName = dto.getDisplayName();
                displayName = StringUtils.isBlank(displayName) ? dto.getFileName() : displayName;
                vo.setFileName(displayName);
                vo.setFilePathUrl(dto.getFilePathUrl());
                vo.setFileTypeName(dto.getFileTypeName());
                // 特殊文件名称处理
                vo.setFileBizType("0");
                if (StringUtils.isAnyBlank(vo.getFilePathUrl(), vo.getFileName())) {
                    log.info("PiggySignService>>>queryPiggySignContractList 文件名称/文件地址缺失的不展示,fundCode:{},fileCode:{}", fundCode, vo.getFileCode());
                    continue;
                }
                contractVOS.add(vo);
            }
        }
    }

    private static void productContractConvert(ProductContractDTO productContractDTO, String fundCode, List<PiggySignContractVO.PiggySignContract> contractVOS) {
        if (CollectionUtils.isEmpty(productContractDTO.getFileList())) {
            return;
        }
        for (ProductContractFileDTO dto : productContractDTO.getFileList()) {
            PiggySignContractVO.PiggySignContract vo = new PiggySignContractVO.PiggySignContract();
            // 根据文件类型处理返回给前端的显示的文件名称
            vo.setFileCode(dto.getFileType());
            String displayName = dto.getDisplayName();
            displayName = StringUtils.isBlank(displayName) ? dto.getFileName() : displayName;
            vo.setFileName(displayName);
            vo.setFilePathUrl(dto.getFilePathUrl());
            vo.setFileTypeName(dto.getFileTypeName());
            // 特殊文件名称处理
            getContractFileName(dto, vo);
            if (StringUtils.isAnyBlank(vo.getFilePathUrl(), vo.getFileName())) {
                log.info("PiggySignService>>>queryPiggySignContractList 文件名称/文件地址缺失的不展示,fundCode:{},fileCode:{}", fundCode, vo.getFileCode());
                continue;
            }
            contractVOS.add(vo);
        }
    }

    private static void getContractFileName(ProductContractFileDTO dto, PiggySignContractVO.PiggySignContract vo) {
        // 给文件分类, 区分是默认文件还是合同文件,该字段目前只是做分类排序用,前端不用
        if (StringUtils.isNotBlank(dto.getBusiType())) {
            vo.setFileBizType("1");
        } else {
            vo.setFileBizType("2");
            // 优先获取展示名称
            if (StringUtils.isNotBlank(dto.getDisplayName())) {
                vo.setFileName(dto.getDisplayName());
            } else {
                if (dto.getFileType().equals(FileTypeEnum.BCXY1_TYPE.getKey()) || dto.getFileType().equals(FileTypeEnum.BCXY2_TYPE.getKey()) || dto.getFileType().equals(FileTypeEnum.BCXY3_TYPE.getKey())) {
                    String newFileName = convertFileName(dto.getFileName());
                    vo.setFileName(newFileName);
                }
            }
        }
    }

    /**
     * @param fileName 文件名称
     * @return java.lang.String
     * @description: 根据文件名称获取文件类型
     * @author: hongdong.xie
     * @date: 2023/8/31 20:21
     * @since JDK 1.8
     */
    private static String convertFileName(String fileName) {
        try {
            int firstIndex = fileName.indexOf("_");
            if (firstIndex < 0) {
                return fileName;
            }
            int secondIndex = fileName.indexOf("_", firstIndex + 1);
            if (secondIndex < 0) {
                return fileName;
            }
            int end = fileName.indexOf(".pdf");
            if (end < 0) {
                return fileName;
            }
            return fileName.substring(secondIndex + 1, end);
        } catch (Exception e) {
            log.error("error in convertFileName", e);
        }
        return fileName;
    }

    public static PiggySignResultVO toPiggySignResultVO(String hkCustNo, String transferBankInfoJson) {
        PiggySignResultVO piggySignResultVO = new PiggySignResultVO();
        if (StringUtils.isNotBlank(transferBankInfoJson)) {
            PiggySignResultVO.TransferBankInfo transferBankInfo = new PiggySignResultVO.TransferBankInfo();
            transferBankInfo = JSON.parseObject(transferBankInfoJson, PiggySignResultVO.TransferBankInfo.class);
            piggySignResultVO.setTransferBankInfos(transferBankInfo);
        }
        return piggySignResultVO;
    }

    public static DigitalSignSignDTO toRedeemFundContractSignDTO(String hkCustNo, String fundCode, String signFlag, String tradeMode, ProductContractDTO productContractDTO) {
        DigitalSignSignDTO digitalSignSignDTO = new DigitalSignSignDTO();
        digitalSignSignDTO.setHkCustNo(hkCustNo);
        digitalSignSignDTO.setFundCode(fundCode);
        digitalSignSignDTO.setContractSignFlag(signFlag);
        digitalSignSignDTO.setTradeMode(tradeMode);
        if (null != productContractDTO && CollectionUtils.isNotEmpty(productContractDTO.getFileList())) {
            List<AgreementDTO> hkFundAgreementDTOList = productContractDTO.getFileList().stream().map(m -> {
                AgreementDTO agreementDTO = new AgreementDTO();
                agreementDTO.setFileCode(m.getFileType());
                agreementDTO.setFileName(m.getFileName());
                // 优先获取展示名称
                if (StringUtils.isNotBlank(m.getDisplayName())) {
                    agreementDTO.setFileName(m.getDisplayName());
                } else {
                    if (m.getFileType().equals(FileTypeEnum.BCXY1_TYPE.getKey()) || m.getFileType().equals(FileTypeEnum.BCXY2_TYPE.getKey()) || m.getFileType().equals(FileTypeEnum.BCXY3_TYPE.getKey())) {
                        String newFileName = convertFileName(m.getFileName());
                        agreementDTO.setFileName(newFileName);
                    }
                }
                agreementDTO.setFilePathUrl(m.getFilePathUrl());
                return agreementDTO;
            }).collect(Collectors.toList());
            digitalSignSignDTO.setContractList(hkFundAgreementDTOList);
        }
        return digitalSignSignDTO;
    }

    public static HkRedeemFundSubmitInfoDTO toHkRedeemFundSubmitInfoDTO(PiggySignSubmitRequest request, String dealNo, String fundCode, BigDecimal redeemVol) {
        HkRedeemFundSubmitInfoDTO hkRedeemFundSubmitInfoDTO = new HkRedeemFundSubmitInfoDTO();
        hkRedeemFundSubmitInfoDTO.setHkCustNo(request.getHkCustNo());
        hkRedeemFundSubmitInfoDTO.setTxPassword(PassWordUtil.encrypt(request.getTxPassword()));
        hkRedeemFundSubmitInfoDTO.setFundCode(fundCode);
        hkRedeemFundSubmitInfoDTO.setRedeemMethod(RedeemMethodEnum.VOL.getKey());
        hkRedeemFundSubmitInfoDTO.setRedeemDirection(RedeemDirectionEnum.KEEPACCOUNT.getKey());
        hkRedeemFundSubmitInfoDTO.setAppVol(redeemVol);
        hkRedeemFundSubmitInfoDTO.setDealNo(dealNo);
        hkRedeemFundSubmitInfoDTO.setExternalDealNo(request.getHbSceneId());
        return hkRedeemFundSubmitInfoDTO;
    }

    public static PiggySignSubmitRequestDTO toPiggySignSubmitRequestDTO(PiggySignSubmitRequest request, String fundCode, String signType) {
        PiggySignSubmitRequestDTO piggySignSubmitRequestDTO = new PiggySignSubmitRequestDTO();
        piggySignSubmitRequestDTO.setPiggyFundCodeList(Collections.singletonList(fundCode));
        piggySignSubmitRequestDTO.setTxPassword(PassWordUtil.encrypt(request.getTxPassword()));
        piggySignSubmitRequestDTO.setHkCustNo(request.getHkCustNo());
        piggySignSubmitRequestDTO.setSignDate(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        piggySignSubmitRequestDTO.setSignType(signType);
        if (CollectionUtils.isNotEmpty(request.getSignFilePathList())) {
            List<PiggySignSubmitRequestDTO.PiggySignSubmitFileDTO> signSubmitFileDTOS = request.getSignFilePathList().stream().map(m -> {
                PiggySignSubmitRequestDTO.PiggySignSubmitFileDTO piggySignSubmitFileDTO = new PiggySignSubmitRequestDTO.PiggySignSubmitFileDTO();
                piggySignSubmitFileDTO.setFileCode(m.getFileCode());
                piggySignSubmitFileDTO.setFilePathUrl(m.getFilePathUrl());
                piggySignSubmitFileDTO.setFileName(m.getFileName());
                return piggySignSubmitFileDTO;
            }).collect(Collectors.toList());
            piggySignSubmitRequestDTO.setSignFilePathList(signSubmitFileDTOS);
        }
        return piggySignSubmitRequestDTO;
    }

    public static PiggySignContractRecordVO toPiggySignContractRecordVO(HkPiggyAgreementDealResponseDTO dealResponseDTO) {
        PiggySignContractRecordVO recordVO = new PiggySignContractRecordVO();
        //判断用户是否有发生过关闭业务 等于 【协议终止方式】枚举值：1-线下自主申请关闭、2-线上自主申请关闭、3-底层基金更换未同意、4-底层基金更换不同意
        List<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> agreementDealDTOS1 = dealResponseDTO.getHkPiggyDealDTOList().stream()
                .filter(getAgreementCancelDealDTOPredicate()).sorted(getAgreementCancelDtReversed()).collect(Collectors.toList());
        //线上自主申请开通 和 底层基金更换同意 的数据
        List<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> agreementDealDTOS = new ArrayList<>();
        //转换后的数据
        List<PiggySignContractRecordVO.PiggySignContractInfo> signContractInfos = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(agreementDealDTOS1)){
            //获取满足条件的取消日期
            String agreementCancelDt = agreementDealDTOS1.get(0).getAgreementCancelTime();
            //筛选【协议签署方式】= ‘2-线上自主申请开通’ 协议签署方式】= ‘4-底层基金更换同意’ 且时间大于 agreementCancelDt 的数据
             agreementDealDTOS = dealResponseDTO.getHkPiggyDealDTOList().stream()
                    .filter(getAgreementSignDealDTOBeforCloseDate(agreementCancelDt))
                    .sorted(Comparator.comparing(HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO::getAgreementSignDt).reversed())
                    .collect(Collectors.toList());
             //数据转换
             signContractInfos = getPiggySignContractInfos(agreementDealDTOS);
            recordVO.setPiggyContractList(signContractInfos);
            return recordVO;
        }

        // 没有关闭业务操作,封装数据
        agreementDealDTOS = dealResponseDTO.getHkPiggyDealDTOList().stream()
                .filter(getAgreementDealDTOPredicate())
                .sorted(Comparator.comparing(HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO::getAgreementSignDt).reversed())
                .collect(Collectors.toList());
        signContractInfos = getPiggySignContractInfos(agreementDealDTOS);
        recordVO.setPiggyContractList(signContractInfos);
        return recordVO;
    }

    /**
     * 获取协议取消日期倒序
     * @return
     */
    private static Comparator<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> getAgreementCancelDtReversed() {
        return Comparator.comparing(HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO::getAgreementCancelTime).reversed();
    }

    private static List<PiggySignContractRecordVO.PiggySignContractInfo> getPiggySignContractInfos(List<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> agreementDealDTOS) {
        List<PiggySignContractRecordVO.PiggySignContractInfo> signContractInfos = agreementDealDTOS.stream().filter(getPiggyAgreementDealDTOPredicate()).map(m -> {
            PiggySignContractRecordVO.PiggySignContractInfo piggySignContractInfo = new PiggySignContractRecordVO.PiggySignContractInfo();
            piggySignContractInfo.setSignDate(m.getAgreementSignDt());
            PiggyProtocolSignTypeEnum piggyProtocolSignTypeEnum = PiggyProtocolSignTypeEnum.getByCode(m.getAgreementSignType());
            if (null != piggyProtocolSignTypeEnum) {
                piggySignContractInfo.setTypeName(piggyProtocolSignTypeEnum.getDisPlay());
            }
            if (CollectionUtils.isNotEmpty(m.getFilePathList())) {
                List<PiggySignContractRecordVO.PiggySignContractRecord> contractRecords = m.getFilePathList().stream().map(f -> {
                    PiggySignContractRecordVO.PiggySignContractRecord piggySignContractRecord = new PiggySignContractRecordVO.PiggySignContractRecord();
                    piggySignContractRecord.setUrl(f.getFilePathUrl());
                    piggySignContractRecord.setFileName(f.getFileName());
                    piggySignContractRecord.setFileType(f.getFileCode());
                    return piggySignContractRecord;
                }).collect(Collectors.toList());
                piggySignContractInfo.setContractList(contractRecords);
            }
            return piggySignContractInfo;
        }).collect(Collectors.toList());
        return signContractInfos;
    }
    private static Predicate<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> getAgreementSignDealDTOBeforCloseDate(String date) {
        return f -> StringUtils.isNotBlank(f.getAgreementCancelTime()) &&
                StringUtils.isNotBlank(f.getAgreementSignType()) &&
                DateUtils.sourceDateAfterTargetDate(f.getAgreementCancelTime(),date,DateUtils.YYYYMMDDHHMMSS) &&
                (PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_ONLINE.getCode().equals(f.getAgreementSignType()) || PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_FUND_CHANGE.getCode().equals(f.getAgreementSignType()));
    }
    private static Predicate<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> getAgreementDealDTOPredicate() {
        return f -> StringUtils.isNotBlank(f.getAgreementSignDt()) &&
                StringUtils.isNotBlank(f.getAgreementSignType()) &&
                (PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_ONLINE.getCode().equals(f.getAgreementSignType()) || PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_FUND_CHANGE.getCode().equals(f.getAgreementSignType()));
    }

    /**
     * @description: 关闭协议过滤器,获取是否有关闭的记录
     * @param
     * @return java.util.function.Predicate<com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO>
     * @author: jinqing.rao
     * @date: 2024/9/12 15:52
     * @since JDK 1.8
     */
    private static Predicate<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> getAgreementCancelDealDTOPredicate() {
        return f -> StringUtils.isNotBlank(f.getAgreementCancelType()) && StringUtils.isNotBlank(f.getAgreementCancelTime()) && null != PiggyProtocolCancelTypeEnum.getEnumByCode(f.getAgreementCancelType());
    }

    /**
     * @param
     * @return java.util.function.Predicate<com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO>
     * @description: 筛选【协议签署方式】= ‘2-线上自主申请开通’ 协议签署方式】= ‘4-底层基金更换同意’
     * @author: jinqing.rao
     * @date: 2024/8/16 14:40
     * @since JDK 1.8
     */
    private static Predicate<HkPiggyAgreementDealResponseDTO.PiggyAgreementDealDTO> getPiggyAgreementDealDTOPredicate() {
        return piggyDealDTO -> PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_ONLINE.getCode().equals(piggyDealDTO.getAgreementSignType()) || PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_FUND_CHANGE.getCode().equals(piggyDealDTO.getAgreementSignType());
    }
}
