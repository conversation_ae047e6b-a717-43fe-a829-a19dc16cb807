package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 重置交易密码
 * @date 2023/6/6 14:28
 * @since JDK 1.8
 */
@Data
public class ResetTradePasswordRequest extends AccountBaseRequest {
    /**
     * 手机号码 必须，二选一
     */
    private String mobile;
    /**
     * 邮箱 必须，二选一
     */
    private String email;
    /**
     * 验证码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码", isRequired = true)
    private String verifyCode;
    /**
     * 新交易密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "新交易密码", isRequired = true)
    private String newTradePassword;
}
