/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.entrance;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;
import com.howbuy.crm.account.client.response.custinfo.CmConsCustSimpleVO;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.portrait.PortraitCustStateEnum;
import com.howbuy.crm.cgi.extservice.request.portrait.GueryConsCodeByUserIdRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBaseRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.entrance.EntranceValidateRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.entrance.QueryCustListRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.QueryConsCodeByUserIdVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.entrance.CustLessInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.entrance.CustLessListVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.entrance.EntranceValidateVO;
import com.howbuy.crm.cgi.manager.outerservice.acccenter.AccCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConscustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmaccount.ConsultantInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.crmwechat.WechatCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.DsLabelValueDTO;
import com.howbuy.crm.cgi.manager.outerservice.dslabel.QueryCustLabelOuterService;
import com.howbuy.crm.portrait.client.enums.LabelEnum;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;
import crm.howbuy.base.utils.DesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/5 19:08
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitEntranceService {

    @Resource
    private AccCommonOuterService accCommonOuterService;

    @Resource
    private WechatCustInfoOuterService wechatCustInfoOuterService;

    @Resource
    private ConsultantInfoOuterService consultantInfoOuterService;

    @Resource
    private ConscustInfoOuterService conscustInfoOuterService;

    @Resource
    private QueryCustLabelOuterService queryCustLabelOuterService;

    @Resource
    private PortraitLoginService portraitLoginService;

    /**
     * 客户画像入口校验 机构代码列表
     */
    @Value("${portraitentranceorgcodelist}")
    private String portraitEntranceOrgCodeList;

    /**
     * 客户画像入口投顾白名单客户列表
     */
    @Value("${portrait_conscode_whitelist}")
    private String portraitConscodeWhitelist;

    /**
     * @param request
     * @return void
     * @description:校验微信绑定
     * <AUTHOR>
     * @date 2024/9/6 13:26
     * @since JDK 1.8
     */
    public EntranceValidateVO validateWechatBind(PortraitBaseRequest request) {
        // 校验微信绑定
        String unionId = accCommonOuterService.queryWechatBindByHboneNo(request.getHboneNo());
        if (StringUtils.isEmpty(unionId)) {
            throw new BusinessException(ExceptionCodeEnum.PORTRAIT_NOT_WECHAT_BIND);
        }
        return validateConsRel(request.getHboneNo());
    }

    /**
     * @param request
     * @return void
     * @description:校验微信绑定和投顾关系
     * <AUTHOR>
     * @date 2024/9/6 13:27
     * @since JDK 1.8
     */
    public EntranceValidateVO validateWechatBindAndConsRel(EntranceValidateRequest request) {
        // 查询微信客户信息
        WechatCustInfoVO wechatCustInfoVO = wechatCustInfoOuterService.queryWechatCustInfo(request.getExternalUserId(), null);
        if (Objects.isNull(wechatCustInfoVO) || StringUtils.isEmpty(wechatCustInfoVO.getHboneNo())) {
            throw new BusinessException(ExceptionCodeEnum.PORTRAIT_NOT_WECHAT_BIND);
        }
        return validateConsRel(wechatCustInfoVO.getHboneNo());
    }

    /**
     * @param hboneNo
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.entrance.EntranceValidateVO
     * @description:(校验投顾关系)
     * <AUTHOR>
     * @date 2025/1/6 17:34
     * @since JDK 1.8
     */
    private EntranceValidateVO validateConsRel(String hboneNo) {
        // 通过会话获取userId
        String userId = PortraitLoginService.getUserId();
        // 校验投顾关系匹配
        String conscode = portraitLoginService.checkUserIdHboneNoMatch(userId, hboneNo);

        EntranceValidateVO entranceValidateVO = new EntranceValidateVO();
        // 加密一账通号处理
        entranceValidateVO.setHboneNo(getEncryptHboneNo(hboneNo));
        entranceValidateVO.setConscode(conscode);
        return entranceValidateVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.entrance.CustLessListVO
     * @description:入口查询客户列表
     * <AUTHOR>
     * @date 2024/9/25 13:30
     * @since JDK 1.8
     */
    public CustLessListVO queryCustListByUserId(QueryCustListRequest request) {
        // 通过会话获取userId
        String userId = PortraitLoginService.getUserId();
        CustLessListVO custLessListVO = new CustLessListVO();
        // 查询投顾编号
        List<CmConsultantInfo> consultantInfoList = consultantInfoOuterService.queryConsultantInfo(userId, YesNoEnum.YES.getCode(), null);
        if (CollectionUtils.isEmpty(consultantInfoList)) {
            log.error("根据userId获取的投顾编号为空，userId: {}", userId);
            return custLessListVO;
        }
        String conscode = consultantInfoList.get(0).getConscode();
        List<CmConsCustSimpleVO> consCustSimpleVOList = this.getCmConsCustSimpleVOS(request, conscode);
        if (CollectionUtils.isNotEmpty(consCustSimpleVOList)) {
            List<String> hboneNoList = consCustSimpleVOList.stream()
                    .filter(vo -> StringUtils.isNotEmpty(vo.getHboneNo()))
                    .map(vo -> vo.getHboneNo()).collect(Collectors.toList());

            Map<String, DsLabelValueDTO> dsLabelValueDTOMap = Maps.newHashMap();
            List<DsLabelValueDTO> dsLabelValueDTOList =
                    queryCustLabelOuterService.queryBatchCustLabel(hboneNoList, LabelEnum.LABEL_020108.getId());
            if (CollectionUtils.isNotEmpty(dsLabelValueDTOList)) {
                dsLabelValueDTOMap = dsLabelValueDTOList.stream()
                        .collect(Collectors.toMap(DsLabelValueDTO::getHboneNo, vo -> vo, (v1, v2) -> v1));
            }

            List<CustLessInfoVO> custLessInfoList = Lists.newArrayList();
            for (CmConsCustSimpleVO cmConsCustSimpleVO : consCustSimpleVOList) {
                DsLabelValueDTO dsLabelValueDTO = dsLabelValueDTOMap.get(cmConsCustSimpleVO.getHboneNo());
                custLessInfoList.add(buildCustLessInfoVO(cmConsCustSimpleVO, dsLabelValueDTO));
            }
            custLessListVO.setCustLessInfoList(custLessInfoList);
        }
        return custLessListVO;
    }

    /**
     * 根据用户ID获取投顾编号
     *
     * @param request 请求参数
     * @return 投顾编号VO
     * <AUTHOR>
     * @date 2025-04-27 14:34:59
     */
    public QueryConsCodeByUserIdVO getConsCodeByUserId(GueryConsCodeByUserIdRequest request) {
        QueryConsCodeByUserIdVO vo = new QueryConsCodeByUserIdVO();
        log.info("根据用户ID获取投顾编号-请求: userId={}", request.getUserId());
        List<CmConsultantInfo> consultantInfoList = consultantInfoOuterService.queryConsultantInfo(request.getUserId(), YesNoEnum.YES.getCode(), null);

        if (CollectionUtils.isEmpty(consultantInfoList)) {
            log.warn("根据userId获取的投顾信息为空，userId: {}", request.getUserId());
            return vo;
        }

        // 通常一个userId只对应一个绑定的投顾，取第一个
        String conscode = consultantInfoList.get(0).getConscode();
        log.info("根据用户ID获取投顾编号-成功: userId={}, conscode={}", request.getUserId(), conscode);
        vo.setConscode(conscode);
        return vo;
    }

    /**
     * 搜索投顾列表
     *
     * @param request  req
     * @param conscode 投顾编号
     * @return List<CmConsCustSimpleVO>
     */
    private List<CmConsCustSimpleVO> getCmConsCustSimpleVOS(QueryCustListRequest request, String conscode) {
        if (StringUtils.isNotEmpty(portraitConscodeWhitelist) && StringUtils.contains(portraitConscodeWhitelist, conscode)) {
            log.info("白名单投顾,查询客户列表, 投顾编号：{}, 白名单={}", conscode, portraitConscodeWhitelist);
            // 白名单投顾
            List<String> orgCodeList = null;
            if (StringUtils.isNotEmpty(portraitEntranceOrgCodeList)) {
                // 将portraitEntranceOrgCodeList按照,分割后转成list
                String[] orgCodeArr = portraitEntranceOrgCodeList.split(",");
                orgCodeList = Arrays.asList(orgCodeArr);
                log.info("白名单投顾,查询客户列表, 投顾编号：{}, 机构代码列表={}", conscode, orgCodeList);
            }
            return conscustInfoOuterService.pageQueryCustSimple(conscode, request.getSearchContent(), true, request.getPage(), request.getSize());
        }
        // 分页查询投顾客户简单信息
        return conscustInfoOuterService.pageQueryCustSimple(conscode, request.getSearchContent(), false, request.getPage(), request.getSize());
    }

    /**
     * @param cmConsCustSimpleVO
     * @param dsLabelValueDTO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.entrance.CustLessInfoVO
     * @description:构建CustLessInfoVO
     * <AUTHOR>
     * @date 2024/9/25 14:41
     * @since JDK 1.8
     */
    private CustLessInfoVO buildCustLessInfoVO(CmConsCustSimpleVO cmConsCustSimpleVO, DsLabelValueDTO dsLabelValueDTO) {
        CustLessInfoVO custLessInfoVO = new CustLessInfoVO();
        custLessInfoVO.setCustName(cmConsCustSimpleVO.getCustname());
        custLessInfoVO.setConscustNo(cmConsCustSimpleVO.getConscustno());
        // 加密一账通号处理
        if (StringUtils.isNotEmpty(cmConsCustSimpleVO.getHboneNo())) {
            custLessInfoVO.setHboneNo(getEncryptHboneNo(cmConsCustSimpleVO.getHboneNo()));
        }
        custLessInfoVO.setMobileMask(cmConsCustSimpleVO.getMobileMask());
        if (Objects.nonNull(dsLabelValueDTO) && StringUtils.isNotEmpty(dsLabelValueDTO.getLabelValue())) {
            custLessInfoVO.setLabelCustStateEnumKey(PortraitCustStateEnum.getCodeByDesc(dsLabelValueDTO.getLabelValue()));
        }
        return custLessInfoVO;
    }

    /**
     * @param hboneNo
     * @return java.lang.String
     * @description:通过一账通号获取加密一账通号
     * <AUTHOR>
     * @date 2024/10/29 13:55
     * @since JDK 1.8
     */
    private static String getEncryptHboneNo(String hboneNo) {
        if (StringUtils.isBlank(hboneNo)) {
            return null;
        }
        try {
            return DesUtil.encrypt(hboneNo, Constants.HBONENO_DES_KEY);
        } catch (Exception e) {
            log.info("getEncryptHboneNo>>加密一账通号失败,hboneNo:{}", hboneNo);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }
}
