package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户画像-发送素材报告内容req
 * @Date 2024/9/2 18:31
 */
public class SendMetarialRequest implements Serializable {

    private static final long serialVersionUID = 7368479996711757154L;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * pdf报告类型 1-资配报告 2-产品报告
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "报告类型", isRequired = true)
    private String reportType;

    /**
     * 报告名称
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "报告名称", isRequired = true)
    private String name;

    /**
     * 资配报告id
     */
    private String assetId;

    /**
     * 素材报告地址
     */
    private String reportUrl;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getReportUrl() {
        return reportUrl;
    }

    public void setReportUrl(String reportUrl) {
        this.reportUrl = reportUrl;
    }
}
