/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @description: App版本号工具类
 * <AUTHOR>
 * @date 2024/5/7 13:03
 * @since JDK 1.8
 */
public class AppVersionUtils {

    public static final String APP_VERSION_1_0_0 = "1.0.0";

    public static final String APP_VERSION_2_0_0 = "2.0.0";

    /**
     *
     * @param version1 最新版本号
     * @param version2 当前版本号
     * @return
     */
    public static boolean compareVersion(String version1, String version2) {
        if(StringUtils.isBlank(version1)){
            return false;
        }
        int result = compare(version1, version2);
        return result > 0;
    }
    /**
     * @description: 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     * @param version1	
     * @param version2
     * @return int
     * @author: jinqing.rao
     * @date: 2024/5/7 15:28
     * @since JDK 1.8
     */
    public static int compare(String version1, String version2) {
        String[] v1 = version1.split("\\.");
        String[] v2 = version2.split("\\.");

        int length = Math.max(v1.length, v2.length);
        for (int i = 0; i < length; i++) {
            int num1 = i < v1.length ? Integer.parseInt(v1[i]) : 0;
            int num2 = i < v2.length ? Integer.parseInt(v2[i]) : 0;

            if (num1 < num2) {
                return -1;
            } else if (num1 > num2) {
                return 1;
            }
        }

        return 0;
    }
}
