package com.howbuy.crm.cgi.extservice.request.account.hkopenacct;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 香港开户填写页地址信息查询
 * <AUTHOR>
 * @date 2023/12/19 14:41
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctAddressRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -4042697092069832995L;
    /**
     * 地址类型  1：居住地址  2：通讯地址  3：出生地  4: 银行填写页公司地址
     */
    private String addressType;


    private String subAddressType;
}
