/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.annualbill;

import com.howbuy.crm.cgi.common.utils.BigDecimalUtils;
import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.manager.domain.asset.annualasset.AnnualTradeDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/12 10:30
 * @since JDK 1.8
 */
@Data
public class AnnualTradeVO extends AccountBaseVO implements Serializable {

    /**
     * 当年买入次数
     */
    private Integer annualBuyCount;

    /**
     * 当年卖出次数
     */
    private Integer annualSellCount;

    /**
     * 当年买入总资产
     */
    private String annualBuyTotalAsset;

    /**
     * 当年卖出总资产
     */
    private String annualSellTotalAsset;

    /**
     * 产品平均持有时长
     */
    private Integer annualAvgHoldDuration;

    /**
     * 持有最长产品盈利
     */
    private String annualLongestHoldProfit;

    /**
     * 持有最长产品名称
     */
    private String annualLongestHoldProductName;

    /**
     * @description:(数据格式转换)
     * @param annualTradeDTO
     * @return com.howbuy.crm.cgi.extservice.vo.annualbill.AnnualTradeVO
     * @author: xufanchao
     * @date: 2024/11/12 13:34
     * @since JDK 1.8
     */
    public static AnnualTradeVO transToVO(AnnualTradeDTO annualTradeDTO) {
        AnnualTradeVO annualTradeVO = new AnnualTradeVO();
        annualTradeVO.setAnnualBuyCount(annualTradeDTO.getAnnualBuyCount());
        annualTradeVO.setAnnualSellCount(annualTradeDTO.getAnnualSellCount());
        annualTradeVO.setAnnualBuyTotalAsset(BigDecimalUtils.formatTh(annualTradeDTO.getAnnualBuyTotalAsset()));
        annualTradeVO.setAnnualSellTotalAsset(BigDecimalUtils.formatTh(annualTradeDTO.getAnnualSellTotalAsset()));
        annualTradeVO.setAnnualAvgHoldDuration(annualTradeDTO.getAnnualAvgHoldDuration());
        annualTradeVO.setAnnualLongestHoldProfit(BigDecimalUtils.formatTh(annualTradeDTO.getAnnualLongestHoldProfit()));
        annualTradeVO.setAnnualLongestHoldProductName(annualTradeDTO.getAnnualLongestHoldProductName());
        return annualTradeVO;
    }
}