/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.dtmsproduct;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.vo.dtmsproduct.ProductWealthAppointListVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsproduct.ProductWealthAppointVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductWealthAppointDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductWealthAppointListDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.QueryProductWealthAppConfigReqDTO;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.ProductWealthAppointOuterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/1/10 20:10
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/dtmsproduct/wealthappoint")
public class WealthAppointController {

    @Autowired
    private ProductWealthAppointOuterService productWealthAppointOuterService;

    /**
     * 产品财富报告文件存储路径 与 storeConfig的relativeDir一致
     */
    public static final String WEALTH_APPOINT_STORE_CONFIG_PATH = "/product/wealthappoint/";


    /**
     * @api {POST} /ext/dtmsproduct/wealthappoint/hkaccquery hkaccquery()
     * @apiVersion 1.0.0
     * @apiGroup ProductWealthAppointController
     * @apiName query()
     * @apiDescription 给账户中心提供的查询接口(按照时间倒排取两条)
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Number} data.total 总记录数
     * @apiSuccess (响应结果) {Number} data.pages 总页数
     * @apiSuccess (响应结果) {Array} data.configVOList 财富配置报告及预约日历列表数据
     * @apiSuccess (响应结果) {Number} data.configVOList.id 主键ID
     * @apiSuccess (响应结果) {String} data.configVOList.investType 投资者类型
     * @apiSuccess (响应结果) {String} data.configVOList.yearMonth 具体年月份
     * @apiSuccess (响应结果) {String} data.configVOList.wealthAllocationReport 财富配置报告
     * @apiSuccess (响应结果) {String} data.configVOList.wealthAllocationReportPath 财富配置报告的地址
     * @apiSuccess (响应结果) {String} data.configVOList.appointmentCalendarSummary 投资者预约日历汇总
     * @apiSuccess (响应结果) {String} data.configVOList.appointmentCalendarSummaryPath 投资者预约日历汇总地址
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"XD4LG7NB","data":{"total":2599,"pages":4260,"configVOList":[{"investType":"9vZl0I47","wealthAllocationReport":"GXgH3","wealthAllocationReportPath":"ourtPORzx","appointmentCalendarSummary":"dFRw0","yearMonth":"e2Mr49X","id":7909,"appointmentCalendarSummaryPath":"vTRU"}]},"description":"3bZyyKezbi","timestampServer":"UvHH1esyp"}
     */
    @PostMapping("/hkaccquery")
    public CgiResponse<ProductWealthAppointListVO> hkaccquery() {
        ProductWealthAppointListVO productWealthAppointListVO = new ProductWealthAppointListVO();
        QueryProductWealthAppConfigReqDTO reqDTO = new QueryProductWealthAppConfigReqDTO();
        CrmResult<ProductWealthAppointListDTO> crmResult = productWealthAppointOuterService.queryList(reqDTO);
        if (Objects.nonNull(crmResult.getData())) {
            ProductWealthAppointListDTO listDTO = crmResult.getData();
            String yearMonth = listDTO.getList().get(0).getYearMonth();
            List<ProductWealthAppointDTO> filterList = listDTO.getList().stream().filter(it -> it.getYearMonth().equals(yearMonth)).collect(Collectors.toList());
            productWealthAppointListVO.setPages(listDTO.getPages());
            productWealthAppointListVO.setTotal(listDTO.getTotal());
            if (!CollectionUtils.isEmpty(listDTO.getList())) {
                List<ProductWealthAppointVO> collect = filterList.stream()
                        .map(configDTO -> buildConfigVO(configDTO))
                        .collect(Collectors.toList());
                productWealthAppointListVO.setConfigVOList(collect);
            }
        }
        return CgiResponse.ok(productWealthAppointListVO);
    }


    /**
     * @description:(转换为vo)
     * @param configDTO
     * @return com.howbuy.crm.cgi.inservice.vo.dtmsproduct.ProductWealthAppointVO
     * @author: xufanchao
     * @date: 2024/1/4 14:06
     * @since JDK 1.8
     */
    private ProductWealthAppointVO buildConfigVO(ProductWealthAppointDTO configDTO) {
        ProductWealthAppointVO productWealthAppointVO = new ProductWealthAppointVO();
        productWealthAppointVO.setId(configDTO.getId());
        productWealthAppointVO.setInvestType(configDTO.getInvestType());
        productWealthAppointVO.setYearMonth(configDTO.getYearMonth());
        if (!Objects.isNull(configDTO.getWealthAllocationReport())) {
            productWealthAppointVO.setWealthAllocationReport(configDTO.getWealthAllocationReport());
            productWealthAppointVO.setWealthAllocationReportPath(configDTO.getWealthAllocationReportPath().replace(WEALTH_APPOINT_STORE_CONFIG_PATH, ""));
        }
        if (!Objects.isNull(configDTO.getAppointmentCalendarSummary())) {
            productWealthAppointVO.setAppointmentCalendarSummary(configDTO.getAppointmentCalendarSummary());
            productWealthAppointVO.setAppointmentCalendarSummaryPath(configDTO.getAppointmentCalendarSummaryPath().replace(WEALTH_APPOINT_STORE_CONFIG_PATH, ""));
        }
        return productWealthAppointVO;
    }

}