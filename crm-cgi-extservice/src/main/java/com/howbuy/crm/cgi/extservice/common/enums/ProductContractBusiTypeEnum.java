package com.howbuy.crm.cgi.extservice.common.enums;

public enum ProductContractBusiTypeEnum {
    // 业务类型 1-认申购；2-赎回
    PURCHASE("1", "认申购"),
    REDEMPTION("2", "赎回"),
   ;
    private final String code;
    private final String desc;

    ProductContractBusiTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
