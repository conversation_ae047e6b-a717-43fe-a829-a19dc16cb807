package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitRecommendRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.recommend.PortraitRecommendService;
import com.howbuy.crm.cgi.extservice.vo.portrait.recommend.PortraitRecommendVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户画像-首页为您推荐查询接口
 * <AUTHOR>
 * @date 2024-03-19 14:45:00
 */
@Slf4j
@RestController
@RequestMapping("/portrait/recommend")
public class PortraitRecommendController {

    @Resource
    private PortraitRecommendService portraitRecommendService;

    /**
     * @api {POST} /ext/portrait/recommend/list 首页为您推荐查询
     * @apiVersion 1.0.0
     * @apiGroup PortraitRecommendController
     * @apiName getRecommendList
     * @apiDescription 首页为您推荐查询接口
     *
     * @apiParam (请求体) {String} recommendTab 为您推荐tab页（0-全部 1-A基础 2-B投教 3-市场资讯 4-金融产品）
     * @apiParam (请求体) {String} conscode 投顾编号（必填）
     * @apiParam (请求体) {String} hboneNo 一账通号（必填）
     * @apiParam (请求体) {Integer} page 分页页码
     * @apiParam (请求体) {Integer} size 每页数量
     *
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.showBalanceReport 是否展示持仓产品报告入口（1-是 0-否）
     * @apiSuccess (响应结果) {String} data.showProductReport 是否展示产品三件套入口（1-是 0-否）
     * @apiSuccess (响应结果) {String} data.redPoint 是否显示小红点（1-是 0-否）
     * @apiSuccess (响应结果) {String} data.total 总数量
     * @apiSuccess (响应结果) {Array} data.dataList 推荐列表
     * @apiSuccess (响应结果) {String} data.dataList.materialId 素材id
     * @apiSuccess (响应结果) {String} data.dataList.title 素材标题
     * @apiSuccess (响应结果) {String} data.dataList.link 素材链接
     * @apiSuccess (响应结果) {String} data.dataList.label 素材分类标签（1-A基础 2-B投教 3-C市场资讯 4-D金融产品）
     * @apiSuccess (响应结果) {String} data.dataList.column 专栏名称
     * @apiSuccess (响应结果) {String} data.dataList.newestTag 最新标签（1-是 0-否）
     * @apiSuccess (响应结果) {Array} data.dataList.productTag 产品标签列表
     * @apiSuccess (响应结果) {Array} data.dataList.keywordList 关键词列表
     * @apiSuccess (响应结果) {String} data.dataList.sendNum 素材发送次数
     * @apiSuccess (响应结果) {String} data.dataList.materialSendType 素材类型（1-素材 2-产品报告 2-资配报告）
     * @apiSuccess (响应结果) {String} data.dataList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     *
     * @apiParamExample 请求体示例
     * {
     *   "recommendTab": "0",
     *   "conscode": "TA123456",
     *   "hboneNo": "HB123456789",
     *   "page": 1,
     *   "size": 20
     * }
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "showBalanceReport": "1",
     *     "showProductReport": "1",
     *     "redPoint": "0",
     *     "total": "10",
     *     "dataList": [{
     *       "materialId": "123",
     *       "title": "投资入门基础知识",
     *       "link": "/article/123",
     *       "label": "1",
     *       "column": "投资基础",
     *       "newestTag": "1",
     *       "productTag": ["基金","股票"],
     *       "keywordList": ["投资","理财"],
     *       "sendNum": "10",
     *       "materialSendType": "1",
     *       "fundCode": "000001"
     *     }]
     *   },
     *   "timestampServer": "1677196800000"
     * }
     */
    @PostMapping("/list")
    public CgiResponse<PortraitRecommendVO> getRecommendList(@RequestBody PortraitRecommendRequest request) {
        PortraitRecommendVO vo = portraitRecommendService.getRecommendList(request);
        return CgiResponse.appOk(vo);
    }

} 