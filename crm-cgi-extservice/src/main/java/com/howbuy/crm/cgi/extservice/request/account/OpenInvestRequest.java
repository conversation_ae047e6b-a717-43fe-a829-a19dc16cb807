package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 投资经验开户暂存接口请求类
 * <AUTHOR>
 * @date 2023/11/30 13:06
 * @since JDK 1.8
 */
@Setter
@Getter
public class OpenInvestRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 4069109135485246995L;

    /**
     *投资者资质  PRO-投资者资质专业, 'NORMAL-投资者资质普通'
     */
    @NotBlank(message = "投资者资质不能为空")
    private String investorQualification;

    /**
     * 接受阅读申明
     */
    private List<String> acceptDeclare;

    /**
     * 资产证明URL列表
     */
    private List<ImageVO> assetCertUrlList;

    /**
     * 问卷id
     */
    private String examId;

    /**
     * 财富来源选项 多选
     *['01-薪金及/或花红', '02-业务收入', '03-退休金', '04-礼物', '05-储蓄', '06-投资回报', '07-遗赠', '08-其他']
     */
    @NotNull(message = "财富来源不能为空")
    private List<String> wealthSource;

    /**
     * 财富来源描述
     */
    private String wealthSourceDesc;

    /**
     * 开户保存类型 必填，01-保存，02-退出
     */
    @NotBlank(message = "开户保存类型不能是空")
    private String openSaveType;

}
