package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON>ai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkOpenAcctCheckVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 开户订单状态
 * <AUTHOR>
 * @date 2023/12/25 18:16
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkOpenAcctSubmitResultVO extends AccountBaseVO  implements Serializable {

    private static final long serialVersionUID = 6492445010246438015L;
    /**
     * 审核结果页 1：审核中 0：不通过
     */
    private String status;

    /**
     * 最小审核页
     */
    private String minCheckPage;

    /**
     * 证件信息审核结果信息
     */
    private List<HkOpenAcctCheckVO> checkResult;

}
