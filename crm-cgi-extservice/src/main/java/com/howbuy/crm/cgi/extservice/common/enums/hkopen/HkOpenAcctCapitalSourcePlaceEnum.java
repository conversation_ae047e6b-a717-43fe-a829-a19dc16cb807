package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 海外开户资料填写页资金来源地
 * <AUTHOR>
 * @date 2023/12/7 15:16
 * @since JDK 1.8
 */
public enum HkOpenAcctCapitalSourcePlaceEnum {

    HONG_KONG("HongKong","香港"),
    AMERICA("America","美国"),
    OTHER("Other","其他");

    private String code;

    private String desc;

    /**
     * @description: 构造函数
     * @param code	编码
     * @param desc  描述
     * @return 
     * @author: jinqing.rao
     * @date: 2023/12/7 15:17
     * @since JDK 1.8
     */
    HkOpenAcctCapitalSourcePlaceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
