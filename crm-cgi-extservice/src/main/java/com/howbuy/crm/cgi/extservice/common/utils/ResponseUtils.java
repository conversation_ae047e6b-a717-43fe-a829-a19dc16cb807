package com.howbuy.crm.cgi.extservice.common.utils;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.constant.ExternalConstant;
import com.howbuy.dfile.HFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.FileNameMap;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @description: 响应头处理工具类
 * <AUTHOR>
 * @date 2024/3/7 16:58
 * @since JDK 1.8
 */
public class ResponseUtils {

    private static final Logger log = LoggerFactory.getLogger(ResponseUtils.class);

    /**
     * @description: 文件下载
     * @param response	 响应体
     * @param fileName 文件名称
     * @param pdfCode	文件的Base64
     * @return void
     * @author: jinqing.rao
     * @date: 2024/3/7 16:59
     * @since JDK 1.8
     */
    public static void downloadFile(HttpServletResponse response, String fileName, String pdfCode) {
        ServletOutputStream outputStream = null;
        try {
            //获取文件流
            byte[] bytes = Base64.getDecoder().decode(pdfCode);
            //获取文件格式后缀
            String type = fileName.substring(fileName.lastIndexOf(".") + 1);
            String contentTypeByFileExtension = getContentTypeByFileExtension(type);
            ///设置文件格式
            response.setContentType(contentTypeByFileExtension);
            try {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } catch (Exception e) {
                fileName ="file"+type;
            }
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            //输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
        } catch (Exception e) {
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_FILE_DOWNLOAD_ERROR);
        }finally {
            if(null != outputStream){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("ResponseUtils>>>downloadPdf 关闭文件流异常",e);
                }
            }
        }
    }
    public static String getContentTypeByFileExtension(String fileExtension) {
        FileNameMap fileNameMap = URLConnection.getFileNameMap();
        return fileNameMap.getContentTypeFor("file." + fileExtension);
    }
}
