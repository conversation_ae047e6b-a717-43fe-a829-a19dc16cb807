package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 查询银行卡明文
 * @date 2023/6/6 16:01
 * @since JDK 1.8
 */
@Data
public class BankCardPlaintextRequest extends AccountBaseRequest {

    /**
     * 交易密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易密码", isRequired = true)
    private String tradePassword;

    /**
     * 香港资金账号 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港资金账号", isRequired = true)
    private String hkCpAcctNo;
}
