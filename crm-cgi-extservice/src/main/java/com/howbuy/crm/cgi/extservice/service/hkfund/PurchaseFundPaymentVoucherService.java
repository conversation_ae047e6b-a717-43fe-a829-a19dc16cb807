/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.convert.fund.HkPurchaseFundConvert;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKPurchaseFundPayVoucherUploadRequest;
import com.howbuy.crm.cgi.extservice.request.hkfund.HKPurchaseFundPaymentVoucherRequest;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherRecordVO;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.HKFundPayVoucherRecordDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.HKPurchaseFundOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HKFundPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 申购基金订单打款凭证
 * @date 2024/4/16 9:46
 * @since JDK 1.8
 */
@Service
public class PurchaseFundPaymentVoucherService {


    @Resource
    private HkFundOuterService hkFundOuterService;

    @Resource
    private HKFundPayVoucherOuterService hkFundPayVoucherOuterService;

    @Resource
    private HkBankCardInfoOuterService hkBankCardInfoOuterService;

    @Resource
    private PiggyPayVoucherOuterService piggyPayVoucherOuterService;

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherVO
     * @description: 申购海外基金, 打款凭证页获取申购订单信息
     * @author: jinqing.rao
     * @date: 2024/4/16 9:47
     * @since JDK 1.8
     */
    public HKPurchaseFundPaymentVoucherVO queryPurchaseFundOrderAmountInfo(HKPurchaseFundPaymentVoucherRequest request) {
        HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO = hkFundOuterService.queryFundOrderInfo(request.getHkCustNo(), request.getOrderNo());
        if (null == hkPurchaseFundOrderInfoDTO) {
            throw new BusinessException(ExceptionCodeEnum.HK_FUND_PRODUCT_NOT_EXIST);
        }
        //获取银行名称
        List<HkBankCardInfoDTO> acctByAcctNoAndCustNo = hkBankCardInfoOuterService.getHkBankAcctByAcctNoAndCustNo(request.getHkCustNo(), hkPurchaseFundOrderInfoDTO.getHkCpAcctNo());
        return HkPurchaseFundConvert.toHKPurchaseFundPaymentVoucherVO(hkPurchaseFundOrderInfoDTO, acctByAcctNoAndCustNo);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherRecordVO
     * @description: 查询打款凭证提交记录接口
     * @author: jinqing.rao
     * @date: 2024/4/16 10:08
     * @since JDK 1.8
     */
    public HKPurchaseFundPaymentVoucherRecordVO queryPayVoucherRecord(HKPurchaseFundPaymentVoucherRequest request) {
        // 查询订单信息
        HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO = hkFundOuterService.queryFundOrderInfo(request.getHkCustNo(), request.getOrderNo());
        if (null == hkPurchaseFundOrderInfoDTO) {
            throw new BusinessException(ExceptionCodeEnum.HK_FUND_PRODUCT_NOT_EXIST);
        }
        // 通过订单号查询打款凭证的详细信息
        HKFundPayVoucherRecordDTO hkFundPayVoucherRecordDTO = hkFundPayVoucherOuterService.queryPayVoucherRecord(request.getHkCustNo(), request.getOrderNo());
        if (null == hkFundPayVoucherRecordDTO) {
            return new HKPurchaseFundPaymentVoucherRecordVO();
        }
        //获取银行的logo
        HkBankCardInfoDTO hkBankAcctLogoBySwiftCode = hkBankCardInfoOuterService.getHkBankAcctLogoBySwiftCode(request.getHkCustNo(), hkFundPayVoucherRecordDTO.getSwiftCode());

        return HkPurchaseFundConvert.toHKPurchaseFundPaymentVoucherRecordVO(hkPurchaseFundOrderInfoDTO, hkFundPayVoucherRecordDTO, hkBankAcctLogoBySwiftCode, request.getHkCustNo());
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundPaymentVoucherVO
     * @description: 申购基金提交打款凭证
     * @author: jinqing.rao
     * @date: 2024/4/17 15:07
     * @since JDK 1.8
     */
    public void submitPayVoucher(HKPurchaseFundPayVoucherUploadRequest request) {
        HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO = hkFundOuterService.queryFundOrderInfo(request.getHkCustNo(), request.getOrderNo());
        if (null == hkPurchaseFundOrderInfoDTO) {
            throw new BusinessException(ExceptionCodeEnum.HK_FUND_PRODUCT_NOT_EXIST);
        }
        //提交打款凭证
        hkFundPayVoucherOuterService.submitPayVoucher(HkPurchaseFundConvert.toHkPurchaseFundPaymentVoucherDTO(request, hkPurchaseFundOrderInfoDTO));
    }
}
