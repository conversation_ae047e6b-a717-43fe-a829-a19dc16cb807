/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.annualbill;

import com.howbuy.crm.cgi.manager.domain.asset.annualasset.AccompanyInfoDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/22 09:01
 * @since JDK 1.8
 */
@Data
public class AccompanyInfoVO {

    /**
     * 初识时间 yyyy-MM-dd
     */
    private String registerTime;

    /**
     * 陪伴天数
     */
    private String accompanyDays;

    public static AccompanyInfoVO transToVO(AccompanyInfoDTO accompanyInfoDTO) {
        AccompanyInfoVO accompanyInfoVO = new AccompanyInfoVO();
        if (accompanyInfoDTO != null) {
            accompanyInfoVO.setRegisterTime(accompanyInfoDTO.getRegisterTime());
            accompanyInfoVO.setAccompanyDays(accompanyInfoDTO.getAccompanyDays());
        }
        return accompanyInfoVO;
    }
}