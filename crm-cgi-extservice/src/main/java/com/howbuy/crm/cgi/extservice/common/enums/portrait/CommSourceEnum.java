/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.portrait;

/**
 * @description: 沟通记录来源 "1"-人工录入 "2"-企微沟通 "3"-crm录入
 * <AUTHOR>
 * @date 2024/10/24 19:49
 * @since JDK 1.8
 */
public enum CommSourceEnum {

    MANUAL("1", "人工录入"),
    WECHAT("2", "企微沟通"),
    CRM("3", "CRM录入");

    private String code;
    private String desc;

    CommSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
