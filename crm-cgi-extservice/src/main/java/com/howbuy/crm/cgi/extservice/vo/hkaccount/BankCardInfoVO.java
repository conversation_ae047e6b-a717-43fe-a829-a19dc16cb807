package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: 银行卡信息
 * @date 2023/6/6 13:22
 * @since JDK 1.8
 */
@Data
public class BankCardInfoVO {

    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行名称 银行中文名称为空取银行名称，否则取银行中文名称
     */
    private String bankName;
    /**
     * 联行号
     */
    private String bankRegionCode;

    /**
     * 银行账户名称
     */
    private String bankAcctName;

    /**
     * 摘要-银行账号
     */
    private String bankAcctDigest;

    /**
     * 掩码-银行账号
     */
    private String bankAcctMask;

    /**
     * 银行账户状态0-正常 1-注销
     */
    private String bankAcctStatus;
    /**
     * 银行logoUrl
     */
    private String bankLogoUrl;
}
