package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 个人中心信息查询
 * @date 2023/11/30 18:08
 * @since JDK 1.8
 */
@Setter
@Getter
public class PersonalCenterVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 8859572469621829284L;

    /**
     * 客户风测过期标识
     */
    private String riskToleranceExpire;

    /**
     * 开户入金状态
     */
    private String openDepositsStatus;

    /**
     * 开户步骤标识
     */
    private String openAcctStepFlag;

}
