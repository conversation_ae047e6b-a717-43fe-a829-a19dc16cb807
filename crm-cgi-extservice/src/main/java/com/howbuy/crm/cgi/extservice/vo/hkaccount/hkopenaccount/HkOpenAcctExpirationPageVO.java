/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/26 18:29
 * @since JDK 1.8
 */
public class HkOpenAcctExpirationPageVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 1262653906112681640L;

    /**
     * 缓存过期的页面,0表示正常
     */
    private String expirePage;

    public String getExpirePage() {
        return expirePage;
    }

    public void setExpirePage(String expirePage) {
        this.expirePage = expirePage;
    }
}
