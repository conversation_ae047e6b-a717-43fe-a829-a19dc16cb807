package com.howbuy.crm.cgi.extservice.common.enums.agreement;


/**
 * @description: 补充协议签署状态枚举
 * <AUTHOR>
 * @date 2025/03/06 15:35:21
 * @since JDK 1.8
 */
public enum SupplementalAgreementSignStatusEnum {

    /**
     * 未签署
     */
    UNSIGNED("0", "未签署"),

    /**
     * 已签署
     */
    SIGNED("1", "已签署");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    SupplementalAgreementSignStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @description: 根据编码获取枚举
     * @param code 编码
     * @return SupplementalAgreementSignStatusEnum 枚举
     */
    public static SupplementalAgreementSignStatusEnum getByCode(String code) {
        for (SupplementalAgreementSignStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * @description: 判断是否已签署
     * @param code 编码
     * @return boolean true-已签署，false-未签署
     */
    public static boolean isSigned(String code) {
        return SIGNED.getCode().equals(code);
    }

    /**
     * @description: 判断是否未签署
     * @param code 编码
     * @return boolean true-未签署，false-已签署
     */
    public static boolean isUnsigned(String code) {
        return UNSIGNED.getCode().equals(code);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}