/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 手机验证码登录接口
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class LoginByMobileAndVerifyCodeRequest extends AccountBaseRequest {

    /**
     * 手机号 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "手机号", isRequired = true)
    private String mobile;
    /**
     * 验证码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码", isRequired = true)
    private String verifyCode;


    /**
     * 手机号区号
     */
    private String mobileAreaCode;

    /**
     * 版本ID
     */
    private String appVersion;

    /**
     * 手机型号 （eg：iPhone15 Pro）
     */
    private String deviceName;

    /**
     * 系统版本号
     */
    private String systemVersion;
}