package com.howbuy.crm.cgi.extservice.common.enums.fund;

public enum HKFundTradeStatusEnum {

//    付款中 确认中 自行撤单 强制撤单 交易成功 部分成功 交易失败
    PAYING("1", "付款中"),
    WAIT_CONFIRM("2", "确认中"),
    SELF_CANCEL("3", "自行撤单"),
    FORCE_CANCEL("4", "强制撤单"),
    CONFIRM_SUCCESS("5", "交易成功"),
    PART_CONFIRM("6", "部分成功"),
    CONFIRM_FAIL("7", "交易失败");

    final String code;
    final String desc;

    HKFundTradeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
