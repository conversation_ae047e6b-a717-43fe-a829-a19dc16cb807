package com.howbuy.crm.cgi.extservice.request.account.hkapp;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: App更新接口请求对象
 * <AUTHOR>
 * @date 2024/2/29 15:47
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAppUpdateRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 466596834495509495L;

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String version;

    /**
     * 主机型
     */
    @NotBlank(message = "主机型不能为空")
    private String parPhoneModel;

    /**
     * 子机型
     */
    @NotBlank(message = "子机型不能为空")
    private String subPhoneModel;

    /**
     * 产品ID
     */
    @NotBlank(message = "产品ID不能为空")
    private String productId;

    /**
     * 渠道ID
     */
    @NotBlank(message = "渠道ID不能为空")
    private String channelId;

}
