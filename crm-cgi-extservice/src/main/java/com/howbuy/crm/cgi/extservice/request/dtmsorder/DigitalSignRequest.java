/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.dtmsorder;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.DtmsBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.AgreementVO;
import lombok.Data;

import java.util.List;

/**
 * @description: (海外产品电子签约接口)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class DigitalSignRequest extends DtmsBaseRequest {
    /**
     * 预约流水号	必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预约流水号", isRequired = true)
    private String preBookId;
    /**
     * 交易密码		必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易密码", isRequired = true)
    private String txPswd;
    /**
     * 协议签订标识	0-未签署；1-已签署 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "协议签订标识", isRequired = true)
    private String signFlag;
    /**
     * 合同及协议列表
     */
    private List<AgreementVO> contractList;

}