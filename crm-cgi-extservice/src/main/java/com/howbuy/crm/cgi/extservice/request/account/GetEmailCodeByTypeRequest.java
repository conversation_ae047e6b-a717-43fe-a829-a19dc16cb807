/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (邮箱验证码请求实体类)
 * @date 2023/11/29 18:58
 * @since JDK 1.8
 */
@Data
public class GetEmailCodeByTypeRequest extends AccountBaseRequest {


    /**
     * 邮箱 前端加密
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "邮箱地址", isRequired = true)
    private String email;


    /**
     * 验证码类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码类型", isRequired = true)
    private String verifyCodeType;

}