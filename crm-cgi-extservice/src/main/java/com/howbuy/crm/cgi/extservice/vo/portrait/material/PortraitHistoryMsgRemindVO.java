package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户画像历史消息提醒VO
 * @Date 2024/9/2 18:34
 */
public class PortraitHistoryMsgRemindVO extends Body implements Serializable {

    private static final long serialVersionUID = -2302863005939129598L;

    /**
     * 总数量
     */
    private String total;

    /**
     * 日期提醒数据列表
     */
    private List<DateRemindMsg> dataList;

    public static class DateRemindMsg implements Serializable {

        private static final long serialVersionUID = 1030411802266182338L;

        /**
         * 提醒日期 yyyy-MM-dd
         */
        private String date;

        /**
         * 消息提醒事件列表
         */
        private List<MsgRemind> msgList;

        public static class MsgRemind implements Serializable {

            private static final long serialVersionUID = -7485280523222322537L;

            /**
             * 提醒类型
             */
            private String remindType;

            /**
             * 提醒类型名称
             */
            private String remindName;

            /**
             * 素材标题
             */
            private String materialTitle;

            /**
             * 连接地址
             */
            private String link;

            /**
             * 微伴id
             */
            private String vbId;

            /**
             * 提醒内容
             */
            private String remindContent;

            /**
             * 提醒图片地址
             */
            private String imgUrl;

            /**
             * 提醒分享描述
             */
            private String remindDesc;

            /**
             * 创建时间 yyyy-MM-dd HH:mm:ss
             */
            private String crateTime;

            /**
             * 产品代码
             */
            private String fundCode;

            /**
             * 素材Id
             */
            private String materialId;


            /**
             * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
             */
            private String materialSendType;

            public String getRemindType() {
                return remindType;
            }

            public void setRemindType(String remindType) {
                this.remindType = remindType;
            }

            public String getRemindName() {
                return remindName;
            }

            public void setRemindName(String remindName) {
                this.remindName = remindName;
            }

            public String getMaterialTitle() {
                return materialTitle;
            }

            public void setMaterialTitle(String materialTitle) {
                this.materialTitle = materialTitle;
            }

            public String getLink() {
                return link;
            }

            public void setLink(String link) {
                this.link = link;
            }

            public String getVbId() {
                return vbId;
            }

            public void setVbId(String vbId) {
                this.vbId = vbId;
            }

            public String getRemindContent() {
                return remindContent;
            }

            public void setRemindContent(String remindContent) {
                this.remindContent = remindContent;
            }

            public String getImgUrl() {
                return imgUrl;
            }

            public void setImgUrl(String imgUrl) {
                this.imgUrl = imgUrl;
            }

            public String getRemindDesc() {
                return remindDesc;
            }

            public void setRemindDesc(String remindDesc) {
                this.remindDesc = remindDesc;
            }

            public String getCrateTime() {
                return crateTime;
            }

            public void setCrateTime(String crateTime) {
                this.crateTime = crateTime;
            }

            public String getFundCode() {
                return fundCode;
            }

            public void setFundCode(String fundCode) {
                this.fundCode = fundCode;
            }

            public String getMaterialId() {
                return materialId;
            }

            public void setMaterialId(String materialId) {
                this.materialId = materialId;
            }

            public String getMaterialSendType() {
                return materialSendType;
            }

            public void setMaterialSendType(String materialSendType) {
                this.materialSendType = materialSendType;
            }
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public List<MsgRemind> getMsgList() {
            return msgList;
        }

        public void setMsgList(List<MsgRemind> msgList) {
            this.msgList = msgList;
        }
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public List<DateRemindMsg> getDataList() {
        return dataList;
    }

    public void setDataList(List<DateRemindMsg> dataList) {
        this.dataList = dataList;
    }

}
