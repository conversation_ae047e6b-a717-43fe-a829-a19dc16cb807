package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.VerifyCodeTypeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.enums.VerifyCodeChannelEnum;
import com.howbuy.crm.cgi.extservice.common.utils.AppNoLoginUtils;
import com.howbuy.crm.cgi.extservice.validator.hkfund.HkFundValidator;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundFeeRateResponseDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundLimitResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoPlaintextDTO;
import com.howbuy.crm.cgi.manager.outerservice.cc.SmsVerifyCodeOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QuerySellInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.encrypt.HwAuthEncryptOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 海外基金服务
 * @author: jinqing.rao
 * @date: 2024/4/24 16:41
 * @since JDK 1.8
 */
@Slf4j
public abstract class AbstractFundOrderService {

    @Value("${active.env}")
    private String activeEnv;

    @Resource
    protected QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;

    @Resource
    protected HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    protected HkBankCardInfoOuterService hkBankCardInfoOuterService;

    @Resource
    protected HkFundOuterService hkFundOuterService;

    @Resource
    protected QuerySellInfoOuterService querySellInfoOuterService;

    @Autowired
    private SmsVerifyCodeOuterService smsVerifyCodeOuterService;

    @Autowired
    private HwAuthEncryptOuterService hwAuthEncryptOuterService;


    public abstract List<HkFundValidator> addHkFundValidators(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO);
    
    
    /**
     * @description: 获取用户的银行卡信息,附带银行的logo
     * @param hkCustNo
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO>
     * @author: jinqing.rao
     * @date: 2024/5/10 14:19
     * @since JDK 1.8
     */
    protected List<HkBankCardInfoDTO> getHkBankAcctLogoList(String hkCustNo) {
        return hkBankCardInfoOuterService.getHkBankAcctLogoList(hkCustNo);
    }

    /**
     * @param verifyCodeType      验证码 1：手机 2：邮箱
     * @param verifyCode          验证码
     * @param hkCustNo            香港客户号
     * @param voiceVerifyCodeEnum
     * @return void
     * @description: 根据渠道类型校验验证码
     * @author: jinqing.rao
     * @date: 2024/4/15 14:42
     * @since JDK 1.8
     */
    protected void validVerifyCodeByType(String verifyCodeType, String verifyCode, String hkCustNo,
                                         VerifyCodeTypeEnum mobileVerifyCodeTypeEnum, VerifyCodeTypeEnum emailVerifyCodeTypeEnum, VerifyCodeTypeEnum voiceVerifyCodeEnum) {
        //兼容自动化平台的测试,跳过验证码的校验功能,因为平台频繁获取验证码,会被拦截
        if (AppNoLoginUtils.isAppNoLogin(activeEnv)) {
            log.info("validVerifyCodeByType >>> 测试环境跳过验证码校验 dev : {}" ,activeEnv);
            return;
        }
        if (VerifyCodeChannelEnum.MOBILE.getCode().equals(verifyCodeType)) {
            String mobile = hkCustInfoOuterService.getMobile(hkCustNo);
            if (StringUtils.isBlank(mobile)) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_NOT_EXIST);
            }
            smsVerifyCodeOuterService.checkSmsVerifyCode(mobile, verifyCode, mobileVerifyCodeTypeEnum.getMessageTempleteEnum());
        } else if (VerifyCodeChannelEnum.EMAIL.getCode().equals(verifyCodeType)){
            HkCustInfoPlaintextDTO custInfoPlaintext = hkCustInfoOuterService.getCustInfoPlaintext(hkCustNo);
            smsVerifyCodeOuterService.checkEmailVerifyCode(custInfoPlaintext.getEmail(), verifyCode, emailVerifyCodeTypeEnum.getMessageTempleteEnum());
        } else if (VerifyCodeChannelEnum.VOICE.getCode().equals(verifyCodeType)) {
            String mobile = hkCustInfoOuterService.getMobile(hkCustNo);
            if (StringUtils.isBlank(mobile)) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_NOT_EXIST);
            }
            String encryptString = hwAuthEncryptOuterService.getEncryptString(Constants.DEFAULT_MOBILE_AREA_CODE + mobile);
            smsVerifyCodeOuterService.verifyvoiceMobileVerifyCode(encryptString, verifyCode, voiceVerifyCodeEnum.getMessageTempleteEnum());
        }
    }

    /**
     * @description: 获取基金的基本信息
     * @param fundCode	基金编码
     * @return com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO
     * @author: jinqing.rao
     * @date: 2024/5/9 17:14
     * @since JDK 1.8
     */
    protected FundBasicInfoDTO getFundBasicInfoDTO(String fundCode) {
        //获取基金的基本信息,不存在抛出异常
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(fundCode);
        return fundBasicInfoDTO;
    }

    /**
     * @description: 查询基金交易限额配置
     * @param fundCode
     * @return com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO
     * @author: jinqing.rao
     * @date: 2024/5/10 10:12
     * @since JDK 1.8
     */
    protected List<FundLimitResponseDTO> getFundLimitDTO(String fundCode,String invstType,String busiCode) {
        return queryFundBasicInfoOuterService.queryFundLimitInfo(fundCode, invstType, busiCode);
    }

    /**
     * @description: 获取基金费率信息
     * @param fundCode	基金Code
     * @param invstType	 投资者类型，选填 0-机构，1-个人，2-产品
     * @param busiCode 业务代码，选填 020-认购、022-申购、024-赎回
     * @param collectRecipient 收取对象， 选填 1-好买、2-管理人
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.dtmsproduct.response.fund.FundFeeRateResponseDTO>
     * @author: jinqing.rao
     * @date: 2024/5/10 14:07
     * @since JDK 1.8
     */
    protected List<FundFeeRateResponseDTO> queryFundFeeRateInfo(String fundCode, String invstType, String busiCode,String collectRecipient) {
        //获取基金的基本信息,不存在抛出异常
        return queryFundBasicInfoOuterService.queryFundFeeRateInfo(fundCode, invstType, busiCode,collectRecipient);
    }


    /**
     * @description: 获取用户香港客户信息
     * @param hkCustNo 香港客户号
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: jinqing.rao
     * @date: 2024/5/9 17:16
     * @since JDK 1.8
     */
    protected HkCustInfoDTO getHkCustInfoDTO(String hkCustNo) {
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfo.getReturnCode())) {
            throw new BusinessException(ExceptionCodeEnum.HK_CUST_INFO_NOT_EXIST);
        }
        return hkCustInfo;
    }
    /**
     * @description: 执行校验器
     *
     * 校验逻辑： 检验通过返回 null ，校验不通过会返回具体的校验信息
     * @param validators 校验器集合
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO
     * @author: jinqing.rao
     * @date: 2024/4/24 17:14
     * @since JDK 1.8
     */
    public HKFundVerificationVO verification(List<HkFundValidator> validators){
        //添加过滤器
        HKFundVerificationVO verificationVO = null;
        for (HkFundValidator validator : validators) {
            verificationVO = validator.verification();
            //校验不通过,返回错误信息
            if(null != verificationVO && StringUtils.isNotBlank(verificationVO.getBuyVerfiyState())){
                break;
            }
        }
        return verificationVO;
    }
}
