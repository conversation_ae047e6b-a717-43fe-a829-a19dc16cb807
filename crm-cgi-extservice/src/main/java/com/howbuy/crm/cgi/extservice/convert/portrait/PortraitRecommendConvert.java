/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.recommend.PortraitRecommendVO;
import com.howbuy.crm.portrait.client.domain.response.material.QueryRecommendMaterialResponse;
import com.howbuy.crm.portrait.client.domain.vo.RecommendMaterialDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户画像-首页为您推荐转换类
 * <AUTHOR>
 * @date 2024-03-19 14:45:00
 */
public class PortraitRecommendConvert {

    /**
     * 转换为推荐列表VO
     * @description 将外部服务返回的Response转换为前端展示的VO
     * @param response 外部服务响应对象
     * @return PortraitRecommendVO 推荐列表VO
     * <AUTHOR>
     * @date 2024-03-19 14:45:00
     */
    public static PortraitRecommendVO convertToVO(QueryRecommendMaterialResponse response) {
        PortraitRecommendVO vo = new PortraitRecommendVO();
        if (response == null) {
            return vo;
        }

        // 设置基本信息
        vo.setShowBalanceReport(response.getShowBalanceReport());
        vo.setShowProductReport(response.getShowProductReport());
        vo.setRedPoint(response.getRedPoint());
        vo.setTotal(String.valueOf(response.getTotal()));

        // 转换推荐列表
        if (!CollectionUtils.isEmpty(response.getDataList())) {
            List<PortraitRecommendVO.RecommendItem> voList = new ArrayList<>();
            response.getDataList().forEach(dto -> {
                if (dto != null) {
                    voList.add(convertToRecommendItem(dto));
                }
            });
            vo.setDataList(voList);
        }

        return vo;
    }

    /**
     * 转换为推荐项VO
     * @description 将外部服务返回的DTO转换为前端展示的推荐项VO
     * @param dto 推荐素材DTO
     * @return PortraitRecommendVO.RecommendItem 推荐项VO
     * <AUTHOR>
     * @date 2024-03-19 14:45:00
     */
    private static PortraitRecommendVO.RecommendItem convertToRecommendItem(RecommendMaterialDTO dto) {
        PortraitRecommendVO.RecommendItem item = new PortraitRecommendVO.RecommendItem();
        if (dto == null) {
            return item;
        }

        item.setMaterialId(dto.getMaterialId());
        item.setTitle(dto.getTitle());
        item.setLink(dto.getLink());
        item.setLabel(dto.getLabel());
        item.setColumn(dto.getColumn());
        item.setNewestTag(dto.getNewestTag());
        item.setProductTag(dto.getProductTag());
        item.setKeywordList(dto.getKeywordList());
        item.setSendNum(String.valueOf(dto.getSendCount()));
        item.setMaterialSendType(dto.getMaterialSendType());
        item.setFundCode(dto.getFundCode());

        return item;
    }
} 