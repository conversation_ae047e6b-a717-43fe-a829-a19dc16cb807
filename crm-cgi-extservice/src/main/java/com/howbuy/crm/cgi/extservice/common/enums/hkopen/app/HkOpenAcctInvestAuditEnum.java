/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen.app;

/**
 * @description: 海外App专业资质信息审核枚举
 * <AUTHOR>
 * @date 2024/3/5 11:19
 * @since JDK 1.8
 */
public enum HkOpenAcctInvestAuditEnum {

    /**
     * 0：未提交
     */
    UNCOMMITTED("0", "未提交"),

    /**
     * 1：审核通过
     */
    PASS("1", "审核通过"),

    /**
     * 2：审核中
     */
    AUDITING("2", "审核中"),

    /**
     * 3：审核不通过
     */
    REJECT("3", "审核不通过"),

    /**
     * 审核通过 已过期
     */
    PASS_EXPIRE("4", "已过期");

    private final String code;

    private final String desc;

    HkOpenAcctInvestAuditEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

