package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.crm.cgi.common.base.PageRequest;
import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 客户画像-首页为您推荐查询请求
 * <AUTHOR>
 * @date 2024-03-19 14:45:00
 */
@Getter
@Setter
public class PortraitRecommendRequest extends PageRequest {

    /**
     * 为您推荐tab页
     * 0-全部 1-A基础 2-B投教 3-市场资讯 4-金融产品
     */
    private String recommendTab;

    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;
} 