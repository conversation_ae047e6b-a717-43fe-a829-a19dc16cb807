package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import crm.howbuy.base.validation.MyValidation;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分险测算提交
 * @date 2023/12/5 10:09
 * @since JDK 1.8
 */
@Setter
@Getter
public class RiskQuestionRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -5505338061764132962L;

    /**
     * 问卷ID
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "问卷ID不能为空",isRequired = true)
    private String examId;
    /**
     * 分险测试中的分险合适性选择 1是 0否
     */
    private String riskWarning;

    /**
     * 答案结果list
     */
    private List<Answer> answerDTOList;

    /**
     * @description: 提交的答案
     * @author: jinqing.rao
     * @date: 2023/12/5 10:12
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class Answer implements Serializable {
        private static final long serialVersionUID = -1234567890123456789L;

        /**
         * 问卷ID
         */
        private String questionId;


        /**
         * 选项列表
         */
        private List<String> optionChars;
    }
}
