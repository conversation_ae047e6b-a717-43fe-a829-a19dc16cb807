/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import com.howbuy.hkacconline.facade.query.kyc.calcanswer.dto.AnswerDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/7 16:12
 * @since JDK 1.8
 */
@Data
public class CalculateDerivativeAnswerVO extends AccountBaseVO implements Serializable {

    /**
     * 计算得到的分数
     */
    private BigDecimal derivativeKnowledgeScore;
    /**
     * 衍生金融工具知识 0-无 1-有
     */
    private String derivativeKnowledge;

    /**
     * 试算结果列表接口
     */
    private List<HkDerAnswerVO> answerVOList;

    public static List<HkDerAnswerVO> buildToAnswerList(Map<String, AnswerDTO> detailMap) {
        if (detailMap.isEmpty()) {
            return Arrays.asList();
        }
        List<HkDerAnswerVO> answerVOList = new ArrayList<>();

        // 按键中的数字部分排序
        Map<String, AnswerDTO> sortedMap = detailMap.entrySet().stream()
                .sorted(Comparator.comparingInt(entry -> Integer.parseInt(entry.getKey().replaceAll("\\D", ""))))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        // 处理键冲突（这里不会冲突）
                        (oldValue, newValue) -> oldValue,
                        // 保持插入顺序
                        LinkedHashMap::new
                ));

        sortedMap.forEach((k, v) -> {
            HkDerAnswerVO hkDerAnswerVO = new HkDerAnswerVO();
            hkDerAnswerVO.setOptionId(k);
            hkDerAnswerVO.setOption(v.getOption());
            hkDerAnswerVO.setScore(v.getScore());
            answerVOList.add(hkDerAnswerVO);
        });
        return answerVOList;
    }

}