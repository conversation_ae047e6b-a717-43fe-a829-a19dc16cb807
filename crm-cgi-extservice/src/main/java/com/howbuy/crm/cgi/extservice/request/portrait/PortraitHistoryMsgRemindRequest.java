package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.crm.base.request.PageRequest;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户画像-历史消息提醒事件req
 * @Date 2024/9/2 18:31
 */
public class PortraitHistoryMsgRemindRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 4967684424658881815L;

    /**
     * 一账通号
     */
    @NotBlank(message = "一账通号不能为空!")
    private String hboneNo;

    /**
     * 投顾号
     */
    @NotBlank(message = "投顾编号不能为空!")
    private String conscode;

    /**
     * 提醒类型筛选列表
     * 1-生日提醒
     * 2-周年提醒
     * 3-未作业提醒
     * 4-已读提醒
     * 5-产品到期提醒
     * 6-投后报告提醒
     * 7-内容上新提醒
     * 8-自定义模版提醒
     * 9-APP产品浏览提醒
     * 10-APP自选浏览提醒
     * 11-APP资讯浏览提醒
     * 12-APP视频浏览提醒
     */
    private List<String> remindTypes;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public List<String> getRemindTypes() {
        return remindTypes;
    }

    public void setRemindTypes(List<String> remindTypes) {
        this.remindTypes = remindTypes;
    }
}
