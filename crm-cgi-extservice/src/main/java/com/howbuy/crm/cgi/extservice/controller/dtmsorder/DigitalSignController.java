/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.dtmsorder;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.dtmsorder.DigitalSignRequest;
import com.howbuy.crm.cgi.extservice.request.dtmsorder.QueryDigitalSignDetailRequest;
import com.howbuy.crm.cgi.extservice.request.dtmsorder.QueryDigitalSignListRequest;
import com.howbuy.crm.cgi.extservice.service.DigtalSignService;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.DigitalSignDetailVO;
import com.howbuy.crm.cgi.extservice.vo.dtmsorder.DigitalSignListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description: (海外产品电子签约)
 * @date 2023/5/17 14:36
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/dtmsorder/digitalsign")
public class DigitalSignController {

    @Autowired
    private DigtalSignService digtalSignService;

    /**
     * APP/小程序在线签约功能已经下线,不支持在线签约的功能。
     *
     * @api {POST} /dtmsorder/digitalsign/querylist 查询海外产品电子签约列表接口
     * @apiVersion 1.0.0
     * @apiGroup DigitalSignController
     * @apiName queryList
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} tradeMode 交易方式	必填，默认全部，0-全部；1-购买；2-赎回
     * @apiParam (请求体) {String} tradeState 交易状态  必填，默认全部，0-全部；1-待签约；2-交易中；3-交易成功；4-交易失败；
     * @apiParam (请求体) {String} timeType 时间类型 必填，默认近一年；0-全部；1-近一月；2-近三月；3-近六月；4-近一年
     * @apiParam (请求体) {Number} pageNo 页码
     * @apiParam (请求体) {Number} pageSize pageSize
     * @apiParamExample 请求体示例
     * {"pageNo":2740,"timeType":"Dvs0fyJ1zF","pageSize":442,"tradeMode":"ZvK","tradeState":"1keEVb"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.needSignCount 待签约记录数
     * @apiSuccess (响应结果) {String} data.total 总数
     * @apiSuccess (响应结果) {Array} data.orderList 签约订单列表
     * @apiSuccess (响应结果) {String} data.orderList.preBookId 预约流水号
     * @apiSuccess (响应结果) {String} data.orderList.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.orderList.fundNameAbbr 产品简称
     * @apiSuccess (响应结果) {String} data.orderList.tradeMode 交易方式 1-购买；2-赎回
     * @apiSuccess (响应结果) {String} data.orderList.preAmt 预约金额
     * @apiSuccess (响应结果) {String} data.orderList.preVol 预约份额
     * @apiSuccess (响应结果) {String} data.orderList.currency 当前币种
     * @apiSuccess (响应结果) {String} data.orderList.preDt 预约日期		yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.orderList.expectTradeDt 预计交易日期  	yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.orderList.onlineSignFlag 线上签约标识  0-不支持；1-支持；2-待确定
     * @apiSuccess (响应结果) {String} data.orderList.tradeState 交易状态	0-全部；1-待签约；2-交易中；3-交易成功；4-交易失败；
     * @apiSuccess (响应结果) {String} data.orderList.signDt 签约日期		yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.orderList.ackDt 确认日期	 	yyyy-MM-dd
     * @apiSuccessExample 响应结果示例
     * {"code":"gA3M","data":{"total":"GnhgQGgJ3","needSignCount":"Jmtyx16SE","orderList":[{"preVol":"6","preDt":"FUY","expectTradeDt":"hBte1052","tradeState":"eaB","preAmt":"1GT7","fundCode":"YTTsu3pj","signDt":"WLytOM0w4","tradeMode":"qIlWp1Z2","currency":"i","preBookId":"jVkzgz7Gm","onlineSignFlag":"QYwV8a412","ackDt":"OgZ0dvRC","fundNameAbbr":"A1"}]},"description":"HxW"}
     */
    @PostMapping("/querylist")
    @Deprecated
    public CgiResponse<DigitalSignListVO> queryList(@RequestBody QueryDigitalSignListRequest req) {
        //APP/小程序在线签约功能已经下线,不支持在线签约的功能。 因为小程序不能发版，接口层面直接返回空
        return CgiResponse.ok(new DigitalSignListVO());
//        DigitalSignListVO vo = digtalSignService.queryList(req.getTradeMode(),req.getTradeState(),req.getTimeType(),
//                req.getPageNo(),req.getPageSize());
        //       return CgiResponse.ok(vo);
    }


    /**
     * @api {POST} /dtmsorder/digitalsign/querydetail 查询海外产品电子签约详情接口
     * @apiVersion 1.0.0
     * @apiGroup DigitalSignController
     * @apiName queryDetail
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} signDealNo 签约订单号 必须
     * @apiParamExample 请求体示例
     * {"signDealNo":"EuF0bM"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.signDealInfo 签约订单信息
     * @apiSuccess (响应结果) {String} data.signDealInfo.preBookId 预约流水号
     * @apiSuccess (响应结果) {String} data.signDealInfo.fundCode 产品代码
     * @apiSuccess (响应结果) {String} data.signDealInfo.fundNameAbbr 产品简称
     * @apiSuccess (响应结果) {String} data.signDealInfo.tradeMode 交易方式 1-购买；2-赎回
     * @apiSuccess (响应结果) {String} data.signDealInfo.preAmt 预约金额
     * @apiSuccess (响应结果) {String} data.signDealInfo.preVol 预约份额
     * @apiSuccess (响应结果) {String} data.signDealInfo.currency 当前币种
     * @apiSuccess (响应结果) {String} data.signDealInfo.preDt 预约日期	yyyyMMdd
     * @apiSuccess (响应结果) {String} data.signDealInfo.expectTradeDt 预计交易日期  yyyyMMdd
     * @apiSuccess (响应结果) {String} data.signDealInfo.onlineSignFlag 线上签约标识  0-不支持；1-支持；2-待确定
     * @apiSuccess (响应结果) {String} data.signDealInfo.tradeState 交易状态	0-全部；1-待签约；2-交易中；3-交易成功；4-交易失败；
     * @apiSuccess (响应结果) {String} data.signDealInfo.signDt 签约日期	yyyyMMdd
     * @apiSuccess (响应结果) {String} data.signDealInfo.fundRiskLevel 产品风险等级 1："R1-低风险"；2："R2-中低风险"；3："R3-中风险"；4："R4-中高风险"；5："R5-高风险"
     * @apiSuccess (响应结果) {String} data.signDealInfo.divMode 分红方式 0-红利再投  1-现金分红 2-N/A不适用；
     * @apiSuccess (响应结果) {String} data.signDealInfo.orgiFeeRatio 原始手续费率
     * @apiSuccess (响应结果) {String} data.signDealInfo.preFeeRate 预约折扣率
     * @apiSuccess (响应结果) {String} data.signDealInfo.feeRate 折扣率
     * @apiSuccess (响应结果) {String} data.signDealInfo.fee 预估手续费 保留两位小数
     * @apiSuccess (响应结果) {String} data.signDealInfo.redeemMethod 赎回方式	 1-按份额赎回；2-按金额赎回；
     * @apiSuccess (响应结果) {String} data.signDealInfo.ackDt 确认日期	 yyyyMMdd
     * @apiSuccess (响应结果) {String} data.signDealInfo.ackAmt 确认金额
     * @apiSuccess (响应结果) {String} data.signDealInfo.ackVol 确认份额
     * @apiSuccess (响应结果) {String} data.signDealInfo.ackFee 确认手续费
     * @apiSuccess (响应结果) {String} data.signDealInfo.ackNav 交易日净值
     * @apiSuccess (响应结果) {String} data.signDealInfo.allRedeemFlag 是否全部赎回标识 1是0否
     * @apiSuccess (响应结果) {String} data.signDealInfo.fundRaiseAccountDisplayFlag 资金募集账户展示标识 1是0否
     * @apiSuccess (响应结果) {String} data.signDealInfo.ackStatus 确认状态 0-确认失败  1-确认成功  2-部分确认
     * @apiSuccess (响应结果) {String} data.isOpenAcct 是否开户 0: 已开户 1: 未开户
     * @apiSuccess (响应结果) {String} data.isTradeAcctActive 交易账号是否激活 0:已激活 1:未激活
     * @apiSuccess (响应结果) {String} data.txPasswdType 交易密码状态     NORMAL("0", "正常"),RESET("1", "重置"),UNSET("2", "未设置");
     * @apiSuccess (响应结果) {Object} data.tradeInfo 交易信息
     * @apiSuccess (响应结果) {Array} data.tradeInfo.paymentTypeList 支付方式列表
     * @apiSuccess (响应结果) {String} data.tradeInfo.payAmt 付款金额 保留两位小数
     * @apiSuccess (响应结果) {Array} data.tradeInfo.redeemDirectionList 回款去向列表
     * @apiSuccess (响应结果) {Array} data.bankCardList 银行卡列表
     * @apiSuccess (响应结果) {String} data.bankCardList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.bankCardList.bankAcctNo 银行卡号      a）默认展示：银行名称"前四位"+"****"+"后四位"；      b）若客户存在多张银行卡首尾4位数字一致，则增加中间位校验，即将中间位不一致的第一位展示出来，      样式为：银行名称"前四位"+"**"+"第一个不一样的"+"**"+"后四位"
     * @apiSuccess (响应结果) {String} data.bankCardList.bankLogoUrl 银行logo地址
     * @apiSuccess (响应结果) {String} data.bankCardList.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.bankCardList.bankCode 银行编号
     * @apiSuccess (响应结果) {Array} data.agreementList 合同及协议列表
     * @apiSuccess (响应结果) {String} data.agreementList.fileCode 文件代码
     * @apiSuccess (响应结果) {String} data.agreementList.fileName 文件名称
     * @apiSuccess (响应结果) {String} data.agreementList.filePathUrl 文件路径
     * @apiSuccessExample 响应结果示例
     * {"code":"UHxu","data":{"isTradeAcctActive":"teM3","bankCardList":[{"bankLogoUrl":"OJcPacEGQU","bankCode":"VjhX","bankAcctNo":"ZD050zSLX","cpAcctNo":"gAJ8b91","bankName":"f2ExJ8GyyO"}],"signDealInfo":{"fee":"t","preDt":"BQ2cnh6","feeRate":"i","redeemMethod":"wl9L","fundCode":"dAefX","ackAmt":"YY","preFeeRate":"jYld","ackFee":"kgzKyBt7c","signDt":"N","currency":"Qj","preBookId":"q6oabnDFwx","onlineSignFlag":"qmiK","fundRaiseAccountDisplayFlag":"sZDp4","fundNameAbbr":"0CFBw","ackVol":"6i","preVol":"q4NMUqV","orgiFeeRatio":"GiNf","divMode":"TfqEjR","expectTradeDt":"fzcfUqDVr","tradeState":"PKfdG","ackNav":"VsE","preAmt":"xU3olF9F","fundRiskLevel":"yz","ackStatus":"41","allRedeemFlag":"o1ZUw4Q2p","tradeMode":"EgB6yP7","ackDt":"r7rAe9fy"},"tradeInfo":{"paymentTypeList":["FjfbyUC"],"redeemDirectionList":["piFs5O"],"payAmt":"af"},"agreementList":[{"filePathUrl":"uU5u0B","fileName":"Gd9F8","fileCode":"YX1aorN3u"}],"isOpenAcct":"1yETV","txPasswdType":"Z0xjF"},"description":"lc"}
     */
    @PostMapping("/querydetail")
    @Deprecated
    public CgiResponse<DigitalSignDetailVO> queryDetail(@RequestBody QueryDigitalSignDetailRequest queryDigitalSignDetailRequest) {
        //APP/小程序在线签约功能已经下线,不支持在线签约的功能。 因为小程序不能发版，接口层面直接返回空
        return CgiResponse.ok(new DigitalSignDetailVO());
//        DigitalSignDetailVO vo = digtalSignService.queryDetail(queryDigitalSignDetailRequest.getSignDealNo());
//        return CgiResponse.ok(vo);
    }

    /**
     * @api {POST} /dtmsorder/digitalsign/sign 海外产品电子签约接口
     * @apiVersion 1.0.0
     * @apiGroup DigitalSignController
     * @apiName sign
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} preBookId 预约流水号	必须
     * @apiParam (请求体) {String} txPswd 交易密码		必须
     * @apiParam (请求体) {String} signFlag 协议签订标识	0-未签署；1-已签署 必须
     * @apiParam (请求体) {Array} contractList 合同及协议列表
     * @apiParam (请求体) {String} contractList.fileCode 协议代码
     * @apiParam (请求体) {String} contractList.fileName 文件名称
     * @apiParam (请求体) {String} contractList.filePathUrl 文件路径
     * @apiParamExample 请求体示例
     * {"signFlag":"Lj","contractList":[{"filePathUrl":"bpYW","fileName":"CT","fileCode":"Z8a"}],"txPswd":"eeFAEc2yv","preBookId":"Z"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.signState 签约状态	 0-成功；1-失败；
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccessExample 响应结果示例
     * {"code":"gf1CYHOvB","data":{"returnCode":"r","description":"hgVVn","signState":"u4S7QZJGF"},"description":"UA"}
     */
    @PostMapping("/sign")
    @Deprecated
    public CgiResponse<Body> sign(@RequestBody DigitalSignRequest req) {
        //APP/小程序在线签约功能已经下线,不支持在线签约的功能。 因为小程序不能发版，接口层面直接返回空
        return CgiResponse.ok(new Body());
//        Body vo = digtalSignService.sign(req.getPreBookId(),req.getTxPswd(),req.getSignFlag(),req.getContractList());
//        if(StringUtils.isNotEmpty(vo.getReturnCode())){
//            CgiResponse cgiResponse = new CgiResponse(vo.getReturnCode(), vo.getDescription(), null);
//            return cgiResponse;
//        }
//        return CgiResponse.ok(vo);
    }
}