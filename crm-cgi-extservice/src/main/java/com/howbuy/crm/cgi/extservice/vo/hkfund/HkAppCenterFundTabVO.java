/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: APP个人中心 资产中心基金TAB页查询接口
 * <AUTHOR>
 * @date 2024/8/7 10:12
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAppCenterFundTabVO extends Body implements Serializable {

    private static final long serialVersionUID = 7655112245021086260L;

    /**
     * 基金产品总资产
     */
    private String fundTotalAsset;
    /**
     *千禧年的基金名称和待投金额
     */
    private List<QXNFundInfo> fundInfoList;

    @Setter
    @Getter
    public static class QXNFundInfo implements Serializable {

        private static final long serialVersionUID = -697063015670313867L;

        /**
         * 基金简称
         */
        private String fundAddr;

        /**
         * 待投金额
         */
        private String residueAmt;

    }

}
