/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BaseAttributeVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BusinessInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustPositionVO;
import com.howbuy.crm.portrait.client.domain.dto.label.BaseAttributeDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.BusinessInfoDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.CustPositionDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/29 16:17
 * @since JDK 1.8
 */
public class PortraitBaseInfoConvert {

    /**
     * @param baseAttributeDTO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BaseAttributeVO
     * @description:baseAttributeDTO 转 BaseAttributeVO
     * <AUTHOR>
     * @date 2024/9/29 16:19
     * @since JDK 1.8
     */
    public static BaseAttributeVO convertBaseAttributeVO(BaseAttributeDTO baseAttributeDTO) {
        if (baseAttributeDTO == null) {
            throw new IllegalArgumentException("参数为空!");
        }
        BaseAttributeVO baseAttributeVO = new BaseAttributeVO();
        baseAttributeVO.setAge(baseAttributeDTO.getAge());
        baseAttributeVO.setGender(baseAttributeDTO.getGender());
        // yyyyMMDD 处理为YYYY年MM月DD日
        if(StringUtils.isNotEmpty(baseAttributeDTO.getBirthday())) {
            baseAttributeVO.setBirthday(DateUtils.formatDateStr(baseAttributeDTO.getBirthday(), DateUtils.CHINESE_YYYYMMDD));
        }
        baseAttributeVO.setZodiac(baseAttributeDTO.getZodiac());
        baseAttributeVO.setBirthRegion(baseAttributeDTO.getBirthRegion());
        baseAttributeVO.setResidenceRegion(baseAttributeDTO.getResidenceRegion());
        baseAttributeVO.setDegreeLabelVO(LabelValueConvert.convertSingleLabelValueVO(baseAttributeDTO.getDegreeLabelVO()));
        baseAttributeVO.setProfessional(baseAttributeDTO.getProfessional());
        baseAttributeVO.setIndustryLabelVO(LabelValueConvert.convertSingleLabelValueVO(baseAttributeDTO.getIndustryLabelVO()));
        baseAttributeVO.setLifeStages(baseAttributeDTO.getLifeStages());
        baseAttributeVO.setMarriageLabelVO(LabelValueConvert.convertSingleLabelValueVO(baseAttributeDTO.getMarriageLabelVO()));
        baseAttributeVO.setChildrenLabelVO(LabelValueConvert.convertSingleLabelValueVO(baseAttributeDTO.getChildrenLabelVO()));
        baseAttributeVO.setParentsLabelVO(LabelValueConvert.convertSingleLabelValueVO(baseAttributeDTO.getParentsLabelVO()));
        baseAttributeVO.setHobbiesLabelVO(LabelValueConvert.convertMultipleLabelValueVO(baseAttributeDTO.getHobbiesLabelVO()));
        baseAttributeVO.setNatureLabelVO(LabelValueConvert.convertMultipleLabelValueVO(baseAttributeDTO.getNatureLabelVO()));
        baseAttributeVO.setSocialPreferencesLabelVO(LabelValueConvert.convertMultipleLabelValueVO(baseAttributeDTO.getSocialPreferencesLabelVO()));
        baseAttributeVO.setChannelPreferencesLabelVO(LabelValueConvert.convertSingleLabelValueVO(baseAttributeDTO.getChannelPreferencesLabelVO()));
        return baseAttributeVO;
    }

    /**
     * @param custPositionDTO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.CustPositionVO
     * @description:(CustPositionDTO转CustPositionVO)
     * <AUTHOR>
     * @date 2024/10/9 16:52
     * @since JDK 1.8
     */
    public static CustPositionVO convertCustPositionVO(CustPositionDTO custPositionDTO) {
        if (custPositionDTO == null) {
            throw new IllegalArgumentException("参数为空!");
        }
        CustPositionVO custPositionVO = new CustPositionVO();
        custPositionVO.setCustLifeCycleEnumKey(custPositionDTO.getCustLifeCycleEnumKey());
        custPositionVO.setCustLifeCycle(custPositionDTO.getCustLifeCycle());
        custPositionVO.setLabelCustState(custPositionDTO.getLabelCustState());
        custPositionVO.setProspectsTypeLabelVO(LabelValueConvert.convertSingleLabelValueVO(custPositionDTO.getProspectsTypeLabelDTO()));
        custPositionVO.setZeroStockState(custPositionDTO.getZeroStockState());
        custPositionVO.setHbInvestorType(custPositionDTO.getHbInvestorType());
        custPositionVO.setHzInvestorType(custPositionDTO.getHzInvestorType());
        custPositionVO.setHkInvestorType(custPositionDTO.getHkInvestorType());
        custPositionVO.setSourceType(custPositionDTO.getSourceType());
        custPositionVO.setSourceChannel(custPositionDTO.getSourceChannel());
        custPositionVO.setCustLayered(custPositionDTO.getCustLayered());
        custPositionVO.setLinkAccount(custPositionDTO.getLinkAccount());
        return custPositionVO;
    }

    /**
     * @param businessInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.portrait.baseinfo.BusinessInfoVO
     * @description:BusinessInfoDTO 转 BusinessInfoVO
     * <AUTHOR>
     * @date 2024/10/9 19:45
     * @since JDK 1.8
     */
    public static BusinessInfoVO convertBusinessInfoVO(BusinessInfoDTO businessInfoDTO) {
        BusinessInfoVO businessInfoVO = new BusinessInfoVO();
        businessInfoVO.setInvestKnowledgeLabelVO(LabelValueConvert.convertSingleLabelValueVO(businessInfoDTO.getInvestKnowledgeLabelDTO()));
        businessInfoVO.setKnowledgeHobbyLabelVO(LabelValueConvert.convertSingleLabelValueVO(businessInfoDTO.getKnowledgeHobbyLabelDTO()));
        businessInfoVO.setStrategyUnderstandLabelVO(LabelValueConvert.convertMultipleLabelValueVO(businessInfoDTO.getStrategyUnderstandLabelDTO()));
        businessInfoVO.setServiceStageLabelVO(LabelValueConvert.convertSingleLabelValueVO(businessInfoDTO.getServiceStageLabelDTO()));
        businessInfoVO.setServiceFormPreferencesLabelVO(LabelValueConvert.convertSingleLabelValueVO(businessInfoDTO.getServiceFormPreferencesLabelDTO()));
        businessInfoVO.setServiceModePreferencesLabelVO(LabelValueConvert.convertSingleLabelValueVO(businessInfoDTO.getServiceModePreferencesLabelDTO()));
        businessInfoVO.setTrustLabelVO(LabelValueConvert.convertSingleLabelValueVO(businessInfoDTO.getTrustLabelDTO()));
        businessInfoVO.setMgmCount(businessInfoDTO.getMgmCount());
        // 孵化时长非空且非负值时（-）进行赋值
        if (StringUtils.isNotEmpty(businessInfoDTO.getHatchTime()) && !businessInfoDTO.getHatchTime().contains("-")) {
            businessInfoVO.setHatchTime(businessInfoDTO.getHatchTime());
        }
        return businessInfoVO;
    }
}
