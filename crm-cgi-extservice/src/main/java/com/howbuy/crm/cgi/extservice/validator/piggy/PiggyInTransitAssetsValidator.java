/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.utils.SpringContextUtil;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHwDealOrderOuterService;

/**
 * @description: 储蓄罐底层基金的在途资产
 * <AUTHOR>
 * @date 2024/7/22 16:14
 * @since JDK 1.8
 */
public class PiggyInTransitAssetsValidator implements PiggyValidator<PiggyBankVerificationVO>{

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;

    private final FundBasicInfoDTO fundBasicInfoDTO;

    private final QueryHwDealOrderOuterService queryHwDealOrderOuterService = SpringContextUtil.getBean(QueryHwDealOrderOuterService.class);


    public PiggyInTransitAssetsValidator(HkCustInfoDTO hkCustInfo,FundBasicInfoDTO fundBasicInfoDTO) {
        this.hkCustInfo = hkCustInfo;
        this.fundBasicInfoDTO = fundBasicInfoDTO;
    }

    @Override
    public PiggyBankVerificationVO verification() {
        // 通过香港客户号和基金代码获取在途的资产信息
        boolean result = queryHwDealOrderOuterService.hasFundInTransitOrder(fundBasicInfoDTO.getFundCode(), hkCustInfo.getHkCustNo());
        if(result){
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_HAS_PIGGY_IN_TRANSIT.getCode());
            piggyBankVerificationVO.setShowVerify(YesNoEnum.YES.getCode());
            return piggyBankVerificationVO;
        }
        return null;
    }
}
