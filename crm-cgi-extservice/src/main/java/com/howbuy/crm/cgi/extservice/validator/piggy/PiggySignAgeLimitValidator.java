/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/7/16 20:06
 * @since JDK 1.8
 */
public class PiggySignAgeLimitValidator implements PiggyValidator<PiggyBankVerificationVO> {
    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;

    private final Integer ageLimit;

    public PiggySignAgeLimitValidator(HkCustInfoDTO hkCustInfo, Integer ageLimit) {
        this.hkCustInfo = hkCustInfo;
        this.ageLimit = ageLimit;
    }

    @Override
    public PiggyBankVerificationVO verification() {
        String birthday = hkCustInfo.getBirthday();
        if (StringUtils.isBlank(birthday)) {
            throw new BusinessException(ExceptionCodeEnum.HK_CUST_BIRTHDAY_ERROR);
        }
        String age = DateUtils.calculateAgeByDate(birthday);
        if (Integer.parseInt(age) > ageLimit) {
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setPiggyLimitAge(ageLimit.toString());
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_AGE_GREATER_THAN_65_LIMIT.getCode());
            return piggyBankVerificationVO;
        }
        return null;
    }

}
