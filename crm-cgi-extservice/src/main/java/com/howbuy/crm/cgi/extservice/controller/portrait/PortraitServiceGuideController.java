package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitServiceGuideRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.serviceguide.PortraitServiceGuideService;
import com.howbuy.crm.cgi.extservice.vo.portrait.PortraitServiceGuideVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 客户画像T@T服务指引控制器
 * @Date 2024/9/2 16:15
 */
@Slf4j
@RestController
@RequestMapping("/portrait/serviceguide")
@Validated
public class PortraitServiceGuideController {

    @Resource
    private PortraitServiceGuideService portraitServiceGuideService;

    /**
     * @api {POST} /ext/portrait/serviceguide/info serviceguide()
     * @apiVersion 1.0.0
     * @apiGroup PortraitServiceGuideController
     * @apiName serviceguide()
     * @apiDescription T@T服务指引接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} conscode 投顾编号
     * @apiParam (请求参数) {String} fwjdKey 服务阶段key
     * @apiParam (请求参数) {String} cardKey 服务卡片key
     * @apiParamExample 请求参数示例
     * fwjdKey=Jo4ueGsd&cardKeyKey=DlgvPnY3dQ&hboneNo=I
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.custCycle 客户周期（潜在-QZ 首交-SJ 复购-FG 零存量-LC）
     * @apiSuccess (响应结果) {String} data.fwzyTitle 服务指引标题（eg：T@T服务指引）
     * @apiSuccess (响应结果) {String} data.fwzyImgUrl 服务指引图片地址
     * @apiSuccess (响应结果) {String} data.defaultShowFwJd 默认展示服务阶段（ex:101、102、103...）
     * @apiSuccess (响应结果) {Array} data.dataList 服务阶段列表
     * @apiSuccess (响应结果) {String} data.dataList.fwjdName 服务阶段名称
     * @apiSuccess (响应结果) {String} data.dataList.fwjdKey 服务阶段key（101、102等）
     * @apiSuccess (响应结果) {Array} data.dataList.cardList 服务卡片列表
     * @apiSuccess (响应结果) {String} data.dataList.cardList.cardKey 卡片key（eg：JSHM、LJKH等）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.cardType 卡片类型 1-普通卡片 2-特殊卡片
     * @apiSuccess (响应结果) {String} data.dataList.cardList.cardName 卡片名称
     * @apiSuccess (响应结果) {String} data.dataList.cardList.cardImgUrl 卡片图片地址
     * @apiSuccess (响应结果) {Array} data.dataList.cardList.materialList 卡片素材列表
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.materialTitle 素材标题
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.materialType 素材类型（SC-素材 GJ-工具 QT-其他）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.specialCode 特殊类型code（ZPBG-资配报告 CPYXSC-营销素材）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.createTime 素材创建时间 yyyy-MM-dd HH:mm:ss
     * @apiSuccess (响应结果) {Array} data.dataList.cardList.materialList.contentList 素材内容列表
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.id 内容ID（cms配置id）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.contentType 内容类型（WZ：文章；WJ：问卷；SP：视频；BG：报告；YXS：研习社）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.contentTitle 内容标题
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.shareTitle 分享标题
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.linkUrl 跳转链接
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.sendNum 发送次数
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.visitNum 用户访问
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.contentImg 内容图片地址
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.label 上新标签（New）1-是 0-否
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.shareImg 分享图片地址
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.shareDesc 分享介绍文案
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.vbRadarId 微伴雷达ID
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.materialId 素材ID
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.configContentType 配置内容类型（ZTY:专题页，QT:其他，SC:素材）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.materialType 素材类型（1-素材 2-持仓报告 3-资配报告）
     * @apiSuccess (响应结果) {String} data.dataList.cardList.materialList.contentList.createTime 创建时间 yyyy-MM-dd HH:mm:ss
     * @apiSuccessExample 响应结果示例
     * {"code":"Leh","data":{"custCycle":"h3zB4ABSB","dataList":[{"cardList":[],"fwjdName":"rrtJ0Y","fwjdKey":"6Q"}],"fwzyImgUrl":"obbNyUrR","fwzyTitle":"bZORyXxe"},"description":"XbzkyKf2yB","timestampServer":"qXeJ"}
     */
    @PostMapping("/info")
    public CgiResponse<PortraitServiceGuideVO> serviceguide(
            @RequestBody PortraitServiceGuideRequest request
    ) {
        return CgiResponse.appOk(portraitServiceGuideService.getPortraitServiceGuideInfo(request));
    }

}
