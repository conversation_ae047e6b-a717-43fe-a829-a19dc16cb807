package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 姓名拆分
 * @date 2023/11/30 17:53
 * @since JDK 1.8
 */
@Setter
@Getter
public class SplitNameVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -1936296435551036839L;

    /**
     * 姓
     */
    private String familyName;

    /**
     * 名
     */
    private String firstName;

    public SplitNameVO(String familyName, String firstName) {
        this.familyName = familyName;
        this.firstName = firstName;
    }
}
