package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 个人中心信息查询请求参数类
 * <AUTHOR>
 * @date 2024/1/25 9:58
 * @since JDK 1.8
 */

@Setter
@Getter
public class HkCustPersonalCenterRequest extends AccountBaseRequest implements Serializable {


    private static final long serialVersionUID = -1334810983268733994L;

    /**
     * 小程序版本号
     */
    @Deprecated
    private String appVersion;
}
