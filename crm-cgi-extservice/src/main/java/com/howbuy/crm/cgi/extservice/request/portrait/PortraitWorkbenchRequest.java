package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 工作台首页查询请求参数
 * <AUTHOR>
 * @date 2024-03-19 14:50:00
 */
@Getter
@Setter
public class PortraitWorkbenchRequest extends Body {

    /**
     * 用户Id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "用户id", isRequired = true)
    private String userId;
} 