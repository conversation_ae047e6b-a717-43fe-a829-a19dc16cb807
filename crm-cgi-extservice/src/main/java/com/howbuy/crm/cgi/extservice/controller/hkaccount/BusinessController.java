/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.MobileAreaVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: (业务)
 * <AUTHOR>
 * @date 2023/5/18 14:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/business")
public class BusinessController {

    /**
     * @api {POST} /account/business/getmobilearealist 查询手机地区接口
     * @apiVersion 1.0.0
     * @apiGroup BusinessController
     * @apiName getMobileAreaList
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.areaList 手机号地区list
     * @apiSuccess (响应结果) {String} data.areaList.areaCode 地区码
     * @apiSuccess (响应结果) {String} data.areaList.areaName 地区名称
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccessExample 响应结果示例
     * {"code":"j3","data":{"returnCode":"2aWrCL","areaList":[{"areaCode":"mVypRLKB","areaName":"wtTUvM"}],"description":"U"},"description":"0d"}
     */
    @PostMapping("/getmobilearealist")
    public CgiResponse<MobileAreaVO> getMobileAreaList() {
        //TODO
        return CgiResponse.ok(null);
    }


}