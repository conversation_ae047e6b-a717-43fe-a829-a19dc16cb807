/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account.hkopenacct;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/3/5 19:16
 * @since JDK 1.8
 */
@Data
public class HkAssetStatusRequest extends AccountBaseRequest {

    /**
     * 是否展示资产 0:不展示 1:展示
     */
    private String showAsset;

    /**
     * 标识类型 type== h5 标识小程序
     */
    private String type;
}