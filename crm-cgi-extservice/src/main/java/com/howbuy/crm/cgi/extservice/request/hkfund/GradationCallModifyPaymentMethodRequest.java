/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/15 15:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class GradationCallModifyPaymentMethodRequest implements Serializable {

    private static final long serialVersionUID = 2577648313330772711L;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String dealNo;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    private String payMethod;

    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 资金账号  可选，支付方式为1-银行卡时必填
     */
    private String cpAcctNo;
}
