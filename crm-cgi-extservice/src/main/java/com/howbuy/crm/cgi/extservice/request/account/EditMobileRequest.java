/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (修改手机号的请求实体类)
 * @date 2023/11/29 17:57
 * @since JDK 1.8
 */
@Data
public class EditMobileRequest extends AccountBaseRequest {

    /**
     * 手机号码 加密
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "手机号", isRequired = true)
    private String mobile;

    /**
     * 验证码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码", isRequired = true)
    private String verifyCode;

    /**
     * 交易密码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易密码", isRequired = true)
    private String txPassword;


}