/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBalanceReportRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitProductReportRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitRecommendUpdateRedpointRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.CheckProductReportPermissionRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.report.PortraitReportService;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.PortraitBalanceReportVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.PortraitProductReportVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.recommend.PortraitRecommendUpdateRedpointVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.report.CheckProductReportPermissionVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 客户画像报告控制器
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@RestController
@RequestMapping("/portrait/report")
public class PortraitReportController {

    @Resource
    private PortraitReportService portraitReportService;

    /**
     * @api {POST} /ext/portrait/report/balance getBalanceReport()
     * @apiVersion 1.0.0
     * @apiGroup PortraitReportController
     * @apiName getBalanceReport()
     * @apiDescription 查询持仓投后报告
     * @apiParam (请求体) {String} conscode 交易账号
     * @apiParam (请求体) {String} hboneNo 好买账号
     * @apiParamExample 请求体示例
     * {
     *   "conscode": "123456",
     *   "hboneNo": "HB123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.hmjjList 好买基金模块列表
     * @apiSuccess (响应结果) {String} data.hmjjList.jjdm 基金代码
     * @apiSuccess (响应结果) {String} data.hmjjList.jjjc 基金简称
     * @apiSuccess (响应结果) {String} data.hmjjList.productSubTypeName 产品子类型名称
     * @apiSuccess (响应结果) {String} data.hmjjList.isMoreReport 是否更多(0：否，1：是)
     * @apiSuccess (响应结果) {Array} data.hmjjList.reportList 报告列表
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.reportId 报告ID
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.reportUrl 报告URL
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.materialId 报告发送ID
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.materialSendType 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.date 报告日期(MM月dd日)
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.title 报告标题
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.isNew 是否new标签(0：否，1：是)
     * @apiSuccess (响应结果) {String} data.hmjjList.reportList.sendNum 发送次数∂
     * @apiSuccess (响应结果) {Array} data.hztzList 好臻投资模块列表(字段同hmjjList)
     * @apiSuccess (响应结果) {Array} data.hmxgList 好买香港模块列表(字段同hmjjList)
     * @apiSuccess (响应结果) {String} data.isAuthorization 用户授权状态(0：否，1：是)
     * @apiSuccess (响应结果) {String} data.tgIsXg 投顾所属组织架构是否香港(0：否，1：是)
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "hmjjList": [
     *       {
     *         "jjdm": "000001",
     *         "jjjc": "示例基金",
     *         "productSubTypeName": "混合型",
     *         "isMoreReport": "1",
     *         "reportList": [
     *           {
     *             "date": "2024-03",
     *             "title": "月报",
     *             "isNew": "1",
     *             "sendNum": "2",
     *             "sendAble": "1"
     *           }
     *         ]
     *       }
     *     ],
     *     "hztzList": [],
     *     "hmxgList": [],
     *     "isAuthorization": "1",
     *     "tgIsXg": "0"
     *   },
     *   "timestampServer": "1710831000000"
     * }
     */
    @PostMapping("/balance")
    public CgiResponse<PortraitBalanceReportVO> getBalanceReport(@RequestBody @Valid PortraitBalanceReportRequest request) {
        PortraitBalanceReportVO vo = portraitReportService.getBalanceReport(request);
        return CgiResponse.appOk(vo);
    }

    /**
     * @api {POST} /ext/portrait/report/product getProductReport()
     * @apiVersion 1.0.0
     * @apiGroup PortraitReportController
     * @apiName getProductReport()
     * @apiDescription 查询产品持仓投后报告
     * @apiParam (请求体) {String} conscode 交易账号
     * @apiParam (请求体) {String} hboneNo 好买账号
     * @apiParam (请求体) {String} fundCode 产品代码
     * @apiParam (请求体) {String} year 年份 yyyy
     * @apiParam (请求体) {Integer} page 分页页码
     * @apiParam (请求体) {Integer} size 每页数量
     * @apiParamExample 请求体示例
     * {
     *   "conscode": "123456",
     *   "hboneNo": "HB123456",
     *   "fundCode": "000001",
     *   "year": "2023",
     *   "page": 1,
     *   "size": 20
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.total 数据总量
     * @apiSuccess (响应结果) {String} data.minReportYear 最小报告年份yyyy
     * @apiSuccess (响应结果) {String} data.maxReportYear 最大报告年份yyyy
     * @apiSuccess (响应结果) {Array} data.dataList 数据列表（按年份分组）
     * @apiSuccess (响应结果) {String} data.dataList.year 报告年份(yyyy)
     * @apiSuccess (响应结果) {Array} data.dataList.reportList 报告列表
     * @apiSuccess (响应结果) {String} data.dataList.reportList.reportId 报告ID
     * @apiSuccess (响应结果) {String} data.dataList.reportList.reportUrl 报告URL
     * @apiSuccess (响应结果) {String} data.dataList.reportList.materialId 报告发送ID
     * @apiSuccess (响应结果) {String} data.dataList.reportList.materialSendType 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
     * @apiSuccess (响应结果) {String} data.dataList.reportList.date 报告日期(MM月dd日)
     * @apiSuccess (响应结果) {String} data.dataList.reportList.title 报告标题
     * @apiSuccess (响应结果) {String} data.dataList.reportList.isNew 是否new标签(0：否，1：是)
     * @apiSuccess (响应结果) {String} data.dataList.reportList.sendNum 发送次数
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "total": "10",
     *     "minReportYear": "2023",
     *     "maxReportYear": "2024",
     *     "dataList": [
     *       {
     *         "year": "2024",
     *         "reportList": [
     *           {
     *             "date": "2024-03",
     *             "title": "月报",
     *             "isNew": "1",
     *             "sendNum": "2",
     *             "sendAble": "1",
     *             "sendAbleReason": ""
     *           }
     *         ]
     *       }
     *     ]
     *   },
     *   "timestampServer": "1710831000000"
     * }
     */
    @PostMapping("/product")
    public CgiResponse<PortraitProductReportVO> getProductReport(@RequestBody PortraitProductReportRequest request) {
        PortraitProductReportVO vo = portraitReportService.getProductReport(request);
        return CgiResponse.appOk(vo);
    }

    /**
     * @api {POST} /ext/portrait/report/update/redpoint updateRedpoint()
     * @apiVersion 1.0.0
     * @apiGroup PortraitReportController
     * @apiName updateRedpoint()
     * @apiDescription 更新投后报告小红点
     * @apiParam (请求体) {String} hboneNo 好买账号
     * @apiParamExample 请求体示例
     * {
     *   "conscode": "123456",
     *   "hboneNo": "HB123456"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.result 更新结果(1-成功，0-失败)
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "data": {
     *     "result": "Y"
     *   },
     *   "timestampServer": "1710831000000"
     * }
     */
    @PostMapping("/update/redpoint")
    public CgiResponse<PortraitRecommendUpdateRedpointVO> updateRedpoint(@RequestBody PortraitRecommendUpdateRedpointRequest request) {
        PortraitRecommendUpdateRedpointVO vo = portraitReportService.updateRedpoint(request);
        return CgiResponse.appOk(vo);
    }

    /**
     * @api {POST} /ext/portrait/report/checksendpermission checkSendPermission()
     * @apiVersion 1.0.0
     * @apiGroup PortraitReportController
     * @apiName checkSendPermission
     * @apiDescription 校验产品报告发送权限
     * @apiParam (请求参数) {String} conscode 投顾编号
     * @apiParam (请求参数) {String} fundCode 产品代码
     * @apiParamExample 请求参数示例
     * {
     *     "conscode": "123456",
     *     "fundCode": "000001"
     * }
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Integer} data.sendAble 是否可发送(0:否 1:是)
     * @apiSuccess (响应结果) {String} data.sendAbleReason 不可发送原因
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {
     *     "code": "000000",
     *     "description": "成功",
     *     "data": {
     *         "sendAble": 1,
     *         "sendAbleReason": ""
     *     },
     *     "timestampServer": "2025-03-13 13:18:57"
     * }
     */
    @PostMapping("/checksendpermission")
    public CgiResponse<CheckProductReportPermissionVO> checkSendPermission(@RequestBody CheckProductReportPermissionRequest request) {
        CheckProductReportPermissionVO vo = portraitReportService.checkPermission(request);
        return CgiResponse.appOk(vo);
    }
} 