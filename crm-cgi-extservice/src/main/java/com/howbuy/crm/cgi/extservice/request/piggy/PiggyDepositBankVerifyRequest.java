/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.piggy;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 打款凭证上传页面的银行卡有效性校验
 * <AUTHOR>
 * @date 2024/7/23 10:27
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositBankVerifyRequest implements Serializable {

    private static final long serialVersionUID = -2365310826797911046L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;
}
