/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.wechat.ConfigSignatureRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.wechat.GetWechatUserIdRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.wechat.PortraitWechatService;
import com.howbuy.crm.cgi.extservice.vo.portrait.wechat.ConfigSignatureVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.wechat.WeChatUserIdVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信控制器
 * @date 2024/9/3 9:49
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/portrait/wechat")
public class PortraitWechatController {

    @Resource
    private PortraitWechatService portraitWechatService;


    /**
     * @api {POST} /ext/portrait/wechat/getuserid getUserId()
     * @apiVersion 1.0.0
     * @apiGroup PortraitWechatController
     * @apiName getUserId()
     * @apiDescription 获取企微用户id
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求参数) {String} code code
     * @apiParamExample 请求参数示例
     * code=3QXMD5P
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.userId 微信用户id
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"IG4e","data":{"userId":"dSZSj4uc"},"description":"UG34","timestampServer":"8qAqBf"}
     */
    @PostMapping("/getuserid")
    public CgiResponse<WeChatUserIdVO> getUserId(@RequestBody GetWechatUserIdRequest request) {
        return CgiResponse.appOk(portraitWechatService.getUserId(request));
    }


    /**
     * @api {POST} /ext/portrait/wechat/getconfigsignature getConfigSignature()
     * @apiVersion 1.0.0
     * @apiGroup PortraitWechatController
     * @apiName getConfigSignature()
     * @apiDescription 获取企微配置签名
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} url url
     * @apiParamExample 请求体示例
     * {"url":"SR3Ila"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.timestamp 时间戳
     * @apiSuccess (响应结果) {String} data.nonceStr 随机字符
     * @apiSuccess (响应结果) {String} data.signature 签名
     * @apiSuccess (响应结果) {String} data.appTimestamp app-时间戳
     * @apiSuccess (响应结果) {String} data.appNonceStr app-随机字符
     * @apiSuccess (响应结果) {String} data.appSignature app-签名
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Xp6uI4","data":{"appNonceStr":"rXw2cd","signature":"hcD0","appSignature":"TOawUcRVw","nonceStr":"X2pxLsyOQ","timestamp":"8s7q8T9","appTimestamp":"qz"},"description":"o","timestampServer":"wTuDTv"}
     */
    @PostMapping("/getconfigsignature")
    public CgiResponse<ConfigSignatureVO> getConfigSignature(@RequestBody ConfigSignatureRequest request) {
        return CgiResponse.appOk(portraitWechatService.getConfigSignature(request));
    }


}
