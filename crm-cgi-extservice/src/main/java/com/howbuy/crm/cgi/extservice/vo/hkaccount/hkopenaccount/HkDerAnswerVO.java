/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.DerAnswerDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/7 18:02
 * @since JDK 1.8
 */
@Data
public class HkDerAnswerVO {

    /**
     * 题目ID
     */
    private String optionId;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 历史选项
     */
    private String option;

    /**
     * @description:(数据格式转换)
     * @param derAnswerDTOList
     * @return java.util.List<com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount.HkDerAnswerVO>
     * @author: xufanchao
     * @date: 2024/11/7 18:04
     * @since JDK 1.8
     */
    public static List<HkDerAnswerVO> buildToDerAnswerList(List<DerAnswerDTO> derAnswerDTOList) {
        if (CollectionUtils.isNotEmpty(derAnswerDTOList)) {
            return derAnswerDTOList.stream().map(it -> {
                HkDerAnswerVO hkDerAnswerVO = new HkDerAnswerVO();
                hkDerAnswerVO.setOption(it.getOption());
                hkDerAnswerVO.setOptionId(it.getOptionId());
                hkDerAnswerVO.setScore(it.getScore());
                return hkDerAnswerVO;
            }).collect(Collectors.toList());

        }else {
            return Arrays.asList();
        }
    }

}