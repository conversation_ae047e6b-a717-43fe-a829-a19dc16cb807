package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 支付方式 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
 * @author: shuai.zhang
 * @date: 2023/5/29
 * @since JDK 1.8
 * @version: 1.0
 */
public enum PaymentModeEnum {

    TELEGRAPHICTRANSFER("1", "电汇"),
    CHECK("2", "支票"),
    OVERSEASPIGGY("3", "海外储蓄罐");

    private final String key;
    private final String desc;

    public static PaymentModeEnum getPaymentModeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        PaymentModeEnum paymentModeEnum = getPaymentModeEnum(code);
        return paymentModeEnum == null ? null : paymentModeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    PaymentModeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
