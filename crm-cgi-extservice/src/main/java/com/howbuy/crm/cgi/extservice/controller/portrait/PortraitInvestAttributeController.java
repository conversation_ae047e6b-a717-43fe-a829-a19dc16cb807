/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitAssetInfoRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitBaseRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitTradeActionRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitWealthLevelRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.invest.PortraitInvestAttributeService;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.CustAssetInfoVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.InvestStyleVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.TradeActionVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.WealthLevelVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/3 16:35
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/portrait/investattribute")
public class PortraitInvestAttributeController {

    @Resource
    private PortraitInvestAttributeService portraitInvestAttributeService;

    /**
     * @api {POST} /ext/portrait/investattribute/queryinveststyle queryInvestStyle()
     * @apiVersion 1.0.0
     * @apiGroup PortraitInvestAttributeController
     * @apiName queryInvestStyle()
     * @apiDescription 查询投资风格
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"L6cNC"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Object} data.subjectiveInvestTargetVO 主观投资目标标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveInvestTargetVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveInvestTargetVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestTargetVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestTargetVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveInvestTargetVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.objectiveInvestTargetVO 客观投资目标标签VO
     * @apiSuccess (响应结果) {String} data.objectiveInvestTargetVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.objectiveInvestTargetVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestTargetVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestTargetVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.objectiveInvestTargetVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveRiskToleranceVO 主观风险承受力标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveRiskToleranceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveRiskToleranceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveRiskToleranceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveRiskToleranceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveRiskToleranceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.objectiveRiskToleranceVO 客观险承受力标签VO
     * @apiSuccess (响应结果) {String} data.objectiveRiskToleranceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.objectiveRiskToleranceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveRiskToleranceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveRiskToleranceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.objectiveRiskToleranceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveLiquidityVO 主观流动性标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveLiquidityVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveLiquidityVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveLiquidityVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveLiquidityVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveLiquidityVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.objectiveLiquidityVO 客观流动性标签VO
     * @apiSuccess (响应结果) {String} data.objectiveLiquidityVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.objectiveLiquidityVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveLiquidityVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveLiquidityVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.objectiveLiquidityVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveInvestDeadlineVO 主观投资期限标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveInvestDeadlineVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveInvestDeadlineVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestDeadlineVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestDeadlineVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveInvestDeadlineVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.objectiveInvestDeadlineVO 客观投资期限标签VO
     * @apiSuccess (响应结果) {String} data.objectiveInvestDeadlineVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.objectiveInvestDeadlineVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestDeadlineVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestDeadlineVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.objectiveInvestDeadlineVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveInvestExperienceVO 主观投资经验标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.objectiveInvestExperienceVO 客观投资经验标签VO
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveInvestExperienceYearVO 主观投资经验年限标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceYearVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceYearVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceYearVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceYearVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveInvestExperienceYearVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.objectiveInvestExperienceYearVO 客观投资经验年限标签VO
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceYearVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceYearVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceYearVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceYearVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.objectiveInvestExperienceYearVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveStrategyPreferenceVO 主观策略偏好标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveStrategyPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.subjectiveStrategyPreferenceVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.subjectiveStrategyPreferenceVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.subjectiveStrategyPreferenceVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.objectiveStrategyPreferenceVO 客观策略偏好标签VO
     * @apiSuccess (响应结果) {String} data.objectiveStrategyPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.objectiveStrategyPreferenceVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.objectiveStrategyPreferenceVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.objectiveStrategyPreferenceVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.subjectiveFofPreferenceVO 主观FOF类偏好
     * @apiSuccess (响应结果) {String} data.subjectiveFofPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.subjectiveFofPreferenceVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.subjectiveFofPreferenceVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.subjectiveFofPreferenceVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.objectiveFofPreferenceVO 客观FOF类偏好
     * @apiSuccess (响应结果) {String} data.objectiveFofPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.objectiveFofPreferenceVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.objectiveFofPreferenceVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.objectiveFofPreferenceVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.subjectiveGlobalAssetPreferenceVO 主观全球资产偏好标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveGlobalAssetPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.subjectiveGlobalAssetPreferenceVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.subjectiveGlobalAssetPreferenceVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.subjectiveGlobalAssetPreferenceVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.objectiveGlobalAssetPreferenceVO 客观全球资产偏好标签VO
     * @apiSuccess (响应结果) {String} data.objectiveGlobalAssetPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.objectiveGlobalAssetPreferenceVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.objectiveGlobalAssetPreferenceVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.objectiveGlobalAssetPreferenceVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {Object} data.subjectiveIncomeExpectedVO 主观收益预期标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveIncomeExpectedVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveIncomeExpectedVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveIncomeExpectedVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveIncomeExpectedVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveIncomeExpectedVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveVolatilityExpectedVO 主观波动预期标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveVolatilityExpectedVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveVolatilityExpectedVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveVolatilityExpectedVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveVolatilityExpectedVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveVolatilityExpectedVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.subjectiveWithdrawnExpectedVO 主观回撤预期标签VO
     * @apiSuccess (响应结果) {String} data.subjectiveWithdrawnExpectedVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.subjectiveWithdrawnExpectedVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveWithdrawnExpectedVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.subjectiveWithdrawnExpectedVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.subjectiveWithdrawnExpectedVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} data.objectiveIncomeExpected 客观收益预期
     * @apiSuccess (响应结果) {String} data.objectiveVolatilityExpected 客观波动预期
     * @apiSuccess (响应结果) {String} data.objectiveWithdrawnExpected 客观回撤预期
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"aie9re","data":{"subjectiveIncomeExpectedVO":{"labelId":"wzPVL","labelBdpValue":"IN","labelValue":"bbfScux7Qq"},"objectiveVolatilityExpected":"Nx0","objectiveInvestDeadlineVO":{"labelId":"4I","labelBdpValue":"xB73N","labelValue":"ydHDoyF"},"subjectiveInvestExperienceYearVO":{"labelId":"mr4i","labelBdpValue":"UgP1xvf","labelValue":"2MqQer"},"subjectiveInvestTargetVO":{"labelId":"2ie","labelBdpValue":"cjDR6","labelValue":"pTEi4rs"},"objectiveInvestExperienceYearVO":{"labelId":"xIA5S3i2","labelBdpValue":"pRvU15","labelValue":"CO9u9K"},"subjectiveStrategyPreferenceVO":{"labelBdpValues":["1q"],"labelCustomizeValues":["8p2"],"labelValues":["7t"],"labelId":"9LX6deb"},"subjectiveInvestDeadlineVO":{"labelId":"XC8K5cu4","labelBdpValue":"vKSxNsBKnH","labelValue":"R"},"objectiveStrategyPreferenceVO":{"labelBdpValues":["j"],"labelCustomizeValues":["CmW7Sz"],"labelValues":["G"],"labelId":"Onecq9toZ"},"subjectiveLiquidityVO":{"labelId":"zoM814gD","labelBdpValue":"CRRCPX","labelValue":"U"},"subjectiveInvestExperienceVO":{"labelId":"LmSaJ","labelBdpValue":"xZeGqfg","labelValue":"8f"},"subjectiveWithdrawnExpectedVO":{"labelId":"uykc","labelBdpValue":"oKxtB","labelValue":"ok"},"objectiveIncomeExpected":"Ean79qHS","objectiveWithdrawnExpected":"0fhNysP2","objectiveInvestTargetVO":{"labelId":"oZai","labelBdpValue":"38sm","labelValue":"yBzava7Z"},"objectiveInvestExperienceVO":{"labelId":"2h2w6","labelBdpValue":"e3Dso2jL","labelValue":"I3yiJGjp68"},"objectiveLiquidityVO":{"labelId":"SY","labelBdpValue":"5FM","labelValue":"NaohtoV"},"subjectiveVolatilityExpectedVO":{"labelId":"JhZ6Qf","labelBdpValue":"709Ycy49L","labelValue":"7RlupitMZr"}},"description":"bWksXyp","timestampServer":"t0vApAW"}
     */
    @PostMapping("/queryinveststyle")
    @ResponseBody
    public CgiResponse<InvestStyleVO> queryInvestStyle(@RequestBody PortraitBaseRequest request) {
        return CgiResponse.appOk(portraitInvestAttributeService.queryInvestStyle(request));
    }

    /**
     * @api {POST} /ext/portrait/investattribute/querywealthlevel queryWealthLevel()
     * @apiVersion 1.0.0
     * @apiGroup PortraitInvestAttributeController
     * @apiName queryWealthLevel()
     * @apiDescription 查询财富水平
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"SceB3vIIdB"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.dataStatsDate 数据统计日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {Object} data.familyTotalAssetVO 家庭总资产 标签VO
     * @apiSuccess (响应结果) {String} data.familyTotalAssetVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.familyTotalAssetVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.familyTotalAssetVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.familyTotalAssetVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.familyTotalAssetVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.personalTotalAssetVO 个人总资产 标签VO
     * @apiSuccess (响应结果) {String} data.personalTotalAssetVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.personalTotalAssetVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.personalTotalAssetVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.personalTotalAssetVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.personalTotalAssetVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.expectedCanInvestAmountVO 预计可投金额 标签VO
     * @apiSuccess (响应结果) {String} data.expectedCanInvestAmountVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.expectedCanInvestAmountVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.expectedCanInvestAmountVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.expectedCanInvestAmountVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.expectedCanInvestAmountVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.incomeSourceVO 收入来源 标签VO
     * @apiSuccess (响应结果) {String} data.incomeSourceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.incomeSourceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.incomeSourceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.incomeSourceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.incomeSourceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.annualIncomeVO 年收入 标签VO
     * @apiSuccess (响应结果) {String} data.annualIncomeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.annualIncomeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.annualIncomeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.annualIncomeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.annualIncomeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.canDominateIncomeVO 可支配年收入 标签VO
     * @apiSuccess (响应结果) {String} data.canDominateIncomeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.canDominateIncomeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.canDominateIncomeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.canDominateIncomeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.canDominateIncomeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.canInvestableRatioVO 其中可投比例 标签VO
     * @apiSuccess (响应结果) {String} data.canInvestableRatioVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.canInvestableRatioVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.canInvestableRatioVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.canInvestableRatioVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.canInvestableRatioVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"JuDEZSGyd","data":{"canDominateIncomeVO":{"labelId":"9i1mg5Oq","labelBdpValue":"a","labelValue":"nQeifELNL"},"expectedCanInvestAmountVO":{"labelId":"p","labelBdpValue":"i7F50","labelValue":"x"},"personalTotalAssetVO":{"labelId":"jWlQ","labelBdpValue":"x","labelValue":"CFhY"},"annualIncomeVO":{"labelId":"OxMI9zGM","labelBdpValue":"af5rpF","labelValue":"Pye63NMdw"},"canInvestableRatioVO":{"labelId":"p","labelBdpValue":"Lj16PJ6Zxj","labelValue":"BU9LZk4dw2"},"incomeSourceVO":{"labelId":"W8GN","labelBdpValue":"UQXKPKpmzj","labelValue":"Cw6"},"familyTotalAssetVO":{"labelId":"7ZjSN","labelBdpValue":"xSNiyKvz","labelValue":"5HCLl5Pca"}},"description":"Kp","timestampServer":"i5By4OU0F"}
     */
    @PostMapping("/querywealthlevel")
    @ResponseBody
    public CgiResponse<WealthLevelVO> queryWealthLevel(@RequestBody PortraitWealthLevelRequest request) {
        return CgiResponse.appOk(portraitInvestAttributeService.queryWealthLevel(request));
    }


    /**
     * @api {POST} /ext/portrait/investattribute/queryassetinfo queryAssetInfo()
     * @apiVersion 1.0.0
     * @apiGroup PortraitInvestAttributeController
     * @apiName queryAssetInfo()
     * @apiDescription 查询客户资产信息接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} externalBalanceWay 包含外部资产方式 0-不包含外部资产 1-包含外部资产
     * @apiParamExample 请求体示例
     * {"externalBalanceWay":"WetXh8KA","hboneNo":"IOvJP"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.dataStatsDate 数据统计日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {String} data.incomeTotalAssetAmt 收益型总资产金额
     * @apiSuccess (响应结果) {String} data.notIncomeAssetAmt 好买非收益创新产品资产金额
     * @apiSuccess (响应结果) {String} data.hkCenterOrg 是否在香港中心 0-否 1-是
     * @apiSuccess (响应结果) {Object} data.investAreaRatioVO 国内海外占比
     * @apiSuccess (响应结果) {String} data.investAreaRatioVO.internalAmt 国内市值
     * @apiSuccess (响应结果) {String} data.investAreaRatioVO.internalRatio 国内占比
     * @apiSuccess (响应结果) {String} data.investAreaRatioVO.internalRatioHundred 国内占比乘100
     * @apiSuccess (响应结果) {String} data.investAreaRatioVO.overseasAmt 海外市值
     * @apiSuccess (响应结果) {String} data.investAreaRatioVO.overseasRatio 海外占比
     * @apiSuccess (响应结果) {String} data.investAreaRatioVO.overseasRatioHundred 海外占比乘100
     * @apiSuccess (响应结果) {Object} data.incomeAssetRatioVO 持仓好买内资产和外部资产占比
     * @apiSuccess (响应结果) {String} data.incomeAssetRatioVO.externalRatio 外部资产占比
     * @apiSuccess (响应结果) {String} data.incomeAssetRatioVO.externalTotalAmt 外部资产总金额
     * @apiSuccess (响应结果) {String} data.incomeAssetRatioVO.howbuyRatio 好买内资产占比
     * @apiSuccess (响应结果) {String} data.incomeAssetRatioVO.howbuyTotalAmt 好买内资产总金额
     * @apiSuccess (响应结果) {String} data.incomeAssetRatioVO.howbuyRatioHundred 好买内资产占比乘100
     * @apiSuccess (响应结果) {String} data.incomeAssetRatioVO.externalRatioHundred 外部资产占比乘100
     * @apiSuccess (响应结果) {Array} data.strategyRatioList 一级策略占比
     * @apiSuccess (响应结果) {String} data.strategyRatioList.type 类型 0 国内 1 海外
     * @apiSuccess (响应结果) {String} data.strategyRatioList.typeName 类型名
     * @apiSuccess (响应结果) {String} data.strategyRatioList.amt 市值
     * @apiSuccess (响应结果) {String} data.strategyRatioList.ratio 占比
     * @apiSuccess (响应结果) {String} data.strategyRatioList.ratioHundred 占比 100
     * @apiSuccess (响应结果) {String} data.strategyRatioList.gnRatioHundred 国内占比100
     * @apiSuccess (响应结果) {String} data.strategyRatioList.gnAmt 国内市值
     * @apiSuccess (响应结果) {String} data.strategyRatioList.hwRatioHundred 海外占比100
     * @apiSuccess (响应结果) {String} data.strategyRatioList.hwAmt 海外市值
     * @apiSuccess (响应结果) {String} data.strategyRatioList.gnRatio 国内占比
     * @apiSuccess (响应结果) {String} data.strategyRatioList.hwRatio 海外占比
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"EOFH","data":{"incomeTotalAssetAmt":"AeytQbENu","notIncomeAssetAmt":"N6ZTZpZl80","hkCenterOrg":"1","strategyRatioList":[{"ratioHundred":"YB","hwRatioHundred":"y1hnzhti","hwRatio":"xgSsTly","typeName":"lTalwQjv","amt":"ws2","hwAmt":"mC5","type":"9vsfC5d","gnRatio":"AM","gnRatioHundred":"uxwjNRlC6P","ratio":"RHO","gnAmt":"NxG5s"}],"incomeAssetRatioVO":{"externalTotalAmt":"3DbjD","externalRatio":"yBP7nRT","howbuyRatioHundred":"WvH0JdthT","howbuyRatio":"mz0QdIx33U","howbuyTotalAmt":"y6","externalRatioHundred":"S"},"investAreaRatioVO":{"internalRatioHundred":"v","internalAmt":"aIZtIi5","overseasRatio":"BxHtQ8Gql8","overseasRatioHundred":"zTQ2M2","overseasAmt":"zdv83oVN","internalRatio":"siEcyG"},"dataStatsDate":"Shpq09ie"},"description":"0rE","timestampServer":"mh"}
     */
    @PostMapping("/queryassetinfo")
    @ResponseBody
    public CgiResponse<CustAssetInfoVO> queryAssetInfo(@RequestBody PortraitAssetInfoRequest request) {
        return CgiResponse.appOk(portraitInvestAttributeService.queryAssetInfo(request));
    }

    /**
     * @api {POST} /ext/portrait/investattribute/querytradeaction queryTradeAction()
     * @apiVersion 1.0.0
     * @apiGroup PortraitInvestAttributeController
     * @apiName queryTradeAction()
     * @apiDescription 查询客户交易行为
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParamExample 请求体示例
     * {"hboneNo":"Yk"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.dataStatsDate 数据统计日期 yyyy-MM-dd
     * @apiSuccess (响应结果) {Object} data.averageBuyCountVO 平均买入次数
     * @apiSuccess (响应结果) {String} data.averageBuyCountVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.averageBuyCountVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageBuyCountVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageBuyCountVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.averageBuyCountVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.averageSellCountVO 平均卖出次数
     * @apiSuccess (响应结果) {String} data.averageSellCountVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.averageSellCountVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageSellCountVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageSellCountVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.averageSellCountVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.averageHoldTimeVO 平均持有时长
     * @apiSuccess (响应结果) {String} data.averageHoldTimeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.averageHoldTimeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageHoldTimeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageHoldTimeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.averageHoldTimeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.singleCountHoldTimeVO 单次最长持有时长
     * @apiSuccess (响应结果) {String} data.singleCountHoldTimeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.singleCountHoldTimeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.singleCountHoldTimeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.singleCountHoldTimeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.singleCountHoldTimeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.singleProductHoldTimeVO 单产品最长持有时长
     * @apiSuccess (响应结果) {String} data.singleProductHoldTimeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.singleProductHoldTimeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.singleProductHoldTimeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.singleProductHoldTimeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.singleProductHoldTimeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.maxEmptyHoldTimeVO 最长空仓时长
     * @apiSuccess (响应结果) {String} data.maxEmptyHoldTimeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.maxEmptyHoldTimeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.maxEmptyHoldTimeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.maxEmptyHoldTimeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.maxEmptyHoldTimeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.clearHoldSinceTimeVO 清仓以来时长
     * @apiSuccess (响应结果) {String} data.clearHoldSinceTimeVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.clearHoldSinceTimeVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.clearHoldSinceTimeVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.clearHoldSinceTimeVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.clearHoldSinceTimeVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.averageBuyPriceVO 平均买入单价
     * @apiSuccess (响应结果) {String} data.averageBuyPriceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.averageBuyPriceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageBuyPriceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageBuyPriceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.averageBuyPriceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.averageSellPriceVO 平均卖出单价
     * @apiSuccess (响应结果) {String} data.averageSellPriceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.averageSellPriceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageSellPriceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.averageSellPriceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.averageSellPriceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.tradeWayPreferenceVO 交易方式偏好
     * @apiSuccess (响应结果) {String} data.tradeWayPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.tradeWayPreferenceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.tradeWayPreferenceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.tradeWayPreferenceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.tradeWayPreferenceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.payWayPreferenceVO 支付方式偏好
     * @apiSuccess (响应结果) {String} data.payWayPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.payWayPreferenceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.payWayPreferenceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.payWayPreferenceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.payWayPreferenceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.dividendsPreferenceVO 分红偏好
     * @apiSuccess (响应结果) {String} data.dividendsPreferenceVO.labelId 标签id
     * @apiSuccess (响应结果) {String} data.dividendsPreferenceVO.labelValue 标签枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.dividendsPreferenceVO.labelBdpValue 标签Bdp枚举值 enumKey
     * @apiSuccess (响应结果) {String} data.dividendsPreferenceVO.labelInputValue 标签输入框值
     * @apiSuccess (响应结果) {String} data.dividendsPreferenceVO.labelBdpInputValue 标签Bdp输入框值
     * @apiSuccess (响应结果) {Object} data.behaviorDeviationVO 行为偏差
     * @apiSuccess (响应结果) {String} data.behaviorDeviationVO.labelId 标签id
     * @apiSuccess (响应结果) {Array} data.behaviorDeviationVO.labelValues 标签枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.behaviorDeviationVO.labelBdpValues 标签BDP枚举值列表 enumKey
     * @apiSuccess (响应结果) {Array} data.behaviorDeviationVO.labelCustomizeValues 标签自定义值列表
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"v7SSJtk","data":{"averageSellCountVO":{"labelId":"Zcx5zvn8","labelBdpValue":"UWbWsY9V9","labelValue":"JBC"},"dividendsPreferenceVO":{"labelId":"2dvJDh","labelBdpValue":"iw","labelValue":"ajLk4yqKu1"},"averageBuyCountVO":{"labelId":"86Blzp","labelBdpValue":"eEMiVeKkkJ","labelValue":"FQRZVI8Ak"},"maxEmptyHoldTimeVO":{"labelId":"25aaMb0","labelBdpValue":"mbD0g","labelValue":"EO"},"averageBuyPriceVO":{"labelId":"rU","labelBdpValue":"ac1G","labelValue":"D"},"tradeWayPreferenceVO":{"labelId":"01aB","labelBdpValue":"en5qHpmIZ","labelValue":"bIj2v1ui"},"averageSellPriceVO":{"labelId":"5E","labelBdpValue":"qgjWLn","labelValue":"3hehDVSJZP"},"dataStatsDate":"N6p84","singleProductHoldTimeVO":{"labelId":"SrHd","labelBdpValue":"9K8t4Ha","labelValue":"6piQyw"},"payWayPreferenceVO":{"labelId":"yeMCW","labelBdpValue":"D3F","labelValue":"t6SHbpV"},"singleCountHoldTimeVO":{"labelId":"UqbC","labelBdpValue":"aiqouY","labelValue":"kgVwibjQ"},"behaviorDeviationVO":{"labelBdpValues":["Ca"],"labelCustomizeValues":["0g4fElT"],"labelValues":["y2y8Js6S6"],"labelId":"J3yMj"},"averageHoldTimeVO":{"labelId":"aqy4xSLaX","labelBdpValue":"e","labelValue":"BtiBJ"},"clearHoldSinceTimeVO":{"labelId":"aIWqTp","labelBdpValue":"x","labelValue":"SYDlKzR"}},"description":"AJHnT","timestampServer":"YDAG8"}
     */
    @PostMapping("/querytradeaction")
    @ResponseBody
    public CgiResponse<TradeActionVO> queryTradeAction(@RequestBody PortraitTradeActionRequest request) {
        return CgiResponse.appOk(portraitInvestAttributeService.queryTradeAction(request));
    }

}
