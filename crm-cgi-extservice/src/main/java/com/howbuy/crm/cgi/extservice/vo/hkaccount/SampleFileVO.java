/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: (示例文件的fan)
 * @date 2024/1/2 09:49
 * @since JDK 1.8
 */
@Data
public class SampleFileVO extends Body {

    /**
     * 中国大陆居民开户材料示例
     */
    private String chinaOpenAccountSample;

    /**
     * 非中国大陆居民开户材料示例
     */
    private String foreignOpenAccountSample;

    /**
     * 雇主书面同意书的示例
     */
    private String employerAgreementSample;

    /**
     * 资产证明示例
     */
    private String assetProofSample;

    /**
     * 个人专业投资者通知示例
     */
    private String investorNoticeSample;

    /**
     * 个人专业投资者声明示例
     */
    private String investorDeclarationSample;

    /**
     * 现居地址、通讯地址证明示例
     */
    private String currentAddressProofSample;

    /**
     * 联名账户补充材料示例
     */
    private String jointAccountSupplementSample;

    /**
     * 银行卡照片示例
     */
    private String bankCardPhotoSample;

    /**
     * 打款凭证示例
     */
    private String paymentVoucherSample;

    /**
     * 条款及细则
     */
    private String termsAndConditions;

    /**
     * 复杂产品警告声明
     */
    private String complesProductStatement;
    /**
     * 个人资料收集声明
     */
    private String personInformationStatement;
}