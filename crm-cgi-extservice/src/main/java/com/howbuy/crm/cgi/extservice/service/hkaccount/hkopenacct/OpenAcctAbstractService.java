package com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.ImageUtils;
import com.howbuy.crm.cgi.extservice.common.constant.ExternalConstant;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctPdfTypeEnum;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.common.utils.hkopen.HkOpenImageUrlUtils;
import com.howbuy.crm.cgi.extservice.service.hkaccount.HkCommonService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.IdTypeVO;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductOpenAccConfigDTO;
import com.howbuy.crm.cgi.manager.outerservice.CrmResult;
import com.howbuy.crm.cgi.manager.outerservice.cms.CmsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.ProductOpenAccOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkAccCommonOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkopen.OpenAcctCatchService;
import com.howbuy.dfile.HFileService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 海外小程序抽象服务
 * @date 2024/1/22 18:40
 * @since JDK 1.8
 */
public abstract class OpenAcctAbstractService {

    private static final Logger log = LoggerFactory.getLogger(OpenAcctAbstractService.class);

    @Resource
    protected HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    protected HkAccCommonOuterService hkAccCommonOuterService;

    @Resource
    protected HkCommonService hkCommonService;

    @Resource
    protected OpenAcctCatchService openAcctCatchService;

    @Resource
    protected CmsOuterService cmsOuterService;

    @Resource
    protected ProductOpenAccOuterService productOpenAccOuterService;


    /**
     * @param imageType       照片的类型
     * @param hkCusNo         香港客户号
     * @param bytes           文件流
     * @param imageFormatType 文件类型
     * @param imageName       文件名称
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO
     * @description: 上传图片并且获取图片对应的DFILE的文件地址和压缩图片的地址URL
     * @author: jinqing.rao
     * @date: 2023/12/20 15:04
     * @since JDK 1.8
     */
    protected ImageVO getUploadImageUrl(String imageType, String hkCusNo, byte[] bytes, String imageFormatType, String imageName) {
        if (StringUtils.isNotBlank(imageName)) {
            //校验文件的原始名称，是否包含小数点,换行符,空格等特殊字符
            if (StringUtils.containsAny(imageName, MarkConstants.SEPARATOR_DOT, File.separator, MarkConstants.SEPARATOR_SPACE, MarkConstants.SEPARATOR_SLASH, "\\n", "\n")) {
                throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_FILE_NAME_FORMAT_ERROR);
            }
        }
        //路劲名称
        String fileUrl = HkOpenImageUrlUtils.getRelativePathUrl(imageType, hkCusNo);
        //基础文件名称 格式 原始名称+ 客户号+ 文件类型+时间戳
        String baseFileName = HkOpenImageUrlUtils.getBaseFileName(imageType, hkCusNo, imageName);
        //基础文件名称添加格式后缀
        String fileName = baseFileName + MarkConstants.SEPARATOR_DOT + imageFormatType;
        //上传
        try {
            HFileService.getInstance().write(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, fileUrl, fileName, bytes);
        } catch (Exception e) {
            log.error("batchUploadIdPhoto>>>上传文件失败,hkCustNo :{}", hkCusNo, e);
        }
        //生成压缩文件
        String thumbnailName = HkOpenImageUrlUtils.getThumbnailNameByBaseFileName(baseFileName);
        //压缩图片添加后缀
        String thumbnailFileName = thumbnailName + MarkConstants.SEPARATOR_DOT + imageFormatType;
        //上传压缩图
        try {
            InputStream inputStream = ImageUtils.compressImage(ImageUtils.byteToInputStream(bytes));
            HFileService.getInstance().write(ExternalConstant.PAY_VOUCHER_TEMP_STORE_CONFIG, fileUrl, thumbnailFileName, inputStream);
        } catch (Exception e) {
            log.error("batchUploadIdPhoto>>>上传压缩图失败,hkCustNo :{}", hkCusNo, e);
        }
        String realFileUrl = fileUrl + fileName;
        String thumbnailFileUrl = fileUrl + thumbnailFileName;
        return new ImageVO(realFileUrl, thumbnailFileUrl);
    }

    /**
     * @return java.lang.String
     * @description: 获取香港客户证件号
     * @author: jinqing.rao
     * @date: 2023/12/7 11:05
     * @since JDK 1.8
     */
    protected static String getHkCusNo() {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        if (StringUtils.isBlank(hkCustNo)) {
            throw new BusinessException(ExceptionCodeEnum.ACCT_ERROR);
        }
        return hkCustNo;
    }


    protected Map<String, String> getIdTypeVOListByCountryCode(String countryCode) {
        Map<String, String> idTypeMap = new HashMap<>();
        List<IdTypeVO> idTypeVOList = hkCommonService.getIdTypeVOList(countryCode, null);
        if (CollectionUtils.isNotEmpty(idTypeVOList)) {
            // idTypeVOList 转换成idTypeMap
            idTypeVOList.forEach(f -> idTypeMap.put(f.getIdType(), f.getIdTypeDesc()));
        }
        return idTypeMap;
    }

    protected ProductOpenAccConfigDTO getProductOpenAccConfigDTO() {
        CrmResult<ProductOpenAccConfigDTO> fileList = productOpenAccOuterService.queryList();
        if (null == fileList || !ExternalConstant.DTMS_SUCCESS.equals(fileList.getCode())) {
            throw new BusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_PDF_NOT_EXIST.getCode(), HkOpenAcctPdfTypeEnum.COMPLEX_PRODUCT_WARNING_DECLARATION.getDesc() + "不存在");
        }
        return fileList.getData();
    }
}
