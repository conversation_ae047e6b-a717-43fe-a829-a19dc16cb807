/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.portrait;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.portrait.comm.AddCommRecordRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.comm.QueryCommRecordRequest;
import com.howbuy.crm.cgi.extservice.service.portrait.comm.PortraitCommRecordService;
import com.howbuy.crm.cgi.extservice.vo.portrait.comm.CommRecordListVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 画像沟通记录
 * @date 2024/10/8 16:20
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/portrait/comm")
public class PortraitCommRecordController {

    @Resource
    private PortraitCommRecordService portraitCommRecordService;


    /**
     * @api {POST} /ext/portrait/comm/addcommrecord addCommRecord()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommRecordController
     * @apiName addCommRecord()
     * @apiDescription 添加沟通记录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} conscode 投顾编号
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} commDate 沟通日期 yyyyMMDD
     * @apiParam (请求体) {String} commTime 沟通时间 hhmmss
     * @apiParam (请求体) {String} commMode 沟通方式
     * @apiParam (请求体) {String} commContext 沟通内容
     * @apiParamExample 请求体示例
     * {"commDate":"FOt29zq","commMode":"XqBFjxd","commTime":"wIL2","conscode":"hqlvhO3x","hboneNo":"7b","commContext":"l"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"7ME3hTiGVP","description":"XLNlT0aD5F","timestampServer":"U"}
     */
    @PostMapping("/addcommrecord")
    @ResponseBody
    public CgiResponse<Body> addCommRecord(@RequestBody AddCommRecordRequest request) {
        portraitCommRecordService.addCommRecord(request);
        return CgiResponse.appOk(new Body());
    }


    /**
     * @api {POST} /ext/portrait/comm/querycommrecord queryCommRecord()
     * @apiVersion 1.0.0
     * @apiGroup PortraitCommRecordController
     * @apiName queryCommRecord()
     * @apiDescription 查询沟通记录
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hboneNo 一账通号
     * @apiParam (请求体) {String} queryDate 查询日期 yyyy-MM
     * @apiParam (请求体) {Number} page 页号
     * @apiParam (请求体) {Number} size 每页大小
     * @apiParamExample 请求体示例
     * {"startDt":"zJF","endDt":"z7Y6","hboneNo":"wR"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {Array} data.commGroupList 沟通分组list
     * @apiSuccess (响应结果) {String} data.commGroupList.date 日期 yyyy-MM
     * @apiSuccess (响应结果) {Array} data.commGroupList.commList 沟通记录列表
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commPerson 沟通人
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commPersonCode 沟通人
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commMonthDate 沟通月份日期 yyyy-MM
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commDate 沟通日期 yyyy-MM-dd HH:mm
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commSource 沟通来源
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commSourceDesc 沟通来源描述
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commMode 沟通方式
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commModeDesc 沟通方式
     * @apiSuccess (响应结果) {String} data.commGroupList.commList.commContext 沟通内容
     * @apiSuccess (响应结果) {Array} data.dateList 日期列表 yyyy年mm月
     * @apiSuccess (响应结果) {Number} data.totalCount 总条数
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"V","data":{"commGroupList":{},"dateList":["vKxJdlilS"],"totalCount":5309},"description":"3FHgWjoy","timestampServer":"0TQ"}
     */
    @PostMapping("/querycommrecord")
    @ResponseBody
    public CgiResponse<CommRecordListVO> queryCommRecord(@RequestBody QueryCommRecordRequest request) {
        return CgiResponse.appOk(portraitCommRecordService.queryCommRecord(request));
    }

}
