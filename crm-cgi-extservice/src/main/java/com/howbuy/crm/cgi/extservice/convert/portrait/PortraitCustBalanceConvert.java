/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.common.utils.NumberUtil;
import com.howbuy.crm.cgi.extservice.vo.portrait.investattribute.CustAssetInfoVO;
import com.howbuy.crm.cgi.manager.domain.asset.assetreport.InvestAreaRatioDTO;
import com.howbuy.crm.cgi.manager.domain.asset.assetreport.RatioDTO;
import com.howbuy.crm.cgi.manager.domain.asset.assetreport.assetbalance.BalanceTypeRatioDTO;
import com.howbuy.crm.cgi.manager.domain.asset.assetreport.assetbalance.PortraitBalanceDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户画像-用户资产信息convert
 * @Date 2024/9/14 09:44
 */
@Slf4j
public class PortraitCustBalanceConvert {

    /**
     * 转换资配持仓信息DTO -> 客户画像-用户资产信息VO
     *
     * @param balanceDataDTO 资配持仓信息DTO
     * @return CustAssetInfoVO
     */
    public static CustAssetInfoVO convert(PortraitBalanceDataDTO balanceDataDTO) {
        CustAssetInfoVO vo = new CustAssetInfoVO();
        convert(balanceDataDTO, vo);
        return vo;
    }

    /**
     * 转换资配持仓信息DTO -> 客户画像-用户资产信息VO
     *
     * @param balanceDataDTO 资配持仓信息DTO
     * @param custAssetVO    用户资产信息VO
     */
    public static void convert(PortraitBalanceDataDTO balanceDataDTO, CustAssetInfoVO custAssetVO) {
        if (null == balanceDataDTO || null == custAssetVO) {
            log.info("PortraitCustBalanceConvert 转换VO参数错误！ balanceDataDTO or custAssetVO is null");
            throw new IllegalArgumentException("参数为空！");
        }

        // 收益型总资产金额
        custAssetVO.setIncomeTotalAssetAmt(NumberUtil.format2(balanceDataDTO.getBalanceTotalAmt()));

        // 国内海外占比
        CustAssetInfoVO.InvestAreaRatioDTO assetAreaRetioVo = new CustAssetInfoVO.InvestAreaRatioDTO();
        InvestAreaRatioDTO investAreaRatioDTO = balanceDataDTO.getInvestAreaRatioDTO();
        convertAssetAreaRetioVO(investAreaRatioDTO, assetAreaRetioVo);
        custAssetVO.setInvestAreaRatioVO(assetAreaRetioVo);

        // 持仓好买内资产和外部资产占比
        CustAssetInfoVO.BalanceTypeRatioDTO balanceTypeRatioVO = new CustAssetInfoVO.BalanceTypeRatioDTO();
        convertBalanceTypeRatioVO(balanceDataDTO.getBalanceTypeDto(), balanceTypeRatioVO);
        custAssetVO.setIncomeAssetRatioVO(balanceTypeRatioVO);

        // 一级策略占比
        List<RatioDTO> strategyRatioList = balanceDataDTO.getStrategyRatioList();
        List<CustAssetInfoVO.RatioDTO> list = convertRatioDTO(strategyRatioList);
        custAssetVO.setStrategyRatioList(list);
    }

    /**
     * 转换一级策略占比
     *
     * @param strategyRatioList 资配一级策略占比DTO
     * @return List
     */
    private static List<CustAssetInfoVO.RatioDTO> convertRatioDTO(List<RatioDTO> strategyRatioList) {
        List<CustAssetInfoVO.RatioDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(strategyRatioList)) {
            log.info("PortraitCustBalanceConvert 转换VO参数错误！ strategyRatioList is empty！");
            return list;
        }

        for (RatioDTO ratioDTO : strategyRatioList) {
            CustAssetInfoVO.RatioDTO dto = new CustAssetInfoVO.RatioDTO();
            dto.setType(ratioDTO.getType());
            dto.setTypeName(ratioDTO.getTypeName());
            dto.setAmt(NumberUtil.format2(ratioDTO.getAmt()));
            dto.setRatio(NumberUtil.format2(ratioDTO.getRatio()));
            dto.setRatioHundred(NumberUtil.format2(ratioDTO.getRatioHundred()));
            dto.setGnRatioHundred(NumberUtil.format2(ratioDTO.getGnRatioHundred()));
            dto.setGnAmt(NumberUtil.format2(ratioDTO.getGnAmt()));
            dto.setHwRatioHundred(NumberUtil.format2(ratioDTO.getHwRatioHundred()));
            dto.setHwAmt(NumberUtil.format2(ratioDTO.getHwAmt()));
            dto.setGnRatio(NumberUtil.format2(ratioDTO.getGnRatio()));
            dto.setHwRatio(NumberUtil.format2(ratioDTO.getHwRatio()));
            list.add(dto);
        }

        return list;
    }

    /**
     * 转换 持仓好买内资产和外部资产占比
     *
     * @param balanceTypeDto     资配持仓信息DTO
     * @param balanceTypeRatioVO 画像国内海外占比VO
     */
    private static void convertBalanceTypeRatioVO(BalanceTypeRatioDTO balanceTypeDto, CustAssetInfoVO.BalanceTypeRatioDTO balanceTypeRatioVO) {
        if (null == balanceTypeDto || null == balanceTypeRatioVO) {
            log.info("PortraitCustBalanceConvert 转换VO参数错误！ balanceTypeDto or balanceTypeRatioVO is null");
            return;
        }
        balanceTypeRatioVO.setExternalRatio(NumberUtil.format2(balanceTypeDto.getExternalRatio()));
        balanceTypeRatioVO.setExternalTotalAmt(NumberUtil.format2(balanceTypeDto.getExternalTotalAmt()));
        balanceTypeRatioVO.setExternalRatioHundred(NumberUtil.format2(balanceTypeDto.getExternalRatioHundred()));
        balanceTypeRatioVO.setHowbuyRatio(NumberUtil.format2(balanceTypeDto.getHowbuyRatio()));
        balanceTypeRatioVO.setHowbuyTotalAmt(NumberUtil.format2(balanceTypeDto.getHowbuyTotalAmt()));
        balanceTypeRatioVO.setHowbuyRatioHundred(NumberUtil.format2(balanceTypeDto.getHowbuyRatioHundred()));
    }

    /**
     * 转换 国内海外占比
     *
     * @param investAreaRatioDTO 资配持仓信息DTO
     * @param assetAreaRetioVo   画像国内海外占比VO
     */
    private static void convertAssetAreaRetioVO(InvestAreaRatioDTO investAreaRatioDTO, CustAssetInfoVO.InvestAreaRatioDTO assetAreaRetioVo) {
        if (null == investAreaRatioDTO || null == assetAreaRetioVo) {
            log.info("PortraitCustBalanceConvert 转换VO参数错误！ investAreaRatioDTO or assetAreaRetioVo is null");
            return;
        }

        assetAreaRetioVo.setInternalAmt(NumberUtil.format2(investAreaRatioDTO.getInternalAmt()));
        assetAreaRetioVo.setInternalRatio(NumberUtil.format2(investAreaRatioDTO.getInternalRatio()));
        assetAreaRetioVo.setInternalRatioHundred(NumberUtil.format2(investAreaRatioDTO.getInternalRatioHundred()));
        assetAreaRetioVo.setOverseasAmt(NumberUtil.format2(investAreaRatioDTO.getOverseasAmt()));
        assetAreaRetioVo.setOverseasRatio(NumberUtil.format2(investAreaRatioDTO.getOverseasRatio()));
        assetAreaRetioVo.setOverseasRatioHundred(NumberUtil.format2(investAreaRatioDTO.getOverseasRatioHundred()));
    }
}
