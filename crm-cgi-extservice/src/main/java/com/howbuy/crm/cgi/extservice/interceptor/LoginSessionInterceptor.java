/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.SessionTimeOutException;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.session.HkCustInfo;
import com.howbuy.crm.cgi.extservice.common.session.HkTradeSession;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 香港账户登录验证
 * @date 2023/5/19 14:37
 * @since JDK 1.8
 */
@Slf4j
@Component
public class LoginSessionInterceptor{
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Object loginInfo = request.getSession().getAttribute(Constants.HK_LOGIN_SESSION_NAME);
        //*******************************************************
        if (Objects.isNull(loginInfo)) {
            String custIp = WebUtil.getCustIP(request);
            log.error("loginInfo is null,user unLogin,custIp:{}",custIp);
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
        HkTradeSession tradeSession = null;
        String custIp = WebUtil.getCustIP(request);
        // 这里做个兼容处理
        if(loginInfo instanceof HkTradeSession){
             tradeSession = (HkTradeSession) loginInfo;
        }else if(loginInfo instanceof String){
            tradeSession = JSON.parseObject((String) loginInfo, HkTradeSession.class);
        }else{
            log.error("tradeSession instanceof error,user unLogin,custIp:{}",custIp);
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
        if(null == tradeSession){
            log.error("tradeSession is null,user unLogin,custIp:{}",custIp);
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
        Object hkCustNo = request.getSession().getAttribute(Constants.HKCUSTNO);
        if(null == hkCustNo){
            hkCustNo = tradeSession.getCustInfo().getHkCustNo();
        }
        request.setAttribute(Constants.HKCUSTNO, hkCustNo);
        JSONObject params = processParams();
        MDC.put(Constants.CUST_NO,tradeSession.getCustInfo().getHkCustNo());
        // 设置客户号
        params.put(Constants.HKCUSTNO, hkCustNo);
        //*******************************************************
        // 设置其他参数
        setOtherParams(tradeSession, params);
        // 设置请求参数
        RequestUtil.setRequestParams(params);

        return true;
    }

    /**
     * @return com.alibaba.fastjson.JSONObject
     * @description: 处理请求的参数
     * @author: hongdong.xie
     * @date: 2023/5/19 14:55
     * @since JDK 1.8
     */
    private JSONObject processParams() {
        JSONObject params = RequestUtil.getRequestParams();
        if (params == null) {
            params = JSON.parseObject("{}");
        }

        return params;
    }

    /**
     * @param tradeSession session
     * @param params       参数
     * @return void
     * @description: 其他参数设置
     * @author: hongdong.xie
     * @date: 2023/5/19 15:58
     * @since JDK 1.8
     */
    private void setOtherParams(HkTradeSession tradeSession, JSONObject params) {
        if (Objects.isNull(tradeSession)) {
            return;
        }
        HkCustInfo custInfo = tradeSession.getCustInfo();
        // 设置客户号
        params.put(Constants.HKCUSTNO, custInfo.getHkCustNo());
        // 设置网点号
        params.put(Constants.COOPID, custInfo.getHkCustNo());
        // 设置渠道号
        params.put(Constants.CORPID, custInfo.getHkCustNo());
    }


}
