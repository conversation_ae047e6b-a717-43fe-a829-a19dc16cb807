/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hkaccount;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.MessageTempleteEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.enums.*;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenCustStatusEnum;
import com.howbuy.crm.cgi.extservice.common.session.HkCustInfo;
import com.howbuy.crm.cgi.extservice.common.session.HkTradeSession;
import com.howbuy.crm.cgi.extservice.common.utils.ExceptionUtil;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.common.utils.PasswordValidator;
import com.howbuy.crm.cgi.extservice.common.utils.RequestUtil;
import com.howbuy.crm.cgi.extservice.request.account.ActivateLoginAccountRequest;
import com.howbuy.crm.cgi.extservice.request.account.ActivateTxAccountRequest;
import com.howbuy.crm.cgi.extservice.request.account.LoginByMobileAndVerifyCodeRequest;
import com.howbuy.crm.cgi.extservice.request.account.VerifyMobileRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountLoginLogRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountLoginOutRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountNeedExitRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.*;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCommonDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoCounterDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoPlaintextDTO;
import com.howbuy.crm.cgi.manager.enums.LoginChannelEnum;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkAcctCreditCodeOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPasswordOuterService;
import com.howbuy.hkacconline.facade.query.queryhkloginlog.QueryHkLoginLogResponse;
import crm.howbuy.base.utils.DesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 直销交易登录服务
 * @date 2023/5/17 11:33
 * @since JDK 1.8
 */
@Slf4j
@Service("loginService")
public class LoginService {

    @Autowired
    private CustInfoService custInfoService;
    @Autowired
    private HkPasswordOuterService hkPasswordOuterService;
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private HkAcctCreditCodeOuterService hkAcctCreditCodeOuterService;

    /**
     * 登录证件类型列表
     */
    @Value("${login.idType.list}")
    private String loginIdTypeList;

    /**
     * 次日登录退出时间点
     */
    @Value("${login.exit.time}")
    private String loginExitTime;

    /**
     * @param request    请求request
     * @param custInfo   客户信息
     * @param hkCustNo   香港客户号
     * @param disCode    分销机构号
     * @param outletCode 网点号
     * @return void
     * @description: 创建登录session
     * @author: hongdong.xie
     * @date: 2023/5/17 15:28
     * @since JDK 1.8
     */
    public void createSession(HttpServletRequest request, HkCustInfoDTO custInfo, String hkCustNo, String disCode, String outletCode) {
        log.info("LoginUtils|message hkCustNo:{}, sessionId:{}", new Object[]{hkCustNo, request.getSession().getId()});

        HkTradeSession loginSession = getLoginSessionNoException(request);
        if (loginSession == null) {
            loginSession = new HkTradeSession();
            loginSession.setDisCode(disCode);
            loginSession.setCoopId(outletCode);
        }
        if (custInfo != null) {
            HkCustInfo userInfo = new HkCustInfo();
            userInfo.setCustName(custInfo.getCustName());
            userInfo.setIdType(custInfo.getIdType());
            userInfo.setIdNoDigest(custInfo.getIdNoDigest());
            userInfo.setIdNoMask(custInfo.getIdNoMask());
            if (!StringUtil.isEmpty(hkCustNo)) {
                userInfo.setHkCustNo(hkCustNo);
            }
            String custIp = WebUtil.getCustIP(request);
            loginSession.setLoginIp(custIp);
            loginSession.setCustInfo(userInfo);
            addLoginSession(request, loginSession);
        }
    }

    /**
     * @param request
     * @param tradeSession
     * @return void
     * @description: 添加session
     * @author: hongdong.xie
     * @date: 2023/5/17 13:56
     * @since JDK 1.8
     */
    public static void addLoginSession(HttpServletRequest request, HkTradeSession tradeSession) {
        request.getSession().setAttribute(Constants.HK_LOGIN_SESSION_NAME, JSON.toJSONString(tradeSession));
    }

    /**
     * @description: 删除会话
     * @param: [request]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/6 14:05
     * @since JDK 1.8
     */
    public static void removeLoginSession(HttpServletRequest request) {
        request.getSession().invalidate();
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.common.session.HkTradeSession
     * @description: 获取session，获取不到不抛出异常
     * @author: hongdong.xie
     * @date: 2023/5/17 14:00
     * @since JDK 1.8
     */
    public static HkTradeSession getLoginSessionNoException(HttpServletRequest request) {
        HkTradeSession  tradeSession = null;
        // JSON 序列化
        Object loginInfo = request.getSession().getAttribute(Constants.HK_LOGIN_SESSION_NAME);
        if(loginInfo instanceof HkTradeSession){
            tradeSession = (HkTradeSession) loginInfo;
        }else if(loginInfo instanceof String){
            tradeSession = JSON.parseObject((String) loginInfo, HkTradeSession.class);
        }
        return tradeSession;
    }

    /**
     * @description: 得到登录session里的客户号 不抛异常
     * @param: []
     * @return: java.lang.String
     * @author: shaoyang.li
     * @date: 2023/6/9 14:58
     * @since JDK 1.8
     */
    public static String getLoginHkCustNoNoException() {
        //海外App 获取HKCustNo
        if (RequestUtil.isHkAppLogin()) {
            return RequestUtil.getParameter(Constants.HKCUSTNO);
        }
        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        HkTradeSession loginSessionByRequest = getLoginSessionNoException(httpRequest);
        if (Objects.isNull(loginSessionByRequest)) {
            return "";
        }
        if (Objects.isNull(loginSessionByRequest.getCustInfo())) {
            return "";
        }
        return loginSessionByRequest.getCustInfo().getHkCustNo();
    }

    /**
     * @description: 密码登录
     * @param: [loginAcctType, areaCode, mobile, idType, idNo, loginPassword]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 11:25
     * @since JDK 1.8
     */
    public HkCustNoVO loginByMobileOrIdNoOldVersion(String loginAcctType, String areaCode, String mobile, String idType, String idNo, String loginPassword) {
        HkCustInfoDTO hkCustInfoDTO;
        String loginAcct;

        if (LoginAcctTypeEnum.ID_NO.getKey().equals(loginAcctType)) {
            hkCustInfoDTO = custInfoService.getHkCustInfoByIdNo(idType, DigestUtil.digest(idNo));
            loginAcct = idNo;
        } else {
            hkCustInfoDTO = custInfoService.getHkCustInfoByMobile(areaCode, DigestUtil.digest(mobile));
            loginAcct = mobile;
        }
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        // 设置香港客户号返回值 前端需要跳转至后续流程的均返回客户号
        hkCustNoVO.setHkCustNo(hkCustInfoDTO.getHkCustNo());

        // 校验登录密码未设置
        if (CustPasswdStatusEnum.UNSET.getKey().equals(hkCustInfoDTO.getCustLoginPasswdType())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.LOGIN_PSWD_UNSET);
        }

        String coopId = RequestUtil.getPublicParams(Constants.COOPID);
        String corpId = RequestUtil.getPublicParams(Constants.CORPID);
        String parPhoneModel = RequestUtil.getPublicParams(Constants.PAR_PHONE_MODEL);
        String version = RequestUtil.getPublicParams(Constants.VERSION);
        String ip = RequestUtil.getHttpParameter(Constants.CUST_IP);
        String deviceId = RequestUtil.getPublicParams(Constants.DEVICE_ID);
        String deviceName = RequestUtil.getParameter(Constants.DEVICE_NAME);
        String tokenId = RequestUtil.getParameter(Constants.TOKEN_ID);
        String tokenIdMask = StringUtil.isBlank(tokenId) ? null : MD5Utils.md5Hex(tokenId, String.valueOf(StandardCharsets.UTF_8));
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.hkPasswordLogin(loginAcctType, areaCode, idType,
                InvstTypeEnum.PERS.getKey(), loginAcct, PassWordUtil.encrypt(loginPassword), coopId, parPhoneModel, version, ip, deviceId, deviceName, corpId, tokenIdMask);
        ExceptionUtil.validateLoginHkCommonDTO(hkCommonDTO);

        // 创建会话
        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        createSession(httpRequest, hkCustInfoDTO, hkCustInfoDTO.getHkCustNo(), "", "");

        if (CustPasswdStatusEnum.RESET.getKey().equals(hkCustInfoDTO.getCustLoginPasswdType())) {
            ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.NEED_LOGIN_ACTIVE);
        } else if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfoDTO.getCustTxPasswdType())) {
            ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.TX_ACCT_UN_ACTIVE);
        }
        return hkCustNoVO;
    }

    /**
     * @description: 密码登录
     * @param: [loginAcctType, areaCode, mobile, idType, idNo, loginPassword, deviceName]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 11:25
     * @since JDK 1.8
     */
    public HkCustNoVO loginByMobileOrIdNoNewVersion(String loginAcctType, String idAreaCode, String areaCode, String mobile, String idType, String email, String idNo, String loginPassword, String deviceName) {
        HkCustInfoDTO hkCustInfoDTO = new HkCustInfoDTO();
        String loginAcct = null;
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        hkCustInfoDTO = getHkCustInfoDTO(loginAcctType, idAreaCode, areaCode, mobile, idType, email, idNo, loginPassword, loginAcct, deviceName, hkCustInfoDTO, hkCustNoVO);

        // 创建会话
        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        createSession(httpRequest, hkCustInfoDTO, hkCustInfoDTO.getHkCustNo(), "", "");

        if (hkCustNoVO.getCustState().equals(HkOpenCustStatusEnum.SIGNOFF.getCode()) || hkCustNoVO.getCustState().equals(HkOpenCustStatusEnum.SLEEP.getCode())) {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.SLEEP.getCode());
            return hkCustNoVO;
        }

        if (CustPasswdStatusEnum.RESET.getKey().equals(hkCustInfoDTO.getCustLoginPasswdType())) {
            ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.NEED_LOGIN_ACTIVE);
        } else if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfoDTO.getCustTxPasswdType())) {
            ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.TX_ACCT_UN_ACTIVE);
        }
        return hkCustNoVO;
    }

    /**
     * @param loginAcctType
     * @param idAreaCode
     * @param areaCode
     * @param mobile
     * @param idType
     * @param email
     * @param idNo
     * @param loginPassword
     * @param deviceName
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HkCustNoVO
     * @description: App登录
     * @author: jinqing.rao
     * @date: 2024/2/29 21:14
     * @since JDK 1.8
     */
    public HkCustNoVO loginAppByMobileOrIdNoNewVersion(String loginAcctType, String idAreaCode, String areaCode, String mobile, String idType, String email, String idNo, String loginPassword, String deviceName) {
        HkCustInfoDTO hkCustInfoDTO = new HkCustInfoDTO();
        String loginAcct = null;
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        hkCustInfoDTO = getHkCustInfoDTO(loginAcctType, idAreaCode, areaCode, mobile, idType, email, idNo, loginPassword, loginAcct, deviceName, hkCustInfoDTO, hkCustNoVO);
        //香港客户号加密
        String encryptHkCustNo = getEncryptHkCustNo(hkCustInfoDTO.getHkCustNo());
        hkCustNoVO.setHkCustNo(encryptHkCustNo);
        saveCreditCode(hkCustInfoDTO);
        if (hkCustNoVO.getCustState().equals(HkOpenCustStatusEnum.SIGNOFF.getCode()) || hkCustNoVO.getCustState().equals(HkOpenCustStatusEnum.SLEEP.getCode())) {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.SLEEP.getCode());
            return hkCustNoVO;
        }
        return hkCustNoVO;
    }

    private void saveCreditCode(HkCustInfoDTO hkCustInfoDTO) {
        String coopId = RequestUtil.getPublicParams(Constants.COOPID);
        String corpId = RequestUtil.getPublicParams(Constants.CORPID);
        String parPhoneModel = RequestUtil.getPublicParams(Constants.PAR_PHONE_MODEL);
        String version = RequestUtil.getPublicParams(Constants.VERSION);
        String ip = RequestUtil.getHttpParameter(Constants.CUST_IP);
        String deviceId = RequestUtil.getPublicParams(Constants.DEVICE_ID);
        String deviceName = RequestUtil.getParameter(Constants.DEVICE_NAME);
        String tokenId = RequestUtil.getParameter(Constants.TOKEN_ID);
        String tokenIdMask = MD5Utils.md5Hex(tokenId, String.valueOf(StandardCharsets.UTF_8));
        hkAcctCreditCodeOuterService.addCreditCode(hkCustInfoDTO.getHkCustNo(), coopId, parPhoneModel, version, ip, deviceId, deviceName, corpId, tokenIdMask);
    }


    private HkCustInfoDTO getHkCustInfoDTO(String loginAcctType, String idAreaCode, String areaCode, String mobile, String idType, String email, String idNo, String loginPassword, String loginAcct, String deviceName, HkCustInfoDTO hkCustInfoDTO, HkCustNoVO hkCustNoVO) {
        // 根据证件号码登录
        if (LoginAcctTypeEnum.ID_NO.getKey().equals(loginAcctType)) {
            // 根据证件号登录的接口需要修改
            List<HkCustInfoCounterDTO> hkCustInfoCounterDtoList = custInfoService.listHkCustInfoByIdNo(DigestUtil.digest(idNo));
            loginAcct = idNo;
            if (CollectionUtils.isEmpty(hkCustInfoCounterDtoList)) {
                ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ACCOUNT_NOT_EXIST);
            }

            // 极端场景  登录证件号在数据库中不唯一
            if (StringUtil.isBlank(idType) && StringUtil.isBlank(idAreaCode) && hkCustInfoCounterDtoList.size() > 1) {
                ExceptionUtil.throwBusinessException(ExceptionCodeEnum.HK_OPEN_ACCOUNT_IDENTITY_TYPE_NOT_EXIST);
            } else {
                if (!StringUtil.isBlank(idType) && !StringUtil.isBlank(idAreaCode)) {
                    areaCode = idAreaCode;
                } else {
                    String newidType = hkCustInfoCounterDtoList.get(0).getIdType();
                    String newareaCode = hkCustInfoCounterDtoList.get(0).getAreaCode();
                    idType = newidType;
                    areaCode = newareaCode;
                }

                // 证件号登录时，根据 国家地区+证件类型+证件号校验账号存在
                hkCustInfoDTO = custInfoService.getHkCustInfoByIdNoandType(idType, DigestUtil.digest(idNo), areaCode);
                hasAccount(hkCustInfoDTO);
            }
            if (!StringUtil.isBlank(idType) && !StringUtil.isBlank(idAreaCode)) {
                areaCode = idAreaCode;
            }
        } else if (LoginAcctTypeEnum.MOBILE.getKey().equals(loginAcctType)) {
            // 根据手机号码登录
            hkCustInfoDTO = custInfoService.getHkCustInfoByMobile(areaCode, DigestUtil.digest(mobile));
            loginAcct = getLoginAcct(areaCode, mobile, hkCustInfoDTO, hkCustNoVO);
        } else if (LoginAcctTypeEnum.EMAIL.getKey().equals(loginAcctType)) {
            // 根据邮箱账号登录
            hkCustInfoDTO = custInfoService.getHkCustInfoByEmail(DigestUtil.digest(email));
            if (areaCode == null) {
                areaCode = "";
            }
            loginAcct = email;
            hasEmailAccount(hkCustInfoDTO);
        }

        // 设置客户状态
        setCustState(hkCustInfoDTO, hkCustNoVO);
        // 设置香港客户号返回值 前端需要跳转至后续流程的均返回客户号
        hkCustNoVO.setHkCustNo(hkCustInfoDTO.getHkCustNo());

        // 校验登录密码未设置
        if (CustPasswdStatusEnum.UNSET.getKey().equals(hkCustInfoDTO.getCustLoginPasswdType())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.LOGIN_PSWD_UNSET);
        }

        String coopId = RequestUtil.getPublicParams(Constants.COOPID);
        String corpId = RequestUtil.getPublicParams(Constants.CORPID);
        String parPhoneModel = RequestUtil.getPublicParams(Constants.PAR_PHONE_MODEL);
        String version = RequestUtil.getPublicParams(Constants.VERSION);
        String ip = RequestUtil.getCustIp();
        String deviceId = RequestUtil.getPublicParams(Constants.DEVICE_ID);
        String tokenId = RequestUtil.getParameter(Constants.TOKEN_ID);
        String tokenIdMask = StringUtil.isBlank(tokenId) ? null : MD5Utils.md5Hex(tokenId, String.valueOf(StandardCharsets.UTF_8));
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.hkPasswordLogin(loginAcctType, areaCode, idType,
                InvstTypeEnum.PERS.getKey(), loginAcct, PassWordUtil.encrypt(loginPassword), coopId, parPhoneModel, version, ip, deviceId, deviceName, corpId, tokenIdMask);
        ExceptionUtil.validateLoginHkCommonDTO(hkCommonDTO);
        return hkCustInfoDTO;
    }

    /**
     * @param hkCustInfoDTO
     * @return void
     * @description:(判断登录账号是否存在)
     * @author: xufanchao
     * @date: 2024/3/8 15:07
     * @since JDK 1.8
     */
    private void hasAccount(HkCustInfoDTO hkCustInfoDTO) {
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfoDTO.getReturnCode())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.IDNO_LOGIN_NOT_EXIST);
        }
    }

    /**
     * @param hkCustInfoDTO
     * @return void
     * @description:(邮箱是否存在)
     * @author: xufanchao
     * @date: 2024/3/15 09:02
     * @since JDK 1.8
     */
    private void hasEmailAccount(HkCustInfoDTO hkCustInfoDTO) {
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfoDTO.getReturnCode())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * @param areaCode
     * @param mobile
     * @param hkCustInfoDTO
     * @param hkCustNoVO
     * @return java.lang.String
     * @description:(判断手机号是否可以登录)
     * @author: xufanchao
     * @date: 2024/3/8 15:06
     * @since JDK 1.8
     */
    private String getLoginAcct(String areaCode, String mobile, HkCustInfoDTO hkCustInfoDTO, HkCustNoVO hkCustNoVO) {
        String loginAcct;
        loginAcct = mobile;
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfoDTO.getReturnCode())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ACCOUNT_NOT_EXIST);
        }
        // 判断是否为大陆手机号
//        if (!areaCode.equals(Constants.DEFAULT_MOBILE_AREA_CODE)) {
//            ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.HK_OPEN_ACCOUNT_MOBILE_TYPE_ERROR);
//        }
        return loginAcct;
    }

    /**
     * @param hkCustInfoDTO
     * @param hkCustNoVO
     * @return void
     * @description:(设置客户状态)
     * @author: xufanchao
     * @date: 2024/3/8 15:01
     * @since JDK 1.8
     */
    private void setCustState(HkCustInfoDTO hkCustInfoDTO, HkCustNoVO hkCustNoVO) {
        // 设置客户状态
        if (hkCustInfoDTO.getCustState() != null && (hkCustInfoDTO.getCustState().equals(CustStateEnum.NORMAL.getKey()) || hkCustInfoDTO.getCustState().equals(CustStateEnum.SLEEP.getKey()))) {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.NORMAL.getCode());
        } else if (hkCustInfoDTO.getCustState() != null && hkCustInfoDTO.getCustState().equals(CustStateEnum.REGISTER.getKey())) {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.SIGNOFF.getCode());
        } else {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.SLEEP.getCode());
        }
    }

    /**
     * @description: 手机验证码登录
     * @param: [req]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 11:28
     * @since JDK 1.8
     */
    public HkCustNoVO loginByMobileAndVerifyCode(LoginByMobileAndVerifyCodeRequest req) {
        // 校验验证码
        verifyCodeService.checkSmsVerifyCode(req.getMobile(), req.getVerifyCode(), MessageTempleteEnum.LOGIN_MOBILE_VERIFYCODE);
        //客户信息不为空
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        // 获取海外信息接口
        HkCustInfoDTO hkCustInfoByMobile = custInfoService.getnewHkCustInfoByMobile(Constants.DEFAULT_MOBILE_AREA_CODE, DigestUtil.digest(req.getMobile()));
        // 当客户信息为空当时候 调用香港账户中心的手机号注册接口
        if (Objects.equals(OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS, hkCustInfoByMobile.getReturnCode())) {
            try {
                HkCustInfoDTO hkCustInfoByRegsiter = hkCustInfoOuterService.getHkCustInfoByRegsiter(req.getMobile(), Constants.DEFAULT_MOBILE_AREA_CODE);
                if (!Objects.isNull(hkCustInfoByRegsiter)) {
                    hkCustNoVO.setHkCustNo(hkCustInfoByRegsiter.getHkCustNo());
                    hkCustInfoByMobile = custInfoService.getHkCustInfoByMobile(Constants.DEFAULT_MOBILE_AREA_CODE, DigestUtil.digest(req.getMobile()));
                    hkCustNoVO.setCustState(HkOpenCustStatusEnum.SIGNOFF.getCode());
                }
            } catch (BusinessException e) {
                // 海外账户中心,注册失败,特别判断
                if (ExceptionCodeEnum.HW_MOBILE_REGISTER_HKCUSTNO_ERROR.getCode().equals(e.getCode())) {
                    ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.HW_MOBILE_REGISTER_HKCUSTNO_ERROR);
                } else {
                    ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.MOBILE_REGISTER_FAIL);
                }
                return hkCustNoVO;
            } catch (Exception e) {
                ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.MOBILE_REGISTER_FAIL);
                return hkCustNoVO;
            }
            return loginCustNoVO(req, hkCustInfoByMobile, hkCustNoVO);
        } else {
            return getHkCustNoVO(req, hkCustInfoByMobile, hkCustNoVO);
        }
    }

    public HkCustNoVO loginAppByMobileAndVerifyCode(LoginByMobileAndVerifyCodeRequest req) {
        // 校验验证码
        verifyCodeService.checkSmsVerifyCode(req.getMobile(), req.getVerifyCode(), MessageTempleteEnum.LOGIN_MOBILE_VERIFYCODE);
        //客户信息不为空
        HkCustNoVO hkCustNoVO = new HkCustNoVO();
        // 获取海外信息接口
        HkCustInfoDTO hkCustInfoByMobile = custInfoService.getnewHkCustInfoByMobile(Constants.DEFAULT_MOBILE_AREA_CODE, DigestUtil.digest(req.getMobile()));
        // 当客户信息为空当时候 调用香港账户中心的手机号注册接口
        if (Objects.equals(OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS, hkCustInfoByMobile.getReturnCode())) {
            try {
                HkCustInfoDTO hkCustInfoByRegsiter = hkCustInfoOuterService.getHkCustInfoByRegsiter(req.getMobile(), Constants.DEFAULT_MOBILE_AREA_CODE);
                if (!Objects.isNull(hkCustInfoByRegsiter)) {
                    hkCustNoVO.setHkCustNo(hkCustInfoByRegsiter.getHkCustNo());
                    hkCustInfoByMobile = custInfoService.getHkCustInfoByMobile(Constants.DEFAULT_MOBILE_AREA_CODE, DigestUtil.digest(req.getMobile()));
                    hkCustNoVO.setCustState(HkOpenCustStatusEnum.SIGNOFF.getCode());
                }
            } catch (BusinessException e) {
                // 海外账户中心,注册失败,特别判断
                if (ExceptionCodeEnum.HW_MOBILE_REGISTER_HKCUSTNO_ERROR.getCode().equals(e.getCode())) {
                    ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.HW_MOBILE_REGISTER_HKCUSTNO_ERROR);
                } else {
                    ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.MOBILE_REGISTER_FAIL);
                }
                return hkCustNoVO;
            } catch (Exception e) {
                ExceptionUtil.fillExceptionInfo(hkCustNoVO, ExceptionCodeEnum.MOBILE_REGISTER_FAIL);
                return hkCustNoVO;
            }
            HkCustNoVO custNoVO = loginCustNoVO(req, hkCustInfoByMobile, hkCustNoVO);
            //香港客户号加密
            String encrypt = getEncryptHkCustNo(custNoVO.getHkCustNo());
            custNoVO.setHkCustNo(encrypt);
            return custNoVO;
        } else {
            HkCustNoVO custNoVO = getHkCustNoVO(req, hkCustInfoByMobile, hkCustNoVO);
            //香港客户号加密
            String encrypt = getEncryptHkCustNo(custNoVO.getHkCustNo());
            custNoVO.setHkCustNo(encrypt);
            return custNoVO;
        }
    }

    /**
     * @param hkCustNo
     * @return java.lang.String
     * @description: 通过客户号获取加密客户号
     * @author: jinqing.rao
     * @date: 2024/3/7 18:03
     * @since JDK 1.8
     */
    private static String getEncryptHkCustNo(String hkCustNo) {
        if (StringUtils.isBlank(hkCustNo)) {
            return null;
        }
        try {
            return DesUtil.encrypt(hkCustNo, Constants.HK_APP_CUST_NO_KEY);
        } catch (Exception e) {
            log.info("getEncryptHkCustNo>>加密客户号失败,custNoVO:{}", hkCustNo);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @param req
     * @param hkCustInfoDTO
     * @param hkCustNoVO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HkCustNoVO
     * @description:(从香港账户中心能获取到客户信息时调用)
     * @author: xufanchao
     * @date: 2023/12/4 18:59
     * @since JDK 1.8
     */
    private HkCustNoVO getHkCustNoVO(LoginByMobileAndVerifyCodeRequest req, HkCustInfoDTO hkCustInfoDTO, HkCustNoVO hkCustNoVO) {
        // 设置客户状态 香港账户中心的客户状态枚举 0-正常(开户)，1-注销，2-休眠(开户)，3-注册，4-开户申请成功
        if (hkCustInfoDTO.getCustState().equals(CustStateEnum.NORMAL.getKey()) || hkCustInfoDTO.getCustState().equals(CustStateEnum.SLEEP.getKey())) {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.NORMAL.getCode());
        } else {
            hkCustNoVO.setCustState(HkOpenCustStatusEnum.SLEEP.getCode());
        }


        HkCustNoVO newHkNoVO = loginCustNoVO(req, hkCustInfoDTO, hkCustNoVO);
        if (hkCustNoVO.getCustState().equals(HkOpenCustStatusEnum.SLEEP.getCode())) {
            return hkCustNoVO;
        }
        // 校验交易密码状态
        if (!RequestUtil.isHkAppLogin()) {
            if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfoDTO.getCustTxPasswdType())) {
                newHkNoVO.setCustState(HkOpenCustStatusEnum.SLEEP.getCode());
                ExceptionUtil.fillExceptionInfo(newHkNoVO, ExceptionCodeEnum.TX_ACCT_UN_ACTIVE);
            }
        }
        return newHkNoVO;
    }

    /**
     * @param req
     * @param hkCustInfoDTO
     * @param hkCustNoVO
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.HkCustNoVO
     * @description:(登录创建session)
     * @author: xufanchao
     * @date: 2023/12/26 13:54
     * @since JDK 1.8
     */
    private HkCustNoVO loginCustNoVO(LoginByMobileAndVerifyCodeRequest req, HkCustInfoDTO hkCustInfoDTO, HkCustNoVO hkCustNoVO) {
        // 设置香港客户号返回值 前端需要跳转至后续流程的均返回客户号
        hkCustNoVO.setHkCustNo(hkCustInfoDTO.getHkCustNo());
        if (RequestUtil.isHkAppLogin()) {
            String coopId = RequestUtil.getPublicParams(Constants.COOPID);
            String corpId = RequestUtil.getPublicParams(Constants.CORPID);
            String parPhoneModel = RequestUtil.getPublicParams(Constants.PAR_PHONE_MODEL);
            String version = RequestUtil.getPublicParams(Constants.VERSION);
            String ip = RequestUtil.getCustIp();
            String deviceId = RequestUtil.getPublicParams(Constants.DEVICE_ID);
            String tokenId = RequestUtil.getParameter(Constants.TOKEN_ID);
            String tokenIdMask = MD5Utils.md5Hex(tokenId, String.valueOf(StandardCharsets.UTF_8));
            saveCreditCode(hkCustInfoDTO);
            hkPasswordOuterService.hkMobileVerifyCodeLogin(req.getMobile(), coopId, parPhoneModel, version, ip, deviceId, req.getDeviceName(), corpId, tokenIdMask);
        } else {
            hkPasswordOuterService.hkMobileVerifyCodeLogin(req.getMobile());
            // 创建会话
            HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
            createSession(httpRequest, hkCustInfoDTO, hkCustInfoDTO.getHkCustNo(), "", "");
        }
        return hkCustNoVO;
    }

    /**
     * @description: 退出登录
     * @param: []
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/7 17:38
     * @since JDK 1.8
     */
    public void loginOut() {
        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        removeLoginSession(httpRequest);
    }

    /**
     * @description: 手机验证接口
     * @param: [req]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/13 12:12
     * @since JDK 1.8
     */
    public void verifyMobile(VerifyMobileRequest req) {
        verifyCodeService.checkSmsVerifyCode(req.getMobile(), req.getVerifyCode(), MessageTempleteEnum.MOBILE_VERIFY_VERIFYCODE);
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfoByMobile(Constants.DEFAULT_MOBILE_AREA_CODE, DigestUtil.digest(req.getMobile()));
        //判断前端传入的的证件号与海外账户中心返回的证件号是否一致
        if (!DigestUtil.digest(req.getIdNo()).equals(hkCustInfoDTO.getIdNoDigest())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ID_NO_DIFF);
        }
        hkPasswordOuterService.hkMobileVerifyCodeLogin(req.getMobile());
        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        createSession(httpRequest, hkCustInfoDTO, hkCustInfoDTO.getHkCustNo(), "", "");
        if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfoDTO.getCustTxPasswdType())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.TX_ACCT_UN_ACTIVE);
        }
    }

    /**
     * @param req
     * @return void
     * @description:(登录账号激活接口)
     * @author: shuai.zhang
     * @date: 2023/6/8 17:33
     * @since JDK 1.8
     */
    public void activateLoginAccount(ActivateLoginAccountRequest req) {
        ParamsValidator.validateParams(req, "mobileDigest#emailDigest");

        String loginHkCustNo = getHkCusNo();
        HkCustInfoPlaintextDTO custInfoPlaintext = custInfoService.getCustInfoPlaintext(loginHkCustNo);
        //手机验证
        if (StringUtils.isNotEmpty(req.getMobileDigest())) {
            String mobilePlaintext = custInfoService.getMobilePlaintext(loginHkCustNo);
            verifyCodeService.checkSmsVerifyCode(mobilePlaintext, req.getVerifyCode(), MessageTempleteEnum.LOGIN_ACTIVATE_MOBILE_VERIFYCODE);
        } else {
            //邮箱验证
            verifyCodeService.checkEmailVerifyCode(custInfoPlaintext.getEmail(), req.getVerifyCode(), MessageTempleteEnum.LOGIN_ACTIVATE_EMAIL_VERIFYCODE);
        }
        //验证证件号
        if (!req.getIdNo().trim().equals(custInfoPlaintext.getIdNo())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ID_NO_DIFF);
        }
        //登录密码校验
        PasswordValidator.validateLoginPassword(req.getLoginPassword());
        //调用重置密码接口   这个接口在账户中心会重置手机或邮箱的验证状态
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.resetPassword(loginHkCustNo, req.getMobileDigest(), req.getEmailDigest(),
                PasswordTypeEnum.LOGIN_PASSWORD.getKey(), PassWordUtil.encrypt(req.getLoginPassword()), null);
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);

        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(loginHkCustNo);
        if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfoDTO.getCustTxPasswdType())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.TX_ACCT_UN_ACTIVE);
        }
    }

    /**
     * @param req
     * @return void
     * @description:(交易账户激活)
     * @author: shuai.zhang
     * @date: 2023/6/8 18:31
     * @since JDK 1.8
     */
    public void activateTxAccount(ActivateTxAccountRequest req) {
        ParamsValidator.validateParams(req, "mobileDigest#emailDigest");
        String loginHkCustNo = getHkCusNo();
        HkCustInfoPlaintextDTO custInfoPlaintext = custInfoService.getCustInfoPlaintext(loginHkCustNo);
        //手机验证
        if (StringUtils.isNotEmpty(req.getMobileDigest())) {
            String mobilePlaintext = custInfoService.getMobilePlaintext(loginHkCustNo);
            verifyCodeService.checkSmsVerifyCode(mobilePlaintext, req.getVerifyCode(), MessageTempleteEnum.TRADE_ACTIVATE_MOBILE_VERIFYCODE);
        } else {
            //邮箱验证
            verifyCodeService.checkEmailVerifyCode(custInfoPlaintext.getEmail(), req.getVerifyCode(), MessageTempleteEnum.TRADE_ACTIVATE_EMAIL_VERIFYCODE);
        }
        //验证证件号
        if (!req.getIdNo().trim().equals(custInfoPlaintext.getIdNo())) {
            ExceptionUtil.throwBusinessException(ExceptionCodeEnum.ID_NO_DIFF);
        }
        //交易密码校验
        HkCustInfoDTO hkCustInfoDTO = custInfoService.getHkCustInfo(loginHkCustNo);
        PasswordValidator.validateTxPassword(req.getTxPassword(), hkCustInfoDTO.getBirthday());
        //调用重置交易密码接口
        HkCommonDTO hkCommonDTO = hkPasswordOuterService.resetPassword(loginHkCustNo, req.getMobileDigest(), req.getEmailDigest(),
                PasswordTypeEnum.TRADE_PASSWORD.getKey(), null, PassWordUtil.encrypt(req.getTxPassword()));
        ExceptionUtil.validatePwdHkCommonDTO(hkCommonDTO);
    }

    /**
     * @return java.lang.String
     * @description: 获取香港客户证件号
     * @author: jinqing.rao
     * @date: 2023/12/7 11:05
     * @since JDK 1.8
     */
    private static String getHkCusNo() {
        String hkCustNo = RequestUtil.getParameter(Constants.HKCUSTNO);
        if (StringUtils.isBlank(hkCustNo)) {
            throw new BusinessException(ExceptionCodeEnum.ACCT_ERROR);
        }
        return hkCustNo;
    }

    /**
     * @param
     * @return com.howbuy.crm.cgi.extservice.vo.hkaccount.LoginIdTypeListVO
     * @description:(获取登录证件类型列表)
     * @author: shaoyang.li
     * @date: 2023/8/2 10:30
     * @since JDK 1.8
     */
    public LoginIdTypeListVO getLoginIdTypeList() {
        LoginIdTypeListVO listVO = new LoginIdTypeListVO();
        List<LoginIdTypeVO> voList = JSON.parseObject(loginIdTypeList, new TypeReference<List<LoginIdTypeVO>>() {
        });
        listVO.setVoList(voList);
        return listVO;
    }

    /**
     * @param req 请求参数
     * @return void
     * @description: app退出登录
     * @author: jinqing.rao
     * @date: 2024/2/29 17:45
     * @since JDK 1.8
     */
    public void appLogout(HkAppAccountLoginOutRequest req) {
        //coopId 对应的是 outletCode
        String coopId = RequestUtil.getPublicParams(Constants.COOPID);
        String corpId = RequestUtil.getPublicParams(Constants.CORPID);
        String parPhoneModel = RequestUtil.getPublicParams(Constants.PAR_PHONE_MODEL);
        String version = RequestUtil.getPublicParams(Constants.VERSION);
        String ip = RequestUtil.getHttpParameter(Constants.CUST_IP);
        String deviceId = RequestUtil.getPublicParams(Constants.DEVICE_ID);
        String tokenId = RequestUtil.getParameter(Constants.TOKEN_ID);
        String remark = "客户安全退出";
        String tokenIdMask = MD5Utils.md5Hex(tokenId, String.valueOf(StandardCharsets.UTF_8));
        hkAcctCreditCodeOuterService.deleteCreditCode(req.getHkCustNo(), coopId, parPhoneModel, version, ip, deviceId, remark, tokenIdMask, corpId);
    }

    /**
     * App是否需要登录退出
     * @param req   请求体
     */
    public LoginNeedExitVO appNeedExit(HkAppAccountNeedExitRequest req) {
        LoginNeedExitVO vo = new LoginNeedExitVO();

        String tokenId = RequestUtil.getParameter(Constants.TOKEN_ID);
        QueryHkLoginLogResponse response = hkCustInfoOuterService.getHkCustLoginLogMsgList(req.getHkCustNo(), tokenId, 1, 1, null, null);
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getLoginLogDTOList())) {
            vo.setNextDayFlag(YesOrNoEnum.NO.getCode());
            log.info("LoginService appNeedExit 查询登录日志为空! hkCustNo={}", req.getHkCustNo());
            return vo;
        }

        // 最近一次登录日期 yyyyMMdd
        String tradeDt = response.getLoginLogDTOList().get(0).getTradeDt();
        if (StringUtil.isBlank(tradeDt)) {
            vo.setNextDayFlag(YesOrNoEnum.NO.getCode());
            log.info("LoginService appNeedExit 查询登录日期为空! hkCustNo={}", req.getHkCustNo());
            return vo;
        }

        Calendar calendar = Calendar.getInstance();
        Date loginDt = DateUtil.string2Date(tradeDt, DateUtil.SHORT_DATEPATTERN);
        calendar.setTime(loginDt);
        // 设置7天后
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        // 设置凌晨4点
        String hour = StringUtils.isBlank(loginExitTime) ? Constants.APP_AUTOEXIT_TIME : loginExitTime;
        calendar.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date exitTime = calendar.getTime();
        String needExitFlag = new Date().after(exitTime) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
        vo.setNextDayFlag(needExitFlag);
        return vo;
    }

    /**
     * App登录日志列表
     * @param req   请求体
     */
    public LoginLogListVO appLoginLogList(HkAppAccountLoginLogRequest req) {
        LoginLogListVO vo = new LoginLogListVO();
        Calendar calendar = Calendar.getInstance();
        Date now = new Date();
        calendar.setTime(now);
        calendar.add(Calendar.MONTH, -1);
        Date startDate = calendar.getTime();
        String start = DateUtil.date2String(startDate, DateUtil.SHORT_DATEPATTERN);
        String end = DateUtil.date2String(now, DateUtil.SHORT_DATEPATTERN);
        QueryHkLoginLogResponse response = hkCustInfoOuterService.getHkCustLoginLogMsgList(req.getHkCustNo(), null, req.getPage(), req.getSize(), start, end);
        if (response.isSuccess()) {
            List<QueryHkLoginLogResponse.HkLoginLogDTO> loginLogDTOList = response.getLoginLogDTOList();
            if (CollectionUtils.isEmpty(loginLogDTOList)) {
                vo.setTotal(Constants.CONSTANT_ZERO);
                return vo;
            }

            List<LoginLogListVO.LoginMsg> list = new ArrayList<>();
            for (QueryHkLoginLogResponse.HkLoginLogDTO hkLoginLogDTO : loginLogDTOList) {
                LoginLogListVO.LoginMsg loginMsg = new LoginLogListVO.LoginMsg();
                loginMsg.setLoginIp(hkLoginLogDTO.getTxIp());
                String loginDtm = null;
                try {
                    loginDtm = DateUtil.format(hkLoginLogDTO.getTradeDt() + hkLoginLogDTO.getTradeTm(), "yyyyMMddHHmmss", DateUtil.DEFAULT_DATESFM);
                } catch (Exception e) {
                    log.error("LoginService appLoginLogList 转换时间异常,loginDtm={}", hkLoginLogDTO.getTradeDt() + hkLoginLogDTO.getTradeTm(), e);
                }
                loginMsg.setLoginDtm(loginDtm);
                loginMsg.setLoginSource(hkLoginLogDTO.getChannel());
                LoginChannelEnum channel = LoginChannelEnum.getEnumByChannel(hkLoginLogDTO.getChannel());
                loginMsg.setLoginSource(hkLoginLogDTO.getDeviceModel());
                loginMsg.setLoginSourceMemo(null!=channel?channel.getName():"");
                loginMsg.setDeviceName(hkLoginLogDTO.getDeviceName());
                list.add(loginMsg);
            }
            vo.setTotal(String.valueOf(response.getTotalCount()));
            vo.setLoginMsgList(list);
        }
        return vo;
    }

}