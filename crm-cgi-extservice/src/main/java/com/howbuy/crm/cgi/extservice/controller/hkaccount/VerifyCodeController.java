/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller.hkaccount;

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.MessageTempleteEnum;
import com.howbuy.crm.cgi.common.enums.VerifyCodeTypeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.common.utils.ParamsValidator;
import com.howbuy.crm.cgi.extservice.request.account.*;
import com.howbuy.crm.cgi.extservice.service.hkaccount.VerifyCodeService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.SendVerifyCodeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: (登录验证码)
 * @date 2023/5/18 14:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/verifycode")
public class VerifyCodeController {

    @Autowired
    private VerifyCodeService verifyCodeService;


    /**
     * @api {POST} /ext/hkaccount/verifycode/getloginmsgverifycode 获取登录短信验证码接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getLoginMsgVerifyCode()
     * @apiDescription 获取登录短信验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 	必须，明文
     * @apiParamExample 请求体示例
     * {"mobile":"huNibQoX"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"LII2L337","data":{"returnCode":"il1n6FS","sendState":"PZgYLaY","description":"g7xuXu"},"description":"dyZo","timestampServer":"LmmPhRFZeA"}
     */
    @PostMapping("/getloginmsgverifycode")
    public CgiResponse<SendVerifyCodeVO> getLoginMsgVerifyCode(@RequestBody MobileRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getLoginMsgVerifyCode(req.getMobile());
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /ext/hkaccount/verifycode/getvoicemsgverifycode getVoiceMsgVerifyCode()
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getVoiceMsgVerifyCode()
     * @apiDescription 获取语音验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 	必须，明文
     * @apiParamExample 请求体示例
     * {"mobile":"r"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"MFN20","data":{"sendState":"B92"},"description":"1TFOPA","timestampServer":"WvYeey"}
     */
    @PostMapping("/getvoicemsgverifycode")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> getVoiceMsgVerifyCode(@RequestBody MobileCodeRequset req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getVoiceMsgVerifyCode(req.getMobile(), req.getVerifyCodeType());
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /ext/hkaccount/verifycode/verifyvoicemobile verifyVoiceMobileMsgCode()
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName verifyVoiceMobileMsgCode()
     * @apiDescription 验证语音验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号码 加密
     * @apiParam (请求体) {String} verifyCodeType 验证码类型
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParamExample 请求体示例
     * {"verifyCode":"EssmKF4UF","mobile":"FyyPgZ5","verifyCodeType":"oqlGRXNWu"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"O4D5","data":{"sendState":"bsK3kuV"},"description":"7t","timestampServer":"fXBOrc"}
     */
    @PostMapping("/verifyvoicemobile")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> verifyVoiceMobileMsgCode(@RequestBody VerifyMobileTypeRequest req) {
        // 1.必填参数校验
        ParamsValidator.validateParams(req, "mobile", "verifyCode", "verifyCodeType");
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 2.调用验证码的验证接口
        try {
            verifyCodeService.verifyvoiceMobileVerifyCode(req.getMobile(), req.getVerifyCode());
        } catch (BusinessException e) {
            return CgiResponse.error(e.getCode(), e.getDesc(), sendVerifyCodeVO);
        }
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /ext/hkaccount/verifycode/getapploginmsgverifycode getAppLoginMsgVerifyCode()
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getAppLoginMsgVerifyCode()
     * @apiDescription App获取验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 	必须，明文
     * @apiParamExample 请求体示例
     * {"mobile":"ECeiBUf"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer 时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"zyjCCkpA","data":{"sendState":"ISwgeNweL"},"description":"D","timestampServer":"40euLgt"}
     */
    @PostMapping("/getapploginmsgverifycode")
    public CgiResponse<SendVerifyCodeVO> getAppLoginMsgVerifyCode(@RequestBody MobileRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getLoginMsgVerifyCode(req.getMobile());
        return CgiResponse.appOk(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /ext/hkaccount/verifycode/getmobileverifycodebytype 手机号验证码获取接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getVerifyCodeByType()
     * @apiDescription 手机号验证码获取接口 需传入验证码类型
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号码 加密
     * @apiParam (请求体) {String} verifyCodeType 验证码类型      02-手机验证短信验证码      03-重置登录密码短信验证码      04-交易账户激活短信验证码      05-登录账户激活短信验证码      06-绑定一账通香港手机号短信验证码      07-解绑一账通香港手机号短信验证码      08-解绑一账通好买手机号短信验证码      09-重置交易密码短信验证码      10-设置交易密码短信验证码      11-绑定一账通好买手机号短信验证码      12-修改手机号短信验证码      13-绑定手机号短信验证码
     * @apiParamExample 请求体示例
     * {"mobile":"6YLEX05n","verifyCodeType":"TMXNrl"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"25xEf","data":{"sendState":"ldZ1tb"},"description":"cL6EerKA","timestampServer":"cYYU"}
     */
    @PostMapping("/getmobileverifycodebytype")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> getMobileVerifyCodeByType(@RequestBody MobileCodeRequset req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getMobileVerifyCodeByType(req);
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /ext/hkaccount/verifycode/getmobiledigestverifycodebytype getMobileDigestVerifyCodeByType()
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getMobileDigestVerifyCodeByType()
     * @apiDescription 手机号摘要验证码发送接口 需传入验证码类型
     * @apiHeader (请求头) {String} content-type=application/json;charset=utf-8 媒体类型
     * @apiParam (请求体) {String} mobileDigest 手机号摘要
     * @apiParam (请求体) {String} verifyCodeType 验证码类型  04-交易账户激活短信验证码；05-登录账户激活短信验证码；06-绑定一账通香港手机号短信验证码；07-解绑一账通香港手机号短信验证码；08-解绑一账通好买手机号短信验证码；
     * @apiParamExample 请求体示例
     * {"mobileDigest":"qHq","verifyCodeType":"cDib6fl1R"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"fy3z","data":{"sendState":"h"},"description":"hHgQyMaA","timestampServer":"bgCFGHV"}
     */
    @PostMapping("/getmobiledigestverifycodebytype")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> getMobileDigestVerifyCodeByType(@RequestBody MobileDigestCodeRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getMobileDigestVerifyCodeByType(req);
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /ext/hkaccount/verifycode/verifymobilebytype 手机号验证码验证接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName verifyMobile()
     * @apiDescription 手机号验证码验证接口 需传入验证码类型
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号码 加密
     * @apiParam (请求体) {String} verifyCodeType 验证码类型
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParamExample 请求体示例
     * {"verifyCode":"eF1jZ8","mobile":"KI","verifyCodeType":"Rl"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"rG","data":{"sendState":"9NASmhA"},"description":"yzfel8MH","timestampServer":"9nAKAKu0"}
     */
    @PostMapping("/verifymobilebytype")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> verifyMobile(@RequestBody VerifyMobileTypeRequest req) {
        // 1.必填参数校验
        ParamsValidator.validateParams(req, "mobile", "verifyCode", "verifyCodeType");
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        // 2.调用验证码的验证接口
        verifyCodeService.checkSmsVerifyCode(req.getMobile(), req.getVerifyCode(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()));
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /ext/hkaccount/verifycode/verifyemailbytype verifyEmail()
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName verifyEmail()
     * @apiDescription 邮箱验证码验证接口 需传入验证码类型
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} email 手机号码 加密
     * @apiParam (请求体) {String} verifyCodeType 验证码类型
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParamExample 请求体示例
     * {"verifyCode":"1RP","email":"Ov3q","verifyCodeType":"QD0Pjqq1q"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"j","data":{"sendState":"ZdPY"},"description":"EPbbIOrQH","timestampServer":"hy3YvXmgA"}
     */
    @PostMapping("/verifyemailbytype")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> verifyEmail(@RequestBody VerifyEmailTypeRequest req) {
        // 1.必填参数校验
        ParamsValidator.validateParams(req, "email", "verifyCode", "verifyCodeType");
        SendVerifyCodeVO sendVerifyCodeVO = new SendVerifyCodeVO();
        verifyCodeService.checkEmailVerifyCode(req.getEmail(), req.getVerifyCode(), VerifyCodeTypeEnum.getVerifyCodeTypeEnumByCode(req.getVerifyCodeType()));
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /ext/hkaccount/verifycode/editmobile 修改手机号接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName editMobile()
     * @apiDescription 修改手机号接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号码 加密
     * @apiParam (请求体) {String} verifyCode 验证码
     * @apiParam (请求体) {String} txPassword 交易密码
     * @apiParamExample 请求体示例
     * {"txPassword":"35oO","verifyCode":"RVM4FIMt","mobile":"5kuceiyvv"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"g5h9Fgxkt","description":"DT","timestampServer":"g6AjW53"}
     */
    @PostMapping("editmobile")
    @ResponseBody
    public CgiResponse<Body> editMobile(@RequestBody EditMobileRequest req) {
        // 校验必填参数
        ParamsValidator.validateParams(req, "mobile", "verifyCode", "txPassword");
        // 修改手机号
        verifyCodeService.editMobile(req.getMobile(), req.getVerifyCode(), req.getTxPassword());
        return CgiResponse.ok(null);
    }


    /**
     * @api {POST} /hkaccount/verifycode/getmobileverifymsgverifycode 获取手机验证短信验证码接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getMobileVerifyMsgVerifyCode()
     * @apiDescription 获取手机验证短信验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 	必须，明文
     * @apiParamExample 请求体示例
     * {"mobile":"sP"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"OeAa","data":{"returnCode":"6Cnl0Tc","sendState":"Ec","description":"viCloTn8"},"description":"b6I","timestampServer":"EP0gj"}
     */
    @PostMapping("/getmobileverifymsgverifycode")
    public CgiResponse<SendVerifyCodeVO> getMobileVerifyMsgVerifyCode(@RequestBody MobileRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getMobileVerifyMsgVerifyCode(req.getMobile());
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /hkaccount/verifycode/getactivatetxaccountmsgverifycode 获取交易账户激活短信验证码接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getActivateTxAccountMsgVerifyCode()
     * @apiDescription 获取交易账户激活短信验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobileDigest 手机号摘要 	必须
     * @apiParamExample 请求体示例
     * {"mobileDigest":"nIbChN"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Nn8fnMJnCy","data":{"returnCode":"lOmL","sendState":"W79pC5DmJz","description":"rQ"},"description":"Qo","timestampServer":"prc07"}
     */
    @PostMapping("/getactivatetxaccountmsgverifycode")
    public CgiResponse<SendVerifyCodeVO> getActivateTxAccountMsgVerifyCode(@RequestBody MobileDigestRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getActivateMobileVerifyCode(req.getMobileDigest(),
                MessageTempleteEnum.TRADE_ACTIVATE_MOBILE_VERIFYCODE);
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /hkaccount/verifycode/getactivatetxaccountemailverifycode 获取交易账户激活邮箱验证码接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getActivateTxAccountEmailVerifyCode()
     * @apiDescription 获取交易账户激活邮箱验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} emailDigest 邮箱地址摘要 	必须
     * @apiParamExample 请求体示例
     * {"emailDigest":"BTnty6E"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"5Fv","data":{"returnCode":"Nt2DmsFav","sendState":"jm","description":"12iX6VMc"},"description":"Ghl22fg","timestampServer":"4pLU3"}
     */
    @PostMapping("/getactivatetxaccountemailverifycode")
    public CgiResponse<SendVerifyCodeVO> getActivateTxAccountEmailVerifyCode(@RequestBody EmailDigestRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getActivateEmailVerifyCode(req.getEmailDigest(),
                MessageTempleteEnum.TRADE_ACTIVATE_EMAIL_VERIFYCODE);
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /hkaccount/verifycode/getactivateaccountmsgverifycode 获取登录账户激活短信验证码接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getActivateAccountMsgVerifyCode()
     * @apiDescription 获取登录账户激活短信验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobileDigest 手机号摘要 	必须
     * @apiParamExample 请求体示例
     * {"mobileDigest":"Fyaa4jIM"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"zaXSOR6JCr","data":{"returnCode":"avZdOUVJl","sendState":"NP","description":"qDVGO"},"description":"UwbB1","timestampServer":"x"}
     */
    @PostMapping("/getactivateaccountmsgverifycode")
    public CgiResponse<SendVerifyCodeVO> getActivateAccountMsgVerifyCode(@RequestBody MobileDigestRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getActivateMobileVerifyCode(req.getMobileDigest(),
                MessageTempleteEnum.LOGIN_ACTIVATE_MOBILE_VERIFYCODE);
        return CgiResponse.ok(sendVerifyCodeVO);
    }


    /**
     * @api {POST} /hkaccount/verifycode/getactivateaccountemailverifycode 获取登录账户激活邮箱验证码接口
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getActivateAccountEmailVerifyCode()
     * @apiDescription 获取登录账户激活邮箱验证码接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} emailDigest 邮箱地址摘要 	必须
     * @apiParamExample 请求体示例
     * {"emailDigest":"x55eD"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"tT6SQxmK5","data":{"returnCode":"yTyu52","sendState":"dZ1h","description":"tSLjU"},"description":"e6bPfI5c","timestampServer":"9g"}
     */
    @PostMapping("/getactivateaccountemailverifycode")
    public CgiResponse<SendVerifyCodeVO> getActivateAccountEmailVerifyCode(@RequestBody EmailDigestRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getActivateEmailVerifyCode(req.getEmailDigest(),
                MessageTempleteEnum.LOGIN_ACTIVATE_EMAIL_VERIFYCODE);
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /hkaccount/verifycode/getresetloginpasswordmobileverifycode 获取重置登录密码手机号验证码
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getResetLoginPasswordMobileVerifyCode()
     * @apiDescription 获取重置登录密码手机号验证码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 	必须，明文
     * @apiParamExample 请求体示例
     * {"mobile":"Ul2H1"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"nNT","data":{"returnCode":"I","sendState":"vOlY9","description":"mUXgFw2tS"},"description":"5XDa2","timestampServer":"Bzcm"}
     */
    @PostMapping("/getresetloginpasswordmobileverifycode")
    public CgiResponse<SendVerifyCodeVO> getResetLoginPasswordMobileVerifyCode(@RequestBody MobileRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getResetLoginPasswordMobileVerifyCode(req.getMobile());
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /hkaccount/verifycode/getresetloginpasswordemailverifycode 获取重置登录密码邮箱验证码
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getResetLoginPasswordEmailVerifyCode()
     * @apiDescription 获取重置登录密码邮箱验证码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} email 邮箱地址 	必须，明文
     * @apiParamExample 请求体示例
     * {"email":"uz3"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"7H4FeI","data":{"returnCode":"mUU5bU","sendState":"a","description":"r"},"description":"Aca1XlPD","timestampServer":"1koD"}
     */
    @PostMapping("/getresetloginpasswordemailverifycode")
    public CgiResponse<SendVerifyCodeVO> getResetLoginPasswordEmailVerifyCode(@RequestBody EmailRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getResetLoginPasswordEmailVerifyCode(req.getEmail());
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /hkaccount/verifycode/getresettxpasswordmobileverifycode 获取重置交易密码手机号验证码
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getResetTxPasswordMobileVerifyCode()
     * @apiDescription 获取重置交易密码手机号验证码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} mobile 手机号 	必须，明文
     * @apiParamExample 请求体示例
     * {"mobile":"GszCU8"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"pK","data":{"returnCode":"cOESMG","sendState":"uV1JUG","description":"sa6UZw"},"description":"07YRYkERNV","timestampServer":"BqXRAOQN"}
     */
    @PostMapping("/getresettxpasswordmobileverifycode")
    public CgiResponse<SendVerifyCodeVO> getResetTxPasswordMobileVerifyCode(@RequestBody MobileRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getResetTxPasswordMobileVerifyCode(req.getMobile());
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /hkaccount/verifycode/getresettxpasswordemailverifycode 获取重置交易密码邮箱验证码
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getResetTxPasswordEmailVerifyCode()
     * @apiDescription 获取重置交易密码邮箱验证码
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} email 邮箱地址 	必须，明文
     * @apiParamExample 请求体示例
     * {"email":"fU"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} data.returnCode 响应码
     * @apiSuccess (响应结果) {String} data.description 响应描述
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"Qrj3Njl5","data":{"returnCode":"bZmlFxJ","sendState":"e37FvJ","description":"0EXKgy"},"description":"BftnrHNqZt","timestampServer":"UL"}
     */
    @PostMapping("/getresettxpasswordemailverifycode")
    public CgiResponse<SendVerifyCodeVO> getResetTxPasswordEmailVerifyCode(@RequestBody EmailRequest req) {
        SendVerifyCodeVO sendVerifyCodeVO = verifyCodeService.getResetTxPasswordEmailVerifyCode(req.getEmail());
        return CgiResponse.ok(sendVerifyCodeVO);
    }

    /**
     * @api {POST} /ext/hkaccount/verifycode/getemailverifycodebytype 邮箱验证码发送接口 需传入验证码类型
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getEmailVerifyCodeByType()
     * @apiDescription 邮箱验证码发送接口 需传入验证码类型
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} email 邮箱 前端加密
     * @apiParam (请求体) {String} verifyCodeType 验证码类型
     * @apiParamExample 请求体示例
     * {"email":"7rniH1Pvi2","verifyCodeType":"SMz1OCj5Jn"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"3","data":{"sendState":"EbWzlUgcke"},"description":"b4IY","timestampServer":"S4MD"}
     */
    @PostMapping("/getemailverifycodebytype")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> getEmailVerifyCodeByType(@RequestBody GetEmailCodeByTypeRequest req) {
        // 1. 参数校验
        ParamsValidator.validateParams(req, "email", "verifyCodeType");
        return CgiResponse.ok(verifyCodeService.sendEmailVerifyCode(req.getEmail(), req.getVerifyCodeType()));
    }

    /**
     * @api {POST} /ext/hkaccount/verifycode/getemaildigestverifycodebytype getEmailDigestVerifyCodeByType()
     * @apiVersion 1.0.0
     * @apiGroup VerifyCodeController
     * @apiName getEmailDigestVerifyCodeByType()
     * @apiDescription 邮箱摘要验证码发送接口 需传入验证码类型
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} emailDigest 邮箱摘要
     * @apiParam (请求体) {String} verifyCodeType 验证码类型 56-绑定一账通香港邮箱邮箱验证码；57-解绑一账通香港邮箱邮箱验证码；58-交易账户激活邮箱验证码；59-登录账户激活邮箱验证码
     * @apiParamExample 请求体示例
     * {"emailDigest":"83","verifyCodeType":"IBZW16"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.sendState 发送状态 0-发送失败；1-发送成功
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"31U3","data":{"sendState":"TYuuJw"},"description":"1AJaU4","timestampServer":"I5V1FbXUV"}
     */
    @PostMapping("/getemaildigestverifycodebytype")
    @ResponseBody
    public CgiResponse<SendVerifyCodeVO> getEmailDigestVerifyCodeByType(@RequestBody GetEmailDigestCodeByTypeRequest req) {
        return CgiResponse.ok(verifyCodeService.getEmailDigestVeerifyCodeByType(req));
    }

}