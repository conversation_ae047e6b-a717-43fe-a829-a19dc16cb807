/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.dtmsorder;

import com.howbuy.crm.cgi.extservice.common.enums.ContractSignFlagEnum;
import com.howbuy.crm.cgi.extservice.request.DtmsBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @description: (海外产品电子签约协议签署接口)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@Data
public class DigitalSignContractSignRequest extends DtmsBaseRequest {
    /**
     * 香港客户号 必填
     */
    private String hkCustNo;
    /**
     * 基金代码	必填
     */
    private String fundCode;
    /**
     * 交易方式	 1-购买；2-赎回
     */
    private String tradeMode;
    /**
     * 协议签订标识	 默认为1；0-未签署；1-已签署
     */
    private String contractSignFlag = ContractSignFlagEnum.SIGN.getKey();
    /**
     * 交易密码	必填
     */
    private String txPswd;
    /**
     * 合同及协议列表	 文件代码	 fileCode
     */
    private List<String> contractList;
}