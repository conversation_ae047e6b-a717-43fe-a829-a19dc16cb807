package com.howbuy.crm.cgi.extservice.common.enums;

/**
 * @description: 验证码类型枚举
 * @author: jinqing.rao
 * @date: 2024/4/15 11:22
 * @since JDK 1.8
 */
public enum VerifyCodeChannelEnum {

    // 1 : 手机号  2: 邮箱; 3: 语音验证码
    MOBILE("1", "手机号"),
    EMAIL("2", "邮箱"),
    VOICE("3", "语音验证码");
    ;

    private final String code;
    private final String desc;

    VerifyCodeChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
