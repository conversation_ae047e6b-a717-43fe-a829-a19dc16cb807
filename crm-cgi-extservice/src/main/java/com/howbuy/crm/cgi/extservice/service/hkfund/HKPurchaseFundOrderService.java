package com.howbuy.crm.cgi.extservice.service.hkfund;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.VerifyCodeTypeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import com.howbuy.crm.cgi.common.utils.AlertLogUtil;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.extservice.common.enums.fund.*;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctOrderStatusEnum;
import com.howbuy.crm.cgi.extservice.convert.fund.HkPurchaseFundConvert;
import com.howbuy.crm.cgi.extservice.request.hkfund.*;
import com.howbuy.crm.cgi.extservice.validator.hkfund.*;
import com.howbuy.crm.cgi.extservice.vo.hkfund.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.QueryFundPurchasePageInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.FundTradeCalendarInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkChangeInvestorQualsResultDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.CustContractSignOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HKFundPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QuerySupportedPiggyBuyFundListOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkChangeInvestorOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.BusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class HKPurchaseFundOrderService extends AbstractFundOrderService {

    @Value("${app.purchase.fund.transfer.bank.info}")
    private String transferBankInfoJson;

    @Resource
    private CustContractSignOuterService custContractSignOuterService;

    @Resource
    private HkChangeInvestorOuterService hkChangeInvestorOuterService;

    @Resource
    private HKFundPayVoucherOuterService hkFundPayVoucherOuterService;

    @Resource
    private QuerySupportedPiggyBuyFundListOuterService querySupportedPiggyBuyFundListOuterService;

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundVerificationVO
     * @description: 购买页入口验证
     * @author: jinqing.rao
     * @date: 2024/4/10 19:37
     * @since JDK 1.8
     */
    public HKFundVerificationVO purchaseFundVerification(HKPurchaseFundBaseRequest request) {
        //获取基金产品基本信息
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(request.getFundCode());
        //获取用户的账户信息
        HkCustInfoDTO hkCustInfo = getHkCustInfoDTO(request.getHkCustNo());
        //添加校验器
        List<HkFundValidator> validators = addHkFundValidators(hkCustInfo, fundBasicInfoDTO);
        //执行校验器
        HKFundVerificationVO verificationVO = verification(validators);
        // 结果不为空,返回具体的状态status,前端根据具体的状态跳转对应的页面
        if (null != verificationVO) {
            return verificationVO;
        }
        HKFundVerificationVO purchaseFundVerificationVO = new HKFundVerificationVO();
        purchaseFundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.NORMAL.getCode());
        return purchaseFundVerificationVO;
    }

    /**
     * @param hkCustInfo       用户信息
     * @param fundBasicInfoDTO 基金信息
     * @return java.util.List<com.howbuy.crm.cgi.extservice.validator.hkfund.HkFundValidator>
     * @description: 添加必要的校验器, 在循环执行, 也可用设计模式代替, 这里主打简单化
     * @author: jinqing.rao
     * @date: 2024/4/24 17:11
     * @since JDK 1.8
     */
    @Override
    public List<HkFundValidator> addHkFundValidators(HkCustInfoDTO hkCustInfo, FundBasicInfoDTO fundBasicInfoDTO) {
        return new ArrayList<HkFundValidator>() {
            private static final long serialVersionUID = -5640545335979781797L;

            {
                //客户状态过滤器
                add(new HkCustStatusValidator(hkCustInfo));
                //校验用户的开户信息
                add(new HkOpenAcctStatusValidator(hkCustInfo, hkCustInfoOuterService));
                //是否绑定银行卡
                add(new BindBankCardValidator(hkCustInfo, hkBankCardInfoOuterService));
                //校验客户风险等级与产品的分险等级是否匹配
                add(new HkFundRiskLevelValidator(hkCustInfo, fundBasicInfoDTO));
                //是否具有衍生品经验
                add(new HkFundDerivativeKnowledgeValidator(hkCustInfo, fundBasicInfoDTO));
                //是否专业投资者
                add(new HkFundInvestorQualificationValidator(hkCustInfo, fundBasicInfoDTO));

            }
        };
    }


    /**
     * @param request 请求参数
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkPurchasePageInfoVO
     * @description: 购买页面基本信息
     * @author:  jinqing.rao
     * @date: 2024/4/11 16:28
     * @since JDK 1.8
     */
    public HkPurchasePageInfoVO queryPurchasePageInfo(HKPurchaseFundBaseRequest request) {
        //获取用户的客户信息
        HkCustInfoDTO hkCustInfo = getHkCustInfoDTO(request.getHkCustNo());
        //获取基金的基本信息
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(request.getFundCode());
        String businessType = BusinessTypeEnum.BUY.getCode();
        String businessCode = null;
        // 分次CALL产品,默认实缴
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getGradationCall())) {
            businessType = BusinessTypeEnum.SUB_AND_FIRST_PAID.getCode();
            businessCode = BusinessCodeEnum._112B.getMCode();
        }
        //调用中台查询购买页面信息
        QueryFundPurchasePageInfoDTO pageInfoDTO = new QueryFundPurchasePageInfoDTO();
        pageInfoDTO.setHkCustNo(request.getHkCustNo());
        pageInfoDTO.setFundCode(request.getFundCode());
        pageInfoDTO.setBusinessType(businessType);
        pageInfoDTO.setBusinessCode(businessCode);
        BuyInfoDTO buyInfoDTO = hkFundOuterService.queryPurchasePageInfo(pageInfoDTO);

        AlertLogUtil.alert(HKPurchaseFundOrderService.class.getName(), "告警接口测试");
        //设置银行卡信息
        List<HkBankCardInfoDTO> hkBankAcctList = hkBankCardInfoOuterService.getHkBankAcctLogoList(request.getHkCustNo());
        // 储蓄罐信息
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
        return HkPurchaseFundConvert.toHkPurchasePageInfoVO(fundBasicInfoDTO, buyInfoDTO, hkBankAcctList, hkCustInfo, hkCustPiggyAgreement);
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundFeeComputeVO
     * @description: 购买金额手续费计算接口
     * @author: jinqing.rao
     * @date: 2024/4/12 9:49
     * @since JDK 1.8
     */
    public HKPurchaseFundFeeComputeVO fundFeeCompute(HKPurchaseFundFeeComputeRequest request) {
        //获取基金的基本信息,基金校验,
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(request.getFundCode());
        HKPurchaseFundFeeComputeInfoDTO feeComputeInfoDTO = null;
        // 分次CALL基金
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getGradationCall())) {
            if (StringUtils.isBlank(request.getPaidAmt())) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "实缴金额不能为空");
            }
            feeComputeInfoDTO = hkFundOuterService.gradationCallFundFeeCompute(request.getHkCustNo(), request.getFundCode(), new BigDecimal(request.getPaidAmt()), new BigDecimal(request.getAppAmt()));
            // 处理返回结果
            return HkPurchaseFundConvert.toHKPurchaseFundFeeComputeVO(feeComputeInfoDTO);
        }

        //调用中台接口,获取手续费信息
        feeComputeInfoDTO = hkFundOuterService.fundFeeCompute(request.getHkCustNo(), request.getFundCode(), new BigDecimal(request.getAppAmt()));
        return HkPurchaseFundConvert.toHKPurchaseFundFeeComputeVO(feeComputeInfoDTO);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkPurchaseFundSubmitVO
     * @description: 申购提交
     * @author: jinqing.rao
     * @date: 2024/4/15 11:07
     * @since JDK 1.8
     */
    public HkPurchaseFundSubmitVO submit(HkPurchaseFundSubmitRequest request) {
        // 海外下单流程优化-- 当是公募基金的时候去掉手机号/邮箱的校验
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(request.getFundCode());
        if (!(HKFundCategoryEnum.PUBLIC_FUND.getCode().equals(fundBasicInfoDTO.getFundCategory()))) {
            //手机验证码或者邮箱校验或者语音验证码校验
            validVerifyCodeByType(request.getVerifyCodeType(), request.getVerifyCode(), request.getHkCustNo(), VerifyCodeTypeEnum.HK_FUND_PURCHASE_VERIFY_CODE, VerifyCodeTypeEnum.HK_FUND_PURCHASE_EMAIL_VERIFY_CODE, VerifyCodeTypeEnum.BUY_VOICE_CODE);
        }
        //校验交易密码,签署合同
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        //签署合同,获取订单号
        CustContractSignDTO responseDTO = getCustContractSignDTO(request);
        //提交申购数据
        HkPurchaseFundSubmitInfoDTO hkPurchaseFundSubmitInfoDTO = HkPurchaseFundConvert.toHkPurchaseFundSubmitInfoDTO(request, responseDTO.getDealNo());

        // 分次CALL产品
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getGradationCall())) {
            return subAndFirstPaidSubmit(request, hkPurchaseFundSubmitInfoDTO);
        }
        // 认申购
        hkFundOuterService.submit(hkPurchaseFundSubmitInfoDTO);

        HkPurchaseFundSubmitVO hkPurchaseFundSubmitVO = new HkPurchaseFundSubmitVO();
        hkPurchaseFundSubmitVO.setOrderNo(responseDTO.getDealNo());
        //返回当前的日期,用作前端提示语的判断显示
        hkPurchaseFundSubmitVO.setCurrentDate(LocalDate.now().toString());
        return hkPurchaseFundSubmitVO;
    }

    /**
     * @param request
     * @param hkPurchaseFundSubmitInfoDTO
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkPurchaseFundSubmitVO
     * @description: 认购+首次实缴 提交
     * 实缴订单需要重新签署合同,生成文件。获取对应的实缴订单号,并且返回实缴订单号，结果页使用
     * @author: jinqing.rao
     * @date: 2025/4/16 13:16
     * @since JDK 1.8
     */
    private HkPurchaseFundSubmitVO subAndFirstPaidSubmit(HkPurchaseFundSubmitRequest request, HkPurchaseFundSubmitInfoDTO hkPurchaseFundSubmitInfoDTO) {
        HkPurchaseFundSubmitVO hkPurchaseFundSubmitVO = new HkPurchaseFundSubmitVO();
        // 校验实缴金额必传
        if (StringUtils.isBlank(request.getPaidAmt())) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "实缴金额不能为空");
        }
        // 实缴签署合同
        CustContractSignDTO paidResponseDTO = getCustContractSignDTO(request);
        // 添加实缴订单号
        hkPurchaseFundSubmitInfoDTO.setPaidDealNo(paidResponseDTO.getDealNo());
        hkFundOuterService.gradationCallFundSubmit(hkPurchaseFundSubmitInfoDTO);

        hkPurchaseFundSubmitVO.setOrderNo(hkPurchaseFundSubmitInfoDTO.getDealNo());
        hkPurchaseFundSubmitVO.setPaidOrderNo(paidResponseDTO.getDealNo());
        //返回当前的日期,用作前端提示语的判断显示
        hkPurchaseFundSubmitVO.setCurrentDate(LocalDate.now().toString());
        return hkPurchaseFundSubmitVO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.CustContractSignDTO
     * @description: 申购提交, 签署合同, 获取订单号
     * @author: jinqing.rao
     * @date: 2025/4/16 14:07
     * @since JDK 1.8
     */
    private CustContractSignDTO getCustContractSignDTO(HkPurchaseFundSubmitRequest request) {
        DigitalSignSignDTO digitalSignSignDTO = HkPurchaseFundConvert.toCustFundContractSignDTO(request.getHkCustNo(), request.getFundCode(),
                request.getSignFlag(), HKFundTradeModeEnum.HK_PURCHASE_FUND.getCode(), request.getContractList(), request.getPrebookDealNo());
        CustContractSignDTO responseDTO = custContractSignOuterService.fundSign(digitalSignSignDTO);
        if (null == responseDTO || StringUtils.isBlank(responseDTO.getDealNo())) {
            throw new BusinessException(ExceptionCodeEnum.HK_PURCHASE_FUND_SIGN_CONTRACT_FAIL);
        }
        return responseDTO;
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HKPurchaseFundApplyResultVO
     * @description: 根据订单号查询申购订单结果
     * @author: jinqing.rao
     * @date: 2024/4/15 15:20
     * @since JDK 1.8
     */
    public HKPurchaseFundApplyResultVO queryApplyOrderResult(HKPurchaseFundApplyResultRequest request) {
        //获取客户信息
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(request.getHkCustNo());
        //获取订单信息
        HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO = hkFundOuterService.queryFundOrderInfo(request.getHkCustNo(), request.getOrderNo());

        //获取基金信息
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(hkPurchaseFundOrderInfoDTO.getFundCode());

        // 分次CALL产品 查询实缴订单信息
        HKPurchaseFundOrderInfoDTO paidFundOrderInfoDTO = new HKPurchaseFundOrderInfoDTO();
        if (YesNoEnum.YES.getCode().equals(fundBasicInfoDTO.getGradationCall()) && StringUtils.isNotBlank(request.getPaidOrderNo())) {
            paidFundOrderInfoDTO = hkFundOuterService.queryFundOrderInfo(request.getHkCustNo(), request.getPaidOrderNo());
        }
        boolean supportBuyPrebook = HKFundPrebookEnum.isSupportBuyPrebook(hkPurchaseFundOrderInfoDTO.getSupportPrebookFlag());
        FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO = null;
        // 不支持预约,获取储蓄罐的截止时间 格式HH:MM:ss,并且获取最小值
        if (!supportBuyPrebook) {
            //可购买的储蓄罐底层基金
            FundBasicInfoDTO piggyFundBasicInfoDTO = querySupportedPiggyBuyFundListOuterService.querySupportBuyPiggyBuyFundList(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
            if (null != piggyFundBasicInfoDTO) {
                fundBasicInfoDTO.setOrderEndTm(piggyFundBasicInfoDTO.getOrderEndTm());
            }
        } else {
            // 若产品支持预约交易 且【业务类型】=认购/申购 的交易日历的【打款截止日期】【打款截止时间】；
            if (StringUtils.isNotBlank(paidFundOrderInfoDTO.getMiddleBusiCode())){
                fundTradeCalendarInfoDTO = getFundTradeCalendarInfoDTO(paidFundOrderInfoDTO, fundTradeCalendarInfoDTO);
            }else{
                fundTradeCalendarInfoDTO = getFundTradeCalendarInfoDTO(hkPurchaseFundOrderInfoDTO, fundTradeCalendarInfoDTO);
            }
        }
        //获取专业投资者是否存在上传资料
        String investorAuditStatus = YesNoEnum.NO.getCode();
        HkChangeInvestorQualsResultDTO hkChangeInvestorQualsResultDTO = hkChangeInvestorOuterService.getInvestAuditDetail(request.getHkCustNo());
        // 若不存在审核中的材料，即无关联材料，或材料【审核状态】=审核通过/作废/审核不通过
        if (HkOpenAcctOrderStatusEnum.PENDING_REVIEW.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
                || HkOpenAcctOrderStatusEnum.PENDING_RECHECK.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag()) ||
                HkOpenAcctOrderStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())
                || HkOpenAcctOrderStatusEnum.REJECTED_TO_INITIAL.getCode().equals(hkChangeInvestorQualsResultDTO.getTxChkFlag())) {
            investorAuditStatus = YesNoEnum.YES.getCode();
        }
        return HkPurchaseFundConvert.toHKPurchaseFundApplyResultVO(hkPurchaseFundOrderInfoDTO, fundBasicInfoDTO, hkCustInfo, transferBankInfoJson, investorAuditStatus, fundTradeCalendarInfoDTO, paidFundOrderInfoDTO);
    }

    /**
     * @param hkPurchaseFundOrderInfoDTO
     * @param fundTradeCalendarInfoDTO
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.FundTradeCalendarInfoDTO
     * @description: 根据订单信息获取交易日历信息
     * @author: jinqing.rao
     * @date: 2025/4/16 9:48
     * @since JDK 1.8
     */
    private FundTradeCalendarInfoDTO getFundTradeCalendarInfoDTO(HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO, FundTradeCalendarInfoDTO fundTradeCalendarInfoDTO) {
        String middleBusiCode = hkPurchaseFundOrderInfoDTO.getMiddleBusiCode();
        if (HkFundMBusiCodeEnum.SUBS.getMCode().equals(middleBusiCode)
                || HkFundMBusiCodeEnum.PURCHASE.getMCode().equals(middleBusiCode)
                || HkFundMBusiCodeEnum._112B.getMCode().equals(middleBusiCode)) {
            String codeByMCode = HkFundMBusiCodeEnum.getCodeByMCode(middleBusiCode);
            //获取产品的打款截止时间
            fundTradeCalendarInfoDTO = queryFundBasicInfoOuterService.queryFundTradeCalendarInfo(hkPurchaseFundOrderInfoDTO.getFundCode(), codeByMCode, hkPurchaseFundOrderInfoDTO.getAppDt(), hkPurchaseFundOrderInfoDTO.getAppTm());
        }
        return fundTradeCalendarInfoDTO;
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.hkfund.HkPurchasePendingMessageDetailVO
     * @description: 认缴+首次实缴查询待缴信息
     * @author: jinqing.rao
     * @date: 2025/4/16 14:43
     * @since JDK 1.8
     */
    public SubPaidPendingDetailVO queryPendingMessageDetail(HKPurchasePendingMessageDetailRequest request) {
        //获取用户的客户信息
        HkCustInfoDTO hkCustInfo = getHkCustInfoDTO(request.getHkCustNo());

        //查询修改支付方式代办消息详情
        ChangePayMethodPageDTO changePayMethodPageDTO = hkFundOuterService.queryPendingMessageDetail(request.getHkCustNo(), request.getOrderNo());

        //获取基金的基本信息
        FundBasicInfoDTO fundBasicInfoDTO = getFundBasicInfoDTO(changePayMethodPageDTO.getChangePayMethodOrderDTO().getFundCode());
        //设置银行卡信息
        List<HkBankCardInfoDTO> hkBankAcctList = hkBankCardInfoOuterService.getHkBankAcctLogoList(request.getHkCustNo());
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
        return HkPurchaseFundConvert.toHkPurchaseSubAndFirstPaidPendingMessageVO(fundBasicInfoDTO, changePayMethodPageDTO, hkBankAcctList, hkCustPiggyAgreement);
    }

    /**
     * @description: 构建请求参数
     * @param request	
     * @param hkPurchaseFundOrderInfoDTO	
     * @param businessType
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.QueryFundPurchasePageInfoDTO
     * @author: jinqing.rao
     * @date: 2025/4/28 16:20
     * @since JDK 1.8
     */
    private static QueryFundPurchasePageInfoDTO buildQueryFundPurchasePageInfoDTO(HKPurchasePendingMessageDetailRequest request,
                                                                                  HKPurchaseFundOrderInfoDTO hkPurchaseFundOrderInfoDTO,
                                                                                  String businessType,
                                                                                  String businessCode) {
        QueryFundPurchasePageInfoDTO pageInfoDTO = new QueryFundPurchasePageInfoDTO();
        pageInfoDTO.setHkCustNo(request.getHkCustNo());

        return pageInfoDTO;
    }

    /**
     * @param request
     * @return void
     * @description: 认缴+首次实缴修改支付方式
     * @author: jinqing.rao
     * @date: 2025/4/16 14:40
     * @since JDK 1.8
     */
    public void modifyPaymentMethod(GradationCallModifyPaymentMethodRequest request) {
        hkFundOuterService.modifyPaymentMethod(request.getHkCustNo(),request.getDealNo(), request.getPayMethod(), request.getCpAcctNo());
    }

}
