package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 翻译结果类
 * @date 2023/11/30 14:54
 * @since JDK 1.8
 */
@Setter
@Getter
@Builder
public class OpenTranslateForeignVO extends AccountBase<PERSON> implements Serializable {

    private static final long serialVersionUID = -6257455829647228831L;

    /**
     * 翻译后信息
     */
    private List<OpenTranslateForeignInfo> translateList;

    /**
     * @description: 中文转拼音的参数信息
     * @author: jinqing.rao
     * @date: 2023/12/14 13:15
     * @since JDK 1.8
     */
    @Setter
    @Getter
    @Builder
    public static class OpenTranslateForeignInfo implements Serializable {

        private static final long serialVersionUID = -6244886495506165598L;

        /**
         * 中文字符串对应的唯一key,前端默认生成,方便获取翻译后的拼音
         */
        private String uuidKey;

        /**
         * 翻译后的语言
         */
        private String foreignLanguage;

        /**
         * 中文
         */
        private String chineseLanguage;
    }
}
