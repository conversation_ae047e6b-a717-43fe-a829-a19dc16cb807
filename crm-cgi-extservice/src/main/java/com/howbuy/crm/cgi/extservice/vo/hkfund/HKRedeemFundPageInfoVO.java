/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkfund;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 赎回页面信息查询
 * <AUTHOR>
 * @date 2024/4/18 13:59
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKRedeemFundPageInfoVO extends Body implements Serializable {

    private static final long serialVersionUID = 2372403044316705636L;

    /**
     * 手机区号
     */
    private String mobileAreaCode;

    /**
     * 手机号掩码
     */
    private String mobileMask;

    /**
     * 邮箱掩码
     */
    private String emailMask;
    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 邮箱地址密文
     */
    private String emailDigest;

    /**
     * 赎回页信息
     */
    private RedeemFundInfoVO redeemFundInfoVO;
    /**
     *  赎回预约信息
     */
    private RedeemPrebookInfoVO redeemPrebookInfoVO;
    /**
     * 赎回费率信息
     */
    private List<RedeemFeeRateInfoVO>  redeemFeeRateInfoVO;
    /**
     * 赎回方向信息
     */
    private RedeemDirectionInfoVO redeemDirectionInfoVO;

    @Setter
    @Getter
    public static class RedeemFundInfoVO implements Serializable {
        private static final long serialVersionUID = 7590880079678783729L;

        /**
         * 基金简称
         */
        private String fundShortName;

        /**
         * 基金英文名
         */
        private String fundEnName;

        /**
         * 是否支持预约  1 : 是 0 : 否
         */
        private String supportPrebook;

        /**
         * 开放开始日期
         */
        private String openStartDate;

        /**
         * 开放结束日期
         */
        private String openEndDate;

        /**
         *  开放结束时间
         */
        private String openEndTime;
        /**
         *  交易时间 yyyy-MM-dd
         */
        private String tradeDate;

        /**
         * 当前日期
         */
        private String currentDate;


        /**
         * 币种
         */
        private String  currencyCode;

        /**
         * 币种描述
         */
        private String currencyDesc;

        /**
         *  有无在途订单 0：否 1：是
         */
        private String  inTransitOrder;

        /**
         * 总份额
         */
        private String totalVol;

        /**
         * 份额单位
         */
        private String  volUnit;

        /**
         * 可用份额
         */
        private String availableVol;

        /**
         * 基金分类
         * 1-公募、2-私募、9-其他
         */
        private String fundCategory;

        /**
         * 总资产
         */
        private String  totalAsset;

        /**
         * 可用资产
         */
        private String  availableAsset;

        /**
         * 净值日期
         */
        private String  navDate;

        /**
         * 1-按份额、2-按金额、3-全部
         */
        private String fundRedeemMethod;

        /**
         * 按金额赎回折价比率
         */
        private String amtRedeemDiscountRatio;


        /**
         * 最低持有份额
         */
        private String minHoldVol;

        /**
         * 最低持有金额
         */
        private String minHoldAmt;

        /**
         *  最低申请份额
         */
        private String minAppVol;


        /**
         *  最低申请金额
         */
        private String minAppAmt;

        /**
         * 最高申请金额
         */
        private String maxAppAmt;

        /**
         * 最高申请份额
         */
        private String maxAppVol;



        /**
         * 份额精度
         */
        private Integer volPrecision;
    }
    
    @Setter
    @Getter
    public static class RedeemPrebookInfoVO implements Serializable {

        private static final long serialVersionUID = -5217970978852919849L;
        /**
         *  预约赎回方式 1-按份额、2-按金额
         */
        private String prebookRedeemMethod;

        /**
         *   预约赎回金额
         */
        private String prebookRedeemAmt;

        /**
         *  预约赎回份额
         */
        private String prebookRedeemVol;

        /**
         *  预约赎回日期
         */
        private String prebookRedeemDate;

        /**
         *  预约赎回时间
         */
        private String prebookRedeemTime;

        /**
         * 预约单号
         */
        private String prebookDealNo;

    }
    
    @Setter
    @Getter
    public static class RedeemFeeRateInfoVO implements Serializable {

        private static final long serialVersionUID = -1458986343108795330L;

        /**
         *   最小费率天数
         */
        private String  minFeeRateDays;

        /**
         *  最大费率天数
         */
        private String  maxFeeRateDays;

        /**
         *  手续费率
         */
        private String feeRate;
    }
    
    @Setter
    @Getter
    public static class RedeemDirectionInfoVO implements Serializable {

        private static final long serialVersionUID = 8660218306954140514L;

        /**
         *   是否签约储蓄罐
         */
        private String  signCxg;

        /**
         * 赎回银行卡列表
         */
        private List<RedeemDirectionBankInfoVO> redeemDirectionBankInfoVO;
        
        @Setter
        @Getter
        public static class RedeemDirectionBankInfoVO implements Serializable {
            private static final long serialVersionUID = -6271974234534852300L;
            /**
             * 资金账号
             */
            private String cpAcctNo;

            /**
             * 银行卡号掩码
             */
            private String bankAcctMask;

            /**
             * 银行名称
             */
            private String bankName;

            /**
             * 银行Logo
             */
            private String bankLogoUrl;
        }

    }




}
