/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Data;


/**
 * <AUTHOR>
 * @description: (客户信息)
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
public class PersonalCenterInfoVO extends AccountBaseVO {

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 香港客户号密文
     */
    private String hkCustNoCipher;
    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号摘要
     */
    private String mobileDigest;
    /**
     * 邮箱地址掩码
     */
    private String emailMask;
    /**
     * 邮箱地址密文
     */
    private String emailDigest;
    /**
     * 客户风险等级
     * 风险等级
     * 0-保守型（最低）
     * 1-保守型
     * 2-稳健型
     * 3-平衡型
     * 4-成长型
     * 5-进取型
     */
    private String riskToleranceLevel;
    /**
     * 银行卡数量
     */
    private String bindCardCount;
    /**
     * 登录密码设置标识	 0正常状态 1重置状态 2-未设置
     */
    private String custLoginPasswdType;
    /**
     * 交易密码设置标识	 0正常状态 1重置状态 2-未设置
     */
    private String custTxPasswdType;
    /**
     * 实名信息是否完整标识	  0-不完整；1-完整
     */
    private String realNameCompletedFlag;


    /**
     * 客户风测过期标识 1：是 0：否
     */
    private String riskToleranceExpire;

    /**
     * 开户入金状态
     * 01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     */
    private String openDepositsStatus;

    /**
     * 开户步骤标识
     * 01-证件信息页；02-个人信息页；03-职业信息页；04-声明信息页；05-投资经验页；06-银行卡页；07-电子签名页
     */
    private String openAcctStepFlag;


    /**
     * 一账通绑定状态 0-未绑定 1-已绑定
     */
    private String hboneBindStatus;

    /**
     * 客户状态0-正常 1-注销 2-休眠 3-注册 4-开户申请成功
     */
    private String custState;

    /**
     * 登录是否激活  1:是   0：否
     */
    private String loginActivate;

    /**
     *交易是否激活  1:是  0：否
     */
    private String transactionActivation;

    /**
     *0:普通(去认证) 1:专业
     */
    private String investorType;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }
    public String getHkCustNoCipher() {
        return hkCustNoCipher;
    }

    public void setHkCustNoCipher(String hkCustNoCipher) {
        this.hkCustNoCipher = hkCustNoCipher;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getEmailMask() {
        return emailMask;
    }

    public void setEmailMask(String emailMask) {
        this.emailMask = emailMask;
    }

    public String getEmailDigest() {
        return emailDigest;
    }

    public void setEmailDigest(String emailDigest) {
        this.emailDigest = emailDigest;
    }

    public String getRiskToleranceLevel() {
        return riskToleranceLevel;
    }

    public void setRiskToleranceLevel(String riskToleranceLevel) {
        this.riskToleranceLevel = riskToleranceLevel;
    }

    public String getBindCardCount() {
        return bindCardCount;
    }

    public void setBindCardCount(String bindCardCount) {
        this.bindCardCount = bindCardCount;
    }

    public String getCustLoginPasswdType() {
        return custLoginPasswdType;
    }

    public void setCustLoginPasswdType(String custLoginPasswdType) {
        this.custLoginPasswdType = custLoginPasswdType;
    }

    public String getCustTxPasswdType() {
        return custTxPasswdType;
    }

    public void setCustTxPasswdType(String custTxPasswdType) {
        this.custTxPasswdType = custTxPasswdType;
    }

    public String getRealNameCompletedFlag() {
        return realNameCompletedFlag;
    }

    public void setRealNameCompletedFlag(String realNameCompletedFlag) {
        this.realNameCompletedFlag = realNameCompletedFlag;
    }

    public String getRiskToleranceExpire() {
        return riskToleranceExpire;
    }

    public void setRiskToleranceExpire(String riskToleranceExpire) {
        this.riskToleranceExpire = riskToleranceExpire;
    }

    public String getOpenDepositsStatus() {
        return openDepositsStatus;
    }

    public void setOpenDepositsStatus(String openDepositsStatus) {
        this.openDepositsStatus = openDepositsStatus;
    }

    public String getOpenAcctStepFlag() {
        return openAcctStepFlag;
    }

    public void setOpenAcctStepFlag(String openAcctStepFlag) {
        this.openAcctStepFlag = openAcctStepFlag;
    }

    public String getHboneBindStatus() {
        return hboneBindStatus;
    }

    public void setHboneBindStatus(String hboneBindStatus) {
        this.hboneBindStatus = hboneBindStatus;
    }

    public String getCustState() {
        return custState;
    }

    public void setCustState(String custState) {
        this.custState = custState;
    }

    public String getLoginActivate() {
        return loginActivate;
    }

    public void setLoginActivate(String loginActivate) {
        this.loginActivate = loginActivate;
    }

    public String getTransactionActivation() {
        return transactionActivation;
    }

    public void setTransactionActivation(String transactionActivation) {
        this.transactionActivation = transactionActivation;
    }

    public String getInvestorType() {
        return investorType;
    }

    public void setInvestorType(String investorType) {
        this.investorType = investorType;
    }
}