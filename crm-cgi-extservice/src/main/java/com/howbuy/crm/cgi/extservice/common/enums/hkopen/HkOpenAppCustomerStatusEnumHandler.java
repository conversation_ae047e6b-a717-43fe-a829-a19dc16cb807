/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAccOrderInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hkopen.HkOpenAcctDepositInfoDTO;
import com.howbuy.crm.cgi.manager.enums.HkOpenAccountStepEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.EnumSet;

/**
 * @description: 海外App个人中心状态枚举接口
 * <AUTHOR>
 * @date 2024/2/23 13:14
 * @since JDK 1.8
 */
public enum HkOpenAppCustomerStatusEnumHandler implements Handler{
    /**
     * 隐藏开户入金区域处理器
     */
    HIDE_ACCOUNT_DEPOSIT_AREA_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>> HIDE_ACCOUNT_DEPOSIT_AREA_HANDLER>> hkCustNo:{}, custStatus : {}",hkCustNo,hkCustInfo.getCustState());
            //客户的状态不为正常状态
            if (!HkOpenAcctStatusEnum.NORMAL.getCode().equals(hkCustInfo.getCustState()) && !HkOpenAcctStatusEnum.DORMANT.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            return HkOpenCustomerStatusEnum.HIDE_ACCOUNT_DEPOSIT_AREA;
        }
    },

    /**
     * 去开户处理器,判断条件,客户状态是注册,且没有暂存信息,表示01-去开户
     */
    OPEN_ACCOUNT_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>> OPEN_ACCOUNT_HANDLER>> hkCustNo:{},custState:{}",hkCustNo,hkCustInfo.getCustState());
            //客户状态不是注册
            if (!HkOpenAcctStatusEnum.REGISTERED.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //判断是否存在暂存信息
            Object object = CacheServiceImpl.getInstance().get(HkOpenAccountStepEnum.IDENTITY_INFORMATION.getHkOpenAccountStepKey(hkCustNo));
            //没有缓存,没有开户订单,返回去开户状态
            if (null == object && (null == hkOpenAccOrderInfoDTO || StringUtils.isBlank(hkOpenAccOrderInfoDTO.getDealNo()))) {
                log.info("App >>> OPEN_ACCOUNT_HANDLER>> 返回去开户状态 hkCustNo:{},custState:{}",hkCustNo,hkCustInfo.getCustState());
                return HkOpenCustomerStatusEnum.OPEN_ACCOUNT;
            }
            return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
        }
    },
    /**
     * 继续开户处理器,客户状态为注册，有暂存信息，不存在开户订单 02-继续处理
     */
    CONTINUE_ACCOUNT_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info(" App >>> CONTINUE_ACCOUNT_HANDLER>> hkCustNo:{},custState:{}", hkCustNo,hkCustInfo.getCustState());
            //客户状态不是注册
            if (!HkOpenAcctStatusEnum.REGISTERED.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            Object object = CacheServiceImpl.getInstance().get(HkOpenAccountStepEnum.IDENTITY_INFORMATION.getHkOpenAccountStepKey(hkCustNo));
            //没有缓存信息,存在开户订单,返回去继续开户
            if (null!= hkOpenAccOrderInfoDTO && StringUtils.isNotBlank(hkOpenAccOrderInfoDTO.getDealNo())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //获取暂存订单中编辑信息的最大页数
            if(null == hkOpenAccOrderInfoDTO){
                hkOpenAccOrderInfoDTO = HkOpenAccOrderInfoDTO.builder().build();
            }
            HkOpenAccountStepEnum hkOpenAccountStepEnum = HkOpenAccountStepEnum.getMaxContinueAccountStep(hkCustNo);
            if(null != hkOpenAccountStepEnum){
                hkOpenAccOrderInfoDTO.setHkOpenAccountStepEnumName(hkOpenAccountStepEnum.name());
            }
            //主要是为了前端展示,返回给前端 1,2,3,4,5
            hkOpenAccOrderInfoDTO.setOpenAcctStep(hkOpenAccountStepEnum == null ? "1" : Integer.valueOf(hkOpenAccountStepEnum.getCode()).toString());
            return HkOpenCustomerStatusEnum.CONTINUE_ACCOUNT;
        }
    },
    /**
     * 查看开户进度处理器,客户状态为注册，存在开户订单且状态为待审核，03-查看开户进度
     */
    VIEW_ACCOUNT_PROGRESS_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>>VIEW_ACCOUNT_PROGRESS_HANDLER>> hkCustInfo:{},hkOpenAccOrderInfoDTO:{}", JSON.toJSONString(hkCustInfo),JSON.toJSONString(hkOpenAccOrderInfoDTO));
            //客户的状态不是注册
            if (!HkOpenAcctStatusEnum.REGISTERED.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //不存在开户订单
            if (null == hkOpenAccOrderInfoDTO || StringUtils.isBlank(hkOpenAccOrderInfoDTO.getDealNo())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //存在开户订单,但状态是待审核
            if (HkOpenAcctOrderStatusEnum.PENDING_REVIEW.getCode().equals(hkOpenAccOrderInfoDTO.getAcctOrderStatus()) ||
                    HkOpenAcctOrderStatusEnum.PENDING_RECHECK.getCode().equals(hkOpenAccOrderInfoDTO.getAcctOrderStatus()) ||
                    HkOpenAcctOrderStatusEnum.REJECTED_TO_INITIAL.getCode().equals(hkOpenAccOrderInfoDTO.getAcctOrderStatus())) {
                return HkOpenCustomerStatusEnum.VIEW_ACCOUNT_PROGRESS;
            }
            return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
        }
    },
    /**
     * 修改开户资料处理器
     */
    MODIFY_ACCOUNT_INFO_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>>MODIFY_ACCOUNT_INFO_HANDLER>> hkCustInfo:{},hkOpenAccOrderInfoDTO:{}", JSON.toJSONString(hkCustInfo),JSON.toJSONString(hkOpenAccOrderInfoDTO));
            //客户的状态不是注册
            if (!HkOpenAcctStatusEnum.REGISTERED.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //不存在开户订单
            if (null == hkOpenAccOrderInfoDTO || StringUtils.isBlank(hkOpenAccOrderInfoDTO.getDealNo())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //存在开户订单,但状态不是驳回
            if (!HkOpenAcctOrderStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(hkOpenAccOrderInfoDTO.getAcctOrderStatus())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            return HkOpenCustomerStatusEnum.MODIFY_ACCOUNT_INFO;
        }
    },
    /**
     * 去入金处理器 客户状态为开户申请成功，不存在线上入金订单，05-去入金
     */
    DEPOSIT_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>>DEPOSIT_HANDLER>> hkCustInfo:{},hkOpenAccOrderInfoDTO:{}", JSON.toJSONString(hkCustInfo),JSON.toJSONString(hkOpenAccOrderInfoDTO));
            //客户的状态不是开户申请成功
            if (!HkOpenAcctStatusEnum.APPLICATION_SUCCESS.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //存在线上入金订单
            if (null != hkOpenAcctDepositInfoDTO && StringUtils.isNotBlank(hkOpenAcctDepositInfoDTO.getDepositStatus())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            return HkOpenCustomerStatusEnum.DEPOSIT;
        }
    },
    /**
     * 查看入金进度处理器 客户状态为开户申请成功，存在线上入金订单且状态为待审核，06-查看入金进度
     */
    VIEW_DEPOSIT_PROGRESS_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>>VIEW_DEPOSIT_PROGRESS_HANDLER>> hkCustInfo:{},hkOpenAccOrderInfoDTO:{}", JSON.toJSONString(hkCustInfo),JSON.toJSONString(hkOpenAccOrderInfoDTO));
            //客户的状态不为开户申请成功
            if (!HkOpenAcctStatusEnum.APPLICATION_SUCCESS.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //不存在线上入金订单
            if (null == hkOpenAcctDepositInfoDTO) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //状态为待审核
            if (!HkOpenAcctDepositStatusEnum.PENDING_RECHECK.getCode().equals(hkOpenAcctDepositInfoDTO.getDepositStatus())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            return HkOpenCustomerStatusEnum.VIEW_DEPOSIT_PROGRESS;
        }
    },
    /**
     * 修改入金资料处理器 客户状态为开户申请成功，存在线上入金订单且状态为驳回，07- 修改入金资料
     */
    MODIFY_DEPOSIT_INFO_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>>MODIFY_DEPOSIT_INFO_HANDLER>> hkCustInfo:{},hkOpenAccOrderInfoDTO:{}", JSON.toJSONString(hkCustInfo),JSON.toJSONString(hkOpenAccOrderInfoDTO));
            //客户的状态不为开户申请成功
            if (!HkOpenAcctStatusEnum.APPLICATION_SUCCESS.getCode().equals(hkCustInfo.getCustState())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            //不存在线上入金订单
            if (null == hkOpenAcctDepositInfoDTO) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            if (!HkOpenAcctDepositStatusEnum.REJECTED_TO_CUSTOMER.getCode().equals(hkOpenAcctDepositInfoDTO.getDepositStatus())) {
                return getNextHandler().handleRequest(hkCustNo,hkCustInfo, hkOpenAccOrderInfoDTO, hkOpenAcctDepositInfoDTO);
            }
            return HkOpenCustomerStatusEnum.MODIFY_DEPOSIT_INFO;
        }
    },

    /**
     *异常处理器,没有命中以上的枚举,抛出异常
     */
    HK_ACCOUNT_OPEN_EXCEPTION_HANDLER {
        @Override
        public HkOpenCustomerStatusEnum handleRequest(String hkCustNo,HkCustInfoDTO hkCustInfo, HkOpenAccOrderInfoDTO hkOpenAccOrderInfoDTO, HkOpenAcctDepositInfoDTO hkOpenAcctDepositInfoDTO) {
            log.info("App >>> HK_ACCOUNT_OPEN_EXCEPTION_HANDLER>> hkCustInfo:{},hkOpenAccOrderInfoDTO:{}", JSON.toJSONString(hkCustInfo),JSON.toJSONString(hkOpenAccOrderInfoDTO));
            throw new BusinessException(ExceptionCodeEnum.ACCT_ERROR);
        }
    };

    private static final Logger log = LoggerFactory.getLogger(HkOpenAppCustomerStatusEnumHandler.class);
    /**
     * 下一个枚举处理器
     */
    private Handler nextHandler;


    /**
     * @description: 初始化处理器链
     * @return
     * @author: jinqing.rao
     * @date: 2023/12/4 14:06
     * @since JDK 1.8
     */
    static {
        // 构建处理器链
        EnumSet.allOf(HkOpenAppCustomerStatusEnumHandler.class).forEach(handler -> {
            int ordinal = handler.ordinal();
            if (ordinal < HkOpenAppCustomerStatusEnumHandler.values().length - 1) {
                handler.nextHandler = HkOpenAppCustomerStatusEnumHandler.values()[ordinal + 1];
            }
        });
    }

    /**
     * @return com.howbuy.crm.cgi.extservice.common.enums.hkopen.Handler
     * @description: 获取下一个枚举类型处理器
     * @author: jinqing.rao
     * @date: 2023/12/4 14:05
     * @since JDK 1.8
     */
    protected Handler getNextHandler() {
        return nextHandler;
    }
}

