package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkopenaccount;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON>ai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 海外小程序开户,保存地址信息返回结果
 * <AUTHOR>
 * @date 2023/12/19 16:00
 * @since JDK 1.8
 */
@Setter
@Getter
@Builder
public class HkOpenAcctProCitySaveVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -655102053856476602L;

    /**
     * 详细地址中文描述
     */
    private String detailAddrCn;

    /**
     * 详细地址英文描述
     */
    private String detailAddrEn;
}
