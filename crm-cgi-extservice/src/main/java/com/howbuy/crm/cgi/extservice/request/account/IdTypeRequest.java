/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * @description: (获取证件的描述的请求实体)
 * <AUTHOR>
 * @date 2023/11/29 14:18
 * @since JDK 1.8
 */
@Data
public class IdTypeRequest extends AccountBaseRequest {

    /**
     * 证件地区码
     */
    private String idAreaCode;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 国家地区码
     */
    private String countryCode;

    /**
     * 业务Code,用于区分不同的场景查询,做特殊的过滤,不传查所有
     */
    private String bizCode;
}