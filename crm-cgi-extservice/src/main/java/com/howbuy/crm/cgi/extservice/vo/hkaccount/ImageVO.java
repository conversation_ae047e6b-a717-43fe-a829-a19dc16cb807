package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/30 10:38
 * @since JDK 1.8
 */
@Setter
@Getter
public class ImageVO  implements Serializable {

    private static final long serialVersionUID = -8256677639017140371L;

    /**
     * 图片地址
     */
    @NotBlank(message = "图片地址不能为空")
    private String url;

    /**
     * 缩略图地址
     */
    private String thumbnailUrl;


    /**
     * 文件类型
     */
    private String exampleFileFormatType;

    /**
     * 文件名称
     */
    private String fileName;

    public ImageVO() {
    }
    public ImageVO(String url, String thumbnailUrl) {
        this.url = url;
        this.thumbnailUrl = thumbnailUrl;
        this.exampleFileFormatType = url.substring(url.lastIndexOf(".") + 1);
    }
    public ImageVO(String url, String thumbnailUrl,String exampleFileFormatType) {
        this.url = url;
        this.thumbnailUrl = thumbnailUrl;
        this.exampleFileFormatType = exampleFileFormatType;
    }
    public ImageVO(String url, String thumbnailUrl,String exampleFileFormatType,String fileName) {
        this.url = url;
        this.thumbnailUrl = thumbnailUrl;
        this.exampleFileFormatType = exampleFileFormatType;
        this.fileName = fileName;
    }
}
