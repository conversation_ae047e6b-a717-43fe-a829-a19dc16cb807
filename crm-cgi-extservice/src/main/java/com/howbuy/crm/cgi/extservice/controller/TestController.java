/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.common.utils.AppEncUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/3/21 13:39
 * @since JDK 1.8
 */
@RestController("extTestController")
@RequestMapping("/test/")
public class TestController {
    @GetMapping("tt")
    public CgiResponse test(){
        TestParams p = new TestParams();
        p.setHkCustNo("1234567");
        p.setName("张三");
        p.setDescription("业务返回描述");
        p.setReturnCode("业务返回码，可为空");
        return CgiResponse.ok(p);

    }

    private static final Logger log = LoggerFactory.getLogger(TestController.class);

    /**
     * @api {GET} /ext/test/deleteCatch deleteCatch()
     * @apiVersion 1.0.0
     * @apiGroup TestController
     * @apiName 删除缓存
     * @apiParam (请求参数) {String} key
     * @apiParamExample 请求参数示例
     * key=Gr3
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"SfFXBSaI8","description":"CB1yd","timestampServer":"SPUW5"}
     */
    @GetMapping("deleteCatch")
    public CgiResponse<Body> deleteCatch(@RequestParam("key") String key){
        Object object = CacheServiceImpl.getInstance().get(key);
        log.info("deleteCatch>>> object:{}", JSON.toJSONString(object));
        CacheServiceImpl.getInstance().remove(key);
        return CgiResponse.ok(null);
    }

    @Setter
    @Getter
    public static class TestParams extends Body {
        private String name;
        private String hkCustNo;
    }
}
