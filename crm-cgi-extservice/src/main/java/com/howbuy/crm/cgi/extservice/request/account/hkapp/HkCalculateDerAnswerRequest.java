/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account.hkapp;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/7 16:10
 * @since JDK 1.8
 */
@Data
public class HkCalculateDerAnswerRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -6393660396712192511L;

    /**
     * 答案列表
     */
    private List<AnswerRequest> answerMap;


    /**
     * 问卷ID
     */
    private String examId;

}