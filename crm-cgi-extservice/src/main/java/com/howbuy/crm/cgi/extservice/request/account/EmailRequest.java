package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 获取邮箱验证码
 * @date 2023/6/7 16:24
 * @since JDK 1.8
 */
@Data
public class EmailRequest extends AccountBaseRequest {

    /**
     * 邮箱地址 	必须，明文
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "邮箱地址", isRequired = true)
    private String email;
}
