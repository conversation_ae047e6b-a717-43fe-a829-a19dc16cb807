package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.ImageVO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description: 开户提交/修改接口
 * <AUTHOR>
 * @date 2023/11/30 14:17
 * @since JDK 1.8
 */
@Setter
@Getter
public class ESignatureRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = 1310841259191694309L;

    /**
     *电子签名图片URL
     */
    @Valid
    private ImageRequest imageUrl;

    /**
     *用户条款文件路径
     */
    private String useTermsFilePath;

    /**
     * 警告声明文件路径
     */
    private String warnStatementFilePath;

    /**
     * 渠道编号  好买香港不用分的这么细，小程序/APP/PC等线上渠道开户，现计划都是传【PH2312W02】
     */
    @NotBlank(message = "渠道路径不能为空")
    private String outletCode;

    @Setter
    @Getter
    public static class ImageRequest implements Serializable {

        private static final long serialVersionUID = 1310841259191694309L;

        /**
         * 电子签名图片URL
         */
        @NotBlank(message = "图片地址不能为空")
        private String url;

        /**
         * 缩略图地址
         */
        @NotBlank(message = "缩略图地址不能为空")
        private String thumbnailUrl;

    }
}
