/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.exception.SessionTimeOutException;
import com.howbuy.crm.cgi.common.utils.WebUtil;
import com.howbuy.crm.cgi.extservice.common.session.PortraitSession;
import com.howbuy.crm.cgi.extservice.service.portrait.entrance.PortraitLoginService;
import crm.howbuy.base.utils.DesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 画像拦截器
 * @date 2024/10/29 11:17
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PortraitInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private PortraitLoginService portraitLoginService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 会话校验
        checkSession(request);
        // 一账通号解密
        String tokenId = request.getParameter(Constants.TOKEN_ID);
        if (StringUtils.isBlank(tokenId)) {
            tokenId = (String) request.getAttribute(Constants.TOKEN_ID);
        }
        String hboneNo = null;
        //post请求且是JSON提交
        if (request.getMethod().equals(HttpMethod.POST.name())) {
            if (request.getContentType().contains(MediaType.APPLICATION_JSON_VALUE)) {
                // 从body中获取hboneNo,并解密重新放到body中
                hboneNo = handlerBodyHboneNo((MyHttpServletRequestWrapper) request, tokenId);
            } else {
                // 从参数中获取hboneNo,并解密重新放到request中
                hboneNo = handlerHboneNo(request, tokenId);
            }
        } else if (request.getMethod().equals(HttpMethod.GET.name())) {
            // 从参数中获取hboneNo,并解密重新放到request中
            hboneNo = handlerHboneNo(request, tokenId);
        }
        // 校验匹配关系
        if (StringUtils.isNotEmpty(hboneNo)) {
            boolean exists = portraitLoginService.existsConsRelCache(PortraitLoginService.getUserId(), hboneNo);
            // 缓存不存在，则重新校验并保存缓存
            if (!exists) {
                portraitLoginService.checkUserIdHboneNoMatch(PortraitLoginService.getUserId(), hboneNo);
            }
        }
        return true;
    }

    /**
     * @param request
     * @return void
     * @description:(校验会话)
     * <AUTHOR>
     * @date 2025/1/6 15:36
     * @since JDK 1.8
     */
    private void checkSession(HttpServletRequest request) {
        // 会话校验
        Object loginInfo = request.getSession().getAttribute(Constants.PORTRAIT_LOGIN_SESSION_NAME);
        if (Objects.isNull(loginInfo)) {
            String custIp = WebUtil.getCustIP(request);
            log.error("loginInfo is null,user unLogin,custIp:{}", custIp);
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
        PortraitSession portraitSession;
        if(loginInfo instanceof String){
            portraitSession = JSON.parseObject((String) loginInfo, PortraitSession.class);
        } else {
            log.error("portraitSession instanceof error,user unLogin,custIp:{}", WebUtil.getCustIP(request));
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
        if (null == portraitSession) {
            log.error("portraitSession is null,user unLogin,custIp:{}", WebUtil.getCustIP(request));
            throw new SessionTimeOutException(ExceptionCodeEnum.SESSION_TIMEOUT);
        }
        request.setAttribute(Constants.PORTRAIT_USER_ID, portraitSession.getUserId());
    }

    /**
     * @param request
     * @param tokenId
     * @return void
     * @description: 处理body中的一账通号
     * <AUTHOR>
     * @date 2024/10/29 13:47
     * @since JDK 1.8
     */
    private static String handlerBodyHboneNo(MyHttpServletRequestWrapper request, String tokenId) {
        MyHttpServletRequestWrapper myHttpServletRequestWrapper = request;
        String body = myHttpServletRequestWrapper.getBody();
        log.info("PortraitInterceptor>>>handlerBodyHboneNo,请求参数 params: {}", body);
        JSONObject jsonObject = JSON.parseObject(body);
        if (null == jsonObject || StringUtils.isBlank(jsonObject.getString(Constants.HBONENO))) {
            return null;
        }
        //香港客户号解密
        String decryptHboneNo = decryptHboneNo(tokenId, jsonObject.getString(Constants.HBONENO));
        //兼容一账通号解密失败
        String hboneNo = StringUtils.isNotEmpty(decryptHboneNo) ? decryptHboneNo : jsonObject.getString(Constants.HBONENO);
        jsonObject.put(Constants.HBONENO, hboneNo);
        myHttpServletRequestWrapper.setBody(jsonObject.toJSONString());
        return hboneNo;
    }

    /**
     * @param request
     * @param tokenId
     * @return void
     * @description:处理一账通号
     * <AUTHOR>
     * @date 2024/10/29 13:17
     * @since JDK 1.8
     */
    private static String handlerHboneNo(HttpServletRequest request, String tokenId) {
        String hboneNo = request.getParameter(Constants.HBONENO);
        if (StringUtils.isBlank(hboneNo)) {
            return null;
        }
        String decryptHboneNo = decryptHboneNo(tokenId, hboneNo);
        request.setAttribute(Constants.HBONENO, decryptHboneNo);
        return decryptHboneNo;
    }

    /**
     * @param tokenId
     * @param hboneNo
     * @return java.lang.String
     * @description:解密一账通号
     * <AUTHOR>
     * @date 2024/10/29 13:16
     * @since JDK 1.8
     */
    private static String decryptHboneNo(String tokenId, String hboneNo) {
        try {
            return DesUtil.decrypt(hboneNo, Constants.HBONENO_DES_KEY);
        } catch (Exception e) {
            log.info("PortraitInterceptor>>>decryptHboneNo>>>解密失败 hboneNo:{},tokenId:{}", hboneNo, tokenId);
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "非法请求-解密失败");
        }
    }


}
