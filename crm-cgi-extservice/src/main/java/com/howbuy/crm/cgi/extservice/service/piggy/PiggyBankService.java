/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.piggy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.YesNoEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.DateUtils;
import com.howbuy.crm.cgi.common.utils.PassWordUtil;
import com.howbuy.crm.cgi.extservice.common.enums.FundValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HKFundTradeModeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyContractBizTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolCancelTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolSignTypeEnum;
import com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggySignStatusEnum;
import com.howbuy.crm.cgi.extservice.convert.piggy.PiggySignConvert;
import com.howbuy.crm.cgi.extservice.request.piggy.*;
import com.howbuy.crm.cgi.extservice.validator.handle.VerificationHandle;
import com.howbuy.crm.cgi.extservice.validator.piggy.context.PiggyVerificationContext;
import com.howbuy.crm.cgi.extservice.validator.piggy.factory.PiggyValidatorFactory;
import com.howbuy.crm.cgi.extservice.validator.piggy.factory.PiggyVerificationContextFactory;
import com.howbuy.crm.cgi.extservice.vo.piggy.*;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.CustContractSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.DigitalSignSignDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkRedeemFundSubmitInfoDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyFundHoldingsDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductContractDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.ProductContractFileDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.HkPiggyAgreementDealResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggyAgreementSignDetailDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.piggy.PiggySignSubmitRequestDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.CustContractSignOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.HkFundOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.FundHoldingsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryFormDocCfgOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryProductContractOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QuerySupportedPiggyBuyFundListOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryTradeDayOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkPiggyBankOuterService;
import com.howbuy.dtms.common.enums.TradeChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/7/16 17:37
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PiggyBankService extends AbstractPiggyService {

    private static final Integer PIGGY_CHANGE_EXPIRE_DATE = 3600;

    private static final String FUND_CODE = "fundCode";

    private static final String SIGN_TIME = "signTime";

    private static final String SIGN_DATE = "signDate";

    private static final String AGREED = "agreed";

    @Value("${app.purchase.fund.transfer.bank.info}")
    private String transferBankInfoJson;

    @Resource
    private QueryProductContractOuterService queryProductContractOuterService;

    @Resource
    private QueryFormDocCfgOuterService queryFormDocCfgOuterService;

    @Resource
    private HkPiggyBankOuterService hkPiggyBankOuterService;

    @Resource
    private PiggyVerificationContextFactory piggyVerificationContextFactory;

    @Resource
    private PiggyValidatorFactory piggyValidatorFactory;

    @Resource
    private FundHoldingsOuterService fundHoldingsOuterService;

    @Resource
    private CustContractSignOuterService custContractSignOuterService;

    @Resource
    protected HkFundOuterService hkFundOuterService;

    @Resource
    private CacheService cacheService;

    @Resource
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Resource
    private QuerySupportedPiggyBuyFundListOuterService querySupportedPiggyBuyFundListOuterService;


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO
     * @description: 海外储蓄罐检验
     * @author: jinqing.rao
     * @date: 2024/7/26 18:07
     * @since JDK 1.8
     */
    public PiggyBankVerificationVO piggySignVerify(PiggyBnakVerificationRequest request) {
        //根据不同的校验类型,构建校验执行器需要的上下文
        PiggyVerificationContext context = piggyVerificationContextFactory.createContext(new PiggyContextRequest(request.getHkCustNo(), request.getBizType()));
        //创建检验管理器
        VerificationHandle<PiggyBankVerificationVO> piggyVerificationDirector = piggyValidatorFactory.createPiggyVerificationDirector(context);
        //执行校验器
        return piggyVerificationDirector.verification();
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignContractVO
     * @description: 海外签约合同列表查询接口
     * 1.获取储蓄罐底层基金代码，暂时不支持多个
     * 2.获取储蓄罐基金合同文件信息
     * 3.获取储蓄罐基金表单合同文件信息
     * 4.封装数据 排序 表单合同>产品合同
     * @author: jinqing.rao
     * @date: 2024/7/17 16:26
     * @since JDK 1.8
     */
    public PiggySignContractVO queryPiggySignContractList(PiggySignContractRequest request) {
        // 获取当前海外储蓄罐底层基金信息
        FundBasicInfoDTO fundBasicInfoDTO = querySupportedPiggyBuyFundListOuterService.querySupportBuyPiggyBuyFundList(DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
        //获取基金的合同文件信息
        ProductContractDTO productContractDTO = queryProductContractOuterService.queryProductContractByFundCode(fundBasicInfoDTO.getFundCode(), null);
        // 获取基金的表单配置合同
        String fileType = null;
        if (PiggyContractBizTypeEnum.PIGGY_SIGN.getCode().equals(request.getBizType())) {
            fileType = PiggyContractBizTypeEnum.getPiggySignFormContract();
        } else if (PiggyContractBizTypeEnum.PIGGY_CHANGE.getCode().equals(request.getBizType())) {
            fileType = PiggyContractBizTypeEnum.getPiggyChangeFormContract();
        }
        if(StringUtils.isBlank(fileType)){
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "合同业务类型错误");
        }
        List<ProductContractFileDTO> productContractFileDTOS = queryFormDocCfgOuterService.queryFormDocCfg(fileType, null);
        //封装数据
        return PiggySignConvert.getPiggySignContractVO(productContractDTO, fundBasicInfoDTO.getFundCode(), productContractFileDTOS);
    }

    /**
     * @param request
     * @return void
     * @description: 海外签约提交接口
     * 1.客户状态校验 a.客户账户状态校验 b.开户状态校验 c.是佛超过基金年龄限制校验 d.交易账号是否激活 e.客户分测等级和基金底层风险等级是否匹配
     * 2.验证交易密码
     * 3.提交账户中心
     * @author: jinqing.rao
     * @date: 2024/7/18 10:54
     * @since JDK 1.8
     */
    public void submitPiggySign(PiggySignSubmitRequest request) {
        PiggyContextRequest piggyContextRequest = new PiggyContextRequest(request.getHkCustNo(), FundValidatorTypeEnum.PIGGY_SIGN_VALIDATOR.getCode());
        //根据不同的校验类型,构建校验执行器需要的上下文
        PiggyVerificationContext context = submitVariationByType(piggyContextRequest);
        // 验证交易密码
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        // 提交账户中心
        PiggySignSubmitRequestDTO piggySignSubmitRequestDTO = PiggySignConvert.toPiggySignSubmitRequestDTO(request, context.getFundCodeList().get(0), PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_ONLINE.getCode());
        hkPiggyBankOuterService.submitPiggySign(piggySignSubmitRequestDTO);
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignResultVO
     * @description: 查询提交结果接口
     * @author: jinqing.rao
     * @date: 2024/7/29 10:56
     * @since JDK 1.8
     */
    public PiggySignResultVO querySubmitResult(PiggyDepositVoucherDetailRequest request) {
        //暂时没有其他逻辑,只需要返回汇款银行卡信息即可
        return PiggySignConvert.toPiggySignResultVO(request.getHkCustNo(), transferBankInfoJson);
    }

    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggySignContractRecordVO
     * @description: 获取海外储蓄罐签约记录查询接口
     * @author: jinqing.rao
     * @date: 2024/7/22 10:40
     * @since JDK 1.8
     */
    public PiggySignContractRecordVO queryPiggySignContractRecord(PiggyBaseParamRequest request) {
        List<String> channelCodes = Arrays.asList(TradeChannelEnum.APP.getCode(), TradeChannelEnum.H5.getCode(), TradeChannelEnum.WAP.getCode());
        HkPiggyAgreementDealResponseDTO dealResponseDTO = hkPiggyBankOuterService.queryPiggySignContractRecord(request.getHkCustNo(), channelCodes, 1, 200);
        if (null == dealResponseDTO || CollectionUtils.isEmpty(dealResponseDTO.getHkPiggyDealDTOList())) {
            return new PiggySignContractRecordVO();
        }
        return PiggySignConvert.toPiggySignContractRecordVO(dealResponseDTO);
    }


    /**
     * @param request
     * @return com.howbuy.crm.cgi.extservice.vo.piggy.PiggyProtocolVariationResultVO
     * @description: 查询海外储蓄罐协议变动结果
     * @author: jinqing.rao
     * @date: 2024/7/22 17:22
     * @since JDK 1.8
     */
    public PiggyProtocolVariationResultVO queryProtocolVariationResult(PiggyProtocolVariationResultRequest request) {
        //从缓存获取用户变更前的海外储蓄罐基金持仓信息,如果没有持仓份额,直接返回
        String key = getPiggyProtocolKeyString(request.getHkCustNo());
        Object result = cacheService.get(key);
        if (null == result) {
            return new PiggyProtocolVariationResultVO();
        }
        JSONObject jsonObject = JSONObject.parseObject(result.toString());
        String fundCode = jsonObject.getString(FUND_CODE);
        String signTime = jsonObject.getString(SIGN_TIME);
        String signDate = jsonObject.getString(SIGN_DATE);
        String agreed = jsonObject.getString(AGREED);
        if (!request.getAgreed().equals(agreed)) {
            throw new BusinessException(ExceptionCodeEnum.PIGGY_AGREED_ERROR);
        }
        //封装响应参数
        return getPiggyProtocolVariationResultVO(request, fundCode, signDate, signTime);
    }

    private PiggyProtocolVariationResultVO getPiggyProtocolVariationResultVO(PiggyProtocolVariationResultRequest request, String fundCode, String signDate, String signTime) {
        PiggyProtocolVariationResultVO resultVO = new PiggyProtocolVariationResultVO();
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(fundCode);

        List<PiggyProtocolVariationResultVO.PiggyProtocolVariationResult> arrayList = new ArrayList<>();
        //储蓄罐变更的时间
        PiggyProtocolVariationResultVO.PiggyProtocolVariationResult changeDatePoint = new PiggyProtocolVariationResultVO.PiggyProtocolVariationResult();
        changeDatePoint.setKeyPointDt(signDate);
        changeDatePoint.setKeyPointTm(signTime);
        arrayList.add(changeDatePoint);
        // 老基金 预计赎回时间+ 老基金名称
        PiggyProtocolVariationResultVO.PiggyProtocolVariationResult expectedRedemptionTime = new PiggyProtocolVariationResultVO.PiggyProtocolVariationResult();
        String paymentEndTm = fundBasicInfoDTO.getPaymentEndTm();
        if (StringUtils.isBlank(paymentEndTm)) {
            throw new BusinessException(ExceptionCodeEnum.FUND_PAYMENT_END_TM_ERROR);
        }
        if (DateUtils.isEarlier(paymentEndTm, signTime)) {
            // T + 1 交易日
            String tradeDt = queryTradeDayOuterService.queryTradeDt(signDate, 1);
            expectedRedemptionTime.setKeyPointDt(tradeDt);
            expectedRedemptionTime.setKeyPointFundName(fundBasicInfoDTO.getFundName());
        } else {
            expectedRedemptionTime.setKeyPointDt(signDate);
        }
        arrayList.add(expectedRedemptionTime);
        if (YesNoEnum.YES.getCode().equals(request.getAgreed())) {
            //获取当前用户签署的
            PiggyAgreementSignDetailDTO piggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
            FundBasicInfoDTO newFundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(piggyAgreement.getPiggyFundCode());
            PiggyProtocolVariationResultVO.PiggyProtocolVariationResult newFundName = new PiggyProtocolVariationResultVO.PiggyProtocolVariationResult();
            newFundName.setKeyPointFundName(newFundBasicInfoDTO.getFundName());
            arrayList.add(newFundName);
        }
        resultVO.setKeyPointInfoList(arrayList);
        return resultVO;
    }

    /**
     * @param request 请求参数
     * @return void
     * @description: 海外储蓄罐协议变动提交
     * <p>
     * 判断该储蓄罐用户是否有持仓信息
     * 有则发起自动赎回下单,赎回下单成功以后提交账户中心,发起储蓄罐协议变更
     * @author: jinqing.rao
     * @date: 2024/7/23 14:20
     * @since JDK 1.8
     */
    public void submitProtocolVariation(PiggySignSubmitRequest request) {
        boolean agreed = !YesNoEnum.YES.getCode().equals(request.getAgreed());
        String bizType = FundValidatorTypeEnum.PIGGY_CHANGE_VALIDATOR.getCode();
        if(agreed){
            bizType = FundValidatorTypeEnum.PIGGY_CHANGE_CLOSE_VALIDATOR.getCode();
        }
        PiggyVerificationContext context = submitVariationByType(new PiggyContextRequest(request.getHkCustNo(),bizType));
        //获取当前基金的持仓Code
        String fundCode = context.getSignFundCode();
        HkCustInfoDTO hkCustInfoDTO = context.getHkCustInfoDTO();
        String newFundCode = context.getFundCodeList().get(0);
        // 验证交易密码
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        //判断是否存在持仓信息
        PiggyFundHoldingsDTO piggyFundHoldingsDTO = fundHoldingsOuterService.queryHoldVolByFundCodeAndHkCustNo(fundCode, hkCustInfoDTO.getHkCustNo());
        BigDecimal usableVol = piggyFundHoldingsDTO.getAvailableVol();
        boolean needAutoRedeem = BigDecimal.ZERO.compareTo(piggyFundHoldingsDTO.getTotalVol()) < 0;
        // 储蓄罐老基金总份额大于0,发起自动赎回下单
        if (needAutoRedeem) {
            //获取基金的合同文件信息
            ProductContractDTO productContractDTO = queryProductContractOuterService.queryProductContract(fundCode, "1");
            //赎回文件签约,获取订单号
            DigitalSignSignDTO digitalSignSignDTO = PiggySignConvert.toRedeemFundContractSignDTO(request.getHkCustNo(), fundCode,
                    YesNoEnum.YES.getCode(), HKFundTradeModeEnum.HK_REDEEM_FUND.getCode(), productContractDTO);
            CustContractSignDTO responseDTO = custContractSignOuterService.fundSign(digitalSignSignDTO);
            //提交赎回的数据
            HkRedeemFundSubmitInfoDTO hkRedeemFundSubmitInfoDTO = PiggySignConvert.toHkRedeemFundSubmitInfoDTO(request, responseDTO.getDealNo(), fundCode, usableVol);
            hkFundOuterService.redeemPiggyOrderSubmit(hkRedeemFundSubmitInfoDTO);
        }
        //不同意底层变更,直接终止当前的储蓄罐协议
        if (agreed) {
            hkPiggyBankOuterService.closePiggySign(hkCustInfoDTO.getHkCustNo(), PiggyProtocolCancelTypeEnum.AGREEMENT_CANCEL_TYPE_FUND_CHANGE_DISAGREE.getCode(), DateUtils.getCurrentDate("yyyyMMdd"),PassWordUtil.encrypt(request.getTxPassword()),newFundCode);
        } else {
            // 提交账户中心
            PiggySignSubmitRequestDTO piggySignSubmitRequestDTO = PiggySignConvert.toPiggySignSubmitRequestDTO(request, newFundCode, PiggyProtocolSignTypeEnum.AGREEMENT_SIGN_TYPE_FUND_CHANGE.getCode());
            hkPiggyBankOuterService.submitPiggySign(piggySignSubmitRequestDTO);
        }
        //提交成功,发生了老基金赎回,缓存基金信息,结果页需要
        if (needAutoRedeem){
            // 数据落缓存,详情结果页需要数据
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(FUND_CODE, fundCode);
            jsonObject.put(AGREED, request.getAgreed());
            jsonObject.put(SIGN_DATE, DateUtils.getCurrentDate(DateUtils.YYYYMMDD));
            jsonObject.put(SIGN_TIME, DateUtils.getCurrentTime(DateUtils.HHMMSS));
            String key = getPiggyProtocolKeyString(request.getHkCustNo());
            cacheService.put(PIGGY_CHANGE_EXPIRE_DATE, key, jsonObject.toJSONString());
        }
    }

    /**
     * 底层储蓄罐协议变更结果页缓存,因为储蓄罐底层基金变换后,结果页不知道老的储蓄罐基金
     *
     * @param hkCustNo
     * @return
     */
    private static String getPiggyProtocolKeyString(String hkCustNo) {
        return CacheKeyPrefix.HK_SAVING_BUCKET_CHANGE_FUND_KEY_PREFIX + hkCustNo;
    }

    private PiggyVerificationContext submitVariationByType(PiggyContextRequest request) {
        //根据不同的校验类型,构建校验执行器需要的上下文
        PiggyVerificationContext context = piggyVerificationContextFactory.createContext(request);
        //创建检验管理器
        VerificationHandle<PiggyBankVerificationVO> piggyVerificationDirector = piggyValidatorFactory.createPiggyVerificationDirector(context);
        //执行校验器
        PiggyBankVerificationVO verification = piggyVerificationDirector.verification();
        if (null != verification && !HkFundVerificationStatusEnum.NORMAL.getCode().equals(verification.getVerfiyState())) {
            ExceptionCodeEnum enumDisPlayByCode = HkFundVerificationStatusEnum.getEnumDisPlayByCode(verification.getVerfiyState());
            log.info("submitVariationByType>>> hkCustNo : {},result:{}", request.getHkCustNo(), JSON.toJSONString(verification));
            if (null == enumDisPlayByCode) {
                throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "检验不通过,提交失败");
            }
            throw new BusinessException(enumDisPlayByCode.getCode(), enumDisPlayByCode.getDescription());
        }
        return context;
    }

    /**
     * @param request
     * @return void
     * @description: 海外储蓄罐关闭接口
     * @author: jinqing.rao
     * @date: 2024/7/29 13:22
     * @since JDK 1.8
     */
    public void closePiggySign(PiggySignCloseRequest request) {
        PiggyContextRequest piggyContextRequest = new PiggyContextRequest(request.getHkCustNo(),FundValidatorTypeEnum.PIGGY_SIGN_LIST_CLOSE_VALIDATOR.getCode());
        //根据不同的校验类型,构建校验执行器需要的上下文
        PiggyVerificationContext context = submitVariationByType(piggyContextRequest);
        // 验证交易密码
        hkCustInfoOuterService.validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        // 提交账户中心
        hkPiggyBankOuterService.closePiggySign(context.getHkCustInfoDTO().getHkCustNo(), PiggyProtocolCancelTypeEnum.AGREEMENT_CANCEL_TYPE_ONLINE.getCode(), DateUtils.getCurrentDate("yyyyMMdd"), PassWordUtil.encrypt(request.getTxPassword()),null);
    }

    /**
     * @param request
     * @return void
     * @description ： 查询用户的签约状态
     * @author: jinqing.rao
     * @date: 2024/9/30 9:43
     * @since JDK 1.8
     */
    public PiggyCustSignStatusVO queryCustSignStatus(PiggyBaseParamRequest request) {
        // 获取海外储蓄罐协议签署状态
        String piggySignStatus = YesNoEnum.NO.getCode();
        PiggyAgreementSignDetailDTO hkCustPiggyAgreement = hkPiggyBankOuterService.getHkCustPiggyAgreement(request.getHkCustNo());
        if (PiggySignStatusEnum.SIGNED.getCode().equals(hkCustPiggyAgreement.getAgreementState())) {
            piggySignStatus = YesNoEnum.YES.getCode();
        }
        return PiggyCustSignStatusVO.builder().signStatus(piggySignStatus).build();
    }
}
