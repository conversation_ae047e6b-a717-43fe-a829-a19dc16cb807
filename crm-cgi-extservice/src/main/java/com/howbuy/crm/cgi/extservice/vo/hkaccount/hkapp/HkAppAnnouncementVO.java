package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: App公告栏返回对象
 * <AUTHOR>
 * @date 2024/2/22 16:23
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkAppAnnouncementVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = 4552767422863063914L;

    private List<HkAppAnnouncementInfo> announcementList;

    @Setter
    @Getter
    public static class HkAppAnnouncementInfo implements Serializable {
        private static final long serialVersionUID = 4552767422863063914L;
        /**
         *  产品ID
         */
        private String productId;
        /**
         * 位置
         */
        private String position;
        /**
         * 重要程度 1: 一次性关闭 2: 重复显示 3: 不可关闭
         */
        private String important;
        /**
         * 顺序
         */
        private String seq;
        /**
         * 说明
         */
        private String desc;
        /**
         * 链接
         */
        private String link;
    }

}
