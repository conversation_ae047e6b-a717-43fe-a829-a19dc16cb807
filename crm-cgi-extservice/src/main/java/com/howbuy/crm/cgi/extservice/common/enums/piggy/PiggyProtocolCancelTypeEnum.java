package com.howbuy.crm.cgi.extservice.common.enums.piggy;

import org.apache.commons.lang3.StringUtils;

public enum PiggyProtocolCancelTypeEnum {

    // 协议终止方式 1-线下自主申请关闭 2-线上自主申请关闭 3-底层基金更换未同意 4-底层基金更换不同意
    AGREEMENT_CANCEL_TYPE_OFFLINE("1", "线下自主申请关闭"),
    AGREEMENT_CANCEL_TYPE_ONLINE("2", "线上自主申请关闭"),
    AGREEMENT_CANCEL_TYPE_FUND_CHANGE_NOT_AGREE("3", "底层基金更换未同意"),
    AGREEMENT_CANCEL_TYPE_FUND_CHANGE_DISAGREE("4", "底层基金更换不同意");

    private final String code;

    private final String desc;

    PiggyProtocolCancelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @description: 通过Code回去对应的枚举类型
     * @param agreementCancelType
     * @return com.howbuy.crm.cgi.extservice.common.enums.piggy.PiggyProtocolCancelTypeEnum
     * @author: jinqing.rao
     * @date: 2024/9/12 15:51
     * @since JDK 1.8
     */
    public static PiggyProtocolCancelTypeEnum getEnumByCode(String agreementCancelType) {
        if (StringUtils.isBlank(agreementCancelType)) {
            return null;
        }
        for (PiggyProtocolCancelTypeEnum piggyProtocolCancelTypeEnum : PiggyProtocolCancelTypeEnum.values()) {
            if (piggyProtocolCancelTypeEnum.getCode().equals(agreementCancelType)) {
                return piggyProtocolCancelTypeEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
