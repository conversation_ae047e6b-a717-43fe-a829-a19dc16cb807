package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 页面过滤选择时间类型
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum TimeTypeEnum {

    ALL("0", "全部"),
    ONEMONTH("1", "近一月"),
    THREEMONTH("2", "近三月"),
    SIXMONTH("3", "近六月"),
    ONEYEAR("4", "近一年");

    private final String key;
    private final String desc;

    public static TimeTypeEnum getTimeTypeEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        TimeTypeEnum timeTypeEnum = getTimeTypeEnum(code);
        return timeTypeEnum == null ? null : timeTypeEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    TimeTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
