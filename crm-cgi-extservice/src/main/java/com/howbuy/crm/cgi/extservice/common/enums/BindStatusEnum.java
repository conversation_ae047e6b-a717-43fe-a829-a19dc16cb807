package com.howbuy.crm.cgi.extservice.common.enums;

/**
 * @description:(绑定状态枚举类)
 * @return
 * @author: xufanchao
 * @date: 2023/12/14 10:14
 * @since JDK 1.8
 */
public enum BindStatusEnum {

    /**
     * 绑定失败
     */
    FAIL("00", "失败"),
    /**
     * 完成
     */
    DONE("01", "完成"),
    /**
     * 验证香港手机号
     */
    VERIFY_HK_MOBILE("06", "验证香港手机号"),
    /**
     * 验证香港邮箱
     */
    VERIFY_HK_EMAIL("56", "验证香港邮箱"),
    ;
    private String code;
    private String desc;

    BindStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static BindStatusEnum getBindStatusEnum(String key) {
        for (BindStatusEnum bindStatusEnum : BindStatusEnum.values()) {
            if (bindStatusEnum.getCode().equals(key)) {
                return bindStatusEnum;
            }
        }
        return null;
    }
}
