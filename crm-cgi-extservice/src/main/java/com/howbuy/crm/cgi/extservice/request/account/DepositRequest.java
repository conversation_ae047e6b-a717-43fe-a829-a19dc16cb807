/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.RemitImageVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/11/30 17:27
 * @since JDK 1.8
 */
@Data
public class DepositRequest extends AccountBaseRequest {

    /**
     * 资金账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "资金账号", isRequired = true)
    private String cpAcctNo;

    /**
     * 汇款币种
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "汇款币种", isRequired = true)
    private String remitCurCode;

    /**
     * 金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "金额", isRequired = true)
    private String amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 汇款图片URL列表
     */
    private List<RemitImageVO> remitImageUrls;
}