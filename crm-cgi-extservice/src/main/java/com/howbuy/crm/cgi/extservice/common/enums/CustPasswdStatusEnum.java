package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: 客户密码状态枚举
 * @date 2023/6/8 11:31
 * @since JDK 1.8
 */
public enum CustPasswdStatusEnum {

    NORMAL("0", "正常"),
    RESET("1", "重置"),
    UNSET("2", "未设置");

    private final String key;
    private final String desc;

    public static CustPasswdStatusEnum getCustPasswdStatusEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        CustPasswdStatusEnum custPasswdStatusEnum = getCustPasswdStatusEnum(code);
        return custPasswdStatusEnum == null ? null : custPasswdStatusEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    CustPasswdStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
