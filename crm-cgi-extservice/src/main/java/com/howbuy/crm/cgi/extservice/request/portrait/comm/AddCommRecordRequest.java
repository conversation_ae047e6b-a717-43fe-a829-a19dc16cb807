/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.portrait.comm;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/8 17:39
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddCommRecordRequest extends BodyRequest {
    /**
     * 投顾编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投顾编号", isRequired = true)
    private String conscode;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 沟通日期 yyyyMMDD
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "沟通日期 yyyyMMDD", isRequired = true)
    private String commDate;

    /**
     * 沟通时间 hhmmss
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "沟通时间 hhmmss", isRequired = true)
    private String commTime;

    /**
     * 沟通方式
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "沟通方式", isRequired = true)
    private String commMode;

    /**
     * 沟通内容
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "沟通内容", isRequired = true)
    private String commContext;
}
