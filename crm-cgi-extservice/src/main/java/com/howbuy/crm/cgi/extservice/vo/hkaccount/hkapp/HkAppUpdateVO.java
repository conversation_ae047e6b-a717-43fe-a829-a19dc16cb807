package com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp;

/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: App更新接口
 * <AUTHOR>
 * @date 2024/2/29 15:42
 * @since JDK 1.8
 */
@Setter
@Getter
@Builder
public class HkAppUpdateVO extends AccountBaseVO implements Serializable {

    private static final long serialVersionUID = -6604891315125262174L;

    /**
     * 文件大小(单位字节)
     */
    private String fileSize;


    /**
     * 更新地址
     */
    private String updateUrl;

    /**
     * 更新描述
     */
    private String updateDesc;

    /**
     *版本号
     */
    private String versionNum;

    /**
     * 版本是否需要更新(0:通知更新 1:强制更新 2:不通知 3:维护通知 4:检测更新)
     */
    private String versionNeedUpdate;

    /**
     * 外区指引
     */
    private String foreignGuide;
}
