/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.portrait.remindrecord;

import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.extservice.common.enums.portrait.MessageRemindTypeEnum;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitHistoryMsgRemindRequest;
import com.howbuy.crm.cgi.extservice.request.portrait.PortraitMsgRemindRequest;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitHistoryMsgRemindVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.material.PortraitMsgShowRemindVO;
import com.howbuy.crm.cgi.manager.outerservice.portrait.remindrecord.PortraitRemindRecordOuterService;
import com.howbuy.crm.portrait.client.domain.dto.remindrecord.CmPortraitRemindRecordDTO;
import com.howbuy.crm.portrait.client.domain.dto.remindrecord.CmPortraitShowRemindRecordDTO;
import com.howbuy.crm.portrait.client.domain.response.remindrecord.QueryPortraitRemindRecordResponse;
import com.howbuy.crm.portrait.client.domain.response.remindrecord.QueryPortraitShowRemindRecordResponse;
import com.howbuy.crm.portrait.client.enums.RemindTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 客户画像-消息提醒记录服务
 * @Date 2024/10/16 09:38
 */
@Slf4j
@Service
public class PortraitRemindRecordService {

    @Resource
    private PortraitRemindRecordOuterService portraitRemindRecordOuterService;

    /**
     * 消息提醒分享图片地址
     */
    @Value("${portrait.message.remind.share.imgurl}")
    private String portraitMessageRemindShareImgurl;

    /**
     * 查询可展示的消息提醒记录列表
     *
     * @param request 请求参数
     * @return PortraitMsgShowRemindVO
     */
    public PortraitMsgShowRemindVO getPortraitMsgShowRemindRecordList(PortraitMsgRemindRequest request) {
        PortraitMsgShowRemindVO vo = new PortraitMsgShowRemindVO();
        List<PortraitMsgShowRemindVO.MsgRemind> msgList = new ArrayList<>();
        vo.setMsgList(msgList);
        QueryPortraitShowRemindRecordResponse response = portraitRemindRecordOuterService.getShowRemindRecordList(request.getHboneNo(), request.getConscode());
        if (null == response || CollectionUtils.isEmpty(response.getRemindList())) {
            return vo;
        }

        StringBuilder sb = new StringBuilder();
        response.getRemindList().stream()
                // 按照时间倒序
                .sorted(Comparator.comparing(CmPortraitShowRemindRecordDTO::getCrateTime).reversed())
                .forEach(v -> {
                    PortraitMsgShowRemindVO.MsgRemind remind = new PortraitMsgShowRemindVO.MsgRemind();
                    remind.setMaterialTitle(v.getMaterialTitle());
                    remind.setRemindContent(v.getRemindContent());
                    remind.setRemindType(getRemindType(v.getRemindType()));
                    remind.setRemindName(getRemindName(v.getRemindType()));
                    remind.setLink(v.getLink());
                    remind.setVbId(v.getVbId());
                    remind.setCrateTime(v.getCrateTime());
                    remind.setImgUrl(StringUtil.isBlank(v.getMaterialImg()) ? portraitMessageRemindShareImgurl : v.getMaterialImg());
                    remind.setRemindDesc(v.getMaterialDesc());
                    remind.setFundCode(v.getFundCode());
                    remind.setMaterialId(v.getMaterialId());
                    remind.setMaterialSendType(v.getMaterialSendType());
                    sb.append(System.lineSeparator()).append(remind.getRemindContent());
                    msgList.add(remind);
                });
        log.info("查询可展示的消息提醒记录列表，remindMessage:{}", sb);
        return vo;
    }

    private String getRemindType(String remindType) {
        MessageRemindTypeEnum remindTypeEnum = MessageRemindTypeEnum.getByType(remindType);
        if (null != remindTypeEnum) {
            return remindTypeEnum.getType();
        }

        RemindTypeEnum rtEnum = RemindTypeEnum.getByCode(remindType);
        if (null != rtEnum) {
            return MessageRemindTypeEnum.Custom.getType();
        }
        return null;
    }

    private String getRemindName(String remindType) {
        MessageRemindTypeEnum remindTypeEnum = MessageRemindTypeEnum.getByType(remindType);
        if (null != remindTypeEnum) {
            return remindTypeEnum.getName();
        }

        RemindTypeEnum rtEnum = RemindTypeEnum.getByCode(remindType);
        if (null != rtEnum) {
            return rtEnum.getName();
        }
        return null;
    }

    /**
     * 分页查询历史消息提醒记录列表
     *
     * @param request 请求参数
     * @return PortraitHistoryMsgRemindVO
     */
    public PortraitHistoryMsgRemindVO getPortraitHistoryRemindRecordPageList(PortraitHistoryMsgRemindRequest request) {
        PortraitHistoryMsgRemindVO vo = new PortraitHistoryMsgRemindVO();
        // 查询范围 近三个月
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -3);
        Date dateThreeMonthsAgo = calendar.getTime();
        String startDate = DateUtil.date2String(dateThreeMonthsAgo, DateUtil.SAMPLE_DATEPATTERN) + Constants.DATE_START_TIME_STR;
        QueryPortraitRemindRecordResponse response = portraitRemindRecordOuterService.getRemindRecordPageList(request.getHboneNo(), request.getConscode(), startDate, null, request.getRemindTypes(), request.getPageNo(), request.getPageSize());
        if (null == response || CollectionUtils.isEmpty(response.getDataList())) {
            return vo;
        }

        String total = response.getTotal();
        List<CmPortraitRemindRecordDTO> dataList = response.getDataList();
        List<PortraitHistoryMsgRemindVO.DateRemindMsg> dateReminds = new ArrayList<>();
        List<PortraitHistoryMsgRemindVO.DateRemindMsg> finalDateReminds = dateReminds;
        dataList.stream()
                .collect(Collectors.groupingBy(v -> DateUtil.date2String(v.getCreateTimestamp(), DateUtil.SAMPLE_DATEPATTERN)))
                .forEach((key, value) -> {
                    PortraitHistoryMsgRemindVO.DateRemindMsg dateRemindMsg = new PortraitHistoryMsgRemindVO.DateRemindMsg();
                    dateRemindMsg.setDate(key);
                    List<PortraitHistoryMsgRemindVO.DateRemindMsg.MsgRemind> remindList = value.stream()
                            .map(this::convertMsgRemindVO)
                            .collect(Collectors.toList());
                    dateRemindMsg.setMsgList(remindList);
                    finalDateReminds.add(dateRemindMsg);
                });

        // 按照日期降序
        dateReminds = dateReminds.stream()
                .sorted(Comparator.comparing(PortraitHistoryMsgRemindVO.DateRemindMsg::getDate).reversed())
                .collect(Collectors.toList());
        vo.setTotal(total);
        vo.setDataList(dateReminds);
        return vo;
    }

    /**
     * 历史提醒消息VO对象转换
     *
     * @param dto 消息提醒DTO
     * @return 消息提醒VO
     */
    private PortraitHistoryMsgRemindVO.DateRemindMsg.MsgRemind convertMsgRemindVO(CmPortraitRemindRecordDTO dto) {
        PortraitHistoryMsgRemindVO.DateRemindMsg.MsgRemind remind = new PortraitHistoryMsgRemindVO.DateRemindMsg.MsgRemind();
        remind.setMaterialTitle(dto.getTitle());
        remind.setRemindContent(dto.getRemindContent());
        MessageRemindTypeEnum remindTypeEnum = MessageRemindTypeEnum.getByType(dto.getRemindType());
        remind.setRemindType(null != remindTypeEnum ? remindTypeEnum.getType() : null);
        remind.setRemindName(null != remindTypeEnum ? remindTypeEnum.getName() : null);
        remind.setLink(dto.getLink());
        remind.setVbId(dto.getVbId());
        remind.setImgUrl(StringUtil.isBlank(dto.getMaterialImg()) ? portraitMessageRemindShareImgurl : dto.getMaterialImg());
        remind.setRemindDesc(dto.getMaterialDesc());
        remind.setCrateTime(DateUtil.date2String(dto.getCreateTimestamp(), DateUtil.DEFAULT_DATESFM));
        remind.setFundCode(dto.getFundCode());
        remind.setMaterialSendType(dto.getMaterialSendType());
        remind.setMaterialId(dto.getMaterialId());
        return remind;
    }
}
