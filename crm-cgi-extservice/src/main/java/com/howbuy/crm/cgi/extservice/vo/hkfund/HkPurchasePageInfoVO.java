package com.howbuy.crm.cgi.extservice.vo.hkfund;

/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 购买页-基金信息查询
 * <AUTHOR>
 * @date 2024/4/9 14:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkPurchasePageInfoVO extends Body implements Serializable {

    private static final long serialVersionUID = -3537351385638441878L;

    /**
     * 手机区号
     */
    private String mobileAreaCode;

    /**
     * 手机号掩码
     */
    private String mobileMask;

    /**
     * 邮箱掩码
     */
    private String emailMask;

    /**
     * 手机号摘要
     */
    private String mobileDigest;

    /**
     * 邮箱地址密文
     */
    private String emailDigest;

    /**
     * 购买页基金信息
     */
    private FundInfoVO buyFundInfoVO;

    /**
     * 预约购买信息
     */
    private PrebookBuyInfoVO prebookBuyInfoVO;

    /**
     *  支付方式信息
     */
    private PayMethodInfoVO payMethodInfoVO;

    /**
     *  银行卡列表
     */
    private List<BuyFundBankCardVO> bankCardList;


    @Setter
    @Getter
    public static class FundInfoVO implements Serializable {

        private static final long serialVersionUID = -3204781420812123623L;

        /**
         * 基金简称
         */
        private String fundShortName;

        /**
         *基金英文名称
         */
        private String fundEnName;

        /**
         * 1:是 0:否
         */
        private String supportPrebook;

        /**
         * 开放开始日期  yyyymmdd
         */
        private String openStartDate;

        /**
         * 基金分类
         * 1-公募、2-私募、9-其他
         */
        private String fundCategory;

        /**
         * 开发结束日期
         */
        private String openEndDate;

        /**
         * 打款截止日期
         */
        private String payEndDate;

        /**
         * 打款截止时间
         */
        private String payEndTime;

        /**
         * 交易日期
         */
        private String tradeDate;

        /**
         * 当前日期
         */
        private String currentDate;

        /**
         * 费用计算方式 0-外扣法、1-内扣法
         */
        private String feeCalMode;

        /**
         * 币种
         */
        private String currencyCode;

        /**
         * 币种描述
         */
        private String currencyDesc;

        /**
         * 最小申购金额
         */
        private String minAppAmt;
        /**
         * 最大申购金额
         */
        private String maxAppAmt;

        /**
         * 级差
         */
        private String differential;
        /**
         * 分次Call产品 1-是 0-否
         */
        private String gradationCall;

        /**
         * 首次意向金比例
         */
        private String firstIntentionRatio;

    }

    @Setter
    @Getter
     public static class PrebookBuyInfoVO implements Serializable {
         private static final long serialVersionUID = -5416567178557779695L;

         /**
          * 预约支付类型 1-电汇、2-支票、3-海外储蓄罐
          */
         private String prebookPayMethod;

         /**
          * 预约申请金额
          */
         private String prebookAppAmt;

         /**
          * 预约折扣率
          */
         private String prebookDiscountRate;

         /**
          * 预约单号
          */
         private String prebookDealNo;


        /**
         * 预约认缴金额
         */
        private String prebookSubAmt;


        /**
         * 预约实缴金额
         */
        private String prebookPaidAmt;

     }
     @Setter
     @Getter
     public static class PayMethodInfoVO implements Serializable {
         private static final long serialVersionUID = -5416567178557779695L;

         /**
          *    否支持储蓄罐支付  0-不支持 1-支持
          */
         private String supportCxgPay;

         /**
          *    是否签约储蓄罐  0-未签署 1-签署
          */
         private String hasSignCxg;

         /**
          *  1 未生效  2 已生效  3 已过期
          */
         private String agreementValidStatus;

         /**
          *  储蓄罐下单结束时间  hhmmss
          */
         private String cxgOrderEndTime;

         /**
          *  当前时间的时分秒是否小于等于储蓄罐下单结束时间
          */
         private boolean beforeCxgOrderEndTime;

         /**
          *  是否支持电汇  0-不支持 1-支持
          */
         private List<FundCurrencyVO> cxgCurrencyVOList;
         
         @Setter
         @Getter
         public static class FundCurrencyVO implements Serializable {

             private static final long serialVersionUID = 7120832541755838488L;

             /**
              * 储蓄罐币种代码 币种 156-人民币、250-法郎、280-马克、344-港元、392-日元、826-英镑、840-美元、954欧元
              */
             private String cxgCurrencyCode;

             /**
              * 储蓄罐币种描述 币种 156-人民币、250-法郎、280-马克、344-港元、392-日元、826-英镑、840-美元、954欧元
              */
             private String cxgCurrencyDesc;
         }
     }

     @Setter
     @Getter
     public static class BuyFundBankCardVO implements Serializable {
         private static final long serialVersionUID = 6945899198946903226L;

         /**
          *  资金账号
          */
         private String cpAcctNo;

         /**
          * 银行卡号掩码
          */
         private String bankAcctMask;

         /**
          * bankName
          */
         private String bankName;

         /**
          * 银行logo
          */
         private String bankLogoUrl;

     }
}
