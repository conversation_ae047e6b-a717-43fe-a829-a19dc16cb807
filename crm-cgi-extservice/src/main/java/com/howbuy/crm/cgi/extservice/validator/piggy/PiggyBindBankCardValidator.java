/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.piggy;

import com.howbuy.crm.cgi.common.utils.SpringContextUtil;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkBankCardInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkBankCardInfoOuterService;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/24 16:54
 * @since JDK 1.8
 */
public class PiggyBindBankCardValidator  implements PiggyValidator<PiggyBankVerificationVO> {

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;
    /**
     * 银行卡服务
     */
    private final HkBankCardInfoOuterService hkBankCardInfoOuterService = SpringContextUtil.getBean(HkBankCardInfoOuterService.class);

    public PiggyBindBankCardValidator(HkCustInfoDTO hkCustInfo) {
        this.hkCustInfo = hkCustInfo;
    }
    @Override
    public PiggyBankVerificationVO verification() {
        List<HkBankCardInfoDTO> hkBankAcctList = hkBankCardInfoOuterService.getHkBankAcctList(hkCustInfo.getHkCustNo());
        if(CollectionUtils.isEmpty(hkBankAcctList)){
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.NOT_BIND_BANK_CARD.getCode());
            return piggyBankVerificationVO;
        }
        return null;
    }
}
