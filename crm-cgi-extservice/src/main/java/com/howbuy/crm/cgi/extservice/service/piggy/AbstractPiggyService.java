/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.piggy;

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggyValidator;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.fund.QueryFundBasicInfoOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/16 17:44
 * @since JDK 1.8
 */
public abstract class AbstractPiggyService {

    @Resource
    protected HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    protected QueryFundBasicInfoOuterService queryFundBasicInfoOuterService;
    /**
     * @description: 获取用户香港客户信息
     * @param hkCustNo 香港客户号
     * @return com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO
     * @author: jinqing.rao
     * @date: 2024/5/9 17:16
     * @since JDK 1.8
     */
    protected HkCustInfoDTO getHkCustInfoDTO(String hkCustNo) {
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(hkCustInfo.getReturnCode())) {
            throw new BusinessException(ExceptionCodeEnum.HK_CUST_INFO_NOT_EXIST);
        }
        return hkCustInfo;
    }
    /**
     * @description: 获取基金基本信息
     * @param fundCode
     * @return com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO
     * @author: jinqing.rao
     * @date: 2024/7/16 20:13
     * @since JDK 1.8
     */
    protected FundBasicInfoDTO getFundBasicInfoDTO(String fundCode) {
        //获取基金的基本信息,不存在抛出异常
        FundBasicInfoDTO fundBasicInfoDTO = queryFundBasicInfoOuterService.queryFundBasicInfo(fundCode);
        return fundBasicInfoDTO;
    }
}
