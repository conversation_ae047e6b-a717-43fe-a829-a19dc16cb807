/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/24 00:00
 * @since JDK 1.8
 */
@Data
public class AssetFundDetailVO {

    /**
     * 产品名称
     */
    private String fundName;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 展示类型 1.非分期成立 2.分起成立 3.千禧年产品
     */
    private String showType;

    /**
     * 参考市值
     */
    private String currencyMarketValue;

    /**
     * 当前收益（当前币种）
     */
    private String currentAssetCurrency;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 参考净值
     */
    private String nav;

    /**
     * 净值分红标识,0-否，1-是
     */
    private String navDivFlag;

    /**
     * 净值日期 YYYY-MM-DD或者MM-DD
     */
    private String navDate;

    /**
     * na flag 标识
     */
    private String naFlag;
    /**
     * 收益计算状态 0-计算中;1-计算成功
     */
    private String incomeCalStat;

    /**
     * 分期成立
     */
    private String stageEstablishFlag;


    /**
     *  总实缴金额
     */
    private String paidInAmt;

    /**
     * 待投金额（当前币种）
     */
    private String currencyUnPaidInAmt;

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 当前收益率 带百分号
     */
    private String yieldRate;

    /**
     * 展示数据的正负 1 正数, 0 相同 ,-1 负数
     */
    private String showDirection;

    /**
     * 是否存在明细 1:存在 0:不存在
     */
    private String hasDetail;

    /**
     * 平衡因子
     */
    private String balanceFactor;
    /**
     * 平衡因子转换完成 1-是 0-否
     */
    private String convertFinish;
    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;

    /**
     * 累计应收管理费
     */
    private String receivManageFee;


    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFee;

    /**
     * 费后持仓市值
     */
    private String currencyMarketValueExFee;
    /**
     * 产品详细信息(如果是分期成立,则会有多条)
     */
    private List<FundItemVO> fundItemVOList;

}