package com.howbuy.crm.cgi.extservice.common.enums.voucher;

/**
 * @description: 打款凭证展示枚举状态
 * @author: jinqing.rao
 * @date: 2025/4/10 9:44
 * @since JDK 1.8
 */
public enum PayVoucherDisPlayStatusEnum {
    ALREADY_CREDITED("1", "已入账"),
    SUBMITTED("2", "已提交"),
    CREDIT_FAILED("3", "入账失败"),
    DUPLICATE_VOUCHER("4", "重复凭证");

    private final String code;
    private final String description;

    PayVoucherDisPlayStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
