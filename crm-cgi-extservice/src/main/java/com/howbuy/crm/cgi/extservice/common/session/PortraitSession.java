/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.session;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/1/6 14:37
 * @since JDK 1.8
 */
@Getter
@Setter
public class PortraitSession implements Serializable {
    private static final long serialVersionUID = 6743588252115132928L;
    /**
     * 网点号
     */
    private String coopId;
    /**
     * 微信用户id
     */
    private String userId;

    /**
     * 当前登录用户IP
     */
    private String loginIp;
}
