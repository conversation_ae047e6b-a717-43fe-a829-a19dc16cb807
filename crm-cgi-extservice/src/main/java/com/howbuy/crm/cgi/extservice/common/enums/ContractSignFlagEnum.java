package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 协议签订标识
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum ContractSignFlagEnum {

    NOTSIGN("0", "未签署"),
    SIGN("1", "已签署");

    private final String key;
    private final String desc;

    public static ContractSignFlagEnum getContractSignFlagEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        ContractSignFlagEnum contractSignFlagEnum = getContractSignFlagEnum(code);
        return contractSignFlagEnum == null ? null : contractSignFlagEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    ContractSignFlagEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
