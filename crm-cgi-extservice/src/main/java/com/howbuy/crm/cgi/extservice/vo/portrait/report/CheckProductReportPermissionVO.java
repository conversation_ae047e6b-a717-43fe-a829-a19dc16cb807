/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.portrait.report;

import com.howbuy.crm.cgi.common.base.Body;

/**
 * 校验产品报告发送权限响应VO
 * <AUTHOR>
 * @date 2025-03-13 13:18:57
 */
public class CheckProductReportPermissionVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 是否能发送（0：否，1：是）
     */
    private String sendAble;

    /**
     * 不可发送原因
     */
    private String sendAbleReason;

    public String getSendAble() {
        return sendAble;
    }

    public void setSendAble(String sendAble) {
        this.sendAble = sendAble;
    }

    public String getSendAbleReason() {
        return sendAbleReason;
    }

    public void setSendAbleReason(String sendAbleReason) {
        this.sendAbleReason = sendAbleReason;
    }
} 