package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;

/**
* @Description 校验登录密码
*
* <AUTHOR>
* @Date 2024/8/16 10:07
*/
public class CheckLoginPasswordRequest extends AccountBaseRequest {

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 登录密码
     */
    private String password;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
