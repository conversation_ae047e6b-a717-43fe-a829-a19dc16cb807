package com.howbuy.crm.cgi.extservice.vo.hkfund;

/**
 * Copyright (c) 2024, <PERSON>g<PERSON>ai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 申请结果页面信息
 * @date 2024/4/9 17:56
 * @since JDK 1.8
 */
@Setter
@Getter
public class HKPurchaseFundApplyResultVO extends Body implements Serializable {

    private static final long serialVersionUID = -2762957768883425746L;


    /**
     * 申请金额
     */
    private String appAmt;

    /**
     * 基金简称
     */
    private String fundShortName;

    /**
     * 打款截止日期  yyyy-mm-dd
     */
    private String payEndDate;

    /**
     * 打款截止时间 hh:mm
     */
    private String payEndTime;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 是否支持预约交易
     */
    private String supportPrebook;

    /**
     * 投资者资质
     */
    private String investorType;

    /**
     * 资产证明是否有效
     */
    private String assetCertVaild;

    /**
     * 必须，1-电汇、2-支票、3-海外储蓄罐
     */
    private String payMethod;

    /**
     * 当前时间  yyyy-MM-dd
     */
    private String currentDate;

    /**
     * 1是 0 否   1表示存在审核中的订单
     */
    private String investorAuditStatus;


    /**
     * 转账银行信息列表
     */
    private TransferBankInfoInnerVO transferBankInfos;


    /**
     * 1 ： 是 当前时间 小于等于 下单支付截止时间  0 ：否   当前时间 大于等于 下单支付截止时间
     */
    private String beforPiggyPayEndTime;

    /**
     * 是否上传打款凭证 审核结果 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     */
    private String uploadStatus;


    /**
     * 分次call标识(股权类有此标识:0-否,1-是)
     */
    private String gradationCall;
    /**
     * 分次CALL信息
     */
    private GradationCallVO gradationCallVO;


    @Setter
    @Getter
    public static class GradationCallVO implements Serializable {
        private static final long serialVersionUID = 1043672785333927198L;

        /**
         * 认缴金额
         */
        private String subAmt;

        /**
         * 实缴金额
         */
        private String paidAmt;

        /**
         * 认缴手续费
         */
        private String subFee;

        /**
         * 实缴手续费
         */
        private String paidFee;

        /**
         * 实缴订单号
         */
        private String paidOrderNo;


    }

    @Setter
    @Getter
    public static class TransferBankInfoInnerVO implements Serializable {

        private static final long serialVersionUID = 1650273541093533205L;

        /**
         * 转账账户名称
         */
        private String transferBankAcctName;

        /**
         * 转账银行名称
         */
        private String transferBankName;

        /**
         * 转账银行地址
         */
        private String transferBankAddress;

        /**
         * 转账银行账号列表
         */
        private List<TransferBankAcct> transferBankAccts;

        /**
         * 国际汇款识别码
         */
        private String transferSwiftCode;


        /**
         * 银行代码
         */
        private String transferBankCode;

        /**
         * 银行logo
         */
        private String bankLogoUrl;

        @Setter
        @Getter
        public static class TransferBankAcct implements Serializable {

            private static final long serialVersionUID = 1650273541093533205L;

            /**
             * 银行账号
             */
            private String bankAcct;

            /**
             * 币种代码
             */
            private String currencyCode;

            /**
             * 币种描述
             */
            private String currencyDesc;

        }
    }
}
