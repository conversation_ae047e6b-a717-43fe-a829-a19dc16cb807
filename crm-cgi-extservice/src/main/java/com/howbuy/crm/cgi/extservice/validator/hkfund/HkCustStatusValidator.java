/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator.hkfund;

import com.howbuy.crm.cgi.extservice.common.enums.CustPasswdStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.hkopen.HkOpenAcctStatusEnum;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HKFundVerificationVO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;

/**
 * @description: 账户状态验证
 * <AUTHOR>
 * @date 2024/4/24 15:39
 * @since JDK 1.8
 */
public class HkCustStatusValidator implements HkFundValidator{

    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;

    public HkCustStatusValidator(HkCustInfoDTO hkCustInfo){
        this.hkCustInfo = hkCustInfo;
    }

    @Override
    public HKFundVerificationVO verification() {
        HKFundVerificationVO hkFundVerificationVO = new HKFundVerificationVO();
        //用户状态是正常/休眠,调用中台校验接口
        if (HkOpenAcctStatusEnum.DORMANT.getCode().equals(hkCustInfo.getCustState())) {
            hkFundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.OPEN_ACCOUNT_STATUS_DORMANT.getCode());
            return hkFundVerificationVO;
        }
        //交易账号是否激活
        if (!CustPasswdStatusEnum.NORMAL.getKey().equals(hkCustInfo.getCustTxPasswdType())) {
            hkFundVerificationVO.setBuyVerfiyState(HkFundVerificationStatusEnum.TRADE_ACCOUNT_NOT_ACTIVATED.getCode());
            return hkFundVerificationVO;
        }
        return null;
    }
}
