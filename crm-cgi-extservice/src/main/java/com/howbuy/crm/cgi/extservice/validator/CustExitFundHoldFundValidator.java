/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.validator;

import com.howbuy.crm.cgi.common.enums.TxChannelEnum;
import com.howbuy.crm.cgi.common.utils.SpringContextUtil;
import com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundVerificationStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherAuditStatusEnum;
import com.howbuy.crm.cgi.extservice.common.enums.voucher.PayVoucherTypeEnum;
import com.howbuy.crm.cgi.extservice.validator.piggy.PiggyValidator;
import com.howbuy.crm.cgi.extservice.vo.piggy.PiggyBankVerificationVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordPageDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.FundBasicInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.PiggyPayVoucherOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.FundHoldingsOuterService;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.QueryHwDealOrderOuterService;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @description: 用户存在指定基金持仓
 * <AUTHOR>
 * @date 2024/9/3 13:46
 * @since JDK 1.8
 */
public class CustExitFundHoldFundValidator implements PiggyValidator<PiggyBankVerificationVO> {

    /**
     * 查询持仓信息服务
     */

    private final FundHoldingsOuterService fundHoldingsOuterService = SpringContextUtil.getBean(FundHoldingsOuterService.class);

    /**
     * 查询在途交易服务
     */

    private final QueryHwDealOrderOuterService queryHwDealOrderOuterService = SpringContextUtil.getBean(QueryHwDealOrderOuterService.class);

    private final PiggyPayVoucherOuterService piggyPayVoucherOuterService = SpringContextUtil.getBean(PiggyPayVoucherOuterService.class);


    /**
     * 客户信息
     */
    private final HkCustInfoDTO hkCustInfo;

    private final FundBasicInfoDTO fundBasicInfoDTO;


    public CustExitFundHoldFundValidator(HkCustInfoDTO hkCustInfo,FundBasicInfoDTO fundBasicInfoDTO) {
        this.fundBasicInfoDTO = fundBasicInfoDTO;
        this.hkCustInfo = hkCustInfo;
    }
    @Override
    public PiggyBankVerificationVO verification() {
        // 判断是否有在途订单
        boolean result = queryHwDealOrderOuterService.hasFundInTransitOrder(fundBasicInfoDTO.getFundCode(),hkCustInfo.getHkCustNo());
        if(result){
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_HAS_POSITION_OR_IN_TRANSIT.getCode());
            return piggyBankVerificationVO;
        }
        // 判断是否有在途打款凭证
        List<String> tradeChannelList = Arrays.asList(TxChannelEnum.WAP.getCode(), TxChannelEnum.HK_APP.getCode(), TxChannelEnum.H5.getCode());
        List<String> type = Collections.singletonList(PayVoucherTypeEnum.DEPOSIT_CASH_ACCOUNT.getCode());
        List<String> auditStatusList = Collections.singletonList(PayVoucherAuditStatusEnum.WAIT_REVIEW.getCode());
        PiggyPayVoucherRecordPageDTO piggyPayVoucherRecordPageDTO = piggyPayVoucherOuterService.queryPiggyDepositVoucherRecordList(hkCustInfo.getHkCustNo(), type, auditStatusList, tradeChannelList, 1, 500);
        if (null != piggyPayVoucherRecordPageDTO && CollectionUtils.isNotEmpty(piggyPayVoucherRecordPageDTO.getPiggyPayVoucherRecordListDTO())) {
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_HAS_POSITION_OR_IN_TRANSIT.getCode());
            return piggyBankVerificationVO;
        }
        // 判断是否有对应基金的持仓
        List<String> fundCodeList = fundHoldingsOuterService.queryCustFundHoldFundByHkCustNo(hkCustInfo.getHkCustNo());
        if(CollectionUtils.isNotEmpty(fundCodeList) && fundCodeList.contains(fundBasicInfoDTO.getFundCode())){
            PiggyBankVerificationVO piggyBankVerificationVO = new PiggyBankVerificationVO();
            piggyBankVerificationVO.setVerfiyState(HkFundVerificationStatusEnum.CUSTOMER_HAS_POSITION_OR_IN_TRANSIT.getCode());
            return piggyBankVerificationVO;
        }
        return null;
    }
}
