package com.howbuy.crm.cgi.extservice.vo.portrait.material;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 素材库对话检索历史查询响应
 * @Date 2024-09-06 09:54:33
 */
@Getter
@Setter
@ToString
public class PortraitMaterialHistoryQueryVO extends Body {

    private static final long serialVersionUID = 1L;

    /**
     * 数据总量
     */
    private String total;

    /**
     * 数据列表
     */
    private List<HistoryItem> dataList;

    /**
     * 历史记录项
     */
    @Getter
    @Setter
    @ToString
    public static class HistoryItem {
        /**
         * 记录id
         */
        private String id;

        /**
         * 检索内容
         */
        private String content;

        /**
         * 检索参数
         */
        private String searchParam;
    }
} 