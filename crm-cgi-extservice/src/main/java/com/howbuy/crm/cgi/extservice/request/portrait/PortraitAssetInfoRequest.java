/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.portrait;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.common.base.BodyRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户画像资产信息请求
 * @Date 2024/9/13 17:15
 */
public class PortraitAssetInfoRequest extends BodyRequest implements Serializable {

    private static final long serialVersionUID = 399326144452881832L;

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hboneNo;

    /**
     * 包含外部资产方式 0-不包含外部资产 1-包含外部资产
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "包含外部资产方式", isRequired = true)
    private String externalBalanceWay;

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getExternalBalanceWay() {
        return externalBalanceWay;
    }

    public void setExternalBalanceWay(String externalBalanceWay) {
        this.externalBalanceWay = externalBalanceWay;
    }
}
