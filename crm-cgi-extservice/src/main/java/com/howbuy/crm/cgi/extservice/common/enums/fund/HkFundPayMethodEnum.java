/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums.fund;

import com.google.common.collect.Lists;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.utils.StringUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 支付方式
 * @date 2024/4/16 16:58
 * @since JDK 1.8
 */
public enum HkFundPayMethodEnum {
    //1-电汇、2-支票、3-海外储蓄罐
    TELEGRAPHICTRANSFER("1", "银行转账划款", 0),
    CHECK("2", "支票", 1),
    OVERSEASPIGGY("3", "海外储蓄罐", 2);



    private  final String code;
    private final String desc;

    private final int index;

    HkFundPayMethodEnum(String code, String desc, int index) {
        this.code = code;
        this.desc = desc;
        this.index = index;
    }



    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static boolean isBnakPayMethod(String paymentType) {
        // 这里的支付类型值是 111 , 110 中，1表示支持，0不支持  历史设计问题, 所以仅仅银行卡 的值应该是100,
        if(StringUtil.isEmpty(paymentType)){
            return false;
        }
        List<String> returnList = Lists.newArrayList();
        for (HkFundPayMethodEnum methodEnum : HkFundPayMethodEnum.values()) {
            if (YesOrNoEnum.YES.getCode().equals(String.valueOf(paymentType.charAt(methodEnum.index)))) {
                returnList.add(methodEnum.getCode());
            }
        }
        // returnList 只有银行卡
        return returnList.size() == 1 && returnList.contains(TELEGRAPHICTRANSFER.getCode());
    }

    /**
     * @description: 检查是否是指定的支付方式, 因为历史原因 paymentType 的值 是 这里的支付类型值是 111 , 110 中，1表示支持，0不支持
     *   本方法也是参考历史的枚举逻辑取值的
     * @param paymentType
     * @param paymentTypeEnum
     * @return java.lang.Boolean
     * @author: jinqing.rao
     * @date: 2024/5/27 15:01
     * @since JDK 1.8
     */
    public static Boolean checkPayMethodForAssign(String paymentType,HkFundPayMethodEnum paymentTypeEnum) {
        // 这里的支付类型值是 111 , 110 中，1表示支持，0不支持  历史设计问题, 所以仅仅银行卡 的值应该是100,
        if(StringUtil.isEmpty(paymentType)){
            return false;
        }
        List<String> returnList = Lists.newArrayList();
        for (HkFundPayMethodEnum methodEnum : HkFundPayMethodEnum.values()) {
            if (YesOrNoEnum.YES.getCode().equals(String.valueOf(paymentType.charAt(methodEnum.index)))) {
                returnList.add(methodEnum.getCode());
            }
        }
        // returnList 只有银行卡
        return returnList.size() == 1 && returnList.contains(paymentTypeEnum.getCode());
    }
    public static HkFundPayMethodEnum getEnumByCode(String code) {
        for (HkFundPayMethodEnum payMethodEnum : HkFundPayMethodEnum.values()) {
            if (payMethodEnum.getCode().equals(code)) {
                return payMethodEnum;
            }
        }
        return null;
    }

    /**
     * @description: copy历史代码,
     * @param payMentMode	值 是 111,110，这种，1表示支持，0不支持。用于表示是否支持多种支付方式
     * @return java.util.List<com.howbuy.crm.cgi.extservice.common.enums.fund.HkFundPayMethodEnum>
     * @author: jinqing.rao
     * @date: 2024/5/10 18:20
     * @since JDK 1.8
     */
    public static List<HkFundPayMethodEnum> getPaymentEnumListByValue(String payMentMode) {
        if (StringUtil.isEmpty(payMentMode)) {
            return Lists.newArrayList();
        }
        List<HkFundPayMethodEnum> returnList = Lists.newArrayList();
        for(HkFundPayMethodEnum hkFundPayMethodEnum : HkFundPayMethodEnum.values()) {
            if (YesOrNoEnum.YES.getCode().equals(String.valueOf(payMentMode.charAt(hkFundPayMethodEnum.index)))) {
                returnList.add(hkFundPayMethodEnum);
            }
        }
        return returnList;
    }
}
