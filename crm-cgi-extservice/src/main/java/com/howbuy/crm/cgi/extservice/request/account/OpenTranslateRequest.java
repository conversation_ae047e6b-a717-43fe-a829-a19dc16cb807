package com.howbuy.crm.cgi.extservice.request.account;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 翻译
 * @date 2023/11/30 14:52
 * @since JDK 1.8
 */
@Setter
@Getter
public class OpenTranslateRequest extends AccountBaseRequest implements Serializable {

    private static final long serialVersionUID = -942948803377406430L;

    /**
     * 请求参数
     */
    @Valid
    private List<OpenTranslateInfo> openTranslateInfoList;

    /**
     * @description: 中文转拼音的参数信息
     * @author: jinqing.rao
     * @date: 2023/12/14 13:15
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class OpenTranslateInfo implements Serializable {

        private static final long serialVersionUID = -6244886495506165598L;

        /**
         * 中文字符串对应的唯一key,前端默认生成,方便获取翻译后的拼音
         */
        @NotBlank(message = "中文字符串对应的唯一key必传")
        private String uuidKey;

        /**
         * 需要翻译的中文字符串
         */
        @NotBlank(message = "需要翻译的中文字符串必传")
        private String chinese;
    }
}
