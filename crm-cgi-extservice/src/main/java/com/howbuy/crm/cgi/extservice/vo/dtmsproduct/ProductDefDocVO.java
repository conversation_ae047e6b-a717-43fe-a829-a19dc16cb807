/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.dtmsproduct;

import com.howbuy.crm.cgi.extservice.vo.DtmsBaseVO;
import lombok.Data;

import java.util.List;

/**
 * @description: (查询产品费率接口)
 * <AUTHOR>
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
@Data
public class ProductDefDocVO extends DtmsBaseVO {
    /**
     * 业务类型	 1-认申购；2-赎回
     */
    private String busiType;
    /**
     * 文件列表
     */
    private List<ProductContractFileVO> fileList;

}