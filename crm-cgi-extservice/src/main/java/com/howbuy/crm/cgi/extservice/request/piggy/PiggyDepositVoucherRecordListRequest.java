/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.piggy;

import com.howbuy.crm.cgi.common.base.PageRequest;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/28 13:51
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositVoucherRecordListRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 7454994629591540694L;

    @NotBlank(message = "香港客户号不能为空")
    private String hkCustNo;

    /**
     * 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     */
    private List<String> payVoucherType;

    /**
     * 已入账 ：1、已提交 ： 2、入账失败 ：3、重复凭证 ： 4
     */
    private String voucherDisStatus;

    /**
     * 1 近一个月   2 近半年  3 近一年
     */
    private String timePeriod;

    /**
     * 时间格式  YYYYMMdd   开始时间和结束时间都是必填的
     */
    private String voucherStartDt;

    /**
     * 时间格式  YYYYMMdd   开始时间和结束时间都是必填的
     */
    private String voucherEndDt;
}
