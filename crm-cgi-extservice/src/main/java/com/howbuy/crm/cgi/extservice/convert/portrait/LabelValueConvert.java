/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.convert.portrait;

import com.howbuy.crm.cgi.extservice.vo.portrait.MultipleLabelValueVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.SingleLabelValueVO;
import com.howbuy.crm.cgi.extservice.vo.portrait.common.CustLabelValueVO;
import com.howbuy.crm.portrait.client.domain.dto.label.CustLabelValueDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.MultipleLabelValueDTO;
import com.howbuy.crm.portrait.client.domain.dto.label.SingleLabelValueDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description 标签领域对象转换类
 * @Date 2024/9/14 15:51
 */
@Slf4j
public class LabelValueConvert {

    /**
     * 单标签领域对象转换 DTO-》VO
     *
     * @param labelValueDTO 单标签DTO
     * @return SingleLabelValueVO
     */
    public static SingleLabelValueVO convertSingleLabelValueVO(SingleLabelValueDTO labelValueDTO) {
        if (labelValueDTO == null) {
            return new SingleLabelValueVO();
        }

        SingleLabelValueVO vo = new SingleLabelValueVO();
        vo.setLabelId(labelValueDTO.getLabelId());
        vo.setLabelValue(labelValueDTO.getLabelValue());
        vo.setLabelBdpValue(labelValueDTO.getLabelBdpValue());
        vo.setLabelInputValue(labelValueDTO.getLabelInputValue());
        vo.setLabelBdpInputValue(labelValueDTO.getLabelBdpInputValue());
        return vo;
    }

    /**
     * 多标签领域对象转换 DTO-》VO
     *
     * @param labelValueDTO 多标签DTO
     * @return MultipleLabelValueVO
     */
    public static MultipleLabelValueVO convertMultipleLabelValueVO(MultipleLabelValueDTO labelValueDTO) {
        if (labelValueDTO == null) {
            return new MultipleLabelValueVO();
        }

        MultipleLabelValueVO vo = new MultipleLabelValueVO();
        vo.setLabelId(labelValueDTO.getLabelId());
        vo.setLabelValues(labelValueDTO.getLabelValues());
        vo.setLabelBdpValues(labelValueDTO.getLabelBdpValues());
        vo.setLabelCustomizeValues(labelValueDTO.getLabelCustomizeValues());
        return vo;
    }

    /**
     * CustLabelValueDTO 转 CustLabelValueVO
     *
     * @param custLabelValueDTO 客户标签DTO
     */
    public static CustLabelValueVO convertCustLabelValueVO(CustLabelValueDTO custLabelValueDTO) {
        CustLabelValueVO vo = new CustLabelValueVO();
        vo.setSingleLabelValue(LabelValueConvert.convertSingleLabelValueVO(custLabelValueDTO.getSingleLabelValue()));
        vo.setMultipleLabelValue(LabelValueConvert.convertMultipleLabelValueVO(custLabelValueDTO.getMultipleLabelValue()));
        return vo;
    }
}
