package com.howbuy.crm.cgi.extservice.common.utils;

import com.howbuy.crm.cgi.common.enums.CommonResultEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/6/8 13:33
 * @since JDK 1.8
 */
@Slf4j
public class ParamsValidator {
    private ParamsValidator() {
    }

    public static final String ARG_SPLIT = ":";

    /**
     * @description: 如果字符串中包含# 则判断该字符串分割后只要有一个不为空则通过
     * @param: [request, args]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/8 13:58
     * @since JDK 1.8
     */
    public static void validateParams(Object request, String... args) {
        validate(request, "", args);
    }

    /**
     * @description: 如果字符串中包含# 则判断该字符串分割后只要有一个不为空则通过
     * @param: [request, msgPrefix, args]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/8 13:58
     * @since JDK 1.8
     */
    public static void validate(Object request, String msgPrefix, String... args) {
        if (CollectionUtils.isEmpty(Arrays.asList(args)) || request == null) {
            throw new ParamsException(CommonResultEnum.PARAM_ERROR.getCode(), CommonResultEnum.PARAM_ERROR.getDescription());
        }
        for (String arg : args) {
            String value = null;
            if (arg.contains("#")) {
                boolean flag = Arrays.stream(arg.split("#"))
                        .map(param -> getProperty(request, param))
                        .anyMatch(StringUtils::isNotEmpty);
                if (!flag) {
                    throw new ParamsException(CommonResultEnum.PARAM_ERROR.getCode(), CommonResultEnum.PARAM_ERROR.getDescription() + ARG_SPLIT + msgPrefix + arg);
                }
            } else {
                value = getProperty(request, arg);
                if (StringUtils.isEmpty(value)) {
                    throw new ParamsException(CommonResultEnum.PARAM_ERROR.getCode(), CommonResultEnum.PARAM_ERROR.getDescription() + ARG_SPLIT + msgPrefix + arg);
                }
            }
        }
    }

    /**
     * @description: 获取属性值
     * @param: [request, propertyName]
     * @return: java.lang.String
     * @author: shaoyang.li
     * @date: 2023/6/12 14:43
     * @since JDK 1.8
     */
    private static String getProperty(Object request, String propertyName) {
        try {
            return BeanUtils.getProperty(request, propertyName);
        } catch (Exception e) {
            log.error("No such field: " + propertyName + ", error:", e);
            return null;
        }
    }

    /**
     * @description: 抛出参数异常
     * @param: [paramName]
     * @return: void
     * @author: shaoyang.li
     * @date: 2023/6/8 13:58
     * @since JDK 1.8
     */
    public static void throwParamsException(String paramName) {
        throw new ParamsException(CommonResultEnum.PARAM_ERROR.getCode(), CommonResultEnum.PARAM_ERROR.getDescription() + ARG_SPLIT + "" + paramName);
    }

}
