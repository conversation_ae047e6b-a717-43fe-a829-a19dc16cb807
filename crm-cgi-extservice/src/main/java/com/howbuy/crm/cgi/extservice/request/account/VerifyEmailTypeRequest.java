/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * @description: (手机号码验证的请求类)
 * <AUTHOR>
 * @date 2023/11/29 14:33
 * @since JDK 1.8
 */
@Data
public class VerifyEmailTypeRequest extends AccountBaseRequest {
    /**
     * 手机号码 加密
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "邮箱", isRequired = true)
    private String email;

    /**
     * 验证码类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码类型", isRequired = true)
    private String verifyCodeType;

    /**
     * 验证码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "验证码", isRequired = true)
    private String verifyCode;

}