/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: (预约状态 1-未确认 2-已确认 4-已撤销 5-打回)
 * @date 2023/8/22 9:24
 * @since JDK 1.8
 */
public enum PrebookStateEnum {
    NOT_ACK("1", "未确认"),
    AC<PERSON>("2", "已确认"),
    QUASH("4", "已撤销"),
    REJECT("5", "打回");

    private final String key;
    private final String desc;

    public static PrebookStateEnum getPrebookState(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        PrebookStateEnum prebookState = getPrebookState(code);
        return prebookState == null ? null : prebookState.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    PrebookStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
