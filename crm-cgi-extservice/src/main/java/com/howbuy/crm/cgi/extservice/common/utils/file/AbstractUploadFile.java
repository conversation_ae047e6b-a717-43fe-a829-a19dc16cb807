/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.common.utils.file;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.common.utils.AlertLogUtil;
import com.howbuy.crm.cgi.extservice.common.enums.FileBizTypeEnum;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.utils.DesUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * @description:  文件上传
 * <AUTHOR>
 * @date 2024/5/10 18:59
 * @since JDK 1.8
 */
public abstract class AbstractUploadFile extends AbstractFile{

    private static final Logger log = LoggerFactory.getLogger(AbstractUploadFile.class);

    /**
     * 文件流
     */
    protected final MultipartFile multipartFile;


    public AbstractUploadFile(MultipartFile multipartFile, String fileBizType){
        this.multipartFile = multipartFile;
        this.fileBizType = fileBizType;
    }

    /**
     * 获取上传对应的存储配置
     */
     void getConfigStore(String fileBizType){
         if(StringUtils.isBlank(fileBizType)){
             throw new BusinessException(ExceptionCodeEnum.HFILE_FILE_UPLOAD_FILE_TYPE_ERROR);
         }
         this.fileBizTypeEnum = FileBizTypeEnum.getFileDirByCode(fileBizType);
         if(null == fileBizTypeEnum){
             throw  new BusinessException(ExceptionCodeEnum.HFILE_FILE_UPLOAD_FILE_TYPE_ERROR);
         }
     }

    /**
     * 获取自定义加密后的url
     *
     */
    public abstract String getEncryptCustomizeUrl();

    protected String initEncryptAlgorithm(String value,String key){
        String encrypt = DesUtil.encrypt(value, key);
        encrypt = encrypt.replaceAll("\\s*", "");
       return encrypt;
    }

    /**
     * @description: 上传文件
     * @param
     * @return void
     * @author: jinqing.rao
     * @date: 2024/5/10 19:14
     * @since JDK 1.8
     */
    public void uploadFile(){
        long startTime = System.currentTimeMillis();
        //初始化默认配置
        this.getConfigStore(fileBizType);
        //初始化上传的路径
        this.filePath = initFilePath();
        //初始化文件名称
        this.fileName = initFileName();
        // 如果filePath以/结尾，需要去除掉，不然中文显示乱码
       String uploadFilePath = filePath.endsWith(File.separator) ? filePath.substring(0, filePath.length() - 1) : filePath;
        try {
            log.info("AbstractUploadFile >>> 文件上传,StoreConfig:{},filePath:{},uploadFilePath:{},fileName:{}",fileBizTypeEnum.getStoreConfig(),filePath,uploadFilePath,fileName);
            HFileService.getInstance().write(fileBizTypeEnum.getStoreConfig(), uploadFilePath, fileName, multipartFile.getBytes());
        } catch (Exception e) {
            log.info("AbstractUploadFile >>> 文件错误,StoreConfig:{},filePath:{},fileName:{}",fileBizTypeEnum.getStoreConfig(),filePath,fileName,e);
            throw new BusinessException(ExceptionCodeEnum.Hk_OPEN_ACCOUNT_APP_FILE_UPLOAD_ERROR);
        }finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            if(duration > 2000){
                AlertLogUtil.alert(AbstractUploadFile.class.getName(), "文件上传耗时:" + duration+"毫秒");
            }
            log.info("AbstractUploadFile >>> 文件上传耗时:{}",duration);
        }
    }

    public String getEncryptUrl() {
        String url = getUploadFileUrl();
        return initEncryptAlgorithm(url, Constants.HK_APP_CUST_NO_KEY);
    }

    public String getUrl() {
        return filePath + fileName;
    }

    public String getFileName() {
        return fileName;
    }


    public String getFileType() {
        return fileType;
    }

    public String getEncryptFileName() {
        return initEncryptAlgorithm(fileName, Constants.HK_APP_CUST_NO_KEY);
    }

    private String getUploadFileUrl() {
        return filePath + File.separator + fileName;
    }
}
