package com.howbuy.crm.cgi.extservice.common.enums.hkopen;

/**
 * @description: 海外开户 资料填写页面 就业情况
 * @author: jinqing.rao
 * @date: 2023/12/7 19:37
 * @since JDK 1.8
 */
public enum HkOpenAcctEmploymentTypeEnum {
    EMPLOYER("01", "雇主"),
    FULL_TIME("02", "全职"),
    PART_TIME("03", "兼职"),
    HOUSEWIFE("04", "主妇"),
    STUDENT("05", "学生"),
    RETIRED("06", "退休"),
    NOT_EMPLOYED("07", "非在职");

    private String code;
    private String desc;

    HkOpenAcctEmploymentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static HkOpenAcctEmploymentTypeEnum getHkOpenAcctEmploymentTypeEnumByCode(String code) {
        for (HkOpenAcctEmploymentTypeEnum typeEnum : HkOpenAcctEmploymentTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
