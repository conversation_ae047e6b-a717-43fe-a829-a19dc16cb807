package com.howbuy.crm.cgi.extservice.request.account;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.cgi.extservice.request.AccountBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 修改交易密码
 * @date 2023/6/6 14:28
 * @since JDK 1.8
 */
@Data
public class ChangeTradePasswordRequest extends AccountBaseRequest {

    /**
     * 原交易密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "原交易密码", isRequired = true)
    private String oldTradePassword;
    /**
     * 新交易密码 必须
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "新交易密码", isRequired = true)
    private String newTradePassword;
}
