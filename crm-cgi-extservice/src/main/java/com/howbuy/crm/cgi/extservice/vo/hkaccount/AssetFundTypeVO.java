/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.vo.hkaccount;

import lombok.Data;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/23 23:57
 * @since JDK 1.8
 */
@Data
public class AssetFundTypeVO {

    /**
     * 总资产
     */
    private String totalAsset;

    /**
     * 展示币种总资产
     */
    private String disCurTotalAsset;

    /**
     * 产品集合类型 0 海外储蓄罐 1 阳光私募
     */
    private String fundSetType;

    /**
     * 基金持仓详情列表
     */
    private List<AssetFundDetailVO> assetFundDetailList;
}