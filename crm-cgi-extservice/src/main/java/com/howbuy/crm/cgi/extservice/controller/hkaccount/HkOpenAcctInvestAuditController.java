package com.howbuy.crm.cgi.extservice.controller.hkaccount;

/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.base.Body;
import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkAppAccountBaseRequest;
import com.howbuy.crm.cgi.extservice.request.account.hkapp.HkOpenAcctInvestAuditRequest;
import com.howbuy.crm.cgi.extservice.service.hkaccount.hkopenacct.HkOpenAcctInvestAuditService;
import com.howbuy.crm.cgi.extservice.vo.hkaccount.hkapp.HkOpenAcctInvestDetailVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 海外App专业资质信息审核
 * <AUTHOR>
 * @date 2024/3/4 19:01
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/hkaccount/openacct/investaudit")
public class HkOpenAcctInvestAuditController {

    @Resource
    private HkOpenAcctInvestAuditService hkOpenAcctInvestAuditService;

    /**
     * @api {POST} /ext/hkaccount/openacct/investaudit/detail getInvestAuditDetail()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctInvestAuditController
     * @apiName getInvestAuditDetail()
     * @apiDescription 投资者专业资质详情查询
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo App 基础传参,
     * @apiParamExample 请求体示例
     * {"hkCustNo":"bz0Xq"}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} data.auditStatus 审核结果 0：未提交 1:审核通过  2:审核中 3:审核不通过 4 : 已过期
     * @apiSuccess (响应结果) {Array} data.propertyImages 图片地址
     * @apiSuccess (响应结果) {String} data.propertyImages.url 图片地址
     * @apiSuccess (响应结果) {String} data.propertyImages.thumbnailUrl 缩略图地址
     * @apiSuccess (响应结果) {String} data.propertyImages.exampleFileFormatType 文件类型
     * @apiSuccess (响应结果) {String} data.refuseReason 拒绝原因
     * @apiSuccess (响应结果) {String} data.investorType 投资者类型
     * @apiSuccess (响应结果) {String} data.assetEffectiveTime 资产有效时间
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"s8utC","data":{"auditStatus":"6R6gPZze","assetEffectiveTime":"Ll4RTJ532","refuseReason":"M4","propertyImages":[{"exampleFileFormatType":"eQd1SVcKRX","url":"dlcd5FEL","thumbnailUrl":"HDo"}],"investorType":"j"},"description":"pnwQ","timestampServer":"7h4W0MkW"}
     */
    @RequestMapping("/detail")
    public CgiResponse<HkOpenAcctInvestDetailVO> getInvestAuditDetail(@RequestBody HkAppAccountBaseRequest request) {
        return CgiResponse.ok(hkOpenAcctInvestAuditService.getInvestAuditDetail(request));
    }

    /**
     * @api {POST} /ext/hkaccount/openacct/investaudit/submit submitInvestAudit()
     * @apiVersion 1.0.0
     * @apiGroup HkOpenAcctInvestAuditController
     * @apiName submitInvestAudit()
     * @apiDescription 投资者专业资质提交接口
     * @apiHeader (header-param) {String} content-type=application/json;charset=utf-8 提交格式
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} investorType 投资者类型
     * @apiParam (请求体) {Array} propertyImages 证明图片相对路径
     * @apiParam (请求体) {String} propertyImages.url 图片地址
     * @apiParam (请求体) {String} propertyImages.thumbnailUrl 缩略图地址
     * @apiParam (请求体) {String} propertyImages.exampleFileFormatType 文件类型
     * @apiParam (请求体) {String} deviceId 设备ID
     * @apiParam (请求体) {String} version 版本号
     * @apiParamExample 请求体示例
     * {"hkCustNo":"PFbEUnhp","deviceId":"D3bBKq","version":"3XMRh2XFlf","investorType":"aU","propertyImages":[{"exampleFileFormatType":"iz","url":"aRJmbQ","thumbnailUrl":"Nfj95k4vco"}]}
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} data 返回内容
     * @apiSuccess (响应结果) {String} timestampServer
     * @apiSuccessExample 响应结果示例
     * {"code":"aHuvQL","description":"gWcAo4ReSr","timestampServer":"XKi79kHWZ"}
     */
    @RequestMapping("/submit")
    public CgiResponse<Body> submitInvestAudit(@RequestBody HkOpenAcctInvestAuditRequest request) {
         hkOpenAcctInvestAuditService.submitInvestAudit(request);
         return CgiResponse.ok(new Body());
    }
}