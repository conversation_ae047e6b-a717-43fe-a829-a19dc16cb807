package com.howbuy.crm.cgi.extservice.common.enums;

import java.util.stream.Stream;

/**
 * @description: 线上签约标识  0-不支持；1-支持；2-待确定
 * @author: shuai.zhang
 * @date: 2023/5/18 9:51
 * @since JDK 1.8
 * @version: 1.0
 */
public enum OnlineSignFlagEnum {

    NOTSUPPORT("0", "不支持"),
    SUPPORT("1", "支持"),
    NOTDEFINE("2", "待确定");

    private final String key;
    private final String desc;

    public static OnlineSignFlagEnum getOnlineSignFlagEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        OnlineSignFlagEnum onlineSignFlagEnum = getOnlineSignFlagEnum(code);
        return onlineSignFlagEnum == null ? null : onlineSignFlagEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    OnlineSignFlagEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
