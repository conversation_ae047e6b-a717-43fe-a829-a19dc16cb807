package com.howbuy.crm.cgi.extservice.common.enums.fund;

public enum HKFundTradeModeEnum {
    // 交易方式 1-申购；2-赎回
    HK_PURCHASE_FUND("1", "购买"),
    HK_REDEEM_FUND("2", "赎回");

    private final String code;
    private final String desc;

    HKFundTradeModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

}
