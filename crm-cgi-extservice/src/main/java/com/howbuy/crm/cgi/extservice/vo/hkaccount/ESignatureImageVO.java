package com.howbuy.crm.cgi.extservice.vo.hkaccount;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.extservice.vo.AccountBaseVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 签名图片解析接口
 * @date 2023/12/25 13:07
 * @since JDK 1.8
 */
@Setter
@Getter
public class ESignatureImageVO extends AccountBaseVO implements Serializable {

    /**
     * 是否和证件信息一致
     */
    private String sameIdName;

    /**
     * 签名信息
     */
    private String signature;
    /**
     * 电子签名图片URL
     */
    private List<ImageVO> eSignatureImages;

}
