/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.request.hkfund;

import com.howbuy.crm.cgi.common.enums.CurrencyEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/31 16:42
 * @since JDK 1.8
 */
@Setter
@Getter
public class SimuBalanceBaseRequest implements Serializable {

    private static final long serialVersionUID = -4655285965584124159L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 当前展示的币种
     */
    private String currency = CurrencyEnum.RMB.getCode();

    /**
     * 基金交易账号
     */
    private String fundTxCode;

    /**
     * app版本号
     */
    private String version = "1.0.0";
}
