# 基金交易代码列表查询单元测试说明文档

## 测试类介绍

- 类名：TestQueryFundTxCodeList
- 测试目标：HkCommonService.queryFundTxCodeList
- 主要功能：测试基金交易代码列表查询功能

## 测试环境准备

1. 继承TestBase基类
2. 使用@InjectMocks注入被测试类
3. 使用@Mock模拟外部依赖：
   - queryFundTxAcctOuterService: 基金交易账号查询外部服务

## 测试用例说明

### 1. testQueryFundTxCodeList_empty

- 测试场景：当没有基金交易账号时
- 测试目的：验证空数据场景的处理
- 测试步骤：
  1. 准备请求参数(香港客户号)
  2. Mock基金交易账号服务返回空列表
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回对象不为空
  - 账号列表为空

### 2. testQueryFundTxCodeList_singleFullCommission

- 测试场景：只有一个全委账户场景
- 测试目的：验证单个全委账户的处理逻辑
- 测试步骤：
  1. 准备请求参数
  2. Mock一个全委账户数据
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 列表大小为1
  - 账户名称为"全权委托账户"(不带序号)

### 3. testQueryFundTxCodeList_singleNonFullCommission

- 测试场景：只有一个非全委账户场景
- 测试目的：验证单个非全委账户的处理逻辑
- 测试步骤：
  1. 准备请求参数
  2. Mock一个非全委账户数据
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 列表大小为1
  - 账户名称为"证券投资账户"(不带序号)

### 4. testQueryFundTxCodeList_multipleFullCommission

- 测试场景：多个全委账户场景
- 测试目的：验证多个全委账户的序号生成逻辑
- 测试步骤：
  1. 准备请求参数
  2. Mock多个全委账户数据
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 列表大小符合预期
  - 账户名称包含序号(如"全权委托账户01")

### 5. testQueryFundTxCodeList_mixedAccounts

- 测试场景：混合场景，既有全委又有非全委账户
- 测试目的：验证混合场景下的处理逻辑
- 测试步骤：
  1. 准备请求参数
  2. Mock混合账户数据
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 列表大小符合预期
  - 全委账户和非全委账户数量正确

## Mock数据说明

### 基金交易账号数据(HkFundTxAcctDTO)

```java
HkFundTxAcctDTO dto = new HkFundTxAcctDTO();
dto.setFundTxAcctNo("TX123456");
dto.setFundTxAccType(YesNoEnum.YES.getCode()); // 全委账户：YES，非全委账户：NO
dto.setFundTxAcctStat("全权委托账户");
```

## 注意事项

1. 包含了空数据和各种账户组合的测试场景
2. 使用PowerMockito.when()方法模拟外部服务调用
3. 使用Assert进行结果验证
4. 测试用例命名规范：test + 方法名 + 场景描述
5. 每个测试用例都有清晰的注释说明测试目的

## 相关文件

- HkCommonService.java: 被测试的服务类
- HkFundTxAcctDTO.java: 基金交易账号数据传输对象
- FundTxCodeListVO.java: 接口返回对象
- TestBase.java: 测试基类
