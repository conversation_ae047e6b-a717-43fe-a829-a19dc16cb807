# 海外观察报告查询服务单元测试

## 测试目标

对`HwOverseaReportService`类中的`queryHwOverseaReport`方法进行单元测试，验证该方法在不同情况下的行为是否符合预期。

## 测试场景

### 1. 正常情况测试 (testQueryHwOverseaReport_normal)

**测试目的**：验证在客户信息正常且配置信息完整的情况下，能否正确返回海外观察报告列表。

**测试步骤**：
1. 创建请求对象并设置香港客户号
2. Mock客户信息，设置投资类型
3. Mock财富配置信息，设置完整的报告名称和URL（包含PDF和图片格式）
4. 调用`queryHwOverseaReport`方法
5. 验证返回结果包含预期的报告列表

**预期结果**：
- 返回的报告列表不为空
- 报告列表包含两个报告项（财富分配报告和预约日历摘要）
- 报告项的文件名、URL和类型正确（PDF类型为"1"，图片类型为"2"）

### 2. 报告URL为空测试 (testQueryHwOverseaReport_empty_report_url)

**测试目的**：验证当财富配置信息中部分报告URL为空时，是否能正确过滤掉无效的报告。

**测试步骤**：
1. 创建请求对象并设置香港客户号
2. Mock客户信息，设置投资类型
3. Mock财富配置信息，设置部分报告URL为空
4. 调用`queryHwOverseaReport`方法
5. 验证返回结果只包含有效的报告

**预期结果**：
- 返回的报告列表不为空
- 报告列表只包含URL不为空的报告项
- 报告项的文件名、URL和类型正确

### 3. 客户信息为空测试 (testQueryHwOverseaReport_null_custInfo)

**测试目的**：验证当客户信息为空时，方法是否会抛出业务异常。

**测试步骤**：
1. 创建请求对象并设置香港客户号
2. Mock客户信息为null
3. 调用`queryHwOverseaReport`方法
4. 验证是否抛出预期的业务异常

**预期结果**：
- 方法抛出`BusinessException`异常

### 4. 客户不存在测试 (testQueryHwOverseaReport_cust_not_exist)

**测试目的**：验证当客户不存在时，方法是否会抛出业务异常。

**测试步骤**：
1. 创建请求对象并设置香港客户号
2. Mock客户信息，设置返回码为客户不存在
3. 调用`queryHwOverseaReport`方法
4. 验证是否抛出预期的业务异常

**预期结果**：
- 方法抛出`BusinessException`异常

### 5. 不支持的文件格式测试 (testQueryHwOverseaReport_unsupported_file_type)

**测试目的**：验证当报告文件格式不受支持时，是否能正确过滤掉这些报告。

**测试步骤**：
1. 创建请求对象并设置香港客户号
2. Mock客户信息，设置投资类型
3. Mock财富配置信息，设置部分报告为不支持的文件格式（如.doc）
4. 调用`queryHwOverseaReport`方法
5. 验证返回结果只包含支持的文件格式的报告

**预期结果**：
- 返回的报告列表不为空
- 报告列表只包含支持的文件格式（PDF、PNG、JPEG）的报告项
- 不支持的文件格式的报告被过滤掉

## 测试依赖

- Mock对象：
  - HkCustInfoOuterService：模拟客户信息查询服务
  - QueryLatestProductWealthOuterService：模拟财富配置信息查询服务

## 测试数据

- 客户号：HK123456
- 投资类型：01
- 报告文件：财富分配报告.pdf、预约日历摘要.png、财富分配报告.doc（不支持的格式）
- 报告URL：http://example.com/wealth.pdf、http://example.com/calendar.png