# 补充协议提交单元测试说明文档

## 测试类介绍
- 类名：TestSubmitSupplementalAgreement
- 测试目标：SupplementalAgreementService.submitSupplementalAgreement
- 主要功能：测试补充协议提交功能

## 测试环境准备
1. 继承TestBase基类
2. 使用@InjectMocks注入被测试类
3. 使用@Mock模拟外部依赖：
   - supplementalAgreementOuterService: 补充协议外部服务
   - hkCustInfoOuterService: 香港客户信息服务

## 测试用例说明

### 1. testSubmitSupplementalAgreement_success
- 测试场景：正常场景，提交成功
- 测试目的：验证正常业务流程
- 测试步骤：
  1. 准备请求参数(香港客户号、基金代码、交易密码、协议ID)
  2. Mock交易密码验证成功
  3. Mock协议提交成功
  4. 执行提交方法
  5. 验证返回结果
- 验证点：
  - 返回对象不为空
  - 验证交易密码验证方法被调用
  - 验证协议提交方法被调用

### 2. testSubmitSupplementalAgreement_password_error
- 测试场景：异常场景，交易密码验证失败
- 测试目的：验证交易密码错误的异常处理
- 测试步骤：
  1. 准备请求参数
  2. Mock交易密码验证抛出异常
  3. 执行提交方法
- 验证点：
  - 预期抛出BusinessException异常

### 3. testSubmitSupplementalAgreement_submit_error
- 测试场景：异常场景，协议提交失败
- 测试目的：验证协议提交失败的异常处理
- 测试步骤：
  1. 准备请求参数
  2. Mock交易密码验证成功
  3. Mock协议提交抛出异常
  4. 执行提交方法
- 验证点：
  - 预期抛出BusinessException异常

## Mock数据说明

### 请求参数(SupplementalAgreementSubmitRequest)
```java
SupplementalAgreementSubmitRequest request = new SupplementalAgreementSubmitRequest();
request.setHkCustNo("HK123456");
request.setFundCode("000001");
request.setTxPassword("123456");
request.setAgreementId("AGR001");
```

## 注意事项
1. 包含了正常场景和异常场景的测试
2. 使用PowerMockito.doNothing()和doThrow()方法模拟外部服务调用
3. 使用Assert进行结果验证
4. 使用Mockito.verify()验证方法调用
5. 使用@Test(expected = BusinessException.class)验证异常
6. 每个测试用例都有清晰的注释说明测试目的

## 相关文件
- SupplementalAgreementService.java: 被测试的服务类
- SupplementalAgreementSubmitRequest.java: 提交请求对象
- SupplementalAgreementSubmitVO.java: 提交响应对象
- TestBase.java: 测试基类 