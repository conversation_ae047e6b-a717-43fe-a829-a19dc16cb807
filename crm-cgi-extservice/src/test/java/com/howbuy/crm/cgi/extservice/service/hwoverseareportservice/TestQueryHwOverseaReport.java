/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.extservice.service.hwoverseareportservice;

import com.howbuy.crm.cgi.common.constants.OutReturnCodes;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.request.hkfund.HwOverseaReportRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.extservice.service.hkfund.HwOverseaReportService;
import com.howbuy.crm.cgi.extservice.vo.hkfund.HwOverseaReportListVO;
import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.QueryProductWealthAppointmentConfigDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.HkCustInfoDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo.QueryLatestProductWealthOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

/**
 * @description: 海外观察报告查询服务单元测试
 * <AUTHOR>
 * @date 2025/4/25 18:08
 * @since JDK 1.8
 */
public class TestQueryHwOverseaReport extends TestBase {

    @InjectMocks
    private HwOverseaReportService hwOverseaReportService = new HwOverseaReportService();

    @Mock
    private HkCustInfoOuterService hkCustInfoOuterService;
    
    @Mock
    private QueryLatestProductWealthOuterService queryLatestProductWealthOuterService;

    /**
     * 测试正常情况下查询海外观察报告
     * 客户状态正常，应返回有效的报告列表
     */
    @Test
    public void testQueryHwOverseaReport_normal() {
        // 准备请求参数
        HwOverseaReportRequest request = new HwOverseaReportRequest();
        request.setHkCustNo("HK123456");
        
        // Mock客户信息
        HkCustInfoDTO hkCustInfo = new HkCustInfoDTO();
        hkCustInfo.setInvestorQualification("PRO"); // 设置投资类型
        PowerMockito.when(hkCustInfoOuterService.getHkCustInfo(Mockito.anyString())).thenReturn(hkCustInfo);
        
        // Mock财富配置信息
        QueryProductWealthAppointmentConfigDTO configDTO = new QueryProductWealthAppointmentConfigDTO();
        configDTO.setWealthAllocationReport("财富分配报告.pdf");
        configDTO.setWealthAllocationReportUrl("http://example.com/wealth.pdf");
        configDTO.setAppointmentCalendarSummary("预约日历摘要.png");
        configDTO.setAppointmentCalendarSummaryUrl("http://example.com/calendar.png");
        PowerMockito.when(queryLatestProductWealthOuterService.queryLatestProductWealthAppointmentConfig(Mockito.anyString())).thenReturn(configDTO);

        // 执行测试
        HwOverseaReportListVO result = hwOverseaReportService.queryHwOverseaReport(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getOverseaReportList());
        Assert.assertEquals(2, result.getOverseaReportList().size());
        
        // 验证第一个报告（财富分配报告）
        Assert.assertEquals("财富分配报告", result.getOverseaReportList().get(0).getFileName());
        Assert.assertEquals("http://example.com/wealth.pdf", result.getOverseaReportList().get(0).getFileUrl());
        Assert.assertEquals("1", result.getOverseaReportList().get(0).getFileType()); // PDF类型
        
        // 验证第二个报告（预约日历摘要）
        Assert.assertEquals("预约日历摘要", result.getOverseaReportList().get(1).getFileName());
        Assert.assertEquals("http://example.com/calendar.png", result.getOverseaReportList().get(1).getFileUrl());
        Assert.assertEquals("2", result.getOverseaReportList().get(1).getFileType()); // 图片类型
    }

    /**
     * 测试当财富配置信息中报告URL为空时的情况
     * 应该过滤掉无效的报告
     */
    @Test
    public void testQueryHwOverseaReport_empty_report_url() {
        // 准备请求参数
        HwOverseaReportRequest request = new HwOverseaReportRequest();
        request.setHkCustNo("HK123456");
        
        // Mock客户信息
        HkCustInfoDTO hkCustInfo = new HkCustInfoDTO();
        hkCustInfo.setInvestorQualification("PRO");
        PowerMockito.when(hkCustInfoOuterService.getHkCustInfo(Mockito.anyString())).thenReturn(hkCustInfo);
        
        // Mock财富配置信息 - 设置部分URL为空
        QueryProductWealthAppointmentConfigDTO configDTO = new QueryProductWealthAppointmentConfigDTO();
        configDTO.setWealthAllocationReport("财富分配报告.pdf");
        configDTO.setWealthAllocationReportUrl(""); // 空URL
        configDTO.setAppointmentCalendarSummary("预约日历摘要.png");
        configDTO.setAppointmentCalendarSummaryUrl("http://example.com/calendar.png");
        PowerMockito.when(queryLatestProductWealthOuterService.queryLatestProductWealthAppointmentConfig(Mockito.anyString())).thenReturn(configDTO);

        // 执行测试
        HwOverseaReportListVO result = hwOverseaReportService.queryHwOverseaReport(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getOverseaReportList());
        Assert.assertEquals(1, result.getOverseaReportList().size()); // 只有一个有效报告
        Assert.assertEquals("预约日历摘要", result.getOverseaReportList().get(0).getFileName());
        Assert.assertEquals("http://example.com/calendar.png", result.getOverseaReportList().get(0).getFileUrl());
    }

    /**
     * 测试客户信息为空的情况
     * 应抛出业务异常
     */
    @Test(expected = BusinessException.class)
    public void testQueryHwOverseaReport_null_custInfo() {
        // 准备请求参数
        HwOverseaReportRequest request = new HwOverseaReportRequest();
        request.setHkCustNo("HK123456");
        
        // Mock客户信息为null
        PowerMockito.when(hkCustInfoOuterService.getHkCustInfo(Mockito.anyString())).thenReturn(null);

        // 执行测试 - 应抛出异常
        hwOverseaReportService.queryHwOverseaReport(request);
    }
    
    /**
     * 测试客户不存在的情况
     * 应抛出业务异常
     */
    @Test(expected = BusinessException.class)
    public void testQueryHwOverseaReport_cust_not_exist() {
        // 准备请求参数
        HwOverseaReportRequest request = new HwOverseaReportRequest();
        request.setHkCustNo("HK123456");
        
        // Mock客户信息 - 返回码表示客户不存在
        HkCustInfoDTO hkCustInfo = new HkCustInfoDTO();
        hkCustInfo.setReturnCode(OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS);
        PowerMockito.when(hkCustInfoOuterService.getHkCustInfo(Mockito.anyString())).thenReturn(hkCustInfo);

        // 执行测试 - 应抛出异常
        hwOverseaReportService.queryHwOverseaReport(request);
    }
    
    /**
     * 测试文件类型为不支持的格式
     * 应过滤掉不支持的文件格式
     */
    @Test
    public void testQueryHwOverseaReport_unsupported_file_type() {
        // 准备请求参数
        HwOverseaReportRequest request = new HwOverseaReportRequest();
        request.setHkCustNo("HK123456");
        
        // Mock客户信息
        HkCustInfoDTO hkCustInfo = new HkCustInfoDTO();
        hkCustInfo.setInvestorQualification("PRO");
        PowerMockito.when(hkCustInfoOuterService.getHkCustInfo(Mockito.anyString())).thenReturn(hkCustInfo);
        
        // Mock财富配置信息 - 设置不支持的文件格式
        QueryProductWealthAppointmentConfigDTO configDTO = new QueryProductWealthAppointmentConfigDTO();
        configDTO.setWealthAllocationReport("财富分配报告.doc"); // 不支持的格式
        configDTO.setWealthAllocationReportUrl("http://example.com/wealth.doc");
        configDTO.setAppointmentCalendarSummary("预约日历摘要.pdf");
        configDTO.setAppointmentCalendarSummaryUrl("http://example.com/calendar.pdf");
        PowerMockito.when(queryLatestProductWealthOuterService.queryLatestProductWealthAppointmentConfig(Mockito.anyString())).thenReturn(configDTO);

        // 执行测试
        HwOverseaReportListVO result = hwOverseaReportService.queryHwOverseaReport(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getOverseaReportList());
        Assert.assertEquals(1, result.getOverseaReportList().size()); // 只有一个有效报告
        Assert.assertEquals("预约日历摘要", result.getOverseaReportList().get(0).getFileName());
    }
}
