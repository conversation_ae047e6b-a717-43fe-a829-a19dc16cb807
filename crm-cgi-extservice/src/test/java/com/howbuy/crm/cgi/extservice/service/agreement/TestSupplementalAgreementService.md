# 补充协议服务单元测试说明文档

## 测试类介绍
- 类名：TestSupplementalAgreementService
- 测试目标：SupplementalAgreementService
- 主要功能：测试补充协议列表查询功能

## 测试环境准备
1. 继承TestBase基类
2. 使用@InjectMocks注入被测试类
3. 使用@Mock模拟外部依赖：
   - supplementalAgreementOuterService: 补充协议外部服务
   - queryFundBasicInfoOuterService: 基金信息查询服务

## 测试用例说明

### 1. testQuerySupplementalAgreementList_empty
- 测试场景：当没有未签署的补充协议时
- 测试目的：验证空数据场景的处理
- 测试步骤：
  1. 准备请求参数(香港客户号)
  2. Mock补充协议服务返回空列表
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回对象不为空
  - 协议列表为空

### 2. testQuerySupplementalAgreementList_normal
- 测试场景：正常场景，有未签署的补充协议且能查询到对应的基金信息
- 测试目的：验证正常业务流程
- 测试步骤：
  1. 准备请求参数
  2. Mock补充协议数据(包含基金代码和协议名称)
  3. Mock基金信息数据(包含基金代码和基金名称)
  4. 执行查询方法
  5. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 基金代码正确("000001")
  - 基金名称正确("测试基金")

### 3. testQuerySupplementalAgreementList_no_fund_info
- 测试场景：异常场景，有未签署的补充协议但查询不到对应的基金信息
- 测试目的：验证基金信息缺失场景的处理
- 测试步骤：
  1. 准备请求参数
  2. Mock补充协议数据
  3. Mock基金信息服务返回空列表
  4. 执行查询方法
  5. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 基金代码正确("000001")
  - 基金名称为null

### 4. testQuerySupplementalAgreementDetail_empty
- 测试场景：当没有未签署的补充协议时
- 测试目的：验证空数据场景的处理
- 测试步骤：
  1. 准备请求参数(香港客户号和基金代码)
  2. Mock补充协议服务返回空列表
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回对象不为空
  - 协议列表为空

### 5. testQuerySupplementalAgreementDetail_normal
- 测试场景：正常场景，有未签署的补充协议，需要按截止时间倒序排序
- 测试目的：验证正常业务流程和排序逻辑
- 测试步骤：
  1. 准备请求参数
  2. Mock补充协议数据(包含两个协议，截止时间不同)
  3. 执行查询方法
  4. 验证返回结果
- 验证点：
  - 返回列表不为空
  - 列表大小为2
  - 第一条数据的截止时间晚于第二条(验证倒序排序)

## Mock数据说明

### 补充协议数据(SupplementalAgreementDTO)
```java
SupplementalAgreementDTO dto = new SupplementalAgreementDTO();
dto.setFundCode("000001");
dto.setAgreementName("测试协议");
dto.setAgreementSignEndDt("2024-03-01 10:00");
```

### 基金信息数据(FundBasicInfoDTO)
```java
FundBasicInfoDTO fundInfo = new FundBasicInfoDTO();
fundInfo.setFundCode("000001");
fundInfo.setFundName("测试基金");
```

## 注意事项
1. 所有测试用例都包含了正向和异常场景
2. 使用PowerMockito.when()方法模拟外部服务调用
3. 使用Assert进行结果验证
4. 测试用例命名规范：test + 方法名 + 场景描述
5. 每个测试用例都有清晰的注释说明测试目的

## 相关文件
- SupplementalAgreementService.java: 被测试的服务类
- SupplementalAgreementDTO.java: 补充协议数据传输对象
- SupplementalAgreementListVO.java: 接口返回对象
- TestBase.java: 测试基类 