package com.howbuy.crm.cgi.extservice.service.agreement;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementSubmitRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementSubmitVO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import com.howbuy.crm.cgi.manager.outerservice.hkacc.HkCustInfoOuterService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

/**
 * <AUTHOR>
 * @description: 补充协议提交单元测试
 * @date 2024/3/6 18:20
 * @since JDK 1.8
 */
public class TestSubmitSupplementalAgreement extends TestBase {

    @InjectMocks
    private SupplementalAgreementService supplementalAgreementService = new SupplementalAgreementService();

    @Mock
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    @Mock
    private HkCustInfoOuterService hkCustInfoOuterService;

    /**
     * @description: 正常场景，提交成功
     */
    @Test
    public void testSubmitSupplementalAgreement_success() {
        // 准备测试数据
        SupplementalAgreementSubmitRequest request = new SupplementalAgreementSubmitRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");
        request.setTxPassword("123456");

        // Mock交易密码验证
        PowerMockito.doNothing().when(hkCustInfoOuterService).validateHkTradePassword(
            Mockito.anyString(), 
            Mockito.anyString()
        );

        // Mock协议提交
        PowerMockito.doNothing().when(supplementalAgreementOuterService).submitSupplementalAgreement(
            Mockito.anyString(),
            Mockito.anyString(),
            Mockito.any()
        );

        // 执行测试
        SupplementalAgreementSubmitVO result = supplementalAgreementService.submitSupplementalAgreement(request);

        // 验证结果
        Assert.assertNotNull(result);
        
        // 验证方法调用
        Mockito.verify(hkCustInfoOuterService).validateHkTradePassword(request.getHkCustNo(), request.getTxPassword());
        Mockito.verify(supplementalAgreementOuterService).submitSupplementalAgreement(
            request.getHkCustNo(), 
            request.getFundCode(),null
        );
    }

    /**
     * @description: 异常场景，交易密码验证失败
     */
    @Test(expected = BusinessException.class)
    public void testSubmitSupplementalAgreement_password_error() {
        // 准备测试数据
        SupplementalAgreementSubmitRequest request = new SupplementalAgreementSubmitRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");
        request.setTxPassword("123456");

        // Mock交易密码验证失败
        PowerMockito.doThrow(new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "交易密码错误"))
            .when(hkCustInfoOuterService)
            .validateHkTradePassword(Mockito.anyString(), Mockito.anyString());

        // 执行测试，预期抛出异常
        supplementalAgreementService.submitSupplementalAgreement(request);
    }

    /**
     * @description: 异常场景，协议提交失败
     */
    @Test(expected = BusinessException.class)
    public void testSubmitSupplementalAgreement_submit_error() {
        // 准备测试数据
        SupplementalAgreementSubmitRequest request = new SupplementalAgreementSubmitRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");
        request.setTxPassword("123456");

        // Mock交易密码验证成功
        PowerMockito.doNothing().when(hkCustInfoOuterService).validateHkTradePassword(
            Mockito.anyString(), 
            Mockito.anyString()
        );

        // Mock协议提交失败
        PowerMockito.doThrow(new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "协议提交失败"))
            .when(supplementalAgreementOuterService)
            .submitSupplementalAgreement(Mockito.anyString(), Mockito.anyString(), Mockito.any());

        // 执行测试，预期抛出异常
        supplementalAgreementService.submitSupplementalAgreement(request);
    }
} 