package com.howbuy.crm.cgi.extservice.service.agreement;

import com.howbuy.crm.cgi.extservice.common.enums.agreement.SupplementalAgreementSignStatusEnum;
import com.howbuy.crm.cgi.extservice.request.agreement.SupplementalAgreementDetailRequest;
import com.howbuy.crm.cgi.extservice.service.TestBase;
import com.howbuy.crm.cgi.extservice.vo.agreement.SupplementalAgreementDetailVO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement.SupplementalAgreementOuterService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 补充协议详情查询单元测试
 * @date 2024/3/6 18:20
 * @since JDK 1.8
 */
public class TestQuerySupplementalAgreementDetail extends TestBase {

    @InjectMocks
    private SupplementalAgreementService supplementalAgreementService = new SupplementalAgreementService();

    @Mock
    private SupplementalAgreementOuterService supplementalAgreementOuterService;

    /**
     * @description: 当没有未签署的补充协议时，返回空列表
     */
    @Test
    public void testQuerySupplementalAgreementDetail_empty() {
        // 准备测试数据
        SupplementalAgreementDetailRequest request = new SupplementalAgreementDetailRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");

        // Mock外部服务返回空列表
        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementDetail(
            Mockito.anyString(), 
            Mockito.anyString(),
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode())
        )).thenReturn(new ArrayList<>());

        // 执行测试
        SupplementalAgreementDetailVO result = supplementalAgreementService.querySupplementalAgreementDetail(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(CollectionUtils.isEmpty(result.getSupplementalAgreementDetailDtlList()));
    }

    /**
     * @description: 正常场景，有未签署的补充协议，按截止时间倒序排序
     */
    @Test
    public void testQuerySupplementalAgreementDetail_normal() {
        // 准备测试数据
        SupplementalAgreementDetailRequest request = new SupplementalAgreementDetailRequest();
        request.setHkCustNo("HK123456");
        request.setFundCode("000001");

        // Mock补充协议数据
        List<SupplementalAgreementDTO> agreementDTOS = new ArrayList<>();
        
        SupplementalAgreementDTO dto1 = new SupplementalAgreementDTO();
        dto1.setFundCode("000001");
        dto1.setAgreementName("测试协议1");
        dto1.setAgreementSignEndDt("2024-03-01 10:00");
        agreementDTOS.add(dto1);

        SupplementalAgreementDTO dto2 = new SupplementalAgreementDTO();
        dto2.setFundCode("000001");
        dto2.setAgreementName("测试协议2");
        dto2.setAgreementSignEndDt("2024-03-02 10:00");
        agreementDTOS.add(dto2);

        PowerMockito.when(supplementalAgreementOuterService.querySupplementalAgreementDetail(
            Mockito.anyString(), 
            Mockito.anyString(),
            Mockito.eq(SupplementalAgreementSignStatusEnum.UNSIGNED.getCode())
        )).thenReturn(agreementDTOS);

        // 执行测试
        SupplementalAgreementDetailVO result = supplementalAgreementService.querySupplementalAgreementDetail(request);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getSupplementalAgreementDetailDtlList()));
        Assert.assertEquals(2, result.getSupplementalAgreementDetailDtlList().size());
        // 验证排序是否正确（按截止时间倒序）
        Assert.assertEquals("2024-03-02 10:00", result.getSupplementalAgreementDetailDtlList().get(0).getAgreementSignEndDt());
        Assert.assertEquals("2024-03-01 10:00", result.getSupplementalAgreementDetailDtlList().get(1).getAgreementSignEndDt());
    }
} 