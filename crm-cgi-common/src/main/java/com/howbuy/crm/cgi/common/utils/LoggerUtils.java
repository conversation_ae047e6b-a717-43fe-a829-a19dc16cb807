/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.crm.cgi.common.utils;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;

import java.util.UUID;

/**
 * @description:日志工具类
 * @reason:日志工具类
 * <AUTHOR>
 * @date 2023-3-8 下午3:42:56
 * @since JDK 1.8
 */
public class LoggerUtils {

    private LoggerUtils() {
        throw new IllegalStateException("Utility class");
    }

    private static final String DEFAULT = "default";
    public static final String CTX_UUID = "traceId";
    public static final String CTX_TXCODE = "txCode";
    public static final String CTX_RANNO = "ranNo";
    

    /**
     * @description: 设置随机号
     * @param ranNo 随机号
     * @return void
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static void setRanNo(String ranNo) {
        ThreadContext.put(CTX_RANNO, ranNo);
    }
 
    /**
     * @description: 获取随机号
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static String getRanNo() {
        return ThreadContext.get(CTX_RANNO);
    }

    /**
     * @description: 设置请求ID
     * @param reqId 请求ID
     * @return void
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static void setReqId(String reqId) {
        ThreadContext.put(CTX_UUID, reqId);
    }
 
    /**
     * @description: 获取请求ID
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static String getReqId() {
        return ThreadContext.get(CTX_UUID);
    }

    /**
     * setConfig:log4j2 ctx 配置属性设置
     * 
     * @param uuid
     * @param txCode
     * <AUTHOR>
     * @date 2016-9-15 下午3:31:27
     */
    public static void setConfig(String uuid, String txCode) {
        if (uuid != null) {
            ThreadContext.put(CTX_UUID, uuid);
        } else {
            ThreadContext.put(CTX_UUID, DEFAULT);
        }

        if (txCode != null) {
            ThreadContext.put(CTX_TXCODE, txCode);
        } else {
            ThreadContext.put(CTX_TXCODE, DEFAULT);
        }
    }

    /**
     * 
     * setUUID:设置UUID
     * 
     * @return void
     * <AUTHOR>
     * @date 2016年10月26日 上午10:51:05
     */
    public static void setUUID() {
        String uuid = UUID.randomUUID().toString();
        ThreadContext.put(CTX_UUID, uuid);
    }

    /**
     * getCfgValue:(获取ThreadContext config 配置的值)
     * 
     * @param key
     * @return String
     * <AUTHOR>
     * @date 2016年9月29日 下午6:51:18
     */
    public static String getCfgValue(String key) {
        return ThreadContext.get(key);
    }
    
    /**
     * setCfgValue:(设置ThreadContext值)
     * @param key
     * @return String
     */
    public static void setCfgValue(String key, String value) {
        ThreadContext.put(key, value);
    }

    /**
     * getUuid:(获取uuid)
     * 
     * @return String
     * <AUTHOR>
     * @date 2016年9月29日 下午8:08:49
     */
    public static String getUuid() {
        return ThreadContext.get(CTX_UUID);
    }

    /**
     * 
     * clearConfig:清空配置
     * 
     * @return void
     * <AUTHOR>
     * @date 2016年10月9日 下午6:46:24
     */
    public static void clearConfig() {
        ThreadContext.clearAll();
    }

    /**
     * @description: 设置子UUID
     * @param uuid 父线程UUID
     * @return void
     * @author: hongdong.xie
     * @date: 2020/11/3 11:12
     * @since JDK 1.8
     */
    public static void setChildUUID(String uuid) {
        // 如果参数UUID为空则自动生成一个新的UUID
        if(StringUtils.isEmpty(uuid)){
            uuid = UUID.randomUUID().toString();
        }

        //设置子uuid，规则为父UUID+3位随机字符串
        uuid = uuid + "_" + RandomStringUtils.randomAlphanumeric(3).toLowerCase();
        ThreadContext.put(CTX_UUID, uuid);
    }

    /**
     * @description: 获取随机号，规则为7位随机数字
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025-04-02 20:50:47
     * @since JDK 1.8
     */
    public static String createRanNo() {
        //生成7位随机数字
        return RandomStringUtils.randomNumeric(7);
    }
}