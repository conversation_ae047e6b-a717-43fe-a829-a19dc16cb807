/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * @description: (验证码的类型)
 * <AUTHOR>
 * @date 2023/12/11 14:16
 * @since JDK 1.8
 */
public enum VerifyCodeTypeEnum {

    /**
     * 登录短信验证码
     */
    LOGIN_MSG_VERIFY_CODE("01", "登录短信验证码",MessageTempleteEnum.LOGIN_MOBILE_VERIFYCODE),
    /**
     * 手机验证短信验证码
     */
    MOBILE_MSG_VERIFY_CODE("02", "手机验证短信验证码",MessageTempleteEnum.MOBILE_VERIFY_VERIFYCODE),
    /**
     * 重置登录密码短信验证码
     */
    RESET_LOGIN_PASSWORD_MOBILE_VERIFY_CODE("03", "重置登录密码短信验证码",MessageTempleteEnum.RESET_LOGIN_PASSWORD_MOBILE_VERIFYCODE),
    /**
     * 交易账户激活短信验证码
     */
    TRADE_ACTIVATE_MOBILE_VERIFY_CODE("04", "交易账户激活邮箱验证码",MessageTempleteEnum.TRADE_ACTIVATE_MOBILE_VERIFYCODE),
    /**
     * 登录激活手机验证码
     */
    LOGIN_ACTIVATE_MOBILE_VERIFYCODE("05", "登录账户激活短信验证码",MessageTempleteEnum.LOGIN_ACTIVATE_MOBILE_VERIFYCODE),
    /**
     * 绑定一账通香港手机号短信验证码
     */
    BIND_HK_MOBILE_VERIFY_CODE("06", "绑定一账通",MessageTempleteEnum.BIND_HK_MOBILE_VERIFYCODE),
    /**
     * 解绑一账通香港手机号短信验证码
     */
    UNBIND_HK_MOBILE_VERIFY_CODE("07", "解绑一账通",MessageTempleteEnum.UNBIND_HK_MOBILE_VERIFYCODE),
    /**
     * 解绑一账通好买手机号短信验证码
     */
    UNBIND_HBONE_MOBILE_VERIFY_CODE("08", "解绑一账通",MessageTempleteEnum.UNBIND_HB_MOBILE_VERIFYCODE),
    /**
     * 重置交易密码短信验证码
     */
    RESET_TRADE_PASSWORD_MOBILE_VERIFY_CODE("09", "重置交易密码短信验证码",MessageTempleteEnum.RESET_TRADE_PASSWORD_MOBILE_VERIFYCODE),
    /**
     * 设置交易密码短信验证码
     */
    SET_TRADE_PASSWORD_MOBILE_VERIFY_CODE("10", "设置交易密码短信验证码",MessageTempleteEnum.SET_TRADE_PASSWORD_MOBILE_VERIFYCODE),
    /**
     * 绑定一账通好买手机号短信验证码
     */
    BIND_HBONE_MOBILE_VERIFY_CODE("11", "绑定一账通",MessageTempleteEnum.BIND_HB_MOBILE_VERIFYCODE),
    /**
     * 修改手机号短信验证码
     */
    EDIT_MOBILE_VERIFY_CODE("12", "修改手机号",MessageTempleteEnum.EDIT_MOBILE_VERIFYCODE),
    /**
     * 绑定手机号短信验证码
     */
    BIND_MOBILE_VERIFY_CODE("13", "绑定手机号",MessageTempleteEnum.BIND_MOBILE_VERIFYCODE),
    /**
     * 手机号 填写开户资料
     */
    MOBILE_WRITE_OPEN_FILE_CODE("14", "填写开户资料",MessageTempleteEnum.MOBILE_WRITE_OPEN_FILE),

    /**
     * 海外基金购买页短信验证码
     */
    HK_FUND_PURCHASE_VERIFY_CODE("15", "申请购买海外产品",MessageTempleteEnum.HK_FUND_PURCHASE_VERIFY_CODE),

    HK_FUND_REDEEM_VERIFY_CODE("16", "申请赎回海外产品",MessageTempleteEnum.HK_FUND_REDEEM_VERIFY_CODE),

    /**
     * 重置交易密码邮箱验证码
     */
    RESET_TRADE_PASSWORD_EMAIL_VERIFY_CODE("52", "重置交易密码邮箱验证码",MessageTempleteEnum.RESET_TRADE_PASSWORD_EMAIL_VERIFYCODE),

    /**
     * 重置登录密码邮箱验证码
     */
    RESET_LOGIN_PASSWORD_EMAIL_VERIFY_CODE("51", "重置登录密码邮箱验证码",MessageTempleteEnum.RESET_LOGIN_PASSWORD_EMAIL_VERIFYCODE),

    /**
     * 设置交易密码邮箱验证码
     */
    SET_TRADE_PASSWORD_EMAIL_VERIFY_CODE("53", "设置交易密码邮箱验证码",MessageTempleteEnum.SET_TRADE_PASSWORD_EMAIL_VERIFYCODE),
    /**
     * 修改邮箱邮箱验证码
     */
    EDIT_EMAIL_VERIFY_CODE("54", "修改邮箱邮箱验证码",MessageTempleteEnum.EDIT_EMAIL_VERIFYCODE),
    /**
     *绑定邮箱邮箱验证码
     */
    BIND_EMAIL_VERIFY_CODE("55", "绑定邮箱邮箱验证码",MessageTempleteEnum.BIND_EMAIL_VERIFYCODE),
    /**
     * 绑定一账通香港邮箱验证码
     */
    BIND_HK_EMAIL_VERIFY_CODE("56", "绑定一账通香港邮箱验证码",MessageTempleteEnum.BIND_HK_EMAIL_VERIFYCODE),
    /**
     * 解绑一账通香港邮箱验证码
     */
    UNBIND_HK_EMAIL_VERIFY_CODE("57", "解绑一账通香港邮箱验证码",MessageTempleteEnum.UNBIND_HK_EMAIL_VERIFYCODE),
    /**
     * 激活交易账号邮箱验证码
     */
    TRADE_ACTIVATE_EMAIL_VERIFY_CODE("58", "激活交易账号邮箱验证码",MessageTempleteEnum.TRADE_ACTIVATE_EMAIL_VERIFYCODE),
    /**
     * 邮箱摘要验证码接口邮箱验证码
     */
    EMAIL_DIGEST_VERIFY_CODE("59", "登录激活的邮箱验证码",MessageTempleteEnum.LOGIN_ACTIVATE_EMAIL_VERIFYCODE),
    /**
     * 手机号 填写开户资料
     */
    EMAIL_WRITE_OPEN_FILE_CODE("60", "填写开户资料",MessageTempleteEnum.EMAIL_WRITE_OPEN_FILE),

    /**
     * 海外基金购买页邮箱验证码
     */
    HK_FUND_PURCHASE_EMAIL_VERIFY_CODE("61", "申请购买海外产品",MessageTempleteEnum.HK_FUND_PURCHASE_EMAIL_VERIFY_CODE),


    HK_FUND_REDEEM_EMAIL_VERIFY_CODE("62", "申请赎回海外产品",MessageTempleteEnum.HK_FUND_REDEEM_EMAIL_VERIFY_CODE),

    /**
     * 买入语音验证码
     */
    BUY_VOICE_CODE("63", "买入语音验证码",MessageTempleteEnum.BUY_VOICE_CODE),

    /**
     * 卖出语音验证码
     */
    SELL_VOICE_CODE("64", "卖出语音验证码",MessageTempleteEnum.SELL_VOICE_CODE),

    ;



    private String code;
    private String name;
    private MessageTempleteEnum messageTempleteEnum;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public MessageTempleteEnum getMessageTempleteEnum() {
        return messageTempleteEnum;
    }

    public void setMessageTempleteEnum(MessageTempleteEnum messageTempleteEnum) {
        this.messageTempleteEnum = messageTempleteEnum;
    }

    VerifyCodeTypeEnum(String code, String name, MessageTempleteEnum messageTempleteEnum) {
        this.code = code;
        this.name = name;
        this.messageTempleteEnum = messageTempleteEnum;
    }

    /**
     * 根据传入的code值匹配到对应的枚举
     */
    public static MessageTempleteEnum getVerifyCodeTypeEnumByCode(String code) {
        for (VerifyCodeTypeEnum verifyCodeTypeEnum : VerifyCodeTypeEnum.values()) {
            if (verifyCodeTypeEnum.getCode().equals(code)) {
                return verifyCodeTypeEnum.getMessageTempleteEnum();
            }
        }
        return null;
    }



}