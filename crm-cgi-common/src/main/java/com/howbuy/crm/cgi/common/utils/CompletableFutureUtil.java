package com.howbuy.crm.cgi.common.utils;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description: CompletableFuture工具类
 * @date 2023/6/16 13:05
 * @since JDK 1.8
 */
public class CompletableFutureUtil {

    private CompletableFutureUtil(){}

    /**
     * 核心线程数
     */
    private static int coreSize = 8;
    /**
     * 阻塞队列容量
     */
    private static int queueSize = 1;
    /**
     * 最大线程数
     */
    private static int maximumSize = 32;

    /**
     * 默认线程池
     */
    private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(coreSize,
            maximumSize,
            1,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<Runnable>(queueSize),
            new ThreadPoolExecutor.CallerRunsPolicy());


    /**
     * @description 并发执行并聚合
     * @param runnables
     * @return void
     * @author: jianjian.yang
     * @date: 2023/6/16 13:18
     * @since JDK 1.8
     */
    public static void runnableFutureJoin(Runnable... runnables){
        CompletableFuture[] futures = new CompletableFuture[runnables.length];
        int index = 0;
        for(Runnable runnable : runnables){
            //执行
            CompletableFuture<Void> future = CompletableFuture.runAsync(runnable, threadPoolExecutor);
            futures[index] = future;
            index++;
        }
        //线程聚合
        CompletableFuture.allOf(futures).join();
    }
}
