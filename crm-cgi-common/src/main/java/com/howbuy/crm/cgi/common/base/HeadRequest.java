/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.base;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.howbuy.crm.cgi.common.utils.CommonRequestUtil.getParameter;

/**
 * @description: 请求头类
 * <AUTHOR>
 * @date 2023/3/13 17:22
 * @since JDK 1.8
 */
public class HeadRequest {

    private static final Logger log =  LoggerFactory.getLogger(HeadRequest.class);

    /**
     * token
     */
    private String token;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * @param
     * @return javax.servlet.http.HttpServletRequest
     * @description: 获取HttpServletRequest
     * @author: hongdong.xie
     * @date: 2023/5/17 15:22
     * @since JDK 1.8
     */
    public static HttpServletRequest getHttpRequest() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("RequestAttributes is null!");
            return null;
        }
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) attributes;
        return requestAttributes.getRequest();
    }

    /**
     * 获取请求头信息
     * @return
     */
    public static String getRequestHeader(String key) {
        HttpServletRequest request = getHttpRequest();
        if(null == request){
            return null;
        }
        return request.getHeader(key);
    }

    /**
     * @param
     * @return boolean
     * @description: 判断是否是海外App的请求
     * @author: jinqing.rao
     * @date: 2024/2/29 18:51
     * @since JDK 1.8
     */
    public static boolean isHkAppLogin() {
        String httpParameter = getHttpParameter(Constants.PRODUCT_ID);
        return StringUtils.isNotEmpty(httpParameter) && Constants.PRODUCT_ID_VALUE.equals(httpParameter);
    }

    /**
     * @param name 参数名字
     * @return java.lang.String
     * @description: 获取请求参数：放在request.params中，且未加密
     * @author: hongdong.xie
     * @date: 2023/5/17 15:20
     * @since JDK 1.8
     */
    public static String getHttpParameter(String name) {
        HttpServletRequest httpRequest = getHttpRequest();
        if(null == httpRequest){
           return null;
        }
        return httpRequest.getParameter(name);
    }
}