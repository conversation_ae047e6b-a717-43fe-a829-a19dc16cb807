package com.howbuy.crm.cgi.common.dict;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 国籍字典
 * @date 2023/6/6 19:21
 * @since JDK 1.8
 */
public class NationalityDict {

    private static final Map<String, String> NATIONALITY_MAP = new HashMap<>();

    /**
     * @description: 根据国籍代码获取国籍描述
     * @param: [nationalityCode]
     * @return: java.lang.String
     * @author: shaoyang.li
     * @date: 2023/6/6 19:30
     * @since JDK 1.8
     */
    public static String getValue(String nationalityCode) {
        return NATIONALITY_MAP.get(nationalityCode);
    }


    static {
        NATIONALITY_MAP.put("CN", "中国内地");
        NATIONALITY_MAP.put("AD", "安道尔");
        NATIONALITY_MAP.put("AE", "阿联酋");
        NATIONALITY_MAP.put("AF", "阿富汗");
        NATIONALITY_MAP.put("AG", "安提瓜和巴布达");
        NATIONALITY_MAP.put("AI", "安圭拉");
        NATIONALITY_MAP.put("AL", "阿尔巴尼亚");
        NATIONALITY_MAP.put("AM", "亚美尼亚");
        NATIONALITY_MAP.put("AO", "安哥拉");
        NATIONALITY_MAP.put("AQ", "南极洲");
        NATIONALITY_MAP.put("AR", "阿根廷");
        NATIONALITY_MAP.put("AS", "美属萨摩亚");
        NATIONALITY_MAP.put("AT", "奥地利");
        NATIONALITY_MAP.put("AU", "澳大利亚");
        NATIONALITY_MAP.put("AW", "阿鲁巴");
        NATIONALITY_MAP.put("AX", "奥兰群岛");
        NATIONALITY_MAP.put("AZ", "阿塞拜疆");
        NATIONALITY_MAP.put("BA", "波黑");
        NATIONALITY_MAP.put("BB", "巴巴多斯");
        NATIONALITY_MAP.put("BD", "孟加拉");
        NATIONALITY_MAP.put("BE", "比利时");
        NATIONALITY_MAP.put("BF", "布基纳法索");
        NATIONALITY_MAP.put("BG", "保加利亚");
        NATIONALITY_MAP.put("BH", "巴林");
        NATIONALITY_MAP.put("BI", "布隆迪");
        NATIONALITY_MAP.put("BJ", "贝宁");
        NATIONALITY_MAP.put("BL", "圣巴泰勒米岛");
        NATIONALITY_MAP.put("BM", "百慕大");
        NATIONALITY_MAP.put("BN", "文莱");
        NATIONALITY_MAP.put("BO", "玻利维亚");
        NATIONALITY_MAP.put("BQ", "荷兰加勒比区");
        NATIONALITY_MAP.put("BR", "巴西");
        NATIONALITY_MAP.put("BS", "巴哈马");
        NATIONALITY_MAP.put("BT", "不丹");
        NATIONALITY_MAP.put("BV", "布韦岛");
        NATIONALITY_MAP.put("BW", "博茨瓦纳");
        NATIONALITY_MAP.put("BY", "白俄罗斯");
        NATIONALITY_MAP.put("BZ", "伯利兹");
        NATIONALITY_MAP.put("CA", "加拿大");
        NATIONALITY_MAP.put("CC", "科科斯群岛");
        NATIONALITY_MAP.put("CD", "刚果（金）");
        NATIONALITY_MAP.put("CF", "中非");
        NATIONALITY_MAP.put("CG", "刚果（布）");
        NATIONALITY_MAP.put("CH", "瑞士");
        NATIONALITY_MAP.put("CI", "科特迪瓦");
        NATIONALITY_MAP.put("CK", "库克群岛");
        NATIONALITY_MAP.put("CL", "智利");
        NATIONALITY_MAP.put("CM", "喀麦隆");
        NATIONALITY_MAP.put("HK", "中国香港");
        NATIONALITY_MAP.put("MO", "中国澳门");
        NATIONALITY_MAP.put("TW", "中国台湾");
        NATIONALITY_MAP.put("CO", "哥伦比亚");
        NATIONALITY_MAP.put("CR", "哥斯达黎加");
        NATIONALITY_MAP.put("CU", "古巴");
        NATIONALITY_MAP.put("CV", "佛得角");
        NATIONALITY_MAP.put("CX", "圣诞岛");
        NATIONALITY_MAP.put("CY", "塞浦路斯");
        NATIONALITY_MAP.put("CZ", "捷克");
        NATIONALITY_MAP.put("DE", "德国");
        NATIONALITY_MAP.put("DJ", "吉布提");
        NATIONALITY_MAP.put("DK", "丹麦");
        NATIONALITY_MAP.put("DM", "多米尼克");
        NATIONALITY_MAP.put("DO", "多米尼加");
        NATIONALITY_MAP.put("DZ", "阿尔及利亚");
        NATIONALITY_MAP.put("EC", "厄瓜多尔");
        NATIONALITY_MAP.put("EE", "爱沙尼亚");
        NATIONALITY_MAP.put("EG", "埃及");
        NATIONALITY_MAP.put("EH", "西撒哈拉");
        NATIONALITY_MAP.put("ER", "厄立特里亚");
        NATIONALITY_MAP.put("ES", "西班牙");
        NATIONALITY_MAP.put("ET", "埃塞俄比亚");
        NATIONALITY_MAP.put("FI", "芬兰");
        NATIONALITY_MAP.put("FJ", "斐济群岛");
        NATIONALITY_MAP.put("FK", "马尔维纳斯群岛（福克兰）");
        NATIONALITY_MAP.put("FM", "密克罗尼西亚联邦");
        NATIONALITY_MAP.put("FO", "法罗群岛");
        NATIONALITY_MAP.put("FR", "法国");
        NATIONALITY_MAP.put("GA", "加蓬");
        NATIONALITY_MAP.put("GB", "英国");
        NATIONALITY_MAP.put("GD", "格林纳达");
        NATIONALITY_MAP.put("GE", "格鲁吉亚");
        NATIONALITY_MAP.put("GF", "法属圭亚那");
        NATIONALITY_MAP.put("GG", "根西岛");
        NATIONALITY_MAP.put("GH", "加纳");
        NATIONALITY_MAP.put("GI", "直布罗陀");
        NATIONALITY_MAP.put("GL", "格陵兰");
        NATIONALITY_MAP.put("GM", "冈比亚");
        NATIONALITY_MAP.put("GN", "几内亚");
        NATIONALITY_MAP.put("GP", "瓜德罗普");
        NATIONALITY_MAP.put("GQ", "赤道几内亚");
        NATIONALITY_MAP.put("GR", "希腊");
        NATIONALITY_MAP.put("GS", "南乔治亚岛和南桑威奇群岛");
        NATIONALITY_MAP.put("GT", "危地马拉");
        NATIONALITY_MAP.put("GU", "关岛");
        NATIONALITY_MAP.put("GW", "几内亚比绍");
        NATIONALITY_MAP.put("GY", "圭亚那");
        NATIONALITY_MAP.put("HM", "赫德岛和麦克唐纳群岛");
        NATIONALITY_MAP.put("HN", "洪都拉斯");
        NATIONALITY_MAP.put("HR", "克罗地亚");
        NATIONALITY_MAP.put("HT", "海地");
        NATIONALITY_MAP.put("HU", "匈牙利");
        NATIONALITY_MAP.put("ID", "印尼");
        NATIONALITY_MAP.put("IE", "爱尔兰");
        NATIONALITY_MAP.put("IL", "以色列");
        NATIONALITY_MAP.put("IM", "马恩岛");
        NATIONALITY_MAP.put("IN", "印度");
        NATIONALITY_MAP.put("IO", "英属印度洋领地");
        NATIONALITY_MAP.put("IQ", "伊拉克");
        NATIONALITY_MAP.put("IR", "伊朗");
        NATIONALITY_MAP.put("IS", "冰岛");
        NATIONALITY_MAP.put("IT", "意大利");
        NATIONALITY_MAP.put("JE", "泽西岛");
        NATIONALITY_MAP.put("JM", "牙买加");
        NATIONALITY_MAP.put("JO", "约旦");
        NATIONALITY_MAP.put("JP", "日本");
        NATIONALITY_MAP.put("KE", "肯尼亚");
        NATIONALITY_MAP.put("KG", "吉尔吉斯斯坦");
        NATIONALITY_MAP.put("KH", "柬埔寨");
        NATIONALITY_MAP.put("KI", "基里巴斯");
        NATIONALITY_MAP.put("KM", "科摩罗");
        NATIONALITY_MAP.put("KN", "圣基茨和尼维斯");
        NATIONALITY_MAP.put("KP", "朝鲜");
        NATIONALITY_MAP.put("KR", "韩国");
        NATIONALITY_MAP.put("KW", "科威特");
        NATIONALITY_MAP.put("KY", "开曼群岛");
        NATIONALITY_MAP.put("KZ", "哈萨克斯坦");
        NATIONALITY_MAP.put("LA", "老挝");
        NATIONALITY_MAP.put("LB", "黎巴嫩");
        NATIONALITY_MAP.put("LC", "圣卢西亚");
        NATIONALITY_MAP.put("LI", "列支敦士登");
        NATIONALITY_MAP.put("LK", "斯里兰卡");
        NATIONALITY_MAP.put("LR", "利比里亚");
        NATIONALITY_MAP.put("LS", "莱索托");
        NATIONALITY_MAP.put("LT", "立陶宛");
        NATIONALITY_MAP.put("LU", "卢森堡");
        NATIONALITY_MAP.put("LV", "拉脱维亚");
        NATIONALITY_MAP.put("LY", "利比亚");
        NATIONALITY_MAP.put("MA", "摩洛哥");
        NATIONALITY_MAP.put("MC", "摩纳哥");
        NATIONALITY_MAP.put("MD", "摩尔多瓦");
        NATIONALITY_MAP.put("ME", "黑山");
        NATIONALITY_MAP.put("MF", "法属圣马丁");
        NATIONALITY_MAP.put("MG", "马达加斯加");
        NATIONALITY_MAP.put("MH", "马绍尔群岛");
        NATIONALITY_MAP.put("MK", "马其顿");
        NATIONALITY_MAP.put("ML", "马里");
        NATIONALITY_MAP.put("MM", "缅甸");
        NATIONALITY_MAP.put("MN", "蒙古");
        NATIONALITY_MAP.put("MP", "北马里亚纳群岛");
        NATIONALITY_MAP.put("MQ", "马提尼克");
        NATIONALITY_MAP.put("MR", "毛里塔尼亚");
        NATIONALITY_MAP.put("MS", "蒙塞拉特岛");
        NATIONALITY_MAP.put("MT", "马耳他");
        NATIONALITY_MAP.put("MU", "毛里求斯");
        NATIONALITY_MAP.put("MV", "马尔代夫");
        NATIONALITY_MAP.put("MW", "马拉维");
        NATIONALITY_MAP.put("MX", "墨西哥");
        NATIONALITY_MAP.put("MY", "马来西亚");
        NATIONALITY_MAP.put("MZ", "莫桑比克");
        NATIONALITY_MAP.put("NA", "纳米比亚");
        NATIONALITY_MAP.put("NC", "新喀里多尼亚");
        NATIONALITY_MAP.put("NE", "尼日尔");
        NATIONALITY_MAP.put("NF", "诺福克岛");
        NATIONALITY_MAP.put("NG", "尼日利亚");
        NATIONALITY_MAP.put("NI", "尼加拉瓜");
        NATIONALITY_MAP.put("NL", "荷兰");
        NATIONALITY_MAP.put("NO", "挪威");
        NATIONALITY_MAP.put("NP", "尼泊尔");
        NATIONALITY_MAP.put("NR", "瑙鲁");
        NATIONALITY_MAP.put("NU", "纽埃");
        NATIONALITY_MAP.put("NZ", "新西兰");
        NATIONALITY_MAP.put("OM", "阿曼");
        NATIONALITY_MAP.put("PA", "巴拿马");
        NATIONALITY_MAP.put("PE", "秘鲁");
        NATIONALITY_MAP.put("PF", "法属波利尼西亚");
        NATIONALITY_MAP.put("PG", "巴布亚新几内亚");
        NATIONALITY_MAP.put("PH", "菲律宾");
        NATIONALITY_MAP.put("PK", "巴基斯坦");
        NATIONALITY_MAP.put("PL", "波兰");
        NATIONALITY_MAP.put("PM", "圣皮埃尔和密克隆");
        NATIONALITY_MAP.put("PN", "皮特凯恩群岛");
        NATIONALITY_MAP.put("PR", "波多黎各");
        NATIONALITY_MAP.put("PS", "巴勒斯坦");
        NATIONALITY_MAP.put("PT", "葡萄牙");
        NATIONALITY_MAP.put("PW", "帕劳");
        NATIONALITY_MAP.put("PY", "巴拉圭");
        NATIONALITY_MAP.put("QA", "卡塔尔");
        NATIONALITY_MAP.put("RE", "留尼汪");
        NATIONALITY_MAP.put("RO", "罗马尼亚");
        NATIONALITY_MAP.put("RS", "塞尔维亚");
        NATIONALITY_MAP.put("RU", "俄罗斯");
        NATIONALITY_MAP.put("RW", "卢旺达");
        NATIONALITY_MAP.put("SA", "沙特阿拉伯");
        NATIONALITY_MAP.put("SB", "所罗门群岛");
        NATIONALITY_MAP.put("SC", "塞舌尔");
        NATIONALITY_MAP.put("SD", "苏丹");
        NATIONALITY_MAP.put("SE", "瑞典");
        NATIONALITY_MAP.put("SG", "新加坡");
        NATIONALITY_MAP.put("SH", "圣赫勒拿");
        NATIONALITY_MAP.put("SI", "斯洛文尼亚");
        NATIONALITY_MAP.put("SJ", "斯瓦尔巴群岛和扬马延岛");
        NATIONALITY_MAP.put("SK", "斯洛伐克");
        NATIONALITY_MAP.put("SL", "塞拉利昂");
        NATIONALITY_MAP.put("SM", "圣马力诺");
        NATIONALITY_MAP.put("SN", "塞内加尔");
        NATIONALITY_MAP.put("SO", "索马里");
        NATIONALITY_MAP.put("SR", "苏里南");
        NATIONALITY_MAP.put("SS", "南苏丹");
        NATIONALITY_MAP.put("ST", "圣多美和普林西比");
        NATIONALITY_MAP.put("SV", "萨尔瓦多");
        NATIONALITY_MAP.put("SY", "叙利亚");
        NATIONALITY_MAP.put("SZ", "斯威士兰");
        NATIONALITY_MAP.put("TC", "特克斯和凯科斯群岛");
        NATIONALITY_MAP.put("TD", "乍得");
        NATIONALITY_MAP.put("TF", "法属南部领地");
        NATIONALITY_MAP.put("TG", "多哥");
        NATIONALITY_MAP.put("TH", "泰国");
        NATIONALITY_MAP.put("TJ", "塔吉克斯坦");
        NATIONALITY_MAP.put("TK", "托克劳");
        NATIONALITY_MAP.put("TL", "东帝汶");
        NATIONALITY_MAP.put("TM", "土库曼斯坦");
        NATIONALITY_MAP.put("TN", "突尼斯");
        NATIONALITY_MAP.put("TO", "汤加");
        NATIONALITY_MAP.put("TR", "土耳其");
        NATIONALITY_MAP.put("TT", "特立尼达和多巴哥");
        NATIONALITY_MAP.put("TV", "图瓦卢");
        NATIONALITY_MAP.put("TZ", "坦桑尼亚");
        NATIONALITY_MAP.put("UA", "乌克兰");
        NATIONALITY_MAP.put("UG", "乌干达");
        NATIONALITY_MAP.put("UM", "美国本土外小岛屿");
        NATIONALITY_MAP.put("US", "美国");
        NATIONALITY_MAP.put("UY", "乌拉圭");
        NATIONALITY_MAP.put("UZ", "乌兹别克斯坦");
        NATIONALITY_MAP.put("VA", "梵蒂冈");
        NATIONALITY_MAP.put("VC", "圣文森特和格林纳丁斯");
        NATIONALITY_MAP.put("VE", "委内瑞拉");
        NATIONALITY_MAP.put("VG", "英属维尔京群岛");
        NATIONALITY_MAP.put("VI", "美属维尔京群岛");
        NATIONALITY_MAP.put("VN", "越南");
        NATIONALITY_MAP.put("VU", "瓦努阿图");
        NATIONALITY_MAP.put("WF", "瓦利斯和富图纳");
        NATIONALITY_MAP.put("WS", "萨摩亚");
        NATIONALITY_MAP.put("YE", "也门");
        NATIONALITY_MAP.put("YT", "马约特");
        NATIONALITY_MAP.put("ZA", "南非");
        NATIONALITY_MAP.put("ZM", "赞比亚");
        NATIONALITY_MAP.put("ZW", "津巴布韦");
    }
}
