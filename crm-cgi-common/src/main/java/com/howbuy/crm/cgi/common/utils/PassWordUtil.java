/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import com.howbuy.common.enums.EncryptAlgEnum;
import com.howbuy.common.security.ByteUtil;
import com.howbuy.common.security.EncryptUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 密码工具类
 * <AUTHOR>
 * @date 2023/6/8 09:32
 * @since JDK 1.8
 */
public class PassWordUtil {

    private PassWordUtil() {
    }

    /**
     * @description: 密码加密算法
     * @param password
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/6/8 09:39
     * @since JDK 1.8
     */
    public static String encrypt(String password) {
        if (!StringUtils.isEmpty(password)) {
            return EncryptUtil.getInstance(EncryptAlgEnum.Des).encMsg(ByteUtil.getBytes(password));
        }
        return password;

    }

    public static void main(String[] args) {
        System.out.println(encrypt("123457"));
    }

}