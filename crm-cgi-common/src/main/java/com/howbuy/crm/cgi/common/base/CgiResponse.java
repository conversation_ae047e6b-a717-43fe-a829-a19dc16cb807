/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.base;

import com.howbuy.crm.cgi.common.constants.Constants;
import com.howbuy.crm.cgi.common.enums.CommonResultEnum;
import com.howbuy.crm.cgi.common.utils.CommonRequestUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 统一接入返回参数封装
 * <AUTHOR>
 * @date 2023/3/13 17:21
 * @since JDK 1.8
 */
@Setter
@Getter
public class CgiResponse<B extends Body> {
    /**
     * 返回码
     */
    private String code;
    /**
     * 返回描述
     */
    private String description;
    /**
     * 返回内容
     */
    private B data;

    private String timestampServer = String.valueOf(System.currentTimeMillis());

    public CgiResponse(){
    }

    public CgiResponse(String code, String description, B data){
        this.code = code;
        this.description = description;
        this.data = data;
    }

    public static <B extends Body> CgiResponse<B> ok(B data){
        //返回成功状态码调整,C010000 调整到0000,目前App和H5都是0000,小程序版本大于1.0.0的都是0000,以前都是C010000
        String requestHeader = HeadRequest.getRequestHeader(Constants.WX_VERSION); // Constants.WX_VERSION  与2024/05/31已经删除,通过CommonRequestUtil.isWXLogin()判断
        if(StringUtils.isNotBlank(requestHeader) && requestHeader.compareTo("1.0.7") >= 0){
            return new CgiResponse<>(CommonResultEnum.GATEWAY_SUCCESS.getCode(), CommonResultEnum.SUCCESS.getDescription(), data);
        }
        if(HeadRequest.isHkAppLogin() || CommonRequestUtil.isH5Login()){
            return new CgiResponse<>(CommonResultEnum.GATEWAY_SUCCESS.getCode(), CommonResultEnum.SUCCESS.getDescription(), data);
        }
        // 小程序因为不能发版，默认都是C010000
        return new CgiResponse<>(CommonResultEnum.SUCCESS.getCode(), CommonResultEnum.SUCCESS.getDescription(), data);
    }

    public static <B extends Body> CgiResponse<B> ok(String description, B data) {
        //返回成功状态码调整,C010000 调整到0000,目前App和H5都是0000,小程序版本大于1.0.0的都是0000,以前都是C010000
        String requestHeader = HeadRequest.getRequestHeader(Constants.WX_VERSION);
        // TODO 暂时比较方法
        if (StringUtils.isNotBlank(requestHeader) && requestHeader.compareTo("1.0.7") >= 0) {
            return new CgiResponse<>(CommonResultEnum.GATEWAY_SUCCESS.getCode(), description, data);
        }
        if (HeadRequest.isHkAppLogin()) {
            return new CgiResponse<>(CommonResultEnum.GATEWAY_SUCCESS.getCode(), description, data);
        }
        return new CgiResponse<>(CommonResultEnum.SUCCESS.getCode(), CommonResultEnum.SUCCESS.getDescription(), data);
    }

    public static <B extends Body> CgiResponse<B> appOk(B data){
        return new CgiResponse<>(CommonResultEnum.GATEWAY_SUCCESS.getCode(), CommonResultEnum.SUCCESS.getDescription(), data);
    }

    /**
     * @description: 返回失败信息
     * @param code	失败码
     * @param message	失败描述
     * @param data	内容
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<B>
     * @author: hongdong.xie
     * @date: 2023/5/22 10:28
     * @since JDK 1.8
     */
    public static <B extends Body> CgiResponse<B> error(String code,String message,B data){
        return new CgiResponse<>(code, message, data);
    }

    /**
     * @description: 返回失败信息
     * @param data	内容
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<B>
     * @author: hongdong.xie
     * @date: 2023/5/22 10:28
     * @since JDK 1.8
     */
    public static <B extends Body> CgiResponse<B> error(B data){
        return new CgiResponse<>(data.getReturnCode(), data.getDescription(), data);
    }

    /**
     * @description: 返回失败信息，不带内容
     * @param code	失败码
     * @param message	失败描述
     * @return com.howbuy.crm.cgi.common.base.CgiResponse<B>
     * @author: hongdong.xie
     * @date: 2023/5/22 10:28
     * @since JDK 1.8
     */
    public static CgiResponse<Body> error(String code,String message){
        return new CgiResponse<>(code, message, null);
    }
}
