package com.howbuy.crm.cgi.common.enums;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/5/8 15:15
 * @since JDK 1.8
 */
public enum YesNoEnum {

    YES("1", "是"),
    NO("0", "否");

    private String code;
    private String description;

    YesNoEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    public static YesNoEnum resolve(String key) {
        for(YesNoEnum yesNoEnum : values()){
            if(yesNoEnum.getCode().equals(key)){
                return yesNoEnum;
            }
        }
        return null;
    }

    /**
     * 通过code获得描述信息
     * @param code
     * @return description 描述
     */
    public static String getDescription(String code) {
        YesNoEnum yesNoEnum = resolve(code);
        return yesNoEnum == null ? null :yesNoEnum.getDescription();
    }
}
