/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.exception;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.crm.cgi.common.cacheservice.CacheKeyPrefix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 业务异常日志上报
 * @date 2024/8/29 14:44
 * @since JDK 1.8
 */
@Slf4j
@Component
public class BusinessExceptionLogReport {

    private static final Long ONE = 1L;
    /**
     * 缓存
     */
    @Resource
    private CacheService cacheService;

    /**
     * 上报时间 默认2分钟
     */
    @Value("${business.exception.log.report.time.window:120}")
    private String reportTimeWindow;

    /**
     * 一个窗口期内错误的次数 默认5次
     */
    @Value("${business.exception.log.report.count:5}")
    private String reportCount;

    /**
     * @description: 获取指定时间范围内的错误次数。
     * 接口逻辑:
     *    1.通过redis的incr方法设置异常码的key缓存, 如果第一次设置,即返回值是1,则设置过期时间。
     *    2.判断是否达到上报次数,如果是则上报异常日志信息
     *    3.超过指定时间后，缓存过期重新走步骤1
     * @param code	
     * @param desc	
     * @param e	
     * @return void
     * @author: jinqing.rao
     * @date: 2024/9/4 17:11
     * @since JDK 1.8
     */
    public void errorReport(String code, String desc, Exception e) {
        String key = CacheKeyPrefix.BUSINESS_EXCEPTION_LOG_REPORT_KEY_PREFIX + code;
        try {
            Long l = cacheService.incrBy(key, 1L);
            if (ONE.equals(l)) {
                cacheService.expires(key, Integer.valueOf(reportTimeWindow));
            }
            if (Long.valueOf(reportCount).compareTo(l) < 0) {
                log.error(ExceptionMarker.getMarker(ExceptionMarker.CGI_SYSTEM_ERROR_KEY), code);
            }
        } catch (Exception ex) {
            cacheService.expires(key, Integer.valueOf(reportTimeWindow));
            log.info("BusinessExceptionLogReport>>>日志上报异常", ex);
        }
    }
}
