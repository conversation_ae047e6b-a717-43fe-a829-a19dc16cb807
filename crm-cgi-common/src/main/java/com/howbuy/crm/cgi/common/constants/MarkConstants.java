/**
 *Copyright (c) 2019, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.crm.cgi.common.constants;

/**
 * @description: 符号常量
 * <AUTHOR>
 * @date 2019年6月17日 下午2:34:47
 * @since JDK 1.8
 */
public class MarkConstants {
    /**
     * 分割符: ";"
     */
    public static final String SEPARATOR_SEMI_COLON = ";";
	/**
     * 分割符: "$"
     */
    public static final String SEPARATOR_DOLLAR = "$";
    /**
     * 分割符: "."
     */
    public static final String SEPARATOR_DOT = ".";

    /**
     * 回车符: "\n"
     */
    public static final String SEPARATOR_RETURN = "\n";

    /**
     * 分割符: "-"
     */
    public static final String SEPARATOR_MIDDLE = "-";
    /**
     * 符号: "_"
     */
    public static final String SEPARATOR_DOWN = "_";
    /**
     * 符号: "#"
     */
    public static final String SEPARATOR_WELL = "#";
    /**
     * 符号: "&"
     */
    public static final String SEPARATOR_AND = "&";
    /**
     * 符号: "*"
     */
    public static final String SEPARATOR_ASTERISK = "*";
    /**
     * 符号: "@"
     */
    public static final String SEPARATOR_INTERNET = "@";
    /**
     * 符号: "|"
     */
    public static final String SEPARATOR_VERTICAL = "|";
    /**
     * 符号: "~@~"
     */
    public static final String SEPARATOR_EYE = "~@~";
    /**
     * 符号: ","
     */
    public static final String SEPARATOR_COMMA = ",";
    /**
     * 符号: "/"
     */
    public static final String SEPARATOR_SLASH = "/";
    /**
     * 符号: ":"
     */
    public static final String SEPARATOR_COLON = ":";

    /**
     * 分割符: " "
     */
    public static final String SEPARATOR_SPACE = " ";

    /**
     * 符号: "%"
     */
    public static final String SEPARATOR_PERCENT = "%";

    /**
     * 符号: "?"
     */
    public static final String SEPARATOR_QUESTION = "?";
    /**
     * 符号: "√"
     */
    public static final String SEPARATOR_RIGHT = "√";
    /**
     * 符号: "、"
     */
    public static final String SEPARATOR_CAESURA = "、";
    /**
     * 符号: "折"
     */
    public static final String DISCOUNT = "折";
}
