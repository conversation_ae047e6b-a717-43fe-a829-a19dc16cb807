/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/29 17:15
 * @since JDK 1.8
 */
public class TmsCgiConstants {

    /**
     * 定期订单状态 1-申请成功 2-下单成功  4-确认成功
     */
    public static final List<String> DQ_SHOW_DETAIL_STATUS = new ArrayList<String>(3);

    static {
        DQ_SHOW_DETAIL_STATUS.add("1");
        DQ_SHOW_DETAIL_STATUS.add("2");
        DQ_SHOW_DETAIL_STATUS.add("4");
    }

    /**
     * 交易方向集合
     */
    public static final List<String> TRADE_DIRECTION_LIST = Arrays.asList("-", "+");

    /**
     * 组合白名单 11-买入、12-存入、21-取出、22-卖出、23-快速卖出、24-取出（极速）、25-取出（普通） 31-定投 42-转投 67-平衡中  52-红利再投 61-波动平衡、62-观点平衡、63-拉杆平衡、71-修改分红方式、73-强增、74-强减、75-强赎、51-现金分红、
     */
    public static final List<String> ZH_WHITE_LIST = Arrays.asList("11", "12","13", "21", "22", "23", "24", "25", "29", "30", "31","34", "42", "44", "52", "61", "62", "63", "64", "65", "66", "67", "71", "73", "74", "75", "51", "103", "46", "47", "120");

    /**
     * 活期白名单  11-买入、12-存入、21-取出、22-卖出、23-快速卖出、24-取出（极速）、25-取出（普通） 26-储蓄罐活期，极速取出  27-储蓄罐活期，普通卖出 28-储蓄罐活期转投 41-活期转投、
     */
    public static final List<String> HQ_WHITE_LIST = Arrays.asList("11", "12", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "41");
    /**
     * 取出（极速）
     */
    public static final String TAKE_OUT_QUICKLY = "取出（极速）";

    /**
     * 公募白名单 11-买入、12-存入、21-取出、22-卖出、23-快速卖出、24-取出（极速）、25-取出（普通）、28-储蓄罐活期转投、31-定投、42-转投、43-转换、51-现金分红、52-红利再投、71-修改分红方式、73-强增、74-强减、75-强赎
     */
    public static final List<String> GM_WHITE_LIST = Arrays.asList("11", "12","13", "21", "22", "23", "24", "25", "28", "29", "30", "31", "32", "42", "43", "51", "52", "71", "73", "74", "75");

    /**
     * 私募白名单  11-买入、12-存入、21-取出、22-卖出、23-快速卖出、24-取出（极速）、25-取出（普通）、51-现金分红、52-红利再投、75-强赎
     */
    public static final List<String> SM_WHITE_LIST = Arrays.asList("11", "12", "21", "22", "23", "24", "25", "29", "30", "33", "51", "52", "75");

    /**
     * 需要展示详情的交易子类型集合
     */
    public static final List<String> TRADE_SUB_TYPE_QC_LIST = Arrays.asList("21", "22", "23", "24", "25");

    /**
     * 定期需要展示详情的交易子类型集合
     */
    public static final List<String> DQ_WHITE_LIST = Arrays.asList("11", "12", "21", "22", "23", "24", "25");

    /**
     * 已撤单
     */
    public static final String HAVE_WITHDRAW = "已撤单";

    /**
     * 强制撤单
     */
    public static final String FORCE_WITHDRAW = "强制撤单";

    /**
     * 未付款无需退款
     */
    public static final String WITHDRAW_NOT_PAY = "未付款无需退款";

    /**
     * 强制撤单类型3
     */
    public static final String FORCE_WITHDRAW_TYPE = "3";

    /**
     * 货币赢+、货币赢+转投类型
     */
    public static final String HBY_ZT_SUB_CLASS = "2";

    /**
     * 券商
     */
    public static final String QS_SUB_CLASS = "11";

    /**
     * 快速赎回
     */
    public static final String BUSI_CODE_QUICKLY_WITHDRAW = "024";

    /**
     * 快速赎回
     */
    public static final String BUSI_CODE_QUICK_WITHDRAW = "089";

    /**
     * 快速赎回
     */
    public static final List<String> BUSI_CODE_QUICK_WITHDRAW_LIST = Arrays.asList("024", "089");

    /**
     * 创新产品
     */
    public static final String CXCP_SUB_CLASS = "12";

    /**
     * 观点平衡
     */
    public static final String TRADE_SUB_TYPE_VIEW_BALANCE = "62";

    /**
     * 主动平衡
     */
    public static final String TRADE_SUB_TYPE_ACTIVE_BALANCE = "64";

    /**
     * 修改分红方式
     */
    public static final String TRADE_SUB_TYPE_MODIFY_BONUS = "71";

    /**
     * 止盈定投交易子类型
     */
    public static final String PRODUCT_SUB_CLASS_ZYDT = "31";

    /**
     * 0的集合
     */
    public static final List<String> STRING_ZERO_LIST = Arrays.asList("0", "0.0", "0.00");

    public static final String ZERO_STR = "0";

    public static final String ONE_STR = "1";

    public static final String TWO_STR = "2";

    public static final String THREE_STR = "3";

    public static final String FOUR_STR = "4";

    /**
     * int 2
     */
    public static int INT_TWO = 2;

    /**
     * int 4
     */
    public static int INT_FOUR = 4;

    /**
     * 空格
     */
    public static final String SPACE =" ";

    /**
     * 右括号
     */
    public static final String RIGHT_BRACKETS =")";

    /**
     * 活期类型
     */
    public static final String HQ_PRODUCT_CLASS = "1";

    /**
     * 理财（定期）
     */
    public static final String LC_DQ_PRODUCT_CLASS = "2";

    /**
     * 策略（组合）
     */
    public static final String CL_ZH_PRODUCT_CLASS = "3";

    /**
     * 公募
     */
    public static final String GM_PRODUCT_CLASS = "4";

    /**
     * 私募
     */
    public static final String SM_PRODUCT_CLASS = "5";


    /**
     * 税延
     */
    public static final String PENSION_CLASS = "7";

    /**
     * 阳光私募
     */
    public static final String SUN_SM = "8";

    /**
     * 固定收益
     */
    public static final String FIXED_SM = "9";

    /**
     * 私募股权
     */
    public static final String STOCK_SM = "10";

    /**
     * 预约
     */
    public static final String SPECIAL_TAG_YY = "0";

    /**
     * 智定投
     */
    public static final String SPECIAL_TAG_ZDT = "1";

    /**
     * 目标盈
     */
    public static final String SPECIAL_TAG_MBY = "2";

    /**
     * 目标盈+
     */
    public static final String SPECIAL_TAG_MBYJ = "3";

    /**
     * 私募
     */
    public static final String SPECIAL_TAG_SM = "4";

    /**
     * 潜龙
     */
    public static final String SPECIAL_TAG_QL = "5";

    /**
     * 全球赢+
     */
    public static final String SPECIAL_TAG_QQYJ = "6";

    /**
     * 货币赢+
     */
    public static final String SPECIAL_TAG_HBYJ = "7";

    /**
     * 自建组合
     */
    public static final String SPECIAL_TAG_ZJZH = "8";

    /**
     * 牛基宝
     */
    public static final String SPECIAL_TAG_NJB = "9";

    /**
     * 极速取出
     */
    public static final String SPECIAL_TAG_JSQC = "10";

    /**
     * 普通取出
     */
    public static final String SPECIAL_TAG_PTQC = "11";

    /**
     * 系统触发
     */
    public static final String SPECIAL_TAG_XTCF = "12";

    /**
     * 默认名称--
     */
    public static final String DEFAULT_NAME = "--";


    /**
     * 转投下交易子类型  42-转投、46-转投、47-转投
     */
    public static final List<String> TRADE_SUB_TYPE_EXCHANGE_WHITE_LIST = Arrays.asList("42", "46", "47");

    /**
     * 货币赢 展示元 交易子类型  11-买入、22-卖出、31-定投；44-货币赢转投
     */
    public static final List<String> TRADE_SUB_TYPE_HBYJ = Arrays.asList("11", "31", "22", "44");

    /**
     * 交易类型为 1-买入
     */
    public static final String TRADE_TYPE_BUY = "1";

    /**
     * 交易类型为 2-卖出
     */
    public static final String TRADE_TYPE_SALE = "2";

    /**
     * 交易类型为 3-定投
     */
    public static final String TRADE_TYPE_DT = "3";

    /**
     * 交易类型为 4-转投
     */
    public static final String TRADE_TYPE_EXCHANGE = "4";

    /**
     * 交易类型为 1-买入  2-卖出 为 3、定投  4-转投 5-分红
     */
    public static final List<String> TRADE_TYPE_HJB_WHITE_LIST = Arrays.asList("1", "2", "3", "4", "5");

    /**
     * 交易类型为 6-平衡
     */
    public static final String TRADE_TYPE_BALANCE = "6";

    /**
     * 标志集合
     */
    public static final List<String> ZHF_LAG_LIST = Arrays.asList("51", "52", "73", "74", "75", "71");

    /**
     * 科创打新产品码
     */
    public static final String CKDX_ZH_PRODUCT_CODE = "kcdxzh";

    /**
     * 产品为私募
     */
    public static final String PRODUCT_CLASS_SM = "5";

    /**
     * 产品子分类 货币赢+
     */
    public static final String PRODUCT_SUB_CLASS_HBYJ = "2";

    /**
     * 产品子分类 自建组合
     */
    public static final String PRODUCT_SUB_CLASS_ZJZH = "5";

    /**
     * 产品子分类 全球赢+
     */
    public static final String PRODUCT_SUB_CLASS_QQYJ = "6";

    /**
     * 产品子分类 安鑫赢+
     */
    public static final String PRODUCT_SUB_CLASS_AXYJ = "7";

    /**
     * 产品子分类 牛基宝
     */
    public static final String PRODUCT_SUB_CLASS_NJB = "8";

    /**
     * 产品子分类  潜龙计划
     */
    public static final String PRODUCT_SUB_CLASS_QLJH = "9";

    /**
     * 产品子分类 券商
     */
    public static final String PRODUCT_SUB_CLASS_QS = "11";

    /**
     * 交易子类型 极速取出
     */
    public static final String TRADE_SUB_TYPE_QUICK_WITHDRAW = "24";

    /**
     * 交易子类型 普通取出
     */
    public static final String TRADE_SUB_TYPE_COMMON_WITHDRAW = "25";

    /**
     * 交易子类型 系统触发
     */
    public static final String TRADE_SUB_TYPE_SYSTEM_TRIGGER = "29";

    /**
     * 交易子类型 普通取出
     */
    public static final String TRADE_SUB_TYPE_SYSTEM_SPARK = "30";

    /**
     * 交易子类型为货币基金
     */
    public static final String TRADE_SUB_TYPE_HB_FUND = "41";

    /**
     * 交易子类型为货币基金
     */
    public static final String TRADE_SUB_TYPE_HUOBI = "42";

    /**
     * 交易子类型为货币基金
     */
    public static final String TRADE_SUB_TYPE_HBYJ_TRANSFER = "44";

    /**
     * 交易子类型为货币基金
     */
    public static final String TRADE_SUB_TYPE_CASH_FH = "51";

    /**
     * 交易子类型为黄金宝
     */
    public static final String TRADE_SUB_TYPE_GOLD = "52";

    /**
     * 交易子类型为简七
     */
    public static final String TRADE_SUB_TYPE_JQ = "64";

    /**
     * 交易子类型为强增
     */
    public static final String TRADE_SUB_TYPE_FORCE_INCR = "73";

    /**
     * 交易子类型为强减
     */
    public static final String TRADE_SUB_TYPE_FORCE_DECR = "74";

    /**
     * 交易子类型为基金清盘
     */
    public static final String CFM_FUNDCLR = "85";

    /**
     * 预约标识1
     */
    public static final String ADVANCE_FLAG_SPECIAL_FLAG = "1";


    /**
     * 智定投标志 智定投
     */
    public static final String SCHEDULE_FLAG_ZDT = "1";

    /**
     * 智定投标志 目标盈
     */
    public static final String SCHEDULE_FLAG_ZYDT = "3";

    /**
     * 智定投标志4
     */
    public static final String SCHEDULE_FLAG_YDZY = "4";


    /**
     * 等待付款
     */
    public static final String WAITING_FOR_PAYING = "等待付款";

    /**
     * sysCode: 33
     */
    public static final String SYS_CODE_THIRTY_THREE = "33";

    /**
     * 现金集合
     */
    public static final List<String> XJ_FEN_LIST = Arrays.asList("52", "73", "74", "75");

    /**
     * 协议类型集合 14-打新计划、15-目标收益 + 小目标、20-兴全
     */
    public static final List<String> PROTOCOL_TYPE_LIST = Arrays.asList("14", "15", "20");

    /**
     * 交易单位：份
     */
    public static final String UNIT_PARTION = "份";

    /**
     * 交易单位：元
     */
    public static final String UNIT_YUAN = "元";

    /**
     * 交易单位：克
     */
    public static final String UNIT_GRAM = "克";

    /**
     * 交易单位：比例
     */
    public static final String UNIT_PERCENT = "%";

    /**
     * 红利再投
     */
    public static final String DIV_MOD_HLZT = "0";

    /**
     * 现金分红
     */
    public static final String DIV_MOD_XJFH = "1";

    /**
     * 交易状态集合
     */
    public static final List<String> TRADE_STATUS_STATUS = Arrays.asList("1", "2", "3", "4");

    /**
     *  投顾显示卖出比例状态： 确认中 已确认 已撤单
     */
    public static final List<String> ADVISER_SHOW_PERCENT_TRADE_STATUS = Arrays.asList("1", "4", "7");

    /**
     * 单位元集合
     */
    public static final List<String> ZH_YUAN_LIST = Arrays.asList("11", "12", "31", "51");

    /**
     * 单位分集合
     */
    public static final List<String> ZH_FEN_LIST = Arrays.asList("52", "73", "74", "75");

    /**
     * 单位比例集合
     */
    public static final List<String> ZH_PERCENT_LIST = Arrays.asList("5", "6", "8", "9");

    /**
     * 公募分集合
     */
    public static final List<String> GM_FEN_LIST = Arrays.asList("42", "43", "52", "73", "74", "75", "87", "88");

    /**
     * 黄金宝产品码
     */
    public static final String PRODUCT_CODE_HJB = "000217";


    /**
     * 元集合
     */
    public static final List<String> YUAN_LIST = Arrays.asList("11", "12", "33", "51", "77");

    /**
     * 分集合
     */
    public static final List<String> FEN_LIST = Arrays.asList("22", "23", "24", "25", "52", "73", "74", "75", "76", "78", "91", "92");

    /**
     * +
     */
    public static final String ADD = "+";

    /**
     * -
     */
    public static final String SUB = "-";

    /**
     * 以基金公司确认为准
     */
    public static final String FUND_COMPANY_CONFIRMATION = "1";

    /**
     * 18:00后
     */
    public static final String AFTER_EIGHT_TEEN = "2";

    /**
     * 24:00前
     */
    public static final String BEFORE_TWENTY_FOUR = "3";

    /**
     * 预计到活期
     */
    public static final String EXCEPTED_TO_THE_CURRENT = "4";

    /**
     * 活期时间转换
     */
    public static final String PASS_THROUGH_TIME_CONVERSION = "5";

    /**
     * 以实际确认时间为准
     */
    public static final String CURRENT_CONFIRMATION = "6";


    /**
     * 以基金公司确认为准
     */
    public static final String FUND_COMPANY_CONFIRMATION_TEXT = "以基金公司确认为准";

    /**
     * (以基金公司确认为准)
     */
    public static final String FUND_COMPANY_CONFIRMATION_PARENTHESES_TEXT = "(以基金公司确认为准)";

    /**
     * 确认完成的18:00后
     */
    public static final String CONFIRM_SUCCESS_AFTER_EIGHT_TEEN_TEXT = "确认完成的18:00后";

    /**
     * 18:00后
     */
    public static final String AFTER_EIGHT_TEEN_TEXT = "18:00后";

    /**
     * 以银行到账时间为准
     */
    public static final String CONFIRM_BANK_ARRIVAL_TEXT = "以银行到账时间为准";

    /**
     * 24:00前以银行到账时间为准
     */
    public static final String BEFORE_TWENTY_FOUR_TEXT = "24:00前";

    /**
     * 24:00前以银行到账时间为准
     */
    public static final String BEFORE_TWENTY_FOUR_CONFIRM_BANK_ARRIVAL_TEXT = "24:00前(以银行到账时间为准)";

    /**
     * 以实际回款时间为准
     */
    public static final String CONFIRM_ACTUAL_ARRIVAL_TEXT = "以实际回款时间为准";

    /**
     * 以实际确认时间为准
     */
    public static final String CONFIRM_ACTUAL_CONFIRM_TEXT = "以实际确认时间为准";

    /**
     * (以实际确认时间为准)
     */
    public static final String CONFIRM_ACTUAL_CONFIRM_PARENTHESES_TEXT = "(以实际确认时间为准)";


    /**
     * 是
     */
    public static final String YES = "1";

    /**
     * (转出
     */
    public static final String LEFT_BRACKET_TRANSFER_OUT = "(转出";

    /**
     * +转入
     */
    public static final String PLUS_TRANSFER_IN = "+转入";




}