/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.constants;

/**
 * @description: fegin服务端key常量类
 * <AUTHOR>
 * @date 2023/3/16 11:15
 * @since JDK 1.8
 */
public class FeginServerKeyConstant {
    private FeginServerKeyConstant(){}
    /**
     * CRM资产配置服务
     */
    public static final String CRM_ASSET_REMOTE = "crm-asset-remote";
    /**
     * 海外直销 产品中心
     */
    public static final String DTMS_PRODUCT_REMOTE = "dtms-product-remote";
    /**
     * 海外直销 订单中心
     */
    public static final String DTMS_ORDER_REMOTE = "dtms-order-remote";


    /**
     * CRM企业微信 服务中心
     */
    public static final String CRM_WECHAT_REMOTE = "crm-wechat-remote";


    /**
     * CRM 资料管理系统
     */
    public static final String CRM_TRADE_SERVER="crm-trade-server";
}