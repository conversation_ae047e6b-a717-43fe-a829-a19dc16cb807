package com.howbuy.crm.cgi.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * @description:(上传证件描述)
 * @author: xufanchao
 * @date: 2023/12/28 16:49
 * @since JDK 1.8
 */
public enum UploadIdTypeDescEnum {
    /**
     * 身份证
     */
    ID_CARD("0,D,E,F", Arrays.asList("请上传身份证人像面", "请上传身份证国徽面")),
    /**
     * 护照
     */
    PASSPORT("1,6", Arrays.asList("请上传身份信息页", "请上传签证页")),
    /**
     * 港澳通行证
     */
    EXIT_ENTRY_PERMIT_FROM_HONG_KONG_AND_MACAO("4", Arrays.asList("请上传身份信息页", "请上传签证页")),
    /**
     * 港澳台居民居住证
     */
    HONG_KONG_MACAO_TAIWAN_RESIDENCE_PERMIT("C", Arrays.asList("请上传居住证人像面", "请上传居住证国徽面")),
    /**
     * 台胞证
     */
    MAINLAND_TRAVEL_PERMIT_FOR_TAIWAN_RESIDENTS_PERMIT("A", Arrays.asList("请上传台胞证人像面", "请上传台胞证背面")),
    /**
     * 其他
     */
    OTHER("7", Arrays.asList("请上传证件")),
    ;


    private String idType;

    private List<String> idTypeDesc;

    UploadIdTypeDescEnum(String idType, List<String> idTypeDesc) {
        this.idType = idType;
        this.idTypeDesc = idTypeDesc;
    }

    /**
     * @description:(获取上传文案)
     * @param code
     * @return java.util.List<java.lang.String>
     * @author: xufanchao
     * @date: 2023/12/28 16:57
     * @since JDK 1.8
     */
    public static List<String> getDesc(String code) {
        for (UploadIdTypeDescEnum uploadIdTypeDescEnum : UploadIdTypeDescEnum.values()) {
            if (uploadIdTypeDescEnum.idType.contains(code)) {
                return uploadIdTypeDescEnum.idTypeDesc;
            }
        }
        return null;
    }
}
