/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.StringTokenizer;

/**
 * @description: web工具类，包含获取IP地址
 * <AUTHOR>
 * @date 2023/5/22 17:11
 * @since JDK 1.8
 */
@Slf4j
public class WebUtil {

    private WebUtil() {
    }

    /**
     * @description:
     * 得到客户的ip.
     * <p>
     * 服务器对服务器方式：客户ip由合作方作为请求参数传递过来，<b>需要controller从参数中获取ip参数设置到request的attribute中(HOWBUY_CUSTIP)</b>
     * <p>
     * 传统客户端方式：直接从request中获取客户端ip
     * @param request
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    public static String getCustIP(HttpServletRequest request) {
        if(null == request){
            return null;
        }
        String ip = getHeaderIP(request, Constants.X_FORWARDED_FOR);
        if (StringUtils.isEmpty(ip)) {
            ip = getHeaderIP(request, Constants.PROXY_CLIENT_IP);
        }
        if (StringUtils.isEmpty(ip)) {
            ip = getHeaderIP(request, Constants.WL_PROXY_CLIENT_IP);
        }
        if (StringUtils.isEmpty(ip)) {
            ip = getHeaderIP(request, Constants.HTTP_X_FORWARDED_FOR);
        }
        if (StringUtils.isEmpty(ip)) {
            ip = getHeaderIP(request, Constants.X_REAL_IP);
        }
        if (StringUtils.isEmpty(ip)) {
            ip = getHeaderIP(request, Constants.HTTP_CLIENT_IP);
        }
        if (StringUtils.isEmpty(ip)) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    private static String getHeaderIP(HttpServletRequest request, String headerName) {
        String ip = request.getHeader(headerName);
        if (StringUtils.isNotEmpty(ip) && !Constants.UNKNOWN.equalsIgnoreCase(ip)) {
            StringTokenizer st = new StringTokenizer(ip, ",");
            if (st.countTokens() > 1) {
                return st.nextToken();
            }
        }
        return ip;
    }

    public static void getAllHeaders(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String,Object> map = new HashMap();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            Enumeration<String> headerValues = request.getHeaders(headerName);

            while (headerValues.hasMoreElements()) {
                String headerValue = headerValues.nextElement();
                map.put(headerName,headerValue);
            }
        }
        log.info("getAllHeaders:{}", JSON.toJSONString(map));
    }

}