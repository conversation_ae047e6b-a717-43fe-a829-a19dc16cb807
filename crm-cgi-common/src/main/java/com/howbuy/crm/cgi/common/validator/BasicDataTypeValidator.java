package com.howbuy.crm.cgi.common.validator;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.ParamsException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 基础数据类型校验
 * @date 2023/12/1 18:08
 * @since JDK 1.8
 */
public class BasicDataTypeValidator {

    private static final Validator VALIDATOR;

    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        VALIDATOR = factory.getValidator();
    }

    public static void validator(Object object) {
        Set<ConstraintViolation<Object>> violations = VALIDATOR.validate(object);
        for (ConstraintViolation<Object> violation : violations) {
            if(null != violation){
                throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(), violation.getMessage());
            }
        }
    }

    /**
     * @param arrays 字节数组
     * @return void
     * @description: 校验多个字节数组是否为空
     * @author: jinqing.rao
     * @date: 2023/12/1 18:17
     * @since JDK 1.8
     */
    public static void validatorArrayType(byte[]... arrays) {
        if (arrays == null || arrays.length == 0) {
            throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR);
        }
        for (byte[] array : arrays) {
            if (array == null || array.length == 0) {
                throw new ParamsException(ExceptionCodeEnum.PARAM_ERROR);
            }
        }
    }

    /**
     * @description: 校验集合是否为空,并且抛出参数异常
     * @param coll 集合
     * @return void
     * @author: jinqing.rao
     * @date: 2023/12/1 18:36
     * @since JDK 1.8
     */
    public static void validatorListType(Collection<?> coll) {
        if (CollectionUtils.isEmpty(coll)) {
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR);
        }
    }

    /**
     * @description: validatorStringType
     * @param custName 中文名字
     * @author: jinqing.rao
     * @date: 2023/12/6 19:58
     * @since JDK 1.8
     */
    public static void validatorStringType(String custName) {
        if (StringUtils.isBlank(custName)) {
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR);
        }
    }

    public static void validatorStringType(String custName,String msg) {
        if (StringUtils.isBlank(custName)) {
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),msg);
        }
    }

    public static void validatorStrings(String msg,String... args) {
        if (StringUtils.isAnyBlank(args)) {
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),msg);
        }
    }

    /**
     * @description: 校验文件流参数
     * @param frontPicture	文件流
     * @param msg 错误描述
     * @return void
     * @author: jinqing.rao
     * @date: 2023/12/28 10:27
     * @since JDK 1.8
     */
    public static void validatorMultipartFile(MultipartFile frontPicture, String msg) {
        if(null == frontPicture || frontPicture.isEmpty()){
            throw new ParamsException(ExceptionCodeEnum.REQUEST_PARAM_ERROR.getCode(),msg);
        }
    }
}
