/**
 * Copyright (c) 2025, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import org.apache.commons.io.FilenameUtils;

/**
 * @description: 文件通用工具类
 * <AUTHOR>
 * @date 2025/4/25 13:26
 * @since JDK 1.8
 */
public class FileUtils {


    /**
     * 获取不带后缀的文件名
     * @param fileName 完整文件名
     * @return 不带后缀的文件名
     */
    public static String getFileNameWithoutExtension(String fileName) {
        return FilenameUtils.getBaseName(fileName);
    }

    /**
     * 获取文件后缀（不包含点号）
     * @param fileName 完整文件名
     * @return 文件后缀，如果没有后缀则返回空字符串
     */
    public static String getFileExtension(String fileName) {
        return FilenameUtils.getExtension(fileName);
    }
}
