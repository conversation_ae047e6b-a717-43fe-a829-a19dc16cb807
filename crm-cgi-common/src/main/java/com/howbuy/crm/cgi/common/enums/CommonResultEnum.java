package com.howbuy.crm.cgi.common.enums;

/**
 * <AUTHOR>
 * @description: 返回状态枚举
 * @date 2023/2/1 13:47
 */
public enum CommonResultEnum {
    //执行成功
    SUCCESS("C010000","操作成功"),
    //海外APP需要返回0000,和网关层保持一致
    GATEWAY_SUCCESS("0000","操作成功"),
    //调用core里面的dubbo接口返回成功
    CORE_SUCCESS("C030000","操作成功"),
    //执行失败
    FAIL("C010001","执行失败"),
    //参数错误
    PARAM_ERROR("C010002","参数错误"),
    //用户未登录
    LOGIN_OUT("C010003","用户未登录"),
    //非法请求
    ILLEGAL_REQUEST("C010004","非法请求"),
    //用户没有此菜单权限
    NO_MENU_AUTH("C010005","用户没有此菜单权限"),
    //用户没有此操作权限
    NO_OPERATE_AUTH("C010006","用户没有此操作权限"),
    //用户没有此客户权限
    NO_CUST_AUTH("C010007","用户没有此客户权限");
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    CommonResultEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(CommonResultEnum b : CommonResultEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
