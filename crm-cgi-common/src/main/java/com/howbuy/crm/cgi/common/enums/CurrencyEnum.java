/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.cgi.common.enums;


import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @description:(币种)
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/12/26 14:02
 * @since JDK 1.8
 */
public enum CurrencyEnum {

	/**
	 * 156-人民币
	 */
	RMB("156", "元","(CNY)"),
	/**
	 * 840-美元
	 */
	USD("840", "美元","(USD)"),
	/**
	 * 344-港元
	 */
	HKD("344", "港元","(HKD)"),

	/**
	 * 978-欧元
	 */
	EUR("978", "欧元","(EUR)"),
	/**
	 * 392-日元
	 */
	JPY("392", "日元","(JPY)"),
	/**
	 * 826-英镑
	 */
	GBP("826", "英镑","(GBP)"),
	/**
	 * 901- 新台币
	 */
	TWD("901", "新台币","(TWD)"),
	/**
	 * 越南盾
	 */
	VND("704", "越南盾","(VND)"),
	;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 币种英文
	 */
	private String enDesription;

	private CurrencyEnum(String code, String description, String enDesription) {
		this.code = code;
		this.description = description;
		this.enDesription = enDesription;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CurrencyEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	public static String getListDescritpion(List<String> codeList) {
		if(codeList==null || codeList.isEmpty()){
			return null;
		}
		// 这里修复产线BUG,code 会出现999 ，和朱琴确认 999为综合币种
		return codeList.stream().map(code->{
			if(StringUtils.equals(code,"999")){
				return "综合币种";
			}
			return getDescription(code);
		}).filter(StringUtils::isNotBlank).reduce((a,b)->a+","+b).orElse(null);
	}

	/**
	 * 获取英文描述
	 * @param code
	 * @return
	 */
	public static String getEnDescription(String code) {
		CurrencyEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getEnDesription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CurrencyEnum getEnum(String code) {
		for(CurrencyEnum statusEnum : CurrencyEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
//		return Stream.of(DepositMatchStatusEnum.values()).filter(tmp -> tmp.code.equals(code)).findFirst().orElse(null);
	}


	/**
	 * 通过 description 直接返回 整个枚举类型
	 * @param description
	 * @return
	 */
	public static CurrencyEnum getEnumByDesc(String description) {
		for(CurrencyEnum statusEnum : CurrencyEnum.values()){
			if(statusEnum.getDescription().equals(description)){
				return statusEnum;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getEnDesription() {
		return enDesription;
	}

	public void setEnDesription(String enDesription) {
		this.enDesription = enDesription;
	}
}
