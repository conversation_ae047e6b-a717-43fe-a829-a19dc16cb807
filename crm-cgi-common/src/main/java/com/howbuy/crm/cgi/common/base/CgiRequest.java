/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.base;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 统一接入请求消息封装
 * <AUTHOR>
 * @date 2023/3/13 17:21
 * @since JDK 1.8
 */
@Getter
@Setter
public class CgiRequest <B extends BodyRequest>{
    /**
     * 请求头
     */
    private HeadRequest head;
    /**
     * 请求体
     */
    private B body;
}