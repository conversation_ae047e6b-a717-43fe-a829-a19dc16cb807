/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

import java.util.stream.Stream;

/**
 * @description: (cgi处理文档，文档系统来源)
 * <AUTHOR>
 * @date 2023/7/24 19:19
 * @since JDK 1.8
 */
public enum DocSystemCodeEnum {

    /**
     * HB_DOC 文档管理系统
     */
    HB_DOC("1","文档管理系统"),

    /**
     * 资料管理系统-资料文件
     */
    CRM_COUNTER("2","资料管理-资料文件"),

    /**
     * 资料管理系统-配置文件
     */
    CRM_COUNTER_MODEL("3","资料管理-配置文件"),

    /**
     * 资料管理系统-签名文件
     */
    CRM_COUNTER_SIGNFILE("4","资料管理-签名文件")
    ;


    /**
     * 规则key
     */
    private String key;
    /**
     * 规则名称
     */
    private String name;

    DocSystemCodeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }


    public static DocSystemCodeEnum getEnum(String key) {
        return Stream.of(DocSystemCodeEnum.values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getName(String code) {
        DocSystemCodeEnum counterBusiProidEnum =getEnum(code);
        return counterBusiProidEnum == null ? null : counterBusiProidEnum.getName();
    }

}