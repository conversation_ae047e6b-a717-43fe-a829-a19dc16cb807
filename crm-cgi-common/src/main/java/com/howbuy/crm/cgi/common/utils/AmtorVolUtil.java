/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import com.howbuy.crm.cgi.common.enums.YesNoEnum;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (金额或者份额处理工具类)
 * @date 2024/3/12 17:59
 * @since JDK 1.8
 */
public class AmtorVolUtil {
    /**
     * 格式化金额
     * @param s
     * @param n
     * @return
     */
    public static String formatMoneyOfNp(BigDecimal s, int n) {
        if (Objects.isNull(s)) {
            return "--";
        }

        String sign = "";
        if (s.compareTo(BigDecimal.ZERO) < 0) {
            s = s.abs();
            sign = "-";
        }
        // 根据小数点进行裁切
        // 当存在小数点的时候用小数点进行裁切
        DecimalFormat decimalFormat = new DecimalFormat("#,###");
        String string = s.toString();
        if (string.toString().contains(".")) {
            String[] split = string.split("\\.");
            if (split.length > 1) {
                String format = decimalFormat.format(new BigDecimal(split[0]));
                return sign + format + "." + split[1];
            }
        } else {
            return sign + decimalFormat.format(s);
        }
        return "--";
    }

    /**
     * 格式化金额
     * @param s
     * @param n
     * @return
     */
    public static String formatTwoMoneyOfNp(BigDecimal s, int n) {
        if (Objects.isNull(s)) {
            return "--";
        }

        String sign = "";
        if (s.compareTo(BigDecimal.ZERO) < 0) {
            s = s.abs();
            sign = "-";
        }

        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        // 设置不使用小数点后的数据
        decimalFormat.setDecimalSeparatorAlwaysShown(false);
        String formatted = decimalFormat.format(s);

        return sign + formatted;
    }



    /**
     * 格式化金额
     * @param s
     * @param n
     * @return
     */
    public static String formatMoneyOfOhterNp(BigDecimal s, int n) {
        if (Objects.isNull(s)) {
            return "--";
        }

        String sign = "";
        if (s.compareTo(BigDecimal.ZERO) < 0) {
            s = s.abs();
            sign = "-";
        }
        // 根据小数点进行裁切
        // 当存在小数点的时候用小数点进行裁切
        DecimalFormat decimalFormat = new DecimalFormat("#,###");
        String string = s.toString();
        if (string.toString().contains(".")) {
            String[] split = string.split("\\.");
            if (split.length > 1) {
                String format = decimalFormat.format(new BigDecimal(split[0]));
                return sign + format + "." + truncateString(split[1]);
            }
        } else {
            return sign + decimalFormat.format(s);
        }
        return "--";
    }

    public static String truncateString(String str) {
        if (str.length() >= 2) {
            return str.substring(0, 2);
        } else {
            return str;
        }
    }

    /**
     * 盘空
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }


    /**
     * @description:(获取份额)
     * @param vol 份额数据
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/12 17:40
     * @since JDK 1.8
     */
    public static String getVol(BigDecimal vol) {
        if (Objects.isNull(vol)) {
            return "--";
        }
        return AmtorVolUtil.formatMoneyOfNp(vol, vol.toString().contains(".") ? vol.toString().split("\\.")[1].length() : 0);
    }

    /**
     * @description:(金额保留两位小数)
     * @param vol
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/29 17:58
     * @since JDK 1.8
     */
    public static String getTwoNum(BigDecimal vol) {
        if (Objects.isNull(vol)) {
            return "--";
        }
        return AmtorVolUtil.formatTwoMoneyOfNp(vol, vol.toString().contains(".") ? vol.toString().split("\\.")[1].length() : 0);
    }

    /**
     * @description:(获取金额数据并且显示单位)
     * @param amt 金额数据
     * @param currency 币种
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/12 17:37
     * @since JDK 1.8
     */
    public static String getAmt(BigDecimal amt, String currency) {
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "--";
        }
        String showAmt = AmtorVolUtil.formatMoneyOfNp(amt, amt.toString().contains(".") ? amt.toString().split("\\.")[1].length() : 0);
        if (showAmt.contains("--")) {
            return "--";
        } else {
            return showAmt;
        }
    }


    /**
     * @description:(获取金额数据并且显示单位)
     * @param amt 金额数据
     * @param currency 币种
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/12 17:37
     * @since JDK 1.8
     */
    public static String getOtherAmt(BigDecimal amt, String currency) {
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "--";
        }
        String showAmt = AmtorVolUtil.formatMoneyOfOhterNp(amt, amt.toString().contains(".") ? amt.toString().split("\\.")[1].length() : 0);
        if (showAmt.contains("--")) {
            return "--";
        } else {
            return showAmt;
        }
    }
    /**
     * @description:(获取净值数据并转换格式)
     * @param amt
     * @param currency
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/12 18:35
     * @since JDK 1.8
     */
    public static String getNavAmt(BigDecimal amt, String currency) {
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "--";
        }
        // amt 字符串数据格式转换千分位数据，并且保留两位小数
        return amt.toString();
    }

    /**
     * @description:(获取净值数据并转换格式)
     * @param amt
     * @param currency
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/12 18:35
     * @since JDK 1.8
     */
    public static String isShowNavAmt(BigDecimal amt, String currency,String showAsset) {
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "--";
        }
        // amt 字符串数据格式转换千分位数据，并且保留两位小数
        return amt.toString();
    }


    /**
     * @param yield
     * @return java.lang.String
     * @description:(获取收益数据并转换格式)
     * @date: 2024/3/12 18:35
     * @since JDK 1.8
     */
    public static String getYield(BigDecimal yield, String showAsset) {
        // showAsset的显示控制(0:显示, 1: 隐藏)
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        if (Objects.isNull(yield)) {
            return "--";
        }
        return yield.toString();
    }

    /**
     * @param amt
     * @return java.lang.String
     * @description:(控制显示内容)
     * @author: xufanchao
     * @date: 2024/3/14 17:07
     * @since JDK 1.8
     */
    public static String isAmtShow(BigDecimal amt, String currency, String showAsset) {
        // showAsset的显示控制(0:显示, 1: 隐藏)
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "--";
        }
        String showAmt = AmtorVolUtil.formatMoneyOfNp(amt, amt.toString().contains(".") ? amt.toString().split("\\.")[1].length() : 0);
        if (showAmt.contains("--")) {
            return "--";
        } else {
            return showAmt;
        }
    }

    /**
     * @param amt
     * @return java.lang.String
     * @description:(控制显示内容)
     * @author: xufanchao
     * @date: 2024/3/14 17:07
     * @since JDK 1.8
     */
    public static String isOtherAmtShow(BigDecimal amt, String showAsset) {
        // showAsset的显示控制(0:显示, 1: 隐藏)
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "--";
        }
        String showAmt = AmtorVolUtil.formatMoneyOfOhterNp(amt, amt.toString().contains(".") ? amt.toString().split("\\.")[1].length() : 0);
        if (showAmt.contains("--")) {
            return "--";
        } else {
            return showAmt;
        }
    }

    /**
     * @param amt
     * @return java.lang.String
     * @description:(控制显示内容)
     * @author: xufanchao
     * @date: 2024/3/14 17:07
     * @since JDK 1.8
     */
    public static String isTwoNumShow(BigDecimal amt, String showAsset) {
        // showAsset的显示控制(0:显示, 1: 隐藏)
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        if (Objects.isNull(amt)) {
            return "--";
        }
        return AmtorVolUtil.formatTwoMoneyOfNp(amt, amt.toString().contains(".") ? amt.toString().split("\\.")[1].length() : 0);
    }

    /**
     * @description:(控制显示内容)
     * @param num
     * @param showAsset
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/14 17:10
     * @since JDK 1.8
     */
    public static String isAllShow(BigDecimal num, String showAsset) {
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        if (Objects.isNull(num)) {
            return "--";
        }
        return num.toString();
    }

    /**
     * @description:(控制显示内容)
     * @param num
     * @param showAsset
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/14 17:10
     * @since JDK 1.8
     */
    public static String isTotalAllShow(BigDecimal num, String showAsset) {
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        if (Objects.isNull(num)) {
            return "0.00";
        }
        return num.toString();
    }

    public static String isTotalAllShowCurrency(BigDecimal amt, String currency, String showAsset) {
        // showAsset的显示控制(0:显示, 1: 隐藏)
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        // amt是BigDecimal 类型,当数据为空的时候返回-- 如果不为空 数据进行千分位展示，并且保留两位小数
        if (Objects.isNull(amt)) {
            return "0.00";
        }
        String showAmt = AmtorVolUtil.formatMoneyOfNp(amt, amt.toString().contains(".") ? amt.toString().split("\\.")[1].length() : 0);
        if (showAmt.contains("--")) {
            return "0.00";
        } else {
            return showAmt;
        }
    }

    /**
     * @description:(控制显示内容)
     * @param num
     * @param showAsset
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2024/3/14 17:11
     * @since JDK 1.8
     */
    public static String isVolShow(BigDecimal num,String showAsset) {
        if (YesNoEnum.YES.getCode().equals(showAsset)) {
            return "****";
        }
        return AmtorVolUtil.formatMoneyOfNp(num, num.toString().contains(".") ? num.toString().split("\\.")[1].length() : 0);
    }
}