/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.threadpool;

import com.howbuy.crm.cgi.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/3/4 09:50
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CommonThreadPool {
    private static volatile CommonThreadPool instance;
    private ThreadPoolExecutor THREAD_POOL = null;
    private int corePoolSize = 80;
    private int maxPoolSize = 200;
    private int keepAliveTime = 60;
    private int workQueueLength = 250;

    public CommonThreadPool() {
        instance = this;
    }

    public static void execute(Runnable command) {
        ThreadPoolExecutor threadPool = instance.getThreadPool();

        try {
            threadPool.execute((new RunnableWapper(command)).init());
        } catch (RejectedExecutionException var3) {
            log.error("CommonThreadPool|execute Exception.", var3);
            log.error("CommonThreadPool|THREAD_POOL|activeCount:{},currPoolSize:{},maxPoolSize:{},taskCount:{}", threadPool.getActiveCount(), threadPool.getPoolSize(), threadPool.getMaximumPoolSize(), threadPool.getTaskCount());
            throw new BusinessException("Z0000002", "已经达到线程池处理上限");
        }
    }

    public static <V> Future<V> submit(Callable<V> command) {
        ThreadPoolExecutor threadPool = instance.getThreadPool();

        try {
            Future<V> result = threadPool.submit((new CallableWapper(command)).init());
            return result;
        } catch (RejectedExecutionException var3) {
            log.error("CommonThreadPool|execute Exception.", var3);
            log.error("CommonThreadPool|THREAD_POOL|activeCount:{},currPoolSize:{},maxPoolSize:{},taskCount:{}", threadPool.getActiveCount(), threadPool.getPoolSize(), threadPool.getMaximumPoolSize(), threadPool.getTaskCount());
            throw new BusinessException("Z0000002", "已经达到线程池处理上限");
        }
    }

    @PostConstruct
    public void init() {
        this.THREAD_POOL = new ThreadPoolExecutor(this.corePoolSize, this.maxPoolSize, (long)this.keepAliveTime, TimeUnit.SECONDS, new LinkedBlockingQueue(this.workQueueLength), new ThreadPoolExecutor.AbortPolicy());
        log.info("CommonThreadPool|init THREAD_POOL|activeCount:{},currPoolSize:{},maxPoolSize:{},taskCount:{}", this.THREAD_POOL.getActiveCount(), this.THREAD_POOL.getPoolSize(), this.THREAD_POOL.getMaximumPoolSize(), this.THREAD_POOL.getTaskCount());
    }

    @PreDestroy
    public void destroy() {
        this.THREAD_POOL.shutdownNow();
    }

    public void setCorePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
    }

    public void setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
    }

    public void setKeepAliveTime(int keepAliveTime) {
        this.keepAliveTime = keepAliveTime;
    }

    public void setWorkQueueLength(int workQueueLength) {
        this.workQueueLength = workQueueLength;
    }

    private ThreadPoolExecutor getThreadPool() {
        return this.THREAD_POOL;
    }
}