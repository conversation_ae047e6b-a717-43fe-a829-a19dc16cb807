/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.cgi.common.enums;


/**
 * @description:(产品风险等级)
 * @author: xuf<PERSON><PERSON><PERSON>
 * @date: 2023/12/26 14:05
 * @since JDK 1.8
 */
public enum ProductRiskLevelEnum {

	/**
	 * R1低风险
	 */
	R1("1", "R1低风险"),
	/**
	 * R2中低风险
	 */
	R2("2", "R2中低风险"),
	/**
	 * R3中风险
	 */
	R3("3", "R3中风险"),
	/**
	 * R4中高风险
	 */
	R4("4", "R4中高风险"),
	/**
	 * R5高风险
	 */
	R5("5", "R5高风险");

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 描述
	 */
	private String description;

	private ProductRiskLevelEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		ProductRiskLevelEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}


    /**
     * 是否是最高风险等级
     * @return
     */
    public static boolean isTopRatingLevel(ProductRiskLevelEnum ratingEnum){
        return ProductRiskLevelEnum.R5.equals(ratingEnum);
    }

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static ProductRiskLevelEnum getEnum(String code) {
		for(ProductRiskLevelEnum statusEnum : ProductRiskLevelEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
