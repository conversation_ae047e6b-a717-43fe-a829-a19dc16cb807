package com.howbuy.crm.cgi.common.utils;

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

import net.coobird.thumbnailator.Thumbnails;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * @description: 图片处理工具类
 * <AUTHOR>
 * @date 2023/12/11 15:38
 * @since JDK 1.8
 */
public class ImageUtils {

    private static final String BASE64_PREFIX = "data:image/jpeg;base64,";

    /**
     * @description: 图片压缩
     * @param sourceFile 当前图片
     * @return void
     * @author: jinqing.rao
     * @date: 2023/12/18 16:29
     * @since JDK 1.8
     */
    public static InputStream  compressImage(InputStream sourceFile) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        //scale(比例),outputQuality(质量)
        Thumbnails.of(sourceFile).scale(0.5f).outputQuality(0.25f).toOutputStream(out);
        return new ByteArrayInputStream(out.toByteArray());
    }
    /**
     * @description: byte[]转InputStream流
     * @param bytes	 字节流
     * @return java.io.InputStream
     * @author: jinqing.rao
     * @date: 2023/12/28 13:55
     * @since JDK 1.8
     */
    public static InputStream byteToInputStream(byte[] bytes) {
        return new ByteArrayInputStream(bytes);
    }

    /**
     * @description: 这个方法使用了Apache Commons Codec库中的Base64类，将byte[]对象转换成base64字符串
     * @param bytes	 字节流
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2023/12/18 16:31
     * @since JDK 1.8
     */
    public static String bytesToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 将base64字符串转换成byte[]
     */
    public static byte[] base64ToBytes(String base64) {
        if(base64.startsWith(BASE64_PREFIX)){
            base64 = base64.substring(BASE64_PREFIX.length());
        }
        return Base64.getDecoder().decode(base64);
    }
}
