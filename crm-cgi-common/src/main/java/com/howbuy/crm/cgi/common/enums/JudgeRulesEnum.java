/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * @description: 规则链引擎规则key枚举类
 * <AUTHOR>
 * @date 2023/6/20 09:35
 * @since JDK 1.8
 */
public enum JudgeRulesEnum {
    /**
     * DEFAULT_RULE：默认规则
     */
    DEFAULT_RULE("DEFAULT_RULE","默认规则"),
    /**
     * USER_VERFYCODE_LOGIN_RULE：验证码登录IP拦截
     */
    USER_VERFYCODE_LOGIN_RULE("USER_VERFYCODE_LOGIN_RULE","验证码登录IP拦截"),
    /**
     * RESET_PASSWORD_VERIFYCODE_RULE：重置密码验证码IP拦截
     */
    RESET_PASSWORD_VERIFYCODE_RULE("RESET_PASSWORD_VERIFYCODE_RULE","重置密码验证码IP拦截"),
    /**
     * MOBILE_VERIFY_VERIFYCODE_RULE：手机验证IP拦截
     */
    MOBILE_VERIFY_VERIFYCODE_RULE("MOBILE_VERIFY_VERIFYCODE_RULE","手机验证IP拦截"),
    /**
     * ACTIVATE_VERIFYCODE_RULE：激活验证码IP拦截
     */
    ACTIVATE_VERIFYCODE_RULE("ACTIVATE_VERIFYCODE_RULE","激活验证码IP拦截"),

    /**
     * EMAIL_VERIFYCODE_RULE：邮箱验证码IP拦截
     */
    EMAIL_VERIFYCODE_RULE("EMAIL_VERIFYCODE_RULE","邮箱验证码IP拦截"),

    ;
    /**
     * 规则key
     */
    private String key;
    /**
     * 规则名称
     */
    private String name;

    JudgeRulesEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    /**
     * @description: 通过代码获取名称
     * @param key
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/6/20 09:36
     * @since JDK 1.8
     */
    public static String getName(String key) {
        for (TxChannelEnum enumObj : TxChannelEnum.values()) {
            if (key != null && key.equals(enumObj.getCode())) {
                return enumObj.getName();
            }
        }
        return null;
    }
}
