/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.threadpool;

import com.howbuy.crm.cgi.common.utils.LoggerUtils;

import java.util.concurrent.Callable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/3/4 09:53
 * @since JDK 1.8
 */
public class CallableWapper<V> implements Callable<V> {
    private String uuid;
    private Callable<V> task;

    public CallableWapper(Callable<V> task) {
        this.task = task;
    }

    public CallableWapper<V> init() {
        this.uuid = LoggerUtils.getUuid();
        return this;
    }

    public V call() throws Exception {
        LoggerUtils.setCfgValue("uuid", this.uuid);
        return this.task.call();
    }

}