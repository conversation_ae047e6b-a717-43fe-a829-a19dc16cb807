/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.constants;

/**
 * @description: 外部系统返回码
 * <AUTHOR>
 * @date 2023/3/16 09:48
 * @since JDK 1.8
 */
public class OutReturnCodes {

    private OutReturnCodes(){}
    /**
     * DUBBO接口返回成功，后台dubbo新框架
     */
    public static final String TP_DUBBO_SUCCESS = "0000000";

    public static final String SUCCESS = "0000";

    /**
     * 客户信息不存在
     */
    public static final String HK_ACC_ONLINE_CUST_NOT_EXISTS = "H5530003";

    /**
     * 登录失败：密码错误
     */
    public static final String HK_ACC_ONLINE_LOGIN_FAIL_ERROR_PASSWORD = "H5530002";

    /**
     * 登录失败：锁定状态
     */
    public static final String HK_ACC_ONLINE_LOGIN_FAIL_LOCKED = "H5530001";
    /**
     * 旧登陆密码错误
     */
    public static final String HK_ACC_ONLINE_OLD_LOGIN_PASSWORD_ERROR = "H5520008";
    /**
     * 旧交易密码错误
     */
    public static final String HK_ACC_ONLINE_OLD_TRADE_PASSWORD_ERROR = "H5520009";
    /**
     * 新旧密码一致
     */
    public static final String HK_ACC_ONLINE_OLD_NEW_EQUAL = "H5520010";
    /**
     * 交易密码错误
     */
    public static final String HK_ACC_TRADE_PASSWORD_ERROR = "H5520011";

    /**
     * 验证码过期
     */
    public static final String SMS_VERIFY_CODE_EXPIRE_ERROR = "3009";

    // 成功状态是C030000 dtms-product现有的成功码,后续需要调整成0000
    public static final String SUCCESS_C030000 = "C030000";


    /**
     * 修改后的【新手机号】是否已经被其他实名一账通A占用
     */
    public static final String HW_CUSTOMER_PHONE_HBONE_HAVE_AUTH = "H5530055";

    /**
     * 海外手机号对应的一账通号绑定了香港客户号
     */
    public static final String HW_CUSTOMER_HBONE_HAVE_HKCUSTNO = "H5530005";

    /**
     * 海外交易密码错误
     */
    public static final String HW_FUND_TRADE_PASSWORD_ERROR_CODE = "C021008";

    /** 一账通号对应的绑定记录不存在 */
    public static final String ACC_CENTER_OUTER_ACCT_LOGIN_BIND_ISNULL = "5220163";

    /**
     * http请求成功
     */
    public static final String HTTP_SUCCESS_CODE = "200";



}