package com.howbuy.crm.cgi.common.exception;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 参数异常
 * @date 2023/6/8 13:43
 * @since JDK 1.8
 */
@Getter
public class ParamsException extends RuntimeException {
    private String code;
    private String desc;

    public ParamsException(ExceptionCodeEnum result) {
        super(result.getDescription());
        this.code = result.getCode();
        this.desc = result.getDescription();
    }

    public ParamsException(String code, String desc) {
        super(desc);
        this.code = code;
        this.desc = desc;
    }

    public ParamsException(ExceptionCodeEnum result, Throwable e) {
        super(result.getDescription(), e);
        this.code = result.getCode();
        this.desc = result.getDescription();
    }
}
