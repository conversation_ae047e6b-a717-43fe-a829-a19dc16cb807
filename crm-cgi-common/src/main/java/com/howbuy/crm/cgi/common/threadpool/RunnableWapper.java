/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.threadpool;

import com.howbuy.crm.cgi.common.utils.LoggerUtils;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/3/4 09:51
 * @since JDK 1.8
 */
public class RunnableWapper implements Runnable {

    private String uuid;
    private Runnable task;

    public RunnableWapper(Runnable task) {
        this.task = task;
    }

    public RunnableWapper init() {
        this.uuid = LoggerUtils.getUuid();
        return this;
    }

    public void run() {
        LoggerUtils.setCfgValue("uuid", this.uuid);
        this.task.run();
    }
}