/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.exception;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import lombok.Getter;

/**
 * @description: session过期异常
 * <AUTHOR>
 * @date 2023/5/17 13:51
 * @since JDK 1.8
 */
@Getter
public class SessionTimeOutException extends RuntimeException {
    private static final long serialVersionUID = -4175093176890421424L;

    private String code;
    private String desc;

    public SessionTimeOutException(ExceptionCodeEnum result){
        super(result.getDescription());
        this.code = result.getCode();
        this.desc = result.getDescription();
    }
    public SessionTimeOutException(String code, String desc){
        super(desc);
        this.code = code;
        this.desc = desc;
    }

    public SessionTimeOutException(ExceptionCodeEnum result,Throwable e){
        super(result.getDescription(),e);
        this.code = result.getCode();
        this.desc = result.getDescription();
    }
}