/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.utils;

import com.howbuy.crm.cgi.common.base.CgiResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.enums.TmsReturnCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;

/**
 * @description: (中台调用判断)
 * <AUTHOR>
 * @date 2024/2/29 16:13
 * @since JDK 1.8
 */
public class TmsInvokerUtil {

    public static boolean isSuccess(String returnCode) {
        return TmsReturnCodeEnum.SUCC_TMS.getCode().equals(returnCode) || TmsReturnCodeEnum.SUCC_NEW.getCode().equals(returnCode) ||TmsReturnCodeEnum.SUCC.getCode().equals(returnCode);
    }

    public static boolean isSuccessException(CgiResponse response) {
        if (response == null) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR);
        }
        if(isSuccess(response.getCode())){
            return true;
        }
        throw new BusinessException(response.getCode(), response.getDescription());
    }

    public static boolean isSuccess(CgiResponse response) {
        if (response == null) {
            return false;
        }
        return isSuccess(response.getCode());
    }
}