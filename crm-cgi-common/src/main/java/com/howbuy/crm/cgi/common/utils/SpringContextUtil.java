package com.howbuy.crm.cgi.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

/**
 * @description:(请在此添加描述)
 * @author: jinqing.rao
 * @date: 2024/1/3 15:53
 * @since JDK 1.8
 */
@Configuration
public class SpringContextUtil implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextUtil.applicationContext = applicationContext;
    }

    /**
     * 从Spring上下文中获取指定名称的Bean实例
     * @param beanName Bean名称
     * @return Bean实例
     */
    public static Object getBean(String beanName) {
        return applicationContext.getBean(beanName);
    }

    /**
     * 从Spring上下文中获取指定类型的Bean实例
     * @param clazz Bean类型
     * @param <T> Bean类型泛型
     * @return Bean实例
     */
    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    /**
     * 从Spring上下文中获取指定名称和类型的Bean实例
     * @param beanName Bean名称
     * @param clazz Bean类型
     * @param <T> Bean类型泛型
     * @return Bean实例
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        return applicationContext.getBean(beanName, clazz);
    }
}