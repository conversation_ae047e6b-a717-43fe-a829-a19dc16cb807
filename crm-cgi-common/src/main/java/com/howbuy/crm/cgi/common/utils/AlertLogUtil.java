package com.howbuy.crm.cgi.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cgi.common.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * 告警日志工具类
 *
 * @author: shaoyang.li
 * @date: 2024/4/19 19:12
 * @since JDK 1.8
 */
public final class AlertLogUtil {

    private static final Logger alertLog = LoggerFactory.getLogger("alertJsonLog");

    /**
     * 告警日志
     *
     * @param clazzName 告警来源类名
     * @param msg       告警信息
     */
    public static void alert(String clazzName, String msg) {
        alert(Constants.ALERT_TOPIC_CRM_CGI_BUSINISE, clazzName, msg);
    }

    /**
     * 告警日志
     *
     * @param topic     告警主题
     * @param clazzName 告警来源类名
     * @param msg       告警信息
     */
    public static void alert(String topic, String clazzName, String msg) {
        String traceId = MDC.get(Constants.TRACE_ID);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateUtils.getCurrentDate(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS));
        jsonObject.put("topic", topic);
        jsonObject.put("tid", traceId);
        jsonObject.put("class", clazzName);
        jsonObject.put("msg", msg);
        alertLog.info("{}", jsonObject.toJSONString());
    }

    private AlertLogUtil() {
    }
}
