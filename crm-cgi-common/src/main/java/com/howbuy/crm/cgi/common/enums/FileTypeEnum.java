/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * @description: (补充协议类型)
 * <AUTHOR>
 * @date 2023/7/24 19:19
 * @since JDK 1.8
 */
public enum FileTypeEnum {

    /**
     * 补充协议1
     */
    BCXY1_TYPE("C7","补充协议1"),

    /**
     * 补充协议2
     */
    BCXY2_TYPE("C8","补充协议2"),

    BCXY3_TYPE("C9","补充协议3"),

    PURCHASE_CONTRACT_TYPE_T1("T1","买入合同签署文件类型"),

    PURCHASE_CONTRACT_TYPE_T2("T2","卖出合同签署文件类型"),

    SUB_PAID_CONTRACT_TYPE_T3("T3","认缴实缴合同签署文件类型"),

    /**
     * 线上补充协议
     */
    ONLINE_SUPPLEMENTAL_AGREEMENT_TYPE("A1","线上补充协议"),


    ;


    /**
     * 规则key
     */
    private String key;
    /**
     * 规则名称
     */
    private String name;

    FileTypeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    /**
     * @description: 通过代码获取名称
     * @param key
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/6/20 09:36
     * @since JDK 1.8
     */
    public static String getName(String key) {
        for (FileTypeEnum enumObj : FileTypeEnum.values()) {
            if (key != null && key.equals(enumObj.getKey())) {
                return enumObj.getName();
            }
        }
        return null;
    }
}