/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.cacheservice.lock;


import com.howbuy.crm.cgi.common.cacheservice.AbstractCacheService;

/**
 * @description: 全局锁服务类，该类实现了对唯一标识加锁的操作
 * <AUTHOR>
 * @date 2023/3/8 17:38
 * @since JDK 1.8
 */
public class LockService extends AbstractCacheService {
    private static final String LOCK_VALUE = " ";

    /**
     *
     * getLock:获取key的锁,true:获取锁成功；false：获取锁失败
     *
     * @param key
     *            锁的唯一标识
     * @return
     * @return boolean true:获取锁成功；false：获取锁失败
     * <AUTHOR>
     * @date 2016年10月28日 下午1:54:27
     */
    public boolean getLock(String key) {
        return getLock(key,120);
    }

    /**
     *
     * getLock:获取key的锁,true:获取锁成功；false：获取锁失败
     *
     * @param key
     *            锁的唯一标识
     * @param seconds
     *            锁过期时间，单位：秒
     * @return
     * @return boolean true:获取锁成功；false：获取锁失败
     * <AUTHOR>
     * @date 2016年10月28日 下午2:00:18
     */
    public boolean getLock(String key, int seconds) {
        Long result = CACHE_SERVICE.putToSet(key, LOCK_VALUE);
        // 1成功，0失败
        if (result == 1L) {
            CACHE_SERVICE.expires(key, seconds);
            return true;
        }
        return false;
    }

    /**
     *
     * releaseLock:释放锁
     *
     * @param key
     *            锁的唯一标识
     * @return void
     * <AUTHOR>
     * @date 2016年10月28日 下午3:46:21
     */
    public void releaseLock(String key) {
        CACHE_SERVICE.remove(key);
    }
}