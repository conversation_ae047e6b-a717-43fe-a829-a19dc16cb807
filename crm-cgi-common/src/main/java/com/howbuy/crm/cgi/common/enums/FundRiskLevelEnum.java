package com.howbuy.crm.cgi.common.enums;

import java.util.stream.Stream;

/**
 * @description:
 * 产品风险等级 1：“R1-低风险”；2：“R2-中低风险”；3：“R3-中风险”；4：“R4-中高风险”；5：“R5-高风险”
 * @author: shuai.zhang
 * @date: 2023/5/29
 * @since JDK 1.8
 * @version: 1.0
 */
public enum FundRiskLevelEnum {

    R1("1", "R1-低风险"),
    R2("2", "R2-中低风险"),
    R3("3", "R3-中风险"),
    R4("4", "R4-中高风险"),
    R5("5", "R5-高风险");

    private final String key;
    private final String desc;

    public static FundRiskLevelEnum getFundRiskLevelEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        FundRiskLevelEnum fundRiskLevelEnum = getFundRiskLevelEnum(code);
        return fundRiskLevelEnum == null ? null : fundRiskLevelEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    FundRiskLevelEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
