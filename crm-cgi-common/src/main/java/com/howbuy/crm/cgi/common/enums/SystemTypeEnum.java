/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * <AUTHOR>
 * @description: (操作系统枚举类)
 * @date 2023/12/28 16:17
 * @since JDK 1.8
 */
public enum SystemTypeEnum {

    /**
     * 安卓
     */
    ANDROID("android","32194908"),
    /**
     * ios
     */
    IOS("ios","28294625"),


    IPHONE("iPhone","28294625");


    private String systemType;
    private String code;

    private SystemTypeEnum(String systemType, String code) {
        this.systemType = systemType;
        this.code = code;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static String getCode(String systemType) {
        for (SystemTypeEnum systemTypeEnum : SystemTypeEnum.values()) {
            if (systemTypeEnum.getSystemType().equalsIgnoreCase(systemType)) {
                return systemTypeEnum.getCode();
            }
        }
        return "";
    }

}