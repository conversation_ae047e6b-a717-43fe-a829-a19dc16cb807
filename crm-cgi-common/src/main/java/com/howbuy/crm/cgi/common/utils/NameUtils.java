package com.howbuy.crm.cgi.common.utils;

import org.springframework.util.CollectionUtils;

import java.util.*;
/**
 * @description: 姓名拆分工具类
 * @author: jinqing.rao
 * @date: 2023/12/6 16:59
 * @since JDK 1.8
 */
public class NameUtils {

    /**
     * 复姓（两字）,国内现存81个。末尾添加三字以上的部分满姓
     */
    private static final List<String> SURNAME_NOW = Arrays.asList("欧阳","太史","端木","上官","司马","东方","独孤","南宫","万俟","闻人","夏侯","诸葛","尉迟","公羊","赫连","澹台","皇甫","宗政","濮阳","公冶","太叔","申屠","公孙","慕容","仲孙","钟离","长孙","宇文","司徒","鲜于","司空","闾丘","子车","亓官","司寇","巫马","公西","颛孙","壤驷","公良","漆雕","乐正","宰父","谷梁","拓跋","夹谷","轩辕","令狐","段干","百里","呼延","东郭","南门","羊舌","微生","公户","公玉","公仪","梁丘","公仲","公上","公门","公山","公坚","左丘","公伯","西门","公祖","第五","公乘","贯丘","公皙","南荣","东里","东宫","仲长","子书","子桑","即墨","达奚","褚师","萨嘛喇","赫舍里","萨克达","钮祜禄","他塔喇","喜塔腊","库雅喇","瓜尔佳","舒穆禄","索绰络","叶赫那拉","依尔觉罗","额尔德特","讷殷富察","叶赫那兰","爱新觉罗","依尔根觉罗");

    /**
     *姓名中的姓
     */
    public static final String SUR_NAME = "surName";


    /**
     *姓名中的名
     */
    public static final String NAME = "name";



    /**
     * 获取姓氏与姓名<br>
     * 姓名在两字时，首字为姓。<br>
     * 姓名大于两字时，优先匹配复姓。<br>
     * @param name 姓名
     * @return map类型数据，姓氏为key值“surName”，名字为value值“name”
     * @author: jinqing.rao
     * @date: 2023/12/6 17:01
     * @since JDK 1.8
     */
    public static Map<String,String> getSurName(List<String> surnameList,String name) {
        //如果复姓没有数据，则使用默认数据
        if(CollectionUtils.isEmpty(surnameList)){
            surnameList = SURNAME_NOW;
        }
        Map<String,String> mapData = new HashMap<>();
        Optional<String> optional = surnameList.stream().filter(name::startsWith).findFirst();
        mapData.put(SUR_NAME, optional.orElseGet(() -> name.substring(0, 1)));
        mapData.put(NAME, optional.map(s -> name.substring(s.length())).orElseGet(() -> name.substring(1)));
        return mapData;
    }
}

