/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/2/29 16:17
 * @since JDK 1.8
 */
public enum TmsReturnCodeEnum {
    /**
     * 成功
     */
    SUCC("0000", "成功"),
    /**
     * 成功
     */
    SUCC_NEW("0000000", "成功"),

    /**
     * 中台成功
     */
    SUCC_TMS("Z0000000", "成功");

    /**
     * 代码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    private TmsReturnCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}