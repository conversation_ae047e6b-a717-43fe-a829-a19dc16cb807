/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.common.enums;

/**
 * <AUTHOR>
 * @description: 消息类型和模板枚举
 * @date 2023/5/18 10:18
 * @since JDK 1.8
 */
public enum MessageTempleteEnum {
    LOGIN_MOBILE_VERIFYCODE("mbvclg", "60422", "手机验证码登录"),
    MOBILE_VERIFY_VERIFYCODE("mbvcvy", "60422", "手机验证验证码"),
    LOGIN_ACTIVATE_MOBILE_VERIFYCODE("mbvclgat", "60424", "登录激活手机验证码"),
    LOGIN_ACTIVATE_EMAIL_VERIFYCODE("emvclgat", "60427", "登录激活邮箱验证码"),
    TRADE_ACTIVATE_MOBILE_VERIFYCODE("mbvctaat", "60423", "交易激活手机验证码"),
    TRADE_ACTIVATE_EMAIL_VERIFYCODE("emvctaat", "60426", "交易激活邮箱验证码"),
    RESET_LOGIN_PASSWORD_MOBILE_VERIFYCODE("mbvcrslg", "60424", "重置登录密码手机验证码"),
    RESET_LOGIN_PASSWORD_EMAIL_VERIFYCODE("emvcrslg", "60427", "重置登录密码邮箱验证码"),
    RESET_TRADE_PASSWORD_MOBILE_VERIFYCODE("mbvcrsta", "60425", "重置交易密码手机验证码"),
    RESET_TRADE_PASSWORD_EMAIL_VERIFYCODE("emvcrsta", "60428", "重置交易密码邮箱验证码"),

     /**
     * 绑定一账通香港手机号短信验证码
     */
    BIND_HK_MOBILE_VERIFYCODE("mbvchbm1", "60454", "绑定一账通"),
    /**
     * 解绑一账通香港手机号短信验证码
     */
    UNBIND_HK_MOBILE_VERIFYCODE("mbvchbm2", "60454", "解绑一账通"),
    /**
     * 解绑一账通好买手机号短信验证码
     */
    UNBIND_HB_MOBILE_VERIFYCODE("mbvchbm3", "60454", "解绑一账通"),
    /**
     * 绑定一账通好买手机号短信验证码
     */
    BIND_HB_MOBILE_VERIFYCODE("mbvchbm4", "60454", "绑定一账通"),
    /**
     * 绑定手机号短信验证码
     */
    BIND_MOBILE_VERIFYCODE("emvcrsta5", "60453", "绑定手机号"),
    /**
     * 修改手机号短信验证码
     */
    EDIT_MOBILE_VERIFYCODE("mbvchbm6", "60453", "修改手机号"),
    /**
     * 手机号 --- 填写开户资料
     */
    MOBILE_WRITE_OPEN_FILE("mbvchbm7", "60453", "填写开户资料"),
    /**
     * 设置交易密码短信验证码
     */
    SET_TRADE_PASSWORD_MOBILE_VERIFYCODE("mbvcrsta8", "60453", "设置交易密码"),
    /**
     * 设置交易密码邮箱验证码
     */
    SET_TRADE_PASSWORD_EMAIL_VERIFYCODE("emvcrsta9", "60455", "设置交易密码"),
    /**
     * 修改邮箱邮箱验证码
     */
    EDIT_EMAIL_VERIFYCODE("emvcrsta10", "60455", "修改邮箱"),
    /**
     * 绑定邮箱邮箱验证码
     */
    BIND_EMAIL_VERIFYCODE("emvcrsta11", "60455", "绑定邮箱"),
    /**
     * 绑定一账通 香港邮箱验证码
     */
    BIND_HK_EMAIL_VERIFYCODE("emvchbm12", "60455", "绑定一账通"),
    /**
     * 解绑一账通 香港邮箱验证码
     */
    UNBIND_HK_EMAIL_VERIFYCODE("emvchbm13", "60455", "解绑一账通"),
    /**
     * 邮箱 --- 填写开户资料
     */
    EMAIL_WRITE_OPEN_FILE("mbvchbm14", "60455", "填写开户资料"),
    HK_FUND_PURCHASE_VERIFY_CODE("mbvchbm15", "60453","申请购买海外产品"),
    HK_FUND_REDEEM_VERIFY_CODE("mbvchbm16", "60453","申请赎回海外产品"),

    HK_FUND_PURCHASE_EMAIL_VERIFY_CODE("mbvctaat61","60455" ,"申请购买海外产品"),
    HK_FUND_REDEEM_EMAIL_VERIFY_CODE("mbvctaat62","60455","申请赎回海外产品"),
    // 买入语音验证码
    BUY_VOICE_CODE("voice65", "60453", "买入语音验证码"),
    // 卖出语音验证码
    SELL_VOICE_CODE("voice66", "60454", "卖出语音验证码");
    ;

    /**
     * 消息类型，消息中心限制长度不能超过10
     */
    private final String code;

    /**
     * 消息模板ID
     */
    private final String businessId;
    /**
     * 消息名称
     */
    private final String name;

    MessageTempleteEnum(String code, String businessId, String name) {
        this.code = code;
        this.businessId = businessId;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getBusinessId() {
        return businessId;
    }

    public String getName() {
        return name;
    }
}
