package com.howbuy.crm.cgi.manager.outerservice.crmaccount;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade;
import com.howbuy.crm.account.client.request.commvisit.*;
import com.howbuy.crm.account.client.response.ExportToFileVO;

import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.*;
import com.howbuy.crm.cgi.common.constants.MarkConstants;
import com.howbuy.crm.cgi.manager.domain.crmaccount.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 拜访纪要防腐层服务
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Slf4j
@Service
public class CmVisitMinutesOuterService {

    @DubboReference(registry = "crm-account-remote", check = false)
    private CmVisitMinutesFacade cmVisitMinutesFacade;

    /**
     * @description 查询客户拜访纪要列表
     * @param queryDTO 查询参数
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.VisitMinutesListDTO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public VisitMinutesListDTO getVisitMinutesList(VisitMinutesQueryDTO queryDTO) {
        log.info("CmVisitMinutesOuterService_getVisitMinutesList_request:{}", JSON.toJSONString(queryDTO));
        try {
            // 构建请求参数
            QueryVisitMinutesListRequest request = new QueryVisitMinutesListRequest();
            request.setOrgCode(queryDTO.getOrgCode());
            request.setConsCode(queryDTO.getConsCode());
            request.setVisitDateStart(queryDTO.getVisitDateStart());
            request.setVisitDateEnd(queryDTO.getVisitDateEnd());
            request.setCreateDateStart(queryDTO.getCreateDateStart());
            request.setCreateDateEnd(queryDTO.getCreateDateEnd());
            request.setVisitPurpose(queryDTO.getVisitPurpose());
            request.setCustName(queryDTO.getCustName());
            request.setConsCustNo(queryDTO.getConsCustNo());
            request.setAccompanyingUser(queryDTO.getAccompanyingUser());
            request.setManagerId(queryDTO.getManagerId());
            request.setFeedbackStatus(queryDTO.getFeedbackStatus());
            request.setPageNo(queryDTO.getPageNo());
            request.setPageSize(queryDTO.getPageSize());
            request.setCurrentUserId(queryDTO.getCurrentUserId());
            
            // 调用防腐层接口
            Response<VisitMinutesListVO> response = cmVisitMinutesFacade.getVisitMinutesList(request);
            log.info("CmVisitMinutesOuterService_getVisitMinutesList_response:{}", JSON.toJSONString(response));
            
            if (!response.isSuccess()) {
                throw new RuntimeException(response.getDescription());
            }
            
            // 转换返回结果
            VisitMinutesListDTO result = new VisitMinutesListDTO();
            result.setTotal(response.getData().getTotal());
            result.setList(convertVisitMinutesList(response.getData().getVisitMinutesList()));
            return result;
        } catch (Exception e) {
            log.error("查询客户拜访纪要列表失败", e);
            throw new RuntimeException("查询客户拜访纪要列表失败:" + e.getMessage());
        }
    }

    /**
     * @description 对象转换
     * @param voList
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.crmaccount.VisitMinutesListDTO.VisitMinutesItemDTO>
     * @author: jianjian.yang
     * @date: 2025/4/11 14:55
     * @since JDK 1.8
     */
    private List<VisitMinutesListDTO.VisitMinutesItemDTO> convertVisitMinutesList(List<VisitMinutesVO> voList) {
        List<VisitMinutesListDTO.VisitMinutesItemDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(voList)) {
            for (VisitMinutesVO vo : voList) {
                VisitMinutesListDTO.VisitMinutesItemDTO dto = new VisitMinutesListDTO.VisitMinutesItemDTO();
                dto.setId(vo.getId());
                dto.setCreateTime(vo.getCreateTime());
                dto.setVisitDt(vo.getVisitDt());
                dto.setVisitPurpose(vo.getVisitPurpose());
                dto.setConsCustNo(vo.getConsCustNo());
                dto.setCustName(vo.getCustName());
                dto.setCreatorName(vo.getCreatorName());
                dto.setCenterName(vo.getCenterName());
                dto.setAreaName(vo.getAreaName());
                dto.setBranchName(vo.getBranchName());
                dto.setVisitType(vo.getVisitType());
                dto.setMarketVal(vo.getMarketVal());
                dto.setHealthAvgStar(vo.getHealthAvgStar());
                dto.setGiveInformation(vo.getGiveInformation());
                dto.setAttendRole(vo.getAttendRole());
                dto.setProductServiceFeedback(vo.getProductServiceFeedback());
                dto.setIpsFeedback(vo.getIpsFeedback());
                dto.setAddAmount(vo.getAddAmount());
                dto.setFocusAsset(vo.getFocusAsset());
                dto.setEstimateNeedBusiness(vo.getEstimateNeedBusiness());
                dto.setNextPlan(vo.getNextPlan());
                dto.setAccompanyingType(vo.getAccompanyingType());
                dto.setAccompanyingUser(vo.getAccompanyingUser());
                dto.setAccompanySummary(vo.getAccompanySummary());
                dto.setAccompanySuggestion(vo.getAccompanySuggestion());
                dto.setManagerName(vo.getManagerName());
                dto.setManagerSummary(vo.getManagerSummary());
                dto.setManagerSuggestion(vo.getManagerSuggestion());
                dto.setCanAccompanyFeedback(vo.getCanAccompanyFeedback());
                dto.setCanManagerFeedback(vo.getCanManagerFeedback());
                dtoList.add(dto);
            }
        }
        return dtoList;
    }

    /**
     * @description 查询客户拜访纪要反馈明细
     * @param queryDTO 查询参数
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.VisitFeedbackDetailDTO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public VisitFeedbackDetailDTO getVisitFeedbackDetail(VisitFeedbackQueryDTO queryDTO) {
        log.info("CmVisitMinutesOuterService_getVisitFeedbackDetail_request:{}", JSON.toJSONString(queryDTO));
        try {
            // 构建请求参数
            QueryVisitMinutesForFeedbackRequest request = new QueryVisitMinutesForFeedbackRequest();
            request.setVisitMinutesId(queryDTO.getVisitMinutesId());
            request.setFeedbackType(queryDTO.getFeedbackType());
            request.setCurrentUserId(queryDTO.getCurrentUserId());
            
            // 调用防腐层接口
            Response<VisitMinutesForFeedbackVO> response = cmVisitMinutesFacade.getVisitMinutesForFeedback(request);
            log.info("CmVisitMinutesOuterService_getVisitFeedbackDetail_response:{}", JSON.toJSONString(response));
            
            if (!response.isSuccess()) {
                throw new RuntimeException(response.getDescription());
            }
            if (response.getData() == null) {
                return null;
            }
            VisitMinutesForFeedbackVO vo = response.getData();
            // 转换返回结果
            VisitFeedbackDetailDTO result = new VisitFeedbackDetailDTO();
            result.setCustName(vo.getCustName());
            result.setConsCustNo(vo.getConsCustNo());
            result.setVisitDt(vo.getVisitDt());
            result.setVisitType(vo.getVisitType());
            result.setMarketVal(vo.getMarketVal());
            result.setHealthAvgStar(vo.getHealthAvgStar());
            result.setVisitPurpose(vo.getVisitPurposeList());
            result.setVisitPurposeOther(vo.getVisitPurposeOther());
            result.setReportId(vo.getReportId());
            result.setReportTitle(vo.getReportTitle());
            result.setGiveInformation(vo.getGiveInformation());
            if(CollectionUtils.isNotEmpty(vo.getAccompanyingList())) {
                result.setAccompanyingUser(Joiner.on(MarkConstants.SEPARATOR_CAESURA).join(
                        vo.getAccompanyingList().stream().filter(Objects::nonNull).collect(Collectors.toList())));
            }
            result.setAttendRole(vo.getAttendRole());
            result.setProductServiceFeedback(vo.getProductServiceFeedback());
            result.setIpsFeedback(vo.getIpsFeedback());
            result.setAddAmountRmb(vo.getAddAmountRmb());
            result.setAddAmountForeign(vo.getAddAmountForeign());
            result.setFocusAsset(vo.getFocusAsset());
            result.setEstimateNeedBusiness(vo.getEstimateNeedBusiness());
            result.setNextPlan(vo.getNextPlan());
            result.setUserName(vo.getAccompanyingUserName());
            result.setAccompanyingId(vo.getAccompanyingId());
            result.setManagerName(vo.getManagerName());
            result.setSummary(vo.getSummary());
            result.setSuggestion(vo.getSuggestion());
            result.setCanNotEditSummary(vo.getCanNotEditSummary());
            
            return result;
        } catch (Exception e) {
            log.error("查询客户拜访纪要反馈明细失败", e);
            throw new RuntimeException("查询客户拜访纪要反馈明细失败:" + e.getMessage());
        }
    }

    /**
     * @description 导出客户拜访纪要列表
     * @param exportDTO 导出参数
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.ExportToFileDTO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public ExportToFileDTO exportVisitMinutes(ExportVisitMinutesDTO exportDTO) {
        log.info("CmVisitMinutesOuterService_exportVisitMinutes_request:{}", JSON.toJSONString(exportDTO));
        try {
            // 构建请求参数
            ExportVisitMinutesRequest request = new ExportVisitMinutesRequest();
            BeanUtils.copyProperties(exportDTO, request);
            
            // 调用防腐层接口
            Response<ExportToFileVO> response = cmVisitMinutesFacade.exportVisitMinutes(request);
            log.info("CmVisitMinutesOuterService_exportVisitMinutes_response:{}", JSON.toJSONString(response));
            
            if (!response.isSuccess()) {
                throw new RuntimeException(response.getDescription());
            }
            
            // 转换返回结果
            ExportToFileDTO result = new ExportToFileDTO();
            BeanUtils.copyProperties(response.getData(), result);
            return result;
        } catch (Exception e) {
            log.error("导出客户拜访纪要列表失败，exportDTO:{}", JSON.toJSONString(exportDTO), e);
            throw new RuntimeException("导出客户拜访纪要列表失败:" + e.getMessage());
        }
    }

    /**
     * @description 查询客户拜访纪要明细
     * @param queryDTO 查询参数
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.VisitMinutesDetailDTO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public VisitMinutesDetailDTO getVisitMinutesDetail(VisitMinutesDetailQueryDTO queryDTO) {
        log.info("CmVisitMinutesOuterService_getVisitMinutesDetail_request:{}", JSON.toJSONString(queryDTO));
        try {
            // 构建请求参数
            QueryVisitMinutesDetailRequest request = new QueryVisitMinutesDetailRequest();
            BeanUtils.copyProperties(queryDTO, request);
            
            // 调用防腐层接口
            Response<VisitMinutesDetailVO> response = cmVisitMinutesFacade.getVisitMinutesDetail(request);
            log.info("CmVisitMinutesOuterService_getVisitMinutesDetail_response:{}", JSON.toJSONString(response));
            
            if (!response.isSuccess()) {
                throw new RuntimeException(response.getDescription());
            }
            if (response.getData() == null) {
                return null;
            }
            // 转换返回结果
            return convertVisitMinutesDetail(response.getData());
        } catch (Exception e) {
            log.error("查询客户拜访纪要明细失败", e);
            throw new RuntimeException("查询客户拜访纪要明细失败:" + e.getMessage());
        }
    }

    /**
     * @description 转换客户拜访纪要明细
     * @param vo 客户拜访纪要明细VO
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.VisitMinutesDetailDTO
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */
    private VisitMinutesDetailDTO convertVisitMinutesDetail(VisitMinutesDetailVO vo) {
        VisitMinutesDetailDTO result = new VisitMinutesDetailDTO();
        result.setCustName(vo.getCustName());
        result.setConsCustNo(vo.getConsCustNo());
        result.setVisitDt(vo.getVisitDt());
        result.setVisitType(vo.getVisitType());
        result.setMarketVal(vo.getMarketVal());
        result.setHealthAvgStar(vo.getHealthAvgStar());
        result.setVisitPurposeList(vo.getVisitPurposeList());
        result.setVisitPurposeOther(vo.getVisitPurposeOther());
        result.setReportId(vo.getReportId());
        result.setGiveInformation(vo.getGiveInformation());
        result.setAccompanyingUser(vo.getAccompanyingUser());
        result.setAttendRole(vo.getAttendRole());
        result.setProductServiceFeedback(vo.getProductServiceFeedback());
        result.setIpsFeedback(vo.getIpsFeedback());
        result.setAddAmountRmb(vo.getAddAmountRmb());
        result.setAddAmountForeign(vo.getAddAmountForeign());
        result.setFocusAsset(vo.getFocusAsset());
        result.setEstimateNeedBusiness(vo.getEstimateNeedBusiness());
        result.setNextPlan(vo.getNextPlan());
        result.setManagerName(vo.getManagerName());
        result.setAccompanyingList(convertAccompanyingList(vo.getAccompanyingList()));
        result.setCanEdit(vo.getCanEditData());
        return result;
    }

    /**
     * @description 转换陪访人/主管反馈列表
     * @param voList 陪访人/主管反馈列表VO
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.crmaccount.AccompanyingDTO>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */ 
    private List<AccompanyingDTO> convertAccompanyingList(List<AccompanyingVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }
        return voList.stream().map(this::convertAccompanying).collect(Collectors.toList());
    }

    /**
     * @description 转换陪访人/主管反馈
     * @param vo 陪访人/主管反馈VO
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.AccompanyingDTO
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */
    private AccompanyingDTO convertAccompanying(AccompanyingVO vo) {
        AccompanyingDTO result = new AccompanyingDTO();
        result.setUserName(vo.getUserName());
        result.setManagerName(vo.getManagerName());
        result.setSummary(vo.getSummary());
        result.setSuggestion(vo.getSuggestion());
        return result;
    }


    /**
     * @description 保存陪访人/主管反馈
     * @param saveDTO 保存参数
     * @return java.lang.Boolean
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public String saveFeedback(SaveVisitFeedbackDTO saveDTO) {
        log.info("CmVisitMinutesOuterService_saveFeedback_request:{}", JSON.toJSONString(saveDTO));
        try {
            // 构建请求参数
            SaveFeedbackRequest request = new SaveFeedbackRequest();
            request.setVisitMinutesId(saveDTO.getVisitMinutesId());
            request.setAccompanyingId(saveDTO.getAccompanyingId());
            request.setFeedbackType(saveDTO.getFeedbackType());
            request.setSummary(saveDTO.getSummary());
            request.setSuggestion(saveDTO.getSuggestion());
            request.setCurrentUserId(saveDTO.getCurrentUserId());
            
            // 调用防腐层接口
            Response<OperationResultVO> response = cmVisitMinutesFacade.saveFeedback(request);
            log.info("CmVisitMinutesOuterService_saveFeedback_response:{}", JSON.toJSONString(response));
            
            if (response.isSuccess()) {
                return response.getData().getMessage();
            }
            
            return null;
        } catch (Exception e) {
            log.error("保存陪访人/主管反馈失败", e);
            throw new RuntimeException("保存陪访人/主管反馈失败:" + e.getMessage());
        }
    }

    /**
     * @description 保存客户反馈
     * @param saveDTO 保存参数
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public String saveCustFeedback(SaveCustFeedbackDTO saveDTO) {
        log.info("CmVisitMinutesOuterService_saveCustFeedback_request:{}", JSON.toJSONString(saveDTO));
        try {
            // 构建请求参数
            SaveCustFeedbackRequest request = new SaveCustFeedbackRequest();
            request.setVisitMinutesId(saveDTO.getVisitMinutesId());
            // 转换陪访人/主管反馈列表
            request.setAccompanyingList(convertAccompanyingRequest(saveDTO.getAccompanyingList()));
            request.setAttendRole(saveDTO.getAttendRole());
            request.setProductServiceFeedback(saveDTO.getProductServiceFeedback());
            request.setIpsFeedback(saveDTO.getIpsFeedback());
            request.setAddAmtRmb(saveDTO.getAddAmtRmb());
            request.setAddAmtForeign(saveDTO.getAddAmtForeign());
            request.setFocusAsset(saveDTO.getFocusAsset());
            request.setEstimateNeedBusiness(saveDTO.getEstimateNeedBusiness());
            request.setNextPlan(saveDTO.getNextPlan());
            request.setCurrentUserId(saveDTO.getCurrentUserId());
            
            // 调用防腐层接口
            Response<OperationResultVO> response = cmVisitMinutesFacade.saveCustFeedback(request);
            log.info("CmVisitMinutesOuterService_saveCustFeedback_response:{}", JSON.toJSONString(response));
            
            if (response.isSuccess()) {
                return response.getData().getMessage();
            }
            
            return null;
        } catch (Exception e) {
            log.error("保存客户反馈失败", e);
            throw new RuntimeException("保存客户反馈失败:" + e.getMessage());
        }
    }

    /** 
     * @description 转换客户反馈
     * @param dtoList 客户反馈DTO列表
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.crmaccount.SaveCustFeedbackRequest.AccompanyingRequest>
     * @author: jianjian.yang
     * @date: 2025/4/11 15:00
     * @since JDK 1.8
     */
    private List<SaveCustFeedbackRequest.AccompanyingRequest> convertAccompanyingRequest(List<SaveCustFeedbackDTO.AccompanyingInfo> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        List<SaveCustFeedbackRequest.AccompanyingRequest> requestList = new ArrayList<>();
        for (SaveCustFeedbackDTO.AccompanyingInfo dto : dtoList) {
            SaveCustFeedbackRequest.AccompanyingRequest request = new SaveCustFeedbackRequest.AccompanyingRequest();
            request.setAccompanyingType(dto.getAccompanyingType());
            request.setAccompanyingUserId(dto.getAccompanyingUserId());
            requestList.add(request);
        }
        return requestList;
    }


    /**
     * @description 修改主管
     * @param updateDTO 修改参数
     * @return java.lang.Boolean
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public String updateManage(UpdateManageDTO updateDTO) {
        log.info("CmVisitMinutesOuterService_updateManage_request:{}", JSON.toJSONString(updateDTO));
        try {
            // 构建请求参数
            UpdateManagerRequest request = new UpdateManagerRequest();
            request.setVisitMinutesIds(updateDTO.getVisitMinutesIds());
            request.setIsClear(updateDTO.getIsClear());
            request.setNewUserId(updateDTO.getNewUserId());
            request.setCurrentUserId(updateDTO.getCurrentUserId());
            
            // 调用防腐层接口
            Response<OperationResultVO> response = cmVisitMinutesFacade.updateManager(request);
            log.info("CmVisitMinutesOuterService_updateManage_response:{}", JSON.toJSONString(response));
            
            if (response.isSuccess()) {
                return response.getData().getMessage();
            }
            
            return null;
        } catch (Exception e) {
            log.error("修改主管失败", e);
            throw new RuntimeException("修改主管失败:" + e.getMessage());
        }
    }

    /**
     * @description 用户搜索
     * @param searchUserParamDTO 搜索参数
     * @return com.howbuy.crm.cgi.manager.domain.crmaccount.SearchUserDTO
     * @author: jianjian.yang
     * @date: 2025/4/9 16:04
     * @since JDK 1.8
     */
    public SearchUserDTO searchUser(SearchUserParamDTO searchUserParamDTO) {
        log.info("CommunicateOuterService_searchUser_request:{}", JSON.toJSONString(searchUserParamDTO));
        try {
            // 构建请求参数
            SearchUserRequest request = new SearchUserRequest();
            request.setSearchParam(searchUserParamDTO.getSearchParam());
            request.setSearchType(searchUserParamDTO.getSearchType());
            request.setCurrentUserId(searchUserParamDTO.getCurrentUserId());

            // 调用防腐层接口
            Response<SearchUserVO> response = cmVisitMinutesFacade.searchUser(request);
            log.info("CommunicateOuterService_searchUser_response:{}", JSON.toJSONString(response));

            if (!response.isSuccess()) {
                throw new RuntimeException(response.getDescription());
            }

            // 转换返回结果
            SearchUserDTO result = new SearchUserDTO();
            List<SearchUserDTO.UserItem> userList = Lists.newArrayList();
            if(response.getData().getUserList() != null) {
                for (SearchUserVO.UserItem item : response.getData().getUserList()) {
                    SearchUserDTO.UserItem userItem = new SearchUserDTO.UserItem();
                    userItem.setUserId(item.getUserId());
                    userItem.setUserName(item.getUserName());
                    userList.add(userItem);
                }
            }
            result.setUserList(userList);
            return result;
        } catch (Exception e) {
            log.error("用户搜索失败", e);
            throw new RuntimeException("用户搜索失败:" + e.getMessage());
        }
    }
}