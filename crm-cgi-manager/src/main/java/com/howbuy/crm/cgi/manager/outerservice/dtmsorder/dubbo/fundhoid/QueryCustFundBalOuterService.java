/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.fundhoid;

import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.QueryCustFundBalResponseDTO;
import com.howbuy.crm.cgi.manager.utils.OutServiceResultUtils;
import com.howbuy.dtms.order.client.domain.request.fundhold.QueryCustFundBalRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.fundhold.CustFundBalVO;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryCustFundBalResponse;
import com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundBalFacade;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/15 20:00
 * @since JDK 1.8
 */
@Component
public class QueryCustFundBalOuterService {

    @DubboReference(registry = "dtms-order-remote",check = false)
    private QueryCustFundBalFacade queryCustFundBalFacade;

    /**
     * @description: 查询用户基金持仓信息
     * @param hkCustNo	香港客户号
     * @param fundCode  基金编码
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund.QueryCustFundBalResponseDTO
     * @author: jinqing.rao
     * @date: 2025/7/15 20:06
     * @since JDK 1.8
     */
    public List<QueryCustFundBalResponseDTO> queryCustFundBal(String hkCustNo, String fundCode) {
        QueryCustFundBalRequest request = new QueryCustFundBalRequest();
        request.setHkCustNo(hkCustNo);
        request.setFundCode(fundCode);
        Response<QueryCustFundBalResponse> response = queryCustFundBalFacade.queryCustFundBal(request);
        OutServiceResultUtils.checkResultSuccessForDtmsOrder(response);
        QueryCustFundBalResponse responseData = response.getData();
        if(null == responseData){
            return new ArrayList<>();
        }
        List<CustFundBalVO> custFundBalVOList = responseData.getCustFundBalVOList();
        if(CollectionUtils.isEmpty(custFundBalVOList)){
            return new ArrayList<>();
        }
        return custFundBalVOList.stream().map(m -> {
            QueryCustFundBalResponseDTO responseDTO = new QueryCustFundBalResponseDTO();
            responseDTO.setHkCustNo(m.getHkCustNo());
            responseDTO.setFundTxAcctNo(m.getFundTxAcctNo());
            responseDTO.setMainFundCode(m.getMainFundCode());
            responseDTO.setFundCode(m.getFundCode());
            responseDTO.setFundManCode(m.getFundManCode());
            responseDTO.setBalanceVol(m.getBalanceVol());
            responseDTO.setBalanceFactor(m.getBalanceFactor());
            responseDTO.setNavDt(m.getNavDt());
            responseDTO.setVolUpdateDt(m.getVolUpdateDt());
            return responseDTO;
        }).collect(Collectors.toList());
    }

}
