/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkacc.hkbalance;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 币种余额明细DTO
 * @Date 2025/7/11 14:27
 */
@Getter
@Setter
public class CurrencyBalanceDetailDTO implements Serializable {

    private static final long serialVersionUID = -1234567890123456786L;

    /**
     * 货币代码
     */
    private String curCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 在途金额
     */
    private BigDecimal inTransitAmt;

    /**
     * 现金余额(期末余额)
     */
    private BigDecimal cashBalance;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmt;

    /**
     * 可用余额
     */
    private BigDecimal availableBalance;

    /**
     * 对应币种汇率
     */
    private BigDecimal currencyRate;

    /**
     * 换汇币种
     */
    private String exchangeCurrency;

    /**
     * 换汇后现金余额
     */
    private BigDecimal exchangeCashBalance;

    /**
     * 换汇后冻结金额
     */
    private BigDecimal exchangeFrozenAmt;

    /**
     * 换汇后可用余额
     */
    private BigDecimal exchangeAvailableBalance;

}
