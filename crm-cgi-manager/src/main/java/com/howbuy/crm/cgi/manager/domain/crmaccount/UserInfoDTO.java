/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.crmaccount;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: A用户信息数据传输对象
 * @author: jin.wang03
 * @date: 2025/4/8 11:19
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class UserInfoDTO {

    /**
     * 用户编码
     */
    private String code;

    /**
     * 用户名称
     */
    private String name;
} 