/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.portrait.qa;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 问答回复DTO
 *
 * <AUTHOR>
 * @date 2025-03-21 10:26:47
 */
@Getter
@Setter
public class CmPortraitQaReplyDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否香港投顾 1-是 0-否
     */
    private String isHkCons;

    /**
     * 回复层级 1-一级回复 2-二级回复 3-三级回复
     */
    private String replyLevel;

    /**
     * 回复类型 1-标签 2-素材 3-产品报告 4-基金搜索 5-经理搜索 6-机构搜索 7-授权引导
     */
    private String replyType;

    /**
     * 回复标题
     */
    private String replyTitle;

    /**
     * 检索条件
     */
    private String searchCondition;

    /**
     * 一级回复
     */
    private String firstReply;

    /**
     * 二级回复
     */
    private String secondReply;

    /**
     * 回复列表总记录数
     */
    private String total;

    /**
     * 回复列表
     */
    private List<CmPortraitQaReplyAnswerDTO> answerList;
} 