/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.convert.portrait.report;

import com.howbuy.crm.cgi.manager.domain.portrait.report.ProductBalanceReportDTO;
import com.howbuy.crm.portrait.client.domain.response.report.ProductBalanceReportQueryResponse;
import com.howbuy.crm.portrait.client.enums.MaterialSendTypeEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 产品持仓投后报告转换类
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
public class ProductBalanceReportConvert {
    
    private static final ProductBalanceReportConvert INSTANCE = new ProductBalanceReportConvert();

    private ProductBalanceReportConvert() {}

    public static ProductBalanceReportConvert getInstance() {
        return INSTANCE;
    }

    /**
     * 转换为产品持仓投后报告DTO
     * @param response 响应对象
     * @return 产品持仓投后报告DTO
     */
    public ProductBalanceReportDTO toProductBalanceReportDTO(ProductBalanceReportQueryResponse response) {
        if (response == null) {
            return null;
        }
        
        ProductBalanceReportDTO dto = new ProductBalanceReportDTO();
        dto.setTotal(Objects.nonNull(response.getTotal()) ? String.valueOf(response.getTotal()) : "0");
        dto.setMinReportYear(response.getMinReportYear());
        dto.setMaxReportYear(response.getMaxReportYear());
        dto.setDataList(convertYearReportList(response.getDataList()));
        return dto;
    }

    /**
     * 转换年度报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<ProductBalanceReportDTO.YearReportDTO> convertYearReportList(List<ProductBalanceReportQueryResponse.ReportYearData> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<ProductBalanceReportDTO.YearReportDTO> targetList = new ArrayList<>();
        for (ProductBalanceReportQueryResponse.ReportYearData source : sourceList) {
            targetList.add(convertYearReport(source));
        }
        return targetList;
    }

    /**
     * 转换年度报告
     * @param source 源对象
     * @return 目标对象
     */
    private ProductBalanceReportDTO.YearReportDTO convertYearReport(ProductBalanceReportQueryResponse.ReportYearData source) {
        if (source == null) {
            return null;
        }

        ProductBalanceReportDTO.YearReportDTO target = new ProductBalanceReportDTO.YearReportDTO();
        target.setYear(source.getYear());
        target.setReportList(convertReportList(source.getReportList()));
        return target;
    }

    /**
     * 转换报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<ProductBalanceReportDTO.ReportDTO> convertReportList(List<ProductBalanceReportQueryResponse.ReportData> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<ProductBalanceReportDTO.ReportDTO> targetList = new ArrayList<>();
        for (ProductBalanceReportQueryResponse.ReportData source : sourceList) {
            targetList.add(convertReport(source));
        }
        return targetList;
    }

    /**
     * 转换报告
     * @param source 源对象
     * @return 目标对象
     */
    private ProductBalanceReportDTO.ReportDTO convertReport(ProductBalanceReportQueryResponse.ReportData source) {
        if (source == null) {
            return null;
        }

        ProductBalanceReportDTO.ReportDTO target = new ProductBalanceReportDTO.ReportDTO();
        target.setDate(source.getDate());
        target.setTitle(source.getTitle());
        target.setIsNew(source.getIsNew());
        target.setSendNum(source.getSendNum());
        target.setReportId(source.getReportId());
        target.setReportUrl(source.getReportUrl());
        target.setMaterialId(source.getReportId());
        target.setMaterialSendType(MaterialSendTypeEnum.POSITION_REPORT.getCode());
        return target;
    }
} 