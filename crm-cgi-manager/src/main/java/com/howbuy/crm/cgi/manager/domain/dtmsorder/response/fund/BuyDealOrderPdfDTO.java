/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/15 13:43
 * @since JDK 1.8
 */
public class BuyDealOrderPdfDTO implements Serializable {

    private static final long serialVersionUID = -5844838415168608157L;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户名称
     */
    private String custName;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品份额
     */
    private String productVol;

    /**
     *  分红方式 0-红利再投 1-现金分红 2-N/A不适用
     */
    private String fundDivMode;

    /**
     * 交易日期，YYYY-MM-DD
     */
    private String tradeDt;


    /**
     * 币种
     */
    private String currency;


    /**
     * 认购金额
     */
    private BigDecimal buyAmt;

    /**
     * 手续费
     */
    private BigDecimal estimateFee;


    /**
     * 支付方式
     */
    private String payMethodList;

    /**
     * 展期
     */
    private String extendPeriod;


    /**
     * 申请日期
     */
    private String appDt;


    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductVol() {
        return productVol;
    }

    public void setProductVol(String productVol) {
        this.productVol = productVol;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getBuyAmt() {
        return buyAmt;
    }

    public void setBuyAmt(BigDecimal buyAmt) {
        this.buyAmt = buyAmt;
    }

    public BigDecimal getEstimateFee() {
        return estimateFee;
    }

    public void setEstimateFee(BigDecimal estimateFee) {
        this.estimateFee = estimateFee;
    }

    public String getPayMethodList() {
        return payMethodList;
    }

    public void setPayMethodList(String payMethodList) {
        this.payMethodList = payMethodList;
    }

    public String getExtendPeriod() {
        return extendPeriod;
    }

    public void setExtendPeriod(String extendPeriod) {
        this.extendPeriod = extendPeriod;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }
}
