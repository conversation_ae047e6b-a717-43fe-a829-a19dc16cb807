/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkfund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 赎回日历DTO
 * @author: 陈杰文
 * @date: 2025-06-25 11:28:45
 * @since JDK 1.8
 */
@Getter
@Setter
public class RedemptionCalendarDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否支持赎回预约交易
     * 1-是 0-否
     */
    private String supportPrebook;

    /**
     * 预约结束日期
     * 格式：yyyyMMdd
     */
    private String advanceEndDt;

    /**
     * 预约结束时间
     * 格式：HHmmss
     */
    private String advanceEndTm;

    /**
     * 可赎回份额
     */
    private BigDecimal availableVol;

    /**
     * 开放开始日期
     * 格式：yyyyMMdd
     */
    private String openStartDt;

    /**
     * 开放结束日期
     * 格式：yyyyMMdd
     */
    private String openEndDt;

    /**
     * 预计交易日期
     * 格式：yyyyMMdd
     */
    private String openDt;

    /**
     * 下单结束时间，
     * 每日开放产品，取基金基本信息的下单结束时间返回
     * 预约产品，取预计日历的预约结束时间返回
     */
    private String orderEndTm;

    /**
     * 预计上报日期
     * 格式：yyyyMMdd
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     * 格式：HHmmss
     */
    private String preSubmitTaTm;
} 