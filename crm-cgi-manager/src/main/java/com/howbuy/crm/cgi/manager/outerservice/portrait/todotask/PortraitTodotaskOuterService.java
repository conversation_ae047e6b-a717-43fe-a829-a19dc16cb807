/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.todotask;

import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.portrait.client.domain.dto.common.OptResultDTO;
import com.howbuy.crm.portrait.client.domain.request.todotask.PortraitTaskIgnoreRequest;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.domain.response.todotask.PortraitTaskIgnoreResponse;
import com.howbuy.crm.portrait.client.facade.todotask.PortraitTaskIgnoreFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 客户画像-待办任务-outer服务
 * @Date 2024/9/10 14:02
 */
@Slf4j
@Service
public class PortraitTodotaskOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private PortraitTaskIgnoreFacade portraitTaskIgnoreFacade;

    /**
     * 工作台待办任务忽略操作
     *
     * @param taskId 待办任务ID
     * @param consCode 投顾编号
     * @return  PortraitServiceGuideVO
     */
    public OptResultDTO executeIgnoreTask(String taskId,String consCode) {
        PortraitTaskIgnoreRequest ignoreRequest = new PortraitTaskIgnoreRequest();
        ignoreRequest.setConscode(consCode);
        ignoreRequest.setTaskId(taskId);
        Response<PortraitTaskIgnoreResponse> response=portraitTaskIgnoreFacade.execute(ignoreRequest);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode(), response.getDescription());
        }
        return response.getData().getResult();
    }

}
