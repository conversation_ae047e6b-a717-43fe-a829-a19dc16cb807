/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/1 14:51
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryTradeRecordListForTradeListRequestDTO implements Serializable {

    private static final long serialVersionUID = 1923826905131443021L;

    /**
     * 海外中台数据源 在途订单状态传 1 申请成功
     */
    private String tradeStatus;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金类别列表
     * 1-公募、2-私募、9-其他
     */
    private List<String> fundCategoryList;

    /**
     * 默认不传 1 是
     */
    private String piggyFund;

    /**
     * 业务类型列表
     * 1-买入、2-卖出、9-其他
     */
    private List<String> busiTypeList;

    /**
     * 交易状态列表
     * 1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
     */
    private List<String> tradeStatusList;

    /**
     * 持仓状态
     * 1-持有、2-已清仓
     */
    private List<String> holdStatus;

    /**
     * 订单开始时间
     * 格式：YYYYMMdd
     */
    private String orderStartDt;

    /**
     * 订单结束时间
     * 格式：YYYYMMdd
     */
    private String orderEndDt;

    /**
     * 基金交易账号
     */
    private String fundTxCode;

    /**
     * 页号
     */
    private int page;

    /**
     * 每页大小
     */
    private int size;
}
