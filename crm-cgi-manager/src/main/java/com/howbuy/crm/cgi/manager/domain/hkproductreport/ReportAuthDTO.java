/**
 * Copyright (c) 2025, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkproductreport;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 产品报告鉴权DTO
 * @author: 陈杰文
 * @date: 2025-06-19 15:07:57
 * @since JDK 1.8
 */
@Getter
@Setter
public class ReportAuthDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告是否能看，0-否，1-是
     */
    private String isViewed;

    /**
     * 报告url，isViewed为1，则返回报告url，否则返回空
     */
    private String redirectUrl;

    /**
     * 报告类型
     * 1:产品单页,2:认购指南,3:产品合同,4:通知公告,5:运作报告,6:调研报告,7:会议纪要,8:信息披露,9:分配公告,10:其他材料,11:推介材料,12:FAQ,13:项目进展,14:风险揭示书
     */
    private String reportType;
} 