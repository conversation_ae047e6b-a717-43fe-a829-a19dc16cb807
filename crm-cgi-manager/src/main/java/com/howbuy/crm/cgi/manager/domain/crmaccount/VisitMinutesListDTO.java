package com.howbuy.crm.cgi.manager.domain.crmaccount;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * @description: 拜访纪要列表DTO
 * @author: jin.wang03
 * @date: 2025-04-09 10:00:00
 */
@Setter
@Getter
public class VisitMinutesListDTO {
    /**
     * 总记录数
     */
    private Long total;

    /**
     * 拜访纪要列表
     */
    private List<VisitMinutesItemDTO> list;

    @Setter
    @Getter
    public static class VisitMinutesItemDTO {
        /**
         * 主键
         */
        private String id;
        /**
         * 创建日期，格式YYYY-MM-DD HH:MM:SS
         */
        private String createTime;

        /**
         * 拜访日期，格式YYYY-MM-DD
         */
        private String visitDt;

        /**
         * 拜访目的，多个用逗号分隔
         */
        private String visitPurpose;

        /**
         * 投顾客户号
         */
        private String consCustNo;

        /**
         * 客户姓名
         */
        private String custName;

        /**
         * 创建人姓名
         */
        private String creatorName;

        /**
         * 所属中心
         */
        private String centerName;

        /**
         * 所属区域
         */
        private String areaName;

        /**
         * 所属分公司
         */
        private String branchName;

        /**
         * 沟通方式
         */
        private String visitType;

        /**
         * 客户存量
         */
        private String marketVal;

        /**
         * 客户综合健康度
         */
        private String healthAvgStar;

        /**
         * 提供资料
         */
        private String giveInformation;

        /**
         * 参与人员及角色
         */
        private String attendRole;

        /**
         * 产品服务反馈
         */
        private String productServiceFeedback;

        /**
         * IPS报告反馈
         */
        private String ipsFeedback;

        /**
         * 币种+金额+万，多个逗号分隔
         */
        private String addAmount;

        /**
         * 关注资产
         */
        private String focusAsset;

        /**
         * 客户需求
         */
        private String estimateNeedBusiness;

        /**
         * 工作计划
         */
        private String nextPlan;

        /**
         * 陪访人类型
         */
        private String accompanyingType;

        /**
         * 陪访人姓名，多个逗号分隔
         */
        private String accompanyingUser;

        /**
         * 陪访人反馈-概要，格式：姓名:内容，多个分号分隔
         */
        private String accompanySummary;

        /**
         * 陪访人反馈-建议，格式：姓名:内容，多个分号分隔
         */
        private String accompanySuggestion;

        /**
         * 上级主管姓名
         */
        private String managerName;

        /**
         * 主管反馈概要
         */
        private String managerSummary;

        /**
         * 主管反馈建议
         */
        private String managerSuggestion;

        /**
         * 是否可陪访人反馈
         */
        private Boolean canAccompanyFeedback;

        /**
         * 是否可主管反馈
         */
        private Boolean canManagerFeedback;
    }
} 