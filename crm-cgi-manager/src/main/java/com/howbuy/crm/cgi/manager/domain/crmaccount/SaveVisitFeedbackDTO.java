package com.howbuy.crm.cgi.manager.domain.crmaccount;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 保存拜访纪要反馈DTO
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Setter
@Getter
public class SaveVisitFeedbackDTO {
    
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
    
    /**
     * 陪访人数据ID
     */
    private String accompanyingId;
    
    /**
     * 反馈类型 1:陪访人反馈 2:主管反馈
     */
    private String feedbackType;
    
    /**
     * 陪访概要
     */
    private String summary;
    
    /**
     * 工作建议
     */
    private String suggestion;
    /**
     * 当前用户ID
     */
    private String currentUserId;
} 