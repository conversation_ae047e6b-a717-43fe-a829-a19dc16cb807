/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.asset.assetreport;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (基金需要展示的对象属性)
 * <AUTHOR>
 * @date 2024/4/23 17:55
 * @since JDK 1.8
 */
@Data
public class FundShowDTO implements Serializable {
    private String benchMarkName;

    private String startDate;

    private String endDate;

    private String benchMarkType;

    // 用于合并的合并函数
    public void merge(FundShowDTO other) {
        this.benchMarkName += "、" + other.benchMarkName;
    }
}