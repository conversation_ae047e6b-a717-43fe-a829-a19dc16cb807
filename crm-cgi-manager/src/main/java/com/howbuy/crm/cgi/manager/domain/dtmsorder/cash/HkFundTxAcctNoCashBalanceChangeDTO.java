/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.cash;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 现金余额换算实体类
 * @date 2024/8/6 9:28
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkFundTxAcctNoCashBalanceChangeDTO implements Serializable {

    private static final long serialVersionUID = 1415004099672745443L;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 数据日期
     */
    private String dateDt;

    /**
     * 换汇后总现金余额
     */
    private BigDecimal totalExchangeCashBalance;

    /**
     * 换汇后总冻结金额
     */
    private BigDecimal totalExchangeFrozenAmt;

    /**
     * 换汇后总可用余额
     */
    private BigDecimal totalExchangeAvailableBalance;

    /**
     * 基金交易账号明细列表
     */
    private List<FundTxAcctBalanceDTO> fundTxAcctBalanceList;

    /**
     * 基金交易账号余额明细
     */
    @Setter
    @Getter
    public static class FundTxAcctBalanceDTO implements Serializable {

        private static final long serialVersionUID = -1234567890123456787L;

        /**
         * 基金交易账号
         */
        private String fundTxAcctNo;

        /**
         * 换汇后总现金余额
         */
        private BigDecimal exchangeTotalCashBalance;

        /**
         * 换汇后总冻结金额
         */
        private BigDecimal exchangeTotalFrozenAmt;

        /**
         * 换汇后总可用余额
         */
        private BigDecimal exchangeTotalAvailableBalance;

        /**
         * 币种余额明细列表
         */
        private List<CurrencyBalanceDTO> currencyBalanceList;
    }

    /**
     * 币种余额明细
     */
    @Setter
    @Getter
    public static class CurrencyBalanceDTO implements Serializable {

        private static final long serialVersionUID = -1234567890123456786L;

        /**
         * 货币代码
         */
        private String curCode;

        /**
         * 基金交易账号
         */
        private String fundTxAcctNo;

        /**
         * 在途金额
         */
        private BigDecimal inTransitAmt;

        /**
         * 现金余额(期末余额)
         */
        private BigDecimal cashBalance;

        /**
         * 冻结金额
         */
        private BigDecimal frozenAmt;

        /**
         * 可用余额
         */
        private BigDecimal availableBalance;

        /**
         * 对应币种汇率
         */
        private BigDecimal currencyRate;

        /**
         * 换汇币种
         */
        private String exchangeCurrency;

        /**
         * 换汇后现金余额
         */
        private BigDecimal exchangeCashBalance;

        /**
         * 换汇后冻结金额
         */
        private BigDecimal exchangeFrozenAmt;

        /**
         * 换汇后可用余额
         */
        private BigDecimal exchangeAvailableBalance;

    }
}
