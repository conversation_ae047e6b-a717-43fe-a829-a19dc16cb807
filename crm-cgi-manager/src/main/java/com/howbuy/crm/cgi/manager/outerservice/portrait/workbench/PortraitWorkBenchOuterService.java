/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.workbench;

import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.portrait.client.domain.dto.workbench.PortraitWorkBenchDTO;
import com.howbuy.crm.portrait.client.domain.request.workbench.QueryPortraitWorkBenchRequest;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.domain.response.workbench.QueryPortraitWorkBenchResponse;
import com.howbuy.crm.portrait.client.facade.workbench.QueryPortraitWorkBenchFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 客户画像-T@T工作台外部服务
 * @Date 2025年3月4日 15:48:54
 */
@Slf4j
@Service
public class PortraitWorkBenchOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private QueryPortraitWorkBenchFacade queryPortraitWorkBenchFacade;

    /**
     * @description:(查询T@T工作台待办任务数据)
     * @param conscode	
     * @return com.howbuy.crm.portrait.client.domain.dto.workbench.PortraitWorkBenchDTO
     * @author: haoran.zhang
     * @date: 2025/3/10 13:25
     * @since JDK 1.8
     */
    public PortraitWorkBenchDTO getWorkbenchHome(String conscode) {
        QueryPortraitWorkBenchRequest request = new QueryPortraitWorkBenchRequest();
        request.setConscode(conscode);
        Response<QueryPortraitWorkBenchResponse> response = queryPortraitWorkBenchFacade.execute(request);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode(), response.getDescription());
        }
        if (null == response.getData()) {
            log.info("查询T@T工作台待办任务数据失败！ conscode={}", conscode);
            return null;
        }

        return response.getData().getWorkBenchInfo();
    }
}
