/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.agreement;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO;
import com.howbuy.dtms.order.client.domain.request.agreement.QuerySupplementalAgreementRequest;
import com.howbuy.dtms.order.client.domain.request.agreement.SignSupplementalAgreementRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.agreement.QuerySupplementalAgreementResponse;
import com.howbuy.dtms.order.client.domain.response.agreement.SignSupplementalAgreementResponse;
import com.howbuy.dtms.order.client.facade.query.agreement.QuerySupplementalAgreementFacade;
import com.howbuy.dtms.order.client.facade.trade.counterparam.agreement.SignSupplementalAgreementFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 补充协议外部服务
 * @date 2025/3/6 18:22
 * @since JDK 1.8
 */
@Component
@Slf4j
public class SupplementalAgreementOuterService {

    @DubboReference(registry = "dtms-order-remote", check = false)
    private QuerySupplementalAgreementFacade querySupplementalAgreementFacade;

    @DubboReference(registry = "dtms-order-remote", check = false)
    private SignSupplementalAgreementFacade signSupplementalAgreementFacade;

    /**
     * @description:(请在此添加描述)
     * @param hkCustNo	
     * @param signStatus	
     * @param fundCodeList
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement.SupplementalAgreementDTO>
     * @author: jinqing.rao
     * @date: 2025/7/7 18:04
     * @since JDK 1.8
     */
    public List<SupplementalAgreementDTO> querySupplementalAgreementListByFundCodeList(String hkCustNo, String signStatus,List<String> fundCodeList) {
        QuerySupplementalAgreementRequest request = new QuerySupplementalAgreementRequest();
        request.setHkCustNo(hkCustNo);
        request.setSignStatus(signStatus);
        request.setFundCodeList(fundCodeList);
        Response<QuerySupplementalAgreementResponse> response = querySupplementalAgreementFacade.execute(request);
        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "查询补充协议列表接口异常");
        }
        if (!response.isSuccess() || null == response.getData()) {
            log.error("SupplementalAgreementOuterService>>>queryUnSignSupplementalAgreementList 查询补充协议列表异常,请求参数:{},返回结果:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "查询补充协议列表结果错误");
        }

        List<SupplementalAgreementDTO> dtoList = new ArrayList<>();
        QuerySupplementalAgreementResponse responseData = response.getData();
        if (CollectionUtils.isNotEmpty(responseData.getAgreementList())) {
            for (QuerySupplementalAgreementResponse.AgreementInfo info : responseData.getAgreementList()) {
                SupplementalAgreementDTO dto = new SupplementalAgreementDTO();
                dto.setAgreementId(info.getAgreementId());
                dto.setAgreementName(info.getAgreementName());
                dto.setAgreementUrl(info.getAgreementUrl());
                dto.setAgreementSignEndDt(info.getAgreementSignEndDt());
                dto.setAgreementDescription(info.getAgreementDescription());
                dto.setFundCode(info.getFundCode());
                dto.setFundName(info.getFundAbbr());
                dto.setSignStatus(info.getSignStatus());
                dto.setSignDate(info.getSignDate());
                dtoList.add(dto);
            }
        }
        return dtoList;
    }

    /**
     * @param hkCustNo   香港客户号
     * @param signStatus 基金代码
     * @return List<SupplementalAgreementDTO>
     * @description: 查询未签署的补充协议列表
     */
    public List<SupplementalAgreementDTO> querySupplementalAgreementList(String hkCustNo, String signStatus,String fundCode) {
        QuerySupplementalAgreementRequest request = new QuerySupplementalAgreementRequest();
        request.setHkCustNo(hkCustNo);
        request.setSignStatus(signStatus);
        request.setFundCode(fundCode);
        Response<QuerySupplementalAgreementResponse> response = querySupplementalAgreementFacade.execute(request);
        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "查询补充协议列表接口异常");
        }
        if (!response.isSuccess() || null == response.getData()) {
            log.error("SupplementalAgreementOuterService>>>queryUnSignSupplementalAgreementList 查询补充协议列表异常,请求参数:{},返回结果:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "查询补充协议列表结果错误");
        }

        List<SupplementalAgreementDTO> dtoList = new ArrayList<>();
        QuerySupplementalAgreementResponse responseData = response.getData();
        if (CollectionUtils.isNotEmpty(responseData.getAgreementList())) {
            for (QuerySupplementalAgreementResponse.AgreementInfo info : responseData.getAgreementList()) {
                SupplementalAgreementDTO dto = new SupplementalAgreementDTO();
                dto.setAgreementId(info.getAgreementId());
                dto.setAgreementName(info.getAgreementName());
                dto.setAgreementUrl(info.getAgreementUrl());
                dto.setAgreementSignEndDt(info.getAgreementSignEndDt());
                dto.setAgreementDescription(info.getAgreementDescription());
                dto.setFundCode(info.getFundCode());
                dto.setFundName(info.getFundAbbr());
                dto.setSignStatus(info.getSignStatus());
                dto.setSignDate(info.getSignDate());
                dtoList.add(dto);
            }
        }
        return dtoList;
    }

    /**
     * @param hkCustNo 香港客户号
     * @param fundCode 基金代码
     * @return SupplementalAgreementDTO
     * @description: 查询补充协议详情
     */
    public List<SupplementalAgreementDTO> querySupplementalAgreementDetail(String hkCustNo, String fundCode, String signStatus) {
        QuerySupplementalAgreementRequest request = new QuerySupplementalAgreementRequest();
        request.setHkCustNo(hkCustNo);
        request.setFundCode(fundCode);
        request.setSignStatus(signStatus);

        Response<QuerySupplementalAgreementResponse> response = querySupplementalAgreementFacade.execute(request);
        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "查询补充协议详情接口异常");
        }
        if (!response.isSuccess() || null == response.getData()) {
            log.error("SupplementalAgreementOuterService>>>querySupplementalAgreementDetail 查询补充协议详情异常,请求参数:{},返回结果:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "查询补充协议详情结果错误");
        }
        List<SupplementalAgreementDTO> supplementalAgreementDTOS = new ArrayList<>();

        QuerySupplementalAgreementResponse responseData = response.getData();
        if (CollectionUtils.isEmpty(responseData.getAgreementList())) {
            return supplementalAgreementDTOS;
        }
        // 获取第一条记录
        supplementalAgreementDTOS = responseData.getAgreementList().stream().map(agreementInfo -> {
            SupplementalAgreementDTO dto = new SupplementalAgreementDTO();
            dto.setAgreementId(agreementInfo.getAgreementId());
            dto.setAgreementName(agreementInfo.getAgreementName());
            dto.setAgreementUrl(agreementInfo.getAgreementUrl());
            dto.setAgreementSignEndDt(agreementInfo.getAgreementSignEndDt());
            dto.setAgreementDescription(agreementInfo.getAgreementDescription());
            dto.setFundCode(agreementInfo.getFundCode());
            dto.setFundName(agreementInfo.getFundAbbr());
            return dto;
        }).collect(Collectors.toList());

        return supplementalAgreementDTOS;
    }

    /**
     * @description: 补充协议签署
     * @param hkCustNo	
     * @param fundCode	
     * @param agreementIdList
     * @return void
     * @author: jinqing.rao
     * @date: 2025/6/11 14:47
     * @since JDK 1.8
     */
    public void submitSupplementalAgreement(String hkCustNo, String fundCode, List<String> agreementIdList) {
        SignSupplementalAgreementRequest request = new SignSupplementalAgreementRequest();
        request.setHkCustNo(hkCustNo);
        request.setFundCode(fundCode);
        request.setAgreementIdList(agreementIdList);
        Response<SignSupplementalAgreementResponse> response = signSupplementalAgreementFacade.execute(request);
        if (null == response) {
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "补充协议签署接口异常");
        }
        if (!response.isSuccess() || null == response.getData()) {
            log.error("SupplementalAgreementOuterService>>>submitSupplementalAgreement 补充协议签署异常,请求参数:{},返回结果:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), response.getDescription());
        }
    }
}
