/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.response.fund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/15 20:04
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryCustFundBalResponseDTO implements Serializable {

    private static final long serialVersionUID = 9186751286921847532L;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 主基金代码
     */
    private String mainFundCode;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 管理人代码
     */
    private String fundManCode;

    /**
     * 总余额
     */
    private BigDecimal balanceVol;

    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;

    /**
     * 净值日期（最新）
     */
    private String navDt;

    /**
     * 份额更新日期
     */
    private String volUpdateDt;
}
