/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.report;

import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.facade.report.CheckProductReportPermissionFacade;
import com.howbuy.crm.portrait.client.domain.response.report.CheckProductReportPermissionResponse;
import com.howbuy.crm.portrait.client.domain.request.report.CheckProductReportPermissionRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 校验产品报告发送权限外部服务
 * <AUTHOR>
 * @date 2025-03-13 13:18:57
 */
@Slf4j
@Service
public class CheckProductReportPermissionOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private CheckProductReportPermissionFacade checkProductReportPermissionFacade;

    /**
     * 校验产品报告发送权限
     * @description 调用远程服务校验产品报告发送权限
     * @param conscode 投顾编号
     * @param fundCode 产品代码
     * @return CheckProductReportPermissionResponse 产品报告发送权限响应对象
     * <AUTHOR>
     * @date 2025-03-13 13:18:57
     */
    public CheckProductReportPermissionResponse checkPermission(String conscode, String fundCode) {
        log.info("校验产品报告发送权限-开始, conscode={}, fundCode={}", conscode, fundCode);
        
        CheckProductReportPermissionRequest request = new CheckProductReportPermissionRequest();
        request.setConscode(conscode);
        request.setFundCode(fundCode);
        
        Response<CheckProductReportPermissionResponse> response = checkProductReportPermissionFacade.execute(request);

        log.info("校验产品报告发送权限-响应结果：{}", response);

        if (response == null || !response.isSuccess()) {
            log.error("校验产品报告发送权限-失败，响应结果：{}", response);
            return null;
        }

        return response.getData();
    }
} 