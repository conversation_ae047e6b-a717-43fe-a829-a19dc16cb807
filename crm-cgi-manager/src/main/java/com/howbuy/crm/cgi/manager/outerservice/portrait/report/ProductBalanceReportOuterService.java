/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.report;

import com.howbuy.crm.cgi.manager.convert.portrait.report.ProductBalanceReportConvert;
import com.howbuy.crm.cgi.manager.domain.portrait.report.ProductBalanceReportDTO;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.facade.report.ProductBalanceReportQueryFacade;
import com.howbuy.crm.portrait.client.domain.request.report.ProductBalanceReportQueryRequest;
import com.howbuy.crm.portrait.client.domain.response.report.ProductBalanceReportQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 产品持仓投后报告查询外部服务
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Slf4j
@Service
public class ProductBalanceReportOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private ProductBalanceReportQueryFacade productBalanceReportQueryFacade;

    /**
     * 查询产品持仓投后报告
     * @param conscode 投顾编号
     * @param hboneNo 一账通号
     * @param fundCode 基金代码
     * @param year 年份
     * @param page 页码
     * @param size 每页数量
     * @return 产品持仓投后报告DTO
     */
    public ProductBalanceReportDTO queryProductBalanceReport(String conscode, String hboneNo,
            String fundCode, String year, Integer page, Integer size) {
        log.info("查询产品持仓投后报告-请求参数：conscode={}, hboneNo={}, fundCode={}, year={}, page={}, size={}", 
                conscode, hboneNo, fundCode, year, page, size);
        
        ProductBalanceReportQueryRequest request = new ProductBalanceReportQueryRequest();
        request.setConscode(conscode);
        request.setHboneNo(hboneNo);
        request.setFundCode(fundCode);
        request.setYear(year);
        request.setPage(page);
        request.setRows(size);

        Response<ProductBalanceReportQueryResponse> response = productBalanceReportQueryFacade.execute(request);
        log.info("查询产品持仓投后报告-响应结果：{}", response);

        if (response == null || !response.isSuccess()) {
            log.error("查询产品持仓投后报告-失败：{}", response);
            return null;
        }

        return ProductBalanceReportConvert.getInstance().toProductBalanceReportDTO(response.getData());
    }
} 