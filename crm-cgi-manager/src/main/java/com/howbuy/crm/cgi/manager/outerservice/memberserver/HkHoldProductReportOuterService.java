/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.memberserver;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.hkproductreport.ReportAuthDTO;
import com.howbuy.member.dto.hk.report.ReportAuthRequestDTO;
import com.howbuy.member.dto.hk.report.ReportAuthResponseDTO;
import com.howbuy.member.service.hk.report.HkHoldProductReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description: 香港持仓产品报告外部服务
 * @author: 陈杰文
 * @date: 2025-06-19 15:07:57
 * @since JDK 1.8
 */
@Slf4j
@Service
public class HkHoldProductReportOuterService {

    @DubboReference(registry = "howbuy-member-remote", check = false)
    private HkHoldProductReportService hkHoldProductReportService;

    /**
     * @description: 产品报告鉴权
     * @param hkCustNo 香港客户号
     * @param reportId 产品报告id
     * @return ReportAuthDTO
     * @author: 陈杰文
     * @date: 2025-06-19 15:07:57
     */
    public ReportAuthDTO reportAuth(String hkCustNo, String reportId) {
        log.info("调用dubbo接口进行产品报告鉴权，香港客户号：{}，报告ID：{}", hkCustNo, reportId);
        
        // 需要根据实际的dubbo接口参数和返回类型进行调整
        ReportAuthRequestDTO request = new ReportAuthRequestDTO();
        request.setHkCustNo(hkCustNo);
        request.setReportId(reportId);
        ReportAuthResponseDTO responseDTO = hkHoldProductReportService.reportAuth(request);
        if (responseDTO == null) {
            log.error("产品报告鉴权接口返回结果为空，香港客户号：{}，报告ID：{}", hkCustNo, reportId);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "产品报告鉴权结果为空！");
        }

        ReportAuthDTO result = new ReportAuthDTO();
        result.setIsViewed(responseDTO.getIsViewed());
        result.setRedirectUrl(responseDTO.getRedirectUrl());
        result.setReportType(responseDTO.getReportType());

        log.info("产品报告鉴权处理完成，香港客户号：{}，报告ID：{}，结果：{}", hkCustNo, reportId, JSON.toJSONString(result));

        return result;
    }
} 