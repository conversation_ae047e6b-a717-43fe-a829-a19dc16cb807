/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsorder;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.hkfund.BuyCalendarDTO;
import com.howbuy.crm.cgi.manager.domain.hkfund.RedemptionCalendarDTO;
import com.howbuy.crm.cgi.manager.domain.hkfund.TradeCalendarDTO;
import com.howbuy.dtms.order.client.domain.request.fund.QueryFundNearestTradeCalenderRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.fund.QueryFundNearestTradeCalenderResponse;
import com.howbuy.dtms.order.client.facade.query.fund.QueryFundNearestTradeCalenderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description: 香港基金交易日历外部服务
 * @author: 陈杰文
 * @date: 2025-06-26 15:57:03
 * @since JDK 1.8
 */
@Service
@Slf4j
public class QueryNearestOpenDtOuterService {

    @DubboReference(registry = "dtms-order-remote", check = false)
    private QueryFundNearestTradeCalenderFacade queryFundNearestTradeCalenderFacade;

    /**
     * @description: 查询基金购买赎回交易日历
     * @param hkCustNo 香港客户号
     * @param fundCode 基金代码
     * @param appDt 申请日期 yyyyMMdd
     * @param appTm 申请时间 HHmmss
     * @return TradeCalendarDTO
     * @author: 陈杰文
     * @date: 2025-06-26 15:57:03
     */
    public TradeCalendarDTO queryNearestOpenDt(String hkCustNo, String fundCode, String appDt, String appTm) {
        log.info("查询基金交易日历开始，香港客户号：{}，基金代码：{}，申请日期：{}，申请时间：{}", hkCustNo, fundCode, appDt, appTm);

        QueryFundNearestTradeCalenderRequest request = new QueryFundNearestTradeCalenderRequest();
        request.setHkCustNo(hkCustNo);
        request.setFundCode(fundCode);
        request.setAppDt(appDt);
        request.setAppTm(appTm);

        Response<QueryFundNearestTradeCalenderResponse> response = queryFundNearestTradeCalenderFacade.execute(request);
        if (null == response || !response.isSuccess()) {
            log.error("获取基金最近开放日数据失败，香港客户号：{}，基金代码：{}，错误信息：{}", hkCustNo, fundCode,
                response != null ? response.getDescription() : "响应为空");
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "获取基金最近开放日数据失败");
        }
        if (null == response.getData()) {
            log.warn("获取基金最近开放日数据为空，香港客户号：{}，基金代码：{}", hkCustNo, fundCode);
            // 返回空的交易日历对象而不是抛异常
            return new TradeCalendarDTO();
        }

        QueryFundNearestTradeCalenderResponse responseData = response.getData();
        TradeCalendarDTO result = buildTradeCalendarDTO(responseData);

        log.info("基金交易日历查询处理完成，香港客户号：{}，基金代码：{}", hkCustNo, fundCode);

        return result;
    }

    /**
     * @description: 构建交易日历DTO
     * @param responseData 响应数据
     * @return TradeCalendarDTO
     * @author: 陈杰文
     * @date: 2025-06-26 15:57:03
     */
    private TradeCalendarDTO buildTradeCalendarDTO(QueryFundNearestTradeCalenderResponse responseData) {
        TradeCalendarDTO result = new TradeCalendarDTO();

        // 构建购买日历
        BuyCalendarDTO buyCalendar = new BuyCalendarDTO();
        if (responseData.getBuyCalendar() != null) {
            buyCalendar.setSupportPrebook(responseData.getBuyCalendar().getSupportPrebook() != null ? 
                responseData.getBuyCalendar().getSupportPrebook() : null);
            buyCalendar.setAdvanceEndDt(responseData.getBuyCalendar().getAdvanceEndDt());
            buyCalendar.setAdvanceEndTm(responseData.getBuyCalendar().getAdvanceEndTm());
            buyCalendar.setOpenStartDt(responseData.getBuyCalendar().getOpenStartDt());
            buyCalendar.setOpenEndDt(responseData.getBuyCalendar().getOpenEndDt());
            buyCalendar.setOpenDt(responseData.getBuyCalendar().getOpenDt());
            buyCalendar.setOrderEndTm(responseData.getBuyCalendar().getOrderEndTm());
            buyCalendar.setPaymentDeadlineDt(responseData.getBuyCalendar().getPaymentDeadlineDt());
            buyCalendar.setPaymentDeadlineTime(responseData.getBuyCalendar().getPaymentDeadlineTime());
            buyCalendar.setPreSubmitTaDt(responseData.getBuyCalendar().getPreSubmitTaDt());
            buyCalendar.setPreSubmitTaTm(responseData.getBuyCalendar().getPreSubmitTaTm());
        }
        result.setBuyCalendar(buyCalendar);

        // 构建赎回日历
        RedemptionCalendarDTO redemptionCalendar = new RedemptionCalendarDTO();
        if (responseData.getRedemptionCalendar() != null) {
            redemptionCalendar.setSupportPrebook(responseData.getRedemptionCalendar().getSupportPrebook() != null ? 
                responseData.getRedemptionCalendar().getSupportPrebook() : null);
            redemptionCalendar.setAdvanceEndDt(responseData.getRedemptionCalendar().getAdvanceEndDt());
            redemptionCalendar.setAdvanceEndTm(responseData.getRedemptionCalendar().getAdvanceEndTm());
            redemptionCalendar.setAvailableVol(responseData.getRedemptionCalendar().getAvailableVol());
            redemptionCalendar.setOpenStartDt(responseData.getRedemptionCalendar().getOpenStartDt());
            redemptionCalendar.setOpenEndDt(responseData.getRedemptionCalendar().getOpenEndDt());
            redemptionCalendar.setOpenDt(responseData.getRedemptionCalendar().getOpenDt());
            redemptionCalendar.setOrderEndTm(responseData.getRedemptionCalendar().getOrderEndTm());
            redemptionCalendar.setPreSubmitTaDt(responseData.getRedemptionCalendar().getPreSubmitTaDt());
            redemptionCalendar.setPreSubmitTaTm(responseData.getRedemptionCalendar().getPreSubmitTaTm());
        }
        result.setRedemptionCalendar(redemptionCalendar);

        return result;
    }
} 