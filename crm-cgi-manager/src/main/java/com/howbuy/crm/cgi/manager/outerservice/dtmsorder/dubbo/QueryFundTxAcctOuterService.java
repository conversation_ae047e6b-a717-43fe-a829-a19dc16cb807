/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo;

import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.crm.cgi.manager.utils.OutServiceResultUtils;
import com.howbuy.dtms.order.client.domain.request.fundtxacct.QueryFundTxAcctListRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.fundtxacct.FundTxAcctVO;
import com.howbuy.dtms.order.client.domain.response.fundtxacct.QueryFundTxAcctListResponse;
import com.howbuy.dtms.order.client.facade.query.fundtxacct.QueryFundTxAcctFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/4/2 15:21
 * @since JDK 1.8
 */
@Component
public class QueryFundTxAcctOuterService {

    private static final String FUND_TX_ACCT_STAT_NORMAL = "1";

    @DubboReference(registry = "dtms-order-remote", check = false)
    private QueryFundTxAcctFacade queryFundTxAcctFacade;


    /**
     * @param hkCustNo
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO>
     * @description: 查询基金交易账号
     * @author: jinqing.rao
     * @date: 2025/4/2 15:33
     * @since JDK 1.8
     */
    public List<HkFundTxAcctDTO> queryFundTxCodeList(String hkCustNo) {
        QueryFundTxAcctListRequest request = new QueryFundTxAcctListRequest();
        request.setHkCustNo(hkCustNo);
        Response<QueryFundTxAcctListResponse> responseResponse = queryFundTxAcctFacade.queryFundTxAcctList(request);
        OutServiceResultUtils.checkResultSuccessForDtmsOrder(responseResponse);
        QueryFundTxAcctListResponse responseData = responseResponse.getData();
        if (null == responseData) {
            return new ArrayList<>();
        }
        List<FundTxAcctVO> fundTxAcctVOList = responseData.getFundTxAcctVOList();
        if (CollectionUtils.isEmpty(fundTxAcctVOList)) {
            return new ArrayList<>();
        }
        return fundTxAcctVOList.stream()
                .filter(it -> FUND_TX_ACCT_STAT_NORMAL.equals(it.getFundTxAcctStat()))
                .map(it -> {
                    HkFundTxAcctDTO hkFundTxAcctDTO = new HkFundTxAcctDTO();
                    hkFundTxAcctDTO.setHkCustNo(it.getHkCustNo());
                    hkFundTxAcctDTO.setFundTxAccType(it.getFundTxAccType());
                    hkFundTxAcctDTO.setFundCode(it.getFundCode());
                    hkFundTxAcctDTO.setFundTxAcctNo(it.getFundTxAcctNo());
                    hkFundTxAcctDTO.setFundTxAcctStat(it.getFundTxAcctStat());
                    return hkFundTxAcctDTO;
                }).collect(Collectors.toList());
    }
}
