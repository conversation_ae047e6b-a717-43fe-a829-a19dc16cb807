/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.convert.portrait.qa;

import com.howbuy.crm.cgi.manager.domain.portrait.qa.CmPortraitQaReplyAnswerDTO;
import com.howbuy.crm.cgi.manager.domain.portrait.qa.CmPortraitQaReplyDTO;
import com.howbuy.crm.portrait.client.domain.dto.qa.QaReplyDTO;
import com.howbuy.crm.portrait.client.domain.request.qa.CmPortraitQaQueryRequest;
import com.howbuy.crm.portrait.client.domain.response.qa.CmPortraitQaQueryResponse;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户画像问答外部服务转换类
 *
 * <AUTHOR>
 * @date 2025-03-21 10:56:33
 */
public class PortraitQaOuterConvert {

    /**
     * 构建查询DTO
     *
     * @param source          来源类型 0-1V1入口 1-工作台
     * @param searchPool      检索池类型 0-全池 1-推荐池
     * @param searchCondition 检索条件
     * @param hboneNo         一账通号
     * @param conscode        投顾编号
     * @param pageNo          页码
     * @param pageSize        每页记录数
     * @param firstReply      一级回复
     * @param secondReply     二级回复
     * @return 查询DTO
     */
    public static CmPortraitQaQueryRequest buildQueryDTO(
            String source,
            String searchPool,
            String searchCondition,
            String hboneNo,
            String conscode,
            int pageNo,
            int pageSize,
            String firstReply,
            String secondReply
    ) {
        CmPortraitQaQueryRequest queryDTO = new CmPortraitQaQueryRequest();
        queryDTO.setSource(source);
        queryDTO.setSearchPool(searchPool);
        queryDTO.setSearchCondition(searchCondition);
        queryDTO.setHboneNo(hboneNo);
        queryDTO.setConscode(conscode);
        queryDTO.setPage(pageNo);
        queryDTO.setRows(pageSize);
        queryDTO.setFirstReply(firstReply);
        queryDTO.setSecondReply(secondReply);
        return queryDTO;
    }

    /**
     * 外部接口结果转换为内部DTO
     *
     * @param resultDTO 外部接口结果
     * @return 内部DTO
     */
    public static CmPortraitQaReplyDTO convertToDTO(CmPortraitQaQueryResponse resultDTO) {
        if (resultDTO == null) {
            return null;
        }
        CmPortraitQaReplyDTO dto = new CmPortraitQaReplyDTO();
        dto.setIsHkCons(resultDTO.getIsHkCons());
        dto.setReplyLevel(resultDTO.getReplyLevel());
        dto.setReplyType(resultDTO.getReplyType());
        dto.setReplyTitle(resultDTO.getReplyTitle());
        dto.setSearchCondition(resultDTO.getSearchCondition());
        dto.setFirstReply(resultDTO.getFirstReply());
        dto.setSecondReply(resultDTO.getSecondReply());
        dto.setTotal(String.valueOf(resultDTO.getTotal()));
        dto.setAnswerList(convertAnswerList(resultDTO.getAnswerList()));
        return dto;
    }

    /**
     * 转换回复列表
     *
     * @param resultList 外部接口结果列表
     * @return 内部DTO列表
     */
    private static List<CmPortraitQaReplyAnswerDTO> convertAnswerList(List<QaReplyDTO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return resultList.stream()
                .map(PortraitQaOuterConvert::convertAnswerDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个回复项
     *
     * @param resultDTO 外部接口结果
     * @return 内部DTO
     */
    private static CmPortraitQaReplyAnswerDTO convertAnswerDTO(QaReplyDTO resultDTO) {
        CmPortraitQaReplyAnswerDTO dto = new CmPortraitQaReplyAnswerDTO();
        dto.setTagName(resultDTO.getTagName());
        dto.setTagCode(resultDTO.getTagCode());
        dto.setMaterialId(resultDTO.getMaterialId());
        dto.setMaterialTitle(resultDTO.getMaterialTitle());
        dto.setMaterialUrl(resultDTO.getMaterialUrl());
        dto.setSendCount(String.valueOf(resultDTO.getSendCount()));
        dto.setLearningProgress(resultDTO.getLearningProgress());
        dto.setMaterialContentType(resultDTO.getMaterialContentType());
        dto.setReportSource(resultDTO.getReportSource());
        dto.setFundCode(resultDTO.getFundCode());
        dto.setFundName(resultDTO.getFundName());
        return dto;
    }
} 