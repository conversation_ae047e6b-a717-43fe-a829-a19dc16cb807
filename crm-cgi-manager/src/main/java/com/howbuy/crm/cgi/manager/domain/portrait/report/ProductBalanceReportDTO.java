/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.portrait.report;

import java.util.List;

/**
 * 产品持仓投后报告DTO
 * <AUTHOR>
 * @date 2025-03-13 13:18:57
 */
public class ProductBalanceReportDTO {

    /**
     * 数据总量
     */
    private String total;

    /**
     * 最小报告年份yyyy
     */
    private String minReportYear;

    /**
     * 最大报告年份yyyy
     */
    private String maxReportYear;

    /**
     * 数据列表（按年份分组）
     */
    private List<YearReportDTO> dataList;

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getMinReportYear() {
        return minReportYear;
    }

    public void setMinReportYear(String minReportYear) {
        this.minReportYear = minReportYear;
    }

    public String getMaxReportYear() {
        return maxReportYear;
    }

    public void setMaxReportYear(String maxReportYear) {
        this.maxReportYear = maxReportYear;
    }

    public List<YearReportDTO> getDataList() {
        return dataList;
    }

    public void setDataList(List<YearReportDTO> dataList) {
        this.dataList = dataList;
    }

    /**
     * 年份分组
     */
    public static class YearReportDTO {
        /**
         * 报告年份（格式：yyyy）
         */
        private String year;

        /**
         * 报告列表
         */
        private List<ReportDTO> reportList;

        public String getYear() {
            return year;
        }

        public void setYear(String year) {
            this.year = year;
        }

        public List<ReportDTO> getReportList() {
            return reportList;
        }

        public void setReportList(List<ReportDTO> reportList) {
            this.reportList = reportList;
        }
    }

    /**
     * 报告项
     */
    public static class ReportDTO {

        /**
         * 报告ID
         */
        private String reportId;

        /**
         * 报告日期 (MM月dd日)
         */
        private String date;

        /**
         * 报告标题
         */
        private String title;

        /**
         * 是否new标签
         * 0：否，1：是
         */
        private String isNew;

        /**
         * 发送次数
         */
        private String sendNum;

        /**
         * 报告URL
         */
        private String reportUrl;

        /**
         * 素材发送ID
         */
        private String materialId;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;

        public String getReportId() {
            return reportId;
        }

        public void setReportId(String reportId) {
            this.reportId = reportId;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getIsNew() {
            return isNew;
        }

        public void setIsNew(String isNew) {
            this.isNew = isNew;
        }

        public String getSendNum() {
            return sendNum;
        }

        public void setSendNum(String sendNum) {
            this.sendNum = sendNum;
        }

        public String getReportUrl() {
            return reportUrl;
        }

        public void setReportUrl(String reportUrl) {
            this.reportUrl = reportUrl;
        }

        public String getMaterialId() {
            return materialId;
        }

        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }

        public String getMaterialSendType() {
            return materialSendType;
        }

        public void setMaterialSendType(String materialSendType) {
            this.materialSendType = materialSendType;
        }
    }
} 