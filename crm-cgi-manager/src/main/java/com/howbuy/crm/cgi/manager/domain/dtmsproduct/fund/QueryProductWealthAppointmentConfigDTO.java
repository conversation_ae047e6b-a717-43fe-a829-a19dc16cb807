/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/25 10:08
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryProductWealthAppointmentConfigDTO implements Serializable {

    private static final long serialVersionUID = -3993586523205587396L;

    /**
     * 投资者类型
     */
    private String investType;

    /**
     * 具体年月份
     */
    private String yearMonth;

    /**
     * 财富配置报告文件名称
     */
    private String wealthAllocationReport;

    /**
     * 预约日历汇总文件http地址
     */
    private String wealthAllocationReportUrl;

    /**
     * 预约日历汇总名称
     */
    private String appointmentCalendarSummary;


    /**
     * 预约日历汇总文件http地址
     */
    private String appointmentCalendarSummaryUrl;

    /**
     * 创建日期时间
     */
    private LocalDateTime createTime;

    /**
     * 更新日期时间
     */
    private LocalDateTime updateTime;
}
