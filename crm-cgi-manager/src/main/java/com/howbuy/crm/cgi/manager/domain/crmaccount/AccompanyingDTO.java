package com.howbuy.crm.cgi.manager.domain.crmaccount;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/14 18:48
 * @since JDK 1.8
 */
@Getter
@Setter
public class AccompanyingDTO {

    /**
     * 陪访人姓名
     */
    private String userName;

    /**
     * 主管姓名
     */
    private String managerName;

    /**
     * 本次陪访概要经验或教训
     */
    private String summary;

    /**
     * 该客户下阶段工作的建议
     */
    private String suggestion;
}