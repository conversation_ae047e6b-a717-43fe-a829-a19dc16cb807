/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.convert.portrait.report;

import com.howbuy.crm.cgi.manager.domain.portrait.report.BalanceReportDTO;
import com.howbuy.crm.portrait.client.domain.response.report.BalanceReportQueryResponse;
import com.howbuy.crm.portrait.client.enums.MaterialSendTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 持仓投后报告转换类
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
public class BalanceReportConvert {
    
    private static final BalanceReportConvert INSTANCE = new BalanceReportConvert();

    private BalanceReportConvert() {}

    public static BalanceReportConvert getInstance() {
        return INSTANCE;
    }

    /**
     * 转换为持仓投后报告DTO
     * @param response 响应对象
     * @return 持仓投后报告DTO
     */
    public BalanceReportDTO toBalanceReportDTO(BalanceReportQueryResponse response) {
        if (response == null) {
            return null;
        }
        
        BalanceReportDTO dto = new BalanceReportDTO();
        
        // 转换好买基金模块list
        dto.setHmjjList(convertFundReportList(response.getHmjjList()));
        // 转换好臻投资模块list
        dto.setHztzList(convertFundReportList(response.getHztzList()));
        // 转换好买香港模块list
        dto.setHmxgList(convertFundReportList(response.getHmxgList()));
        // 转换用户授权状态
        dto.setIsAuthorization(response.getIsAuthorization());
        // 转换投顾所属组织架构是否香港
        dto.setTgIsXg(response.getTgIsXg());
        
        return dto;
    }

    /**
     * 转换基金报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<BalanceReportDTO.FundReportDTO> convertFundReportList(List<BalanceReportQueryResponse.FundReportVO> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<BalanceReportDTO.FundReportDTO> targetList = new ArrayList<>();
        for (BalanceReportQueryResponse.FundReportVO source : sourceList) {
            targetList.add(convertFundReport(source));
        }
        return targetList;
    }

    /**
     * 转换基金报告
     * @param source 源对象
     * @return 目标对象
     */
    private BalanceReportDTO.FundReportDTO convertFundReport(BalanceReportQueryResponse.FundReportVO source) {
        if (source == null) {
            return null;
        }

        BalanceReportDTO.FundReportDTO target = new BalanceReportDTO.FundReportDTO();
        target.setJjdm(source.getJjdm());
        target.setJjjc(source.getJjjc());
        target.setProductSubTypeName(source.getProductSubTypeName());
        target.setIsMoreReport(source.getIsMoreReport());
        target.setReportList(convertReportList(source.getReportList()));
        return target;
    }

    /**
     * 转换报告列表
     * @param sourceList 源列表
     * @return 目标列表
     */
    private List<BalanceReportDTO.ReportDTO> convertReportList(List<BalanceReportQueryResponse.ReportVO> sourceList) {
        if (sourceList == null) {
            return null;
        }

        List<BalanceReportDTO.ReportDTO> targetList = new ArrayList<>();
        for (BalanceReportQueryResponse.ReportVO source : sourceList) {
            targetList.add(convertReport(source));
        }
        return targetList;
    }

    /**
     * 转换报告
     * @param source 源对象
     * @return 目标对象
     */
    private BalanceReportDTO.ReportDTO convertReport(BalanceReportQueryResponse.ReportVO source) {
        if (source == null) {
            return null;
        }

        BalanceReportDTO.ReportDTO target = new BalanceReportDTO.ReportDTO();
        target.setDate(source.getDate());
        target.setTitle(source.getTitle());
        target.setIsNew(source.getIsNew());
        target.setSendNum(source.getSendNum());
        target.setReportId(source.getReportId());
        target.setReportUrl(source.getReportUrl());
        target.setMaterialId(source.getReportId());
        target.setMaterialSendType(MaterialSendTypeEnum.POSITION_REPORT.getCode());
        return target;
    }
} 