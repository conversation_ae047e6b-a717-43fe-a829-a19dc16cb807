/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.response;

import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.piggy.PiggyPayVoucherRecordDTO;
import lombok.Getter;
import lombok.Setter;
import org.w3c.dom.stylesheets.LinkStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/10 19:31
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositVoucherDuplicateCheckResponseDTO {

    /**
     *  打款凭证列表
     */
    private List<DuplicateCheckDetailDTO> piggyPayVoucherRecordDTO;

    @Setter
    @Getter
    public static class DuplicateCheckDetailDTO {
        /**
         * 打款凭证订单号
         */
        private String voucherNo;

        /**
         * 申请日期
         */
        private String appDate;

        /**
         * 申请时间
         */
        private String appTime;

        /**
         * 汇款金额
         */
        private BigDecimal remitAmt;

        /**
         * 打款凭证类型: 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
         */
        private String voucherType;

        /**
         * 汇款币种
         */
        private String remitCurrency;

        /**
         * 实际到账金额
         */
        private BigDecimal actualPayAmt;

        /**
         * 实际到账币种
         */
        private String actualPayCurrency;

        /**
         * 审核状态
         */
        private String auditStatus;


        /**
         * 审核原因
         */
        private String auditReason;
    }
}
