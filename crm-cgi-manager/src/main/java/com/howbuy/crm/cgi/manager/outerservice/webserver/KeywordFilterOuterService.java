/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.webserver;


import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.persistence.web.KeywordFilter;
import com.howbuy.web.service.business.KeywordFilterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @description: 敏感词校验外部服务
 * @author: 陈杰文
 * @date: 2025-07-15 10:59:35
 * @since JDK 1.8
 */
@Slf4j
@Service
public class KeywordFilterOuterService {

    @DubboReference(registry = "howbuy-web-server", check = false)
    private KeywordFilterService keywordFilterService;

    /**
     * @description: 敏感词校验
     * @param keyword 待校验的关键字
     * @return Boolean 校验结果，true-校验通过，false-校验不通过
     * @author: 陈杰文
     * @date: 2025-07-15 10:59:35
     */
    public boolean validateKeyword(String keyword) {
        log.info("调用dubbo接口进行敏感词校验，关键字：{}", keyword);

        // 参数校验
        if (!StringUtils.hasText(keyword)) {
            log.warn("敏感词校验参数为空");
            throw new BusinessException(ExceptionCodeEnum.PARAM_ERROR.getCode(), "待校验关键字不能为空");
        }

        try {
            // 调用敏感词接口匹配待校验文本中是否包含敏感词
            List<String> sensitiveWordList = keywordFilterService.matchKeyWord(keyword);
            
            if (CollectionUtils.isEmpty(sensitiveWordList)) {
                // 如果敏感词列表为空，则认为校验通过
                log.info("敏感词校验处理完成，关键字：【{}】，校验通过！", keyword);
                return true;
            }

            log.info("敏感词校验处理完成，关键字：【{}】，校验不通过！", keyword);
        } catch (Exception e) {
            log.error("敏感词校验调用异常，关键字：{}", keyword, e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), "敏感词校验服务异常");
        }
        return false;
    }
} 