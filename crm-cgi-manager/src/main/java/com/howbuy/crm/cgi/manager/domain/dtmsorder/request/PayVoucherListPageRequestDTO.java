/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.request;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/4/10 10:10
 * @since JDK 1.8
 */
@Setter
@Getter
public class PayVoucherListPageRequestDTO implements Serializable {

    private static final long serialVersionUID = -8331146840238636576L;

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 打款凭证类型
     */
    private List<String> voucherTypeList;

    /**
     * 审核状态
     */
    private List<String> auditStatusList;
    /**
     * 渠道集合
     */
    private List<String> tradeChannelList;

    /**
     * 重复凭证
     */
    private String duplicateVoucher;

    /**
     * 时间格式  YYYYMMdd   开始时间和结束时间都是必填的
     */
    private String voucherStartDt;

    /**
     * 时间格式  YYYYMMdd   开始时间和结束时间都是必填的
     */
    private String voucherEndDt;
}
