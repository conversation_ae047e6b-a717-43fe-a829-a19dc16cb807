/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo.capital;

import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital.QueryInTransitTradeCapitalRequestDTO;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.response.capital.QueryInTransitTradeCapitalResponseDTO;
import com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.TradeRecordInfoDTO;
import com.howbuy.crm.cgi.manager.utils.OutServiceResultUtils;
import com.howbuy.dtms.order.client.domain.request.order.QueryInTransitTradeCapitalRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.order.DealOrderPageInfoVO;
import com.howbuy.dtms.order.client.domain.response.order.QueryInTransitTradeCapitalResponse;
import com.howbuy.dtms.order.client.facade.query.order.QueryInTransitTradeCapitalFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 在途交易资金服务类
 * <AUTHOR>
 * @date 2025/7/9 9:48
 * @since JDK 1.8
 */
@Component
public class QueryInTransitTradeCapitalOuterService {

    @DubboReference(registry = "dtms-order-remote",check = false)
    private QueryInTransitTradeCapitalFacade queryInTransitTradeCapitalFacade;

    /**
     * @description: 查询在途资金交易
     * @param requestDTO	 * @param requestDTO
     * @return com.howbuy.crm.cgi.manager.domain.dtmsorder.response.capital.QueryInTransitTradeCapitalResponseDTO
     * @author: jinqing.rao
     * @date: 2025/7/9 10:07
     * @since JDK 1.8
     */
    public QueryInTransitTradeCapitalResponseDTO queryInTransitTradeCapital(QueryInTransitTradeCapitalRequestDTO requestDTO) {
        QueryInTransitTradeCapitalRequest request = new QueryInTransitTradeCapitalRequest();
        request.setHkCustNo(requestDTO.getHkCustNo());
        request.setDealNo(requestDTO.getDealNo());
        request.setMidBusinessCodeList(requestDTO.getMidBusinessCodeList());
        Response<QueryInTransitTradeCapitalResponse> response = queryInTransitTradeCapitalFacade.execute(request);
        OutServiceResultUtils.checkResultSuccessForDtmsOrder(response);
        QueryInTransitTradeCapitalResponse responseData = response.getData();
        if (null == responseData) {
            return QueryInTransitTradeCapitalResponseDTO.builder().build();
        }
        List<QueryInTransitTradeCapitalResponse.InTransitTradeCapitalInfo> inTransitTradeCapitalInfoList = responseData.getInTransitTradeCapitalInfoList();
        if(CollectionUtils.isEmpty(inTransitTradeCapitalInfoList)){
            return QueryInTransitTradeCapitalResponseDTO.builder().build();
        }
        List<QueryInTransitTradeCapitalResponseDTO.InTransitTradeCapitalDTO> transitTradeCapitalDTOList = inTransitTradeCapitalInfoList.stream().map(m -> {
            return QueryInTransitTradeCapitalResponseDTO.InTransitTradeCapitalDTO.builder()
                    .dealNo(m.getDealNo())
                    .fundTxAcctNo(m.getFundTxAcctNo())
                    .fundCode(m.getFundCode())
                    .fundName(m.getFundName())
                    .hkCustNo(m.getHkCustNo())
                    .build();

        }).collect(Collectors.toList());
        return QueryInTransitTradeCapitalResponseDTO.builder()
                .inTransitTradeCapitalList(transitTradeCapitalDTOList)
                .build();
    }

    /**
     * @description: 查询待回款订单信息
     * @param requestDTO
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord.TradeRecordInfoDTO>
     * @author: jinqing.rao
     * @date: 2025/7/15 10:22
     * @since JDK 1.8
     */
    public List<TradeRecordInfoDTO> queryInTransitTradeCapitalOrderList(QueryInTransitTradeCapitalRequestDTO requestDTO) {
        QueryInTransitTradeCapitalRequest request = new QueryInTransitTradeCapitalRequest();
        request.setHkCustNo(requestDTO.getHkCustNo());
        request.setDealNo(requestDTO.getDealNo());
        request.setMidBusinessCodeList(requestDTO.getMidBusinessCodeList());
        Response<QueryInTransitTradeCapitalResponse> response = queryInTransitTradeCapitalFacade.execute(request);
        OutServiceResultUtils.checkResultSuccessForDtmsOrder(response);
        QueryInTransitTradeCapitalResponse responseData = response.getData();
        if (null == responseData) {
            return new ArrayList<>();
        }
        List<DealOrderPageInfoVO> dealOrderList = responseData.getDealOrderList();
        if(CollectionUtils.isEmpty(dealOrderList)){
            return new ArrayList<>();
        }
        return dealOrderList.stream().map(m -> {
            TradeRecordInfoDTO tradeRecordInfoDTO = new TradeRecordInfoDTO();
            tradeRecordInfoDTO.setTradeType(m.getBusinessType());
            //设置交易状态
            tradeRecordInfoDTO.setOrderStatus(m.getOrderStatus());
            tradeRecordInfoDTO.setAppDate(m.getAppDt());
            tradeRecordInfoDTO.setProductName(m.getFundShortName());
            tradeRecordInfoDTO.setProductCode(m.getFundCode());
            tradeRecordInfoDTO.setAppTime(m.getAppTm());
            tradeRecordInfoDTO.setAppAmount(m.getAppAmt());
            tradeRecordInfoDTO.setAppVol(m.getAppVol());
            tradeRecordInfoDTO.setAckAmt(m.getAckAmt());
            tradeRecordInfoDTO.setAckVol(m.getAckVol());
            tradeRecordInfoDTO.setCurrency(m.getCurrency());
            tradeRecordInfoDTO.setPayStatus(m.getPayStatus());
            tradeRecordInfoDTO.setFundCode(m.getFundCode());
            tradeRecordInfoDTO.setRedeemType(m.getRedeemType());
            tradeRecordInfoDTO.setTransferProductName(m.getIntoFundName());
            Long dealNo = m.getDealNo();
            tradeRecordInfoDTO.setDealNo(null == dealNo ? null : dealNo.toString());
            tradeRecordInfoDTO.setFundDivMode(m.getFundDivMode());
            tradeRecordInfoDTO.setMiddleBusiCode(m.getMiddleBusiCode());
            tradeRecordInfoDTO.setNetAppAmt(m.getNetAppAmt());

            return tradeRecordInfoDTO;
        }).collect(Collectors.toList());
    }
}
