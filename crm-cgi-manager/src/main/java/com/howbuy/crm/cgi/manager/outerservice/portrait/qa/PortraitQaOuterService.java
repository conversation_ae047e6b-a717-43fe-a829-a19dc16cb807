/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.qa;

import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.convert.portrait.qa.PortraitQaOuterConvert;
import com.howbuy.crm.cgi.manager.domain.portrait.qa.CmPortraitQaReplyDTO;
import com.howbuy.crm.portrait.client.domain.request.qa.CmPortraitQaQueryRequest;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.domain.response.qa.CmPortraitQaQueryResponse;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.portrait.client.facade.qa.CmPortraitQaQueryFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 客户画像问答外部服务
 *
 * <AUTHOR>
 * @date 2025-03-21 10:56:33
 */
@Service
@Slf4j
public class PortraitQaOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private CmPortraitQaQueryFacade cmPortraitQaQueryFacade;

    /**
     * 获取问答回复
     *
     * @param source          来源类型 0-1V1入口 1-工作台
     * @param searchPool      检索池类型 0-全池 1-推荐池
     * @param searchCondition 检索条件
     * @param hboneNo         一账通号
     * @param conscode        投顾编号
     * @param pageNo          页码
     * @param pageSize        每页记录数
     * @param firstReply      一级回复内容
     * @param secondReply     二级回复内容
     * @return 回复结果
     */
    public CmPortraitQaReplyDTO getQaReply(
            String source,
            String searchPool,
            String searchCondition,
            String hboneNo,
            String conscode,
            int pageNo,
            int pageSize,
            String firstReply,
            String secondReply
    ) {
        try {
            CmPortraitQaQueryRequest queryDTO = PortraitQaOuterConvert.buildQueryDTO(
                    source, searchPool, searchCondition, hboneNo,
                    conscode, pageNo, pageSize, firstReply, secondReply
            );
            Response<CmPortraitQaQueryResponse> response = cmPortraitQaQueryFacade.execute(queryDTO);
            if (null == response) {
                log.error("调用问答回复查询接口失败，返回信息：{}", response);
                return null;
            }
            if (!response.isSuccess()) {
                log.error("调用问答回复查询接口失败，返回信息：{}", response);
                if (com.howbuy.crm.portrait.client.enums.ExceptionCodeEnum.QA_SEARCH_CONDITION_INVALID.getCode().equals(response.getCode())) {
                    throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(),
                            ExceptionCodeEnum.QA_SEARCH_CONDITION_INVALID.getDescription());
                }
                return null;
            }

            return PortraitQaOuterConvert.convertToDTO(response.getData());
        } catch (Exception e) {
            log.error("调用问答回复查询接口异常", e);
            throw new BusinessException(ExceptionCodeEnum.SYSTEM_ERROR.getCode(), ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
        }
    }
} 