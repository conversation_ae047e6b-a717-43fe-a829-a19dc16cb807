/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.material;

import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.portrait.material.AddMetarialSearchHisDTO;
import com.howbuy.crm.portrait.client.domain.dto.common.OptResultDTO;
import com.howbuy.crm.portrait.client.domain.request.material.AddMaterialSearchHisRequest;
import com.howbuy.crm.portrait.client.domain.request.material.DeleteMaterialSearchHisRequest;
import com.howbuy.crm.portrait.client.domain.request.material.QueryMaterialSearchHisRequest;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.domain.response.material.AddMaterialSearchHisResponse;
import com.howbuy.crm.portrait.client.domain.response.material.DeleteMaterialSearchHisResponse;
import com.howbuy.crm.portrait.client.domain.response.material.QueryMaterialSearchHisResponse;
import com.howbuy.crm.portrait.client.facade.material.AddMaterialSearchHisFacade;
import com.howbuy.crm.portrait.client.facade.material.DeleteMaterialSearchHisFacade;
import com.howbuy.crm.portrait.client.facade.material.QueryMaterialSearchHisFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description: (客户画像-话术回复检索历史-外部服务)
 * <AUTHOR>
 * @date 2025/3/17 15:12
 * @since JDK 1.8
 */
@Slf4j
@Service
public class PortraitMaterialSearchHisOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private DeleteMaterialSearchHisFacade deleteMaterialSearchHisFacade;
    @DubboReference(registry = "crm-portrait-remote", check = false)
    private AddMaterialSearchHisFacade addMaterialSearchHisFacade;

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private QueryMaterialSearchHisFacade queryMaterialSearchHisFacade;


    /**
     * @description:(删除话术回复检索历史)
     * @param conscode	
     * @param hboneNo	
     * @return com.howbuy.crm.portrait.client.domain.dto.common.OptResultDTO
     * @author: haoran.zhang
     * @date: 2025/3/17 16:53
     * @since JDK 1.8
     */
    public OptResultDTO deleteMaterialSearchHis(String conscode, String hboneNo) {
        DeleteMaterialSearchHisRequest request=new DeleteMaterialSearchHisRequest();
        request.setConscode(conscode);
        request.setHboneNo(hboneNo);
        Response<DeleteMaterialSearchHisResponse> response=deleteMaterialSearchHisFacade.execute(request);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode(), response.getDescription());
        }
        return response.getData().getResult();
    }


    /**
     * @description:(添加话术回复检索历史)
     * @param addDto
     * @return com.howbuy.crm.portrait.client.domain.dto.common.OptResultDTO
     * @author: haoran.zhang
     * @date: 2025/3/17 16:53
     * @since JDK 1.8
     */
    public OptResultDTO addMaterialSearchHis(AddMetarialSearchHisDTO addDto) {
        AddMaterialSearchHisRequest request=new AddMaterialSearchHisRequest();
        request.setConscode(addDto.getConscode());
        request.setHboneNo(addDto.getHboneNo());
        request.setContent(addDto.getContent());
        request.setSearchParam(addDto.getSearchParam());
        Response<AddMaterialSearchHisResponse> response=addMaterialSearchHisFacade.execute(request);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode(), response.getDescription());
        }
        return response.getData().getResult();
    }



    /**
     * @description 分页查询素材搜索历史记录
     * @param hboneNo 一账通号
     * @param conscode 投顾编号
     * @param startTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param pageNum 页码，从1开始
     * @param pageSize 每页记录数
     * @return Page<CrmPortraitMaterialSearchHisPO> 分页查询结果
     * <AUTHOR>
     * @date 2025/3/18 13:20
     */
    public QueryMaterialSearchHisResponse queryMaterialSearchHis(String hboneNo, String conscode, String startTime, int pageNum, int pageSize ){
        QueryMaterialSearchHisRequest request=new QueryMaterialSearchHisRequest();
        request.setConscode(conscode);
        request.setHboneNo(hboneNo);
        request.setStartTime(startTime);
        request.setPage(pageNum);
        request.setRows(pageSize);
        Response<QueryMaterialSearchHisResponse>  response=queryMaterialSearchHisFacade.execute(request);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode(), response.getDescription());
        }
        return response.getData();
    }

}