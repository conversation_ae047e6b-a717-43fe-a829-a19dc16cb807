package com.howbuy.crm.cgi.manager.domain.asset.assetreport.homepage;

import com.howbuy.crm.cgi.manager.domain.asset.QueryCustomerDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: asset
 * @author: yu.z<PERSON>
 * @date: 2023/3/17 16:51
 * @version: 1.0
 * @since JDK 1.8
 */
@Setter
@Getter
@ToString
public class QueryAssetForVisitDTO extends QueryCustomerDTO {

    /**
     * 创建人：投顾编码
     */
    private String creator;

    /**
     * 第几页
     */
    private Integer page = 1;
    /**
     * 每页显示多少条记录
     */
    private Integer size = 20;
    /**
     * 排序字段
     */
    private String sort;
    /**
     * 排序方式
     */
    private String order;
}
