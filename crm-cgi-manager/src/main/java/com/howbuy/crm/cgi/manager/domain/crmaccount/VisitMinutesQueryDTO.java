package com.howbuy.crm.cgi.manager.domain.crmaccount;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * @description: 拜访纪要查询DTO
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Setter
@Getter
public class VisitMinutesQueryDTO {

    /**
     * 机构编码
     */
    private String orgCode;
    /**
     * 投顾Code
     */
    private String consCode;
    
    /**
     * 拜访日期开始，格式YYYYMMDD
     */
    private String visitDateStart;
    
    /**
     * 拜访日期结束，格式YYYYMMDD
     */
    private String visitDateEnd;
    
    /**
     * 创建日期开始，格式YYYYMMDD
     */
    private String createDateStart;
    
    /**
     * 创建日期结束，格式YYYYMMDD
     */
    private String createDateEnd;
    
    /**
     * 拜访目的列表，多选
     */
    private List<String> visitPurpose;
    
    /**
     * 客户姓名，精确匹配
     */
    private String custName;
    
    /**
     * 投顾客户号，精确匹配
     */
    private String consCustNo;
    
    /**
     * 陪访人，精确匹配
     */
    private String accompanyingUser;
    
    /**
     * 上级主管，精确匹配
     */
    private String managerId;
    
    /**
     * 反馈情况，可选：陪访人未填、上级主管未填、陪访人已填、上级主管已填
     */
    private List<String> feedbackStatus;
    
    /**
     * 分页页码
     */
    private Integer pageNo;
    
    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 当前登录用户ID
     */
    private String currentUserId;
} 