/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.report;

import com.howbuy.crm.cgi.manager.convert.portrait.report.BalanceReportConvert;
import com.howbuy.crm.cgi.manager.domain.portrait.report.BalanceReportDTO;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.facade.report.BalanceReportQueryFacade;
import com.howbuy.crm.portrait.client.domain.request.report.BalanceReportQueryRequest;
import com.howbuy.crm.portrait.client.domain.response.report.BalanceReportQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 持仓投后报告查询外部服务
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Slf4j
@Service
public class BalanceReportOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private BalanceReportQueryFacade balanceReportQueryFacade;

    /**
     * 查询持仓投后报告
     * @param conscode 投顾编号
     * @param hboneNo 一账通号
     * @return 持仓投后报告DTO
     */
    public BalanceReportDTO queryBalanceReport(String conscode, String hboneNo) {
        log.info("查询持仓投后报告-请求参数：conscode={}, hboneNo={}", conscode, hboneNo);
        
        BalanceReportQueryRequest request = new BalanceReportQueryRequest();
        request.setConscode(conscode);
        request.setHboneNo(hboneNo);

        Response<BalanceReportQueryResponse> response = balanceReportQueryFacade.execute(request);
        log.info("查询持仓投后报告-响应结果：{}", response);

        if (response == null || !response.isSuccess()) {
            log.error("查询持仓投后报告-失败：{}", response);
            return null;
        }

        return BalanceReportConvert.getInstance().toBalanceReportDTO(response.getData());
    }
} 