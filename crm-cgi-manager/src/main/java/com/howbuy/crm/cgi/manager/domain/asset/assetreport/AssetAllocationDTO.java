/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.asset.assetreport;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/2/18 14:04
 * @since JDK 1.8
 */
@Data
public class AssetAllocationDTO implements Serializable {
    /**
     * 1001-资配报告话术库
     */
    private String proid;

    /**
     * 内容id
     */
    private String contentid;

    /**
     * 客户号
     */
    private String consno;

    /**
     * 投顾code
     */
    private String conscode;

    /**
     * 所属页面名
     */
    private String pagename;

    /**
     * 所属模块名称
     */
    private String modulename;

    private String url;

    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;

    private String ext6;

    private String ext7;

    private String ext8;

    private String ext9;

    private String ext10;

    private String ip;

    private String ts;

}