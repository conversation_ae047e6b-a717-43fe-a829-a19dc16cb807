/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.material;

import com.howbuy.crm.portrait.client.domain.request.material.QueryRecommendMaterialRequest;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.domain.response.material.QueryRecommendMaterialResponse;
import com.howbuy.crm.portrait.client.facade.material.QueryRecommendMaterialFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 查询推荐列表外部服务
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Slf4j
@Service
public class QueryRecommendMaterialOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private QueryRecommendMaterialFacade queryRecommendMaterialFacade;

    /**
     * 查询推荐列表
     * @description 调用远程服务查询推荐列表信息
     * @param conscode 投顾编号
     * @param hboneNo 一账通号
     * @param recommendTab 推荐tab页
     * @param page 分页页码
     * @param size 每页数量
     * @return QueryRecommendMaterialResponse 推荐列表响应对象
     * <AUTHOR>
     * @date 2024-03-19 15:30:00
     */
    public QueryRecommendMaterialResponse queryRecommendMaterial(String conscode, String hboneNo, String recommendTab, Integer page, Integer size) {
        log.info("查询推荐列表-请求参数：conscode={}, hboneNo={}, recommendTab={}, page={}, size={}", 
                conscode, hboneNo, recommendTab, page, size);
        
        QueryRecommendMaterialRequest request = new QueryRecommendMaterialRequest();
        request.setConscode(conscode);
        request.setHboneNo(hboneNo);
        request.setType(recommendTab);
        request.setPage(page);
        request.setRows(size);
        Response<QueryRecommendMaterialResponse> response = queryRecommendMaterialFacade.execute(request);

        log.info("查询推荐列表-响应结果：{}", response);

        if (response == null || !response.isSuccess()) {
            log.error("查询推荐列表-失败，响应结果：{}", response);
            return null;
        }

        return response.getData();
    }
} 