/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.portrait.report;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 持仓投后报告DTO
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Getter
@Setter
public class BalanceReportDTO {
    
    /**
     * 好买基金模块list
     */
    private List<FundReportDTO> hmjjList;

    /**
     * 好臻投资模块list
     */
    private List<FundReportDTO> hztzList;

    /**
     * 好买香港模块list
     */
    private List<FundReportDTO> hmxgList;

    /**
     * 用户是否授权（0：否，1：是）
     */
    private String isAuthorization;

    /**
     * 投顾所属组织架构是否香港（0：否，1：是）
     */
    private String tgIsXg;

    @Getter
    @Setter
    public static class FundReportDTO {
        /**
         * 基金代码
         */
        private String jjdm;

        /**
         * 基金简称
         */
        private String jjjc;

        /**
         * 产品子类型名称
         */
        private String productSubTypeName;

        /**
         * 是否更多
         * 0：否，1：是
         */
        private String isMoreReport;

        /**
         * 报告集合
         */
        private List<ReportDTO> reportList;
    }

    @Getter
    @Setter
    public static class ReportDTO {

        /**
         * 报告ID
         */
        private String reportId;

        /**
         * 报告日期 (MM月dd日)
         */
        private String date;

        /**
         * 报告标题
         */
        private String title;

        /**
         * 是否new标签（0：否，1：是）
         */
        private String isNew;

        /**
         * 发送次数
         */
        private String sendNum;

        /**
         * 报告URL
         */
        private String reportUrl;

        /**
         * 素材发送ID
         */
        private String materialId;

        /**
         * 素材发送类型枚举 1：素材 2：持仓报告 3：资配报告
         */
        private String materialSendType;

    }
} 