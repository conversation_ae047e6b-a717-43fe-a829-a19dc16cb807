/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.portrait.comm;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/8 17:26
 * @since JDK 1.8
 */
@Data
public class AddCommRecordDTO {
    /**
     * 投顾编号
     */
    private String conscode;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 沟通日期 yyyyMMDD
     */
    private String commDate;

    /**
     * 沟通时间 hhmmss
     */
    private String commTime;

    /**
     * 沟通方式
     */
    private String commMode;

    /**
     * 沟通内容
     */
    private String commContext;
}
