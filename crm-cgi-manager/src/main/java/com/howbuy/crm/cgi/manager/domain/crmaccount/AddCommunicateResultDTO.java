package com.howbuy.crm.cgi.manager.domain.crmaccount;

import com.howbuy.crm.cgi.common.base.Body;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 新增客户沟通记录返回值对象
 * @date 2025/04/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddCommunicateResultDTO extends Body {
    /**
     * 沟通记录ID
     */
    private String communicateId;
    
    /**
     * 拜访纪要ID
     */
    private String visitMinutesId;
} 