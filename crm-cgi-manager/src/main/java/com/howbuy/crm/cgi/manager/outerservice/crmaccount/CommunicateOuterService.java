/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.crmaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade;
import com.howbuy.crm.account.client.facade.commvisit.CsCommVisitFacade;
import com.howbuy.crm.account.client.request.commvisit.AddCommunicateRecordRequest;
import com.howbuy.crm.account.client.request.commvisit.QueryVisitMinutesListRequest;
import com.howbuy.crm.account.client.request.commvisit.VisitInitDataRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.commvisit.AddCommunicateRecordResponse;
import com.howbuy.crm.account.client.response.commvisit.VisitInitDataResponse;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesListVO;
import com.howbuy.crm.account.client.response.commvisit.VisitMinutesVO;
import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.crmaccount.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 客户沟通记录外部服务
 * @author: jin.wang03
 * @date: 2025-04-08 19:27:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CommunicateOuterService {

    @DubboReference(registry = "crm-account-remote", check = false)
    private CsCommVisitFacade csCommVisitFacade;

    @DubboReference(registry = "crm-account-remote", check = false)
    private CmVisitMinutesFacade cmVisitMinutesFacade;

    /**
     * 获取客户沟通记录新增页初始化数据
     *
     * @param consCode 投顾编码
     * @return 初始化数据
     */
    public VisitInitDTO getVisitInitData(String consCode) {
        VisitInitDataRequest request = new VisitInitDataRequest();
        request.setConsCode(consCode);

        Response<VisitInitDataResponse> response = csCommVisitFacade.getVisitInitData(request);
        if (!response.isSuccess()) {
            log.error("获取客户沟通记录新增页初始化数据失败，consCode:{},response:{}", consCode, JSON.toJSONString(response));
            return new VisitInitDTO();
        }

        return convertToDTO(response.getData());
    }

    /**
     * 将外部服务响应对象转换为内部DTO
     *
     * @param vo 外部服务响应对象
     * @return 内部DTO
     */
    private VisitInitDTO convertToDTO(VisitInitDataResponse vo) {
        if (vo == null) {
            return new VisitInitDTO();
        }

        VisitInitDTO dto = new VisitInitDTO();
        dto.setProjectManagerUserList(convertUserList(vo.getProjectManagerUserList()));
        dto.setManageUserList(convertUserList(vo.getManageUserList()));
        dto.setSupervisorUserList(convertUserList(vo.getSupervisorUserList()));
        dto.setOtherUserList(convertUserList(vo.getOtherUserList()));
        return dto;
    }

    /**
     * 转换用户列表
     *
     * @param userList 外部服务用户列表
     * @return 内部DTO用户列表
     */
    private List<UserInfoDTO> convertUserList(List<VisitInitDataResponse.UserInfo> userList) {
        List<UserInfoDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(userList)) {
            return result;
        }

        for (VisitInitDataResponse.UserInfo user : userList) {
            UserInfoDTO dto = new UserInfoDTO();
            dto.setCode(user.getCode());
            dto.setName(user.getName());
            result.add(dto);
        }
        return result;
    }

    /**
     * @description: 获取拜访纪要列表
     * @param request 查询参数
     * @return VisitMinutesListDTO 拜访纪要列表
     * @author: jin.wang03
     * @date: 2025-04-09 10:00:00
     */
    public VisitMinutesListDTO getVisitMinutesList(QueryVisitMinutesListRequest request) {
        QueryVisitMinutesListRequest queryVisitMinutesListRequest = new QueryVisitMinutesListRequest();
        log.info("CommunicateOuterService_getVisitMinutesList_request:{}", JSON.toJSONString(request));
        Response<VisitMinutesListVO> response = cmVisitMinutesFacade.getVisitMinutesList(queryVisitMinutesListRequest);
        log.info("CommunicateOuterService_getVisitMinutesList_response:{}", JSON.toJSONString(response));
        
        if (!response.isSuccess() || response.getData() == null) {
            log.error("获取拜访纪要列表失败，request:{},response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            return new VisitMinutesListDTO();
        }

        VisitMinutesListDTO result = new VisitMinutesListDTO();
        result.setTotal(response.getData().getTotal());
        
        if (CollectionUtils.isNotEmpty(response.getData().getVisitMinutesList())) {
            List<VisitMinutesListDTO.VisitMinutesItemDTO> list = new ArrayList<>();
            for (VisitMinutesVO item : response.getData().getVisitMinutesList()) {
                VisitMinutesListDTO.VisitMinutesItemDTO dto = new VisitMinutesListDTO.VisitMinutesItemDTO();
                dto.setCreateTime(item.getCreateTime());
                dto.setVisitDt(item.getVisitDt());
                dto.setVisitPurpose(item.getVisitPurpose());
                dto.setConsCustNo(item.getConsCustNo());
                dto.setCustName(item.getCustName());
                dto.setCreatorName(item.getCreatorName());
                dto.setCenterName(item.getCenterName());
                dto.setAreaName(item.getAreaName());
                dto.setBranchName(item.getBranchName());
                dto.setVisitType(item.getVisitType());
                dto.setMarketVal(item.getMarketVal());
                dto.setHealthAvgStar(item.getHealthAvgStar());
                dto.setGiveInformation(item.getGiveInformation());
                dto.setAttendRole(item.getAttendRole());
                dto.setProductServiceFeedback(item.getProductServiceFeedback());
                dto.setIpsFeedback(item.getIpsFeedback());
                dto.setAddAmount(item.getAddAmount());
                dto.setFocusAsset(item.getFocusAsset());
                dto.setEstimateNeedBusiness(item.getEstimateNeedBusiness());
                dto.setNextPlan(item.getNextPlan());
                dto.setAccompanyingType(item.getAccompanyingType());
                dto.setAccompanyingUser(item.getAccompanyingUser());
                dto.setAccompanySummary(item.getAccompanySummary());
                dto.setAccompanySuggestion(item.getAccompanySuggestion());
                dto.setManagerName(item.getManagerName());
                dto.setManagerSummary(item.getManagerSummary());
                dto.setManagerSuggestion(item.getManagerSuggestion());
                dto.setCanAccompanyFeedback(item.getCanAccompanyFeedback());
                dto.setCanManagerFeedback(item.getCanManagerFeedback());
                list.add(dto);
            }
            result.setList(list);
        }
        
        return result;
    }

    /**
     * 新增客户沟通记录
     *
     * @param dto 新增沟通记录DTO
     */
    public AddCommunicateResultDTO addCommunicate(AddCommunicateDTO dto) {
        log.info("调用crm-account服务新增客户沟通记录开始，dto:{}", JSON.toJSONString(dto));
        
        // 构建请求对象
        AddCommunicateRecordRequest request = buildAddCommunicateRequest(dto);
        
        // 调用crm-account服务
        Response<AddCommunicateRecordResponse> response = csCommVisitFacade.addCommunicateRecord(request);
        log.info("调用crm-account服务新增客户沟通记录，response:{}", JSON.toJSONString(response));
        if (Objects.isNull(response)) {
            AddCommunicateResultDTO addCommunicateResultDTO = new AddCommunicateResultDTO();
            addCommunicateResultDTO.setReturnCode(ExceptionCodeEnum.SYSTEM_ERROR.getCode());
            addCommunicateResultDTO.setDescription(ExceptionCodeEnum.SYSTEM_ERROR.getDescription());
            return addCommunicateResultDTO;
        }
        if (!response.isSuccess()) {
            AddCommunicateResultDTO addCommunicateResultDTO = new AddCommunicateResultDTO();
            addCommunicateResultDTO.setReturnCode(response.getCode());
            addCommunicateResultDTO.setDescription(response.getDescription());
            return addCommunicateResultDTO;
        }

        AddCommunicateResultDTO addCommunicateResultDTO = new AddCommunicateResultDTO();
        addCommunicateResultDTO.setCommunicateId(response.getData().getCommunicateId());
        addCommunicateResultDTO.setVisitMinutesId(response.getData().getVisitMinutesId());
        addCommunicateResultDTO.setReturnCode(response.getCode());
        addCommunicateResultDTO.setDescription(response.getDescription());
        return addCommunicateResultDTO;
    }
    
    /**
     * 构建新增沟通记录请求对象
     *
     * @param dto DTO对象
     * @return 请求对象
     */
    private AddCommunicateRecordRequest buildAddCommunicateRequest(AddCommunicateDTO dto) {
        AddCommunicateRecordRequest request = new AddCommunicateRecordRequest();

        request.setOperator(dto.getOperator());
        // 设置沟通记录信息
        AddCommunicateDTO.CommunicateReq communicateReq = dto.getCommunicateReq();
        AddCommunicateRecordRequest.CommunicateReq communicateReq1 = new AddCommunicateRecordRequest.CommunicateReq();
        BeanUtils.copyProperties(communicateReq, communicateReq1);
        request.setCommunicateReq(communicateReq1);
        
        // 设置预约信息
        AddCommunicateDTO.BookingReq bookingReq = dto.getBookingReq();
        if (Objects.nonNull(bookingReq)) {
            AddCommunicateRecordRequest.BookingReq bookingReq1 = new AddCommunicateRecordRequest.BookingReq();
            BeanUtils.copyProperties(bookingReq, bookingReq1);
            request.setBookingReq(bookingReq1);
        }
        
        // 设置拜访纪要信息
        AddCommunicateDTO.VisitMinutesReq visitMinutesReq = dto.getVisitMinutesReq();
        if (Objects.nonNull(visitMinutesReq)) {
            AddCommunicateRecordRequest.VisitMinutesReq visitMinutesReq1 = new AddCommunicateRecordRequest.VisitMinutesReq();
            BeanUtils.copyProperties(visitMinutesReq, visitMinutesReq1);
            request.setVisitMinutesReq(visitMinutesReq1);
        }
        
        // 设置陪访人列表
        if (Objects.nonNull(dto.getVisitMinutesReq()) && CollectionUtils.isNotEmpty(dto.getVisitMinutesReq().getAccompanyingList())) {
            List<AddCommunicateDTO.AccompanyingPerson> accompanyingList = dto.getVisitMinutesReq().getAccompanyingList();
            List<AddCommunicateRecordRequest.AccompanyingPerson> collect = accompanyingList.stream().map(accompanyingPerson -> {
                AddCommunicateRecordRequest.AccompanyingPerson accompanyingPerson1 = new AddCommunicateRecordRequest.AccompanyingPerson();
                BeanUtils.copyProperties(accompanyingPerson, accompanyingPerson1);
                return accompanyingPerson1;
            }).collect(Collectors.toList());
            request.getVisitMinutesReq().setAccompanyingList(collect);
        }
        
        return request;
    }
} 