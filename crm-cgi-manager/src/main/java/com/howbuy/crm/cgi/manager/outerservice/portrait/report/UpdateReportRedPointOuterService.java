/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.portrait.report;

import com.howbuy.crm.portrait.client.domain.request.material.UpdateReportRedPointRequest;
import com.howbuy.crm.portrait.client.domain.response.Response;
import com.howbuy.crm.portrait.client.facade.material.UpdateReportRedPointFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 更新投后报告小红点外部服务
 * <AUTHOR>
 * @date 2024-03-19 15:30:00
 */
@Slf4j
@Service
public class UpdateReportRedPointOuterService {

    @DubboReference(registry = "crm-portrait-remote", check = false)
    private UpdateReportRedPointFacade updateReportRedPointFacade;

    /**
     * 更新投后报告小红点
     * @description 调用远程服务更新投后报告小红点状态
     * @param hboneNo 一账通号
     * @return 更新结果，true-成功，false-失败
     * <AUTHOR>
     * @date 2024-03-19 15:30:00
     */
    public boolean updateReportRedPoint(String hboneNo) {
        log.info("更新投后报告小红点-请求参数：hboneNo={}", hboneNo);
        
        UpdateReportRedPointRequest request = new UpdateReportRedPointRequest();
        request.setHboneNo(hboneNo);
        Response<Void> response = updateReportRedPointFacade.updateReportRedPoint(request);

        log.info("更新投后报告小红点-响应结果：{}", response);

        if (response == null || !response.isSuccess()) {
            log.error("更新投后报告小红点-失败，响应结果：{}", response);
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }
} 