/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkfin;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/7/8 13:39
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkFinRefundInfoDTO implements Serializable {

    private static final long serialVersionUID = -4339999877677304343L;

    /**
     * 申请流水号
     */
    private String applyId;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 数据来源：【3-线下资金系统、4-线上】
     */
    private String dataSource;

    /**
     * 审核状态：【0-正常、1-删除、2-未审核，3-审核拒绝、4-无需审核】
     */
    private String recStat;

    /** 0-无需清算、1-未清算、3-清算中、4-清算成功、5-清算失败 */
    private String settleStatus;

    /**
     * 客户删除标志：【0-否、1-是】
     */
    private String deleteFlag;

    /**
     * 退款币种
     */
    private String curCode;

    /**
     * 退款金额
     */
    private BigDecimal occurBalance;

    /**
     * 清算币种
     */
    private String settleCurCode;

    /**
     * 预计清算日期
     */
    private String settleDt;

    /**
     * 实际清算日期
     */
    private String realSettleDt;

    /**
     * 创建时间(yyyy-mm-dd hh:mm:ss)
     */
    private String createTime;

    /**
     * 审核时间(yyyy-mm-dd hh:mm:ss)
     */
    private String auditTime;
}
