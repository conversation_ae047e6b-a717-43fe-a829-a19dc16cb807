/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/2 15:25
 * @since JDK 1.8
 */
@Setter
@Getter
public class HkFundTxAcctDTO implements Serializable {

    private static final long serialVersionUID = -4790576696793416601L;


    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 0-非全委 1-全委
     */
    private String fundTxAccType;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金交易账号状态 1-正常 2-注销 3-冻结
     */
    private String fundTxAcctStat;

    /**
     * 创建时间
     */
    private Date createTimestamp;

    /**
     * 是否存在持仓
     * 0-否 1-是
     */
    private String hasBalance;

}
