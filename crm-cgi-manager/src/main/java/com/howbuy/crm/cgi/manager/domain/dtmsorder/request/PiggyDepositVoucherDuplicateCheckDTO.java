/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.request;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/10 19:29
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggyDepositVoucherDuplicateCheckDTO implements Serializable {

    private static final long serialVersionUID = -3801850594562669759L;


    /**
     * 香港资金账号
     */
    private String cpAcctNo;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 银行swiftCode码值
     */
    private String swiftCode;

    /**
     * 汇款账户币种
     */
    private String remitCurrency;

    /**
     * 汇款金额
     */
    private String remitAmt;

    /**
     * 审核状态
     */
    private List<String> auditStatusList;

    /**
     * 申请时间
     */
    private String appDt;
}
