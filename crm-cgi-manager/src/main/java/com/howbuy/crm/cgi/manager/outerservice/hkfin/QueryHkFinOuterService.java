/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.hkfin;

import com.howbuy.crm.cgi.common.enums.ExceptionCodeEnum;
import com.howbuy.crm.cgi.common.exception.BusinessException;
import com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoDTO;
import com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoRequestDTO;
import com.howbuy.hkfin.facade.ResultDto;
import com.howbuy.hkfin.facade.query.control.QueryRefundApplyFacade;
import com.howbuy.hkfin.facade.query.control.QueryRefundApplyRequest;
import com.howbuy.hkfin.facade.query.control.QueryRefundApplyResponse;
import com.howbuy.hkfin.facade.query.control.RefundInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/8 13:30
 * @since JDK 1.8
 */
@Component
public class QueryHkFinOuterService {

    @DubboReference(registry = "hk-fin-service", check = false)
    private QueryRefundApplyFacade queryRefundApplyFacade;

    /**
     * @description: 查询申请提款订单接口
     * @param hkFinRefundInfoRequestDTO 请求参数
     * @return java.util.List<com.howbuy.crm.cgi.manager.domain.hkfin.HkFinRefundInfoDTO>
     * @author: jinqing.rao
     * @date: 2025/7/8 13:47
     * @since JDK 1.8
     */
    public List<HkFinRefundInfoDTO> queryRefundApplyOrderList(HkFinRefundInfoRequestDTO hkFinRefundInfoRequestDTO) {
        // 构建参数
        QueryRefundApplyRequest request = buildQueryRefundApplyRequest(hkFinRefundInfoRequestDTO);

        ResultDto<QueryRefundApplyResponse> resultDto = queryRefundApplyFacade.execute(request);
        if(!resultDto.isSuccess()){
            throw new BusinessException(ExceptionCodeEnum.QUERY_HK_FIN_REFUND_ORDER_ERROR);
        }
        QueryRefundApplyResponse dtoData = resultDto.getData();
        if(null == dtoData){
            return new ArrayList<>();
        }
        List<RefundInfo> refundList = dtoData.getRefundList();
        if(CollectionUtils.isEmpty(refundList)){
            return new ArrayList<>();
        }
        return refundList.stream().map(m -> {
            HkFinRefundInfoDTO hkFinRefundInfoDTO = new HkFinRefundInfoDTO();
            hkFinRefundInfoDTO.setApplyId(m.getApplyId());
            hkFinRefundInfoDTO.setHkCustNo(m.getHkCustNo());
            hkFinRefundInfoDTO.setFundTxAcctNo(m.getFundTxAcctNo());
            hkFinRefundInfoDTO.setDataSource(m.getDataSource());
            hkFinRefundInfoDTO.setRecStat(m.getRecStat());
            hkFinRefundInfoDTO.setSettleStatus(m.getSettleStatus());
            hkFinRefundInfoDTO.setDeleteFlag(m.getDeleteFlag());
            hkFinRefundInfoDTO.setCurCode(m.getCurCode());
            hkFinRefundInfoDTO.setOccurBalance(m.getOccurBalance());
            hkFinRefundInfoDTO.setSettleCurCode(m.getSettleCurCode());
            hkFinRefundInfoDTO.setSettleDt(m.getSettleDt());
            hkFinRefundInfoDTO.setRealSettleDt(m.getRealSettleDt());
            hkFinRefundInfoDTO.setCreateTime(m.getCreateTime());
            hkFinRefundInfoDTO.setAuditTime(m.getAuditTime());
            return hkFinRefundInfoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * @description: 构建参数
     * @param hkFinRefundInfoRequestDTO
     * @return com.howbuy.hkfin.facade.query.control.QueryRefundApplyRequest
     * @author: jinqing.rao
     * @date: 2025/7/8 13:50
     * @since JDK 1.8
     */
    private static QueryRefundApplyRequest buildQueryRefundApplyRequest(HkFinRefundInfoRequestDTO hkFinRefundInfoRequestDTO) {
        QueryRefundApplyRequest request = new QueryRefundApplyRequest();
        request.setHkCustNo(hkFinRefundInfoRequestDTO.getHkCustNo());
        request.setFundTxAcctNoList(hkFinRefundInfoRequestDTO.getFundTxAcctNoList());
        request.setDataSource(hkFinRefundInfoRequestDTO.getDataSource());
        request.setRecStatList(hkFinRefundInfoRequestDTO.getRecStatList());
        request.setOrderPayStatusList(hkFinRefundInfoRequestDTO.getOrderPayStatusList());
        request.setDeleteFlag(hkFinRefundInfoRequestDTO.getDeleteFlag());
        request.setStartDataDt(hkFinRefundInfoRequestDTO.getStartDataDt());
        request.setEndDataDt(hkFinRefundInfoRequestDTO.getEndDataDt());
        return request;
    }

}
