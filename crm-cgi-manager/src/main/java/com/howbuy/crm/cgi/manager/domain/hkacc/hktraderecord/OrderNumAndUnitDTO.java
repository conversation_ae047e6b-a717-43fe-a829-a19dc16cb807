/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkacc.hktraderecord;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/5/27 19:28
 * @since JDK 1.8
 */
@Setter
@Getter
public class OrderNumAndUnitDTO implements Serializable {

    private static final long serialVersionUID = 2694718084828279231L;
    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请币种
     */
    private String currency;

    /**
     * 赎回方式
     */
    private String redeemType;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private String num;

    /**
     * 分红方式
     */
    private String fundDivMode;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认净值
     */
    private Integer volPrecision;

    /**
     * 净申金额
     */
    private BigDecimal netAppAmt;

    /**
     * 中台业务码
     */
    private String middleBusiCode;
}
