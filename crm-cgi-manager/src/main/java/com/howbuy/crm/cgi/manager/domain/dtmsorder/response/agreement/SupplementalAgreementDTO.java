package com.howbuy.crm.cgi.manager.domain.dtmsorder.response.agreement;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 补充协议DTO
 * @author: jinqing.rao
 * @date: 2024/3/6 18:22
 * @since JDK 1.8
 */
@Setter
@Getter
public class SupplementalAgreementDTO {

    /**
     * 补签协议ID
     */
    private String agreementId;

    /**
     * 协议签署截止时间
     * 格式：YYYY-MM-DD HH:MM
     */
    private String agreementSignEndDt;

    /**
     * 协议名称
     */
    private String agreementName;

    /**
     * 基金Code
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 协议说明
     */
    private String agreementDescription;

    /**
     * 协议地址
     * 同一个产品，是一样的，所以通过静态链接地址访问
     */
    private String agreementUrl;

    /**
     * 签署状态
     */
    private String signStatus;


    /**
     * 签署时间
     */
    private String signDate;
} 