/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/7 14:27
 * @since JDK 1.8
 */
@Setter
@Getter
public class ChangePayMethodPageDTO {

    /**
     * 修改支付方式订单信息
     */
    private ChangePayMethodOrderDTO changePayMethodOrderDTO;

    /**
     * 支付方式信息
     */
    private PayMethodInfoDTO payMethodInfoVO;

    @Setter
    @Getter
    public static class ChangePayMethodOrderDTO implements Serializable {

        private static final long serialVersionUID = 4306567561021343718L;
        /**
         * 支付方式  1 银行卡  2 支票  3 银行卡
         */
        private String paymentType;

        /**
         * 打款截止日期
         */
        private String payEndDate;

        /**
         * 打款截止日期 hh:mm
         */
        private String payEndTime;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 实缴金额
         */
        private BigDecimal appAmt;

        /**
         *  如果是银行卡支付，会返回资金账号
         */
        private String cpAcctNo;
    }
}
