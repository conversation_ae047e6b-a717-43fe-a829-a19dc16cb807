/**
 * Copyright (c) 2025, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkfund;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 购买日历DTO
 * @author: 陈杰文
 * @date: 2025-06-25 11:28:45
 * @since JDK 1.8
 */
@Getter
@Setter
public class BuyCalendarDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否支持购买预约交易
     * 1-是 0-否
     */
    private String supportPrebook;

    /**
     * 预约结束日期
     * 格式：yyyyMMdd
     */
    private String advanceEndDt;

    /**
     * 预约结束时间
     * 格式：HHmmss
     */
    private String advanceEndTm;

    /**
     * 当前购买开放开始日期
     * 格式：yyyyMMdd
     */
    private String openStartDt;

    /**
     * 当前购买开放结束日期
     * 格式：yyyyMMdd
     */
    private String openEndDt;

    /**
     * 开放日期
     * 格式：yyyyMMdd
     */
    private String openDt;

    /**
     * 下单结束时间，
     * 每日开放产品，取基金基本信息的下单结束时间返回
     * 预约产品，取预计日历的预约结束时间返回
     */
    private String orderEndTm;

    /**
     * 产品打款截止日期
     */
    private String paymentDeadlineDt;

    /**
     * 产品打款截止时间
     */
    private String paymentDeadlineTime;

    /**
     * 预计上报日期
     * 格式：yyyyMMdd
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     * 格式：HHmmss
     */
    private String preSubmitTaTm;
} 