package com.howbuy.crm.cgi.manager.domain.crmaccount;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * @description: 拜访纪要反馈明细DTO
 * @author: jianjian.yang
 * @date: 2025-04-09 16:04:00
 */
@Setter
@Getter
public class VisitFeedbackDetailDTO {
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 投顾客户号
     */
    private String consCustNo;
    
    /**
     * 拜访日期，格式YYYYMMDD
     */
    private String visitDt;
    
    /**
     * 沟通方式
     */
    private String visitType;
    
    /**
     * 客户存量
     */
    private String marketVal;
    
    /**
     * 客户综合健康度
     */
    private String healthAvgStar;
    
    /**
     * 拜访目的列表
     */
    private List<String> visitPurpose;
    
    /**
     * 拜访目的其他说明
     */
    private String visitPurposeOther;
    
    /**
     * 报告ID
     */
    private String reportId;
    
    /**
     * 报告标题
     */
    private String reportTitle;
    
    /**
     * 提供资料
     */
    private String giveInformation;
    
    /**
     * 陪访人姓名，多个逗号分隔
     */
    private String accompanyingUser;
    
    /**
     * 参与人员及角色
     */
    private String attendRole;
    
    /**
     * 产品服务反馈
     */
    private String productServiceFeedback;
    
    /**
     * IPS报告反馈
     */
    private String ipsFeedback;
    
    /**
     * 人民币金额
     */
    private String addAmountRmb;
    
    /**
     * 外币金额
     */
    private String addAmountForeign;
    
    /**
     * 关注资产
     */
    private String focusAsset;
    
    /**
     * 客户需求
     */
    private String estimateNeedBusiness;
    
    /**
     * 工作计划
     */
    private String nextPlan;
    
    /**
     * 陪访人姓名
     */
    private String userName;
    
    /**
     * 陪访人数据ID
     */
    private String accompanyingId;
    
    /**
     * 主管姓名
     */
    private String managerName;
    
    /**
     * 概要
     */
    private String summary;
    
    /**
     * 建议
     */
    private String suggestion;

    /**
     * 是否不可编辑概要经验与教训
     */
    private Boolean canNotEditSummary;
} 