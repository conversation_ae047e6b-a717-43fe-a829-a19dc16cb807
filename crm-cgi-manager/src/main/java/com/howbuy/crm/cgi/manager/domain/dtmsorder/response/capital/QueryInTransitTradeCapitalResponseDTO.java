/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.response.capital;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/9 9:54
 * @since JDK 1.8
 */
@Builder
@Getter
public class QueryInTransitTradeCapitalResponseDTO implements Serializable {

    private static final long serialVersionUID = 438867249191195618L;

    /**
     * 持仓列表
     */
    private List<InTransitTradeCapitalDTO> inTransitTradeCapitalList;

    @Builder
    @Getter
    public static class InTransitTradeCapitalDTO implements Serializable {

        private static final long serialVersionUID = -1943011242401094304L;

        /**
         * 交易单号
         */
        private String dealNo;

        /**
         * 基金交易账号
         */
        private String fundTxAcctNo;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 香港客户号
         */
        private String hkCustNo;
    }
}
