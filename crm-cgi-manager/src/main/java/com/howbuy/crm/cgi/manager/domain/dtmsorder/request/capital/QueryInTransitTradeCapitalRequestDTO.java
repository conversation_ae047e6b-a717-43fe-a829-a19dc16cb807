/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.dtmsorder.request.capital;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/9 9:52
 * @since JDK 1.8
 */
@Builder
@Getter
public class QueryInTransitTradeCapitalRequestDTO implements Serializable {

    private static final long serialVersionUID = -5353815661943316859L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 中台业务码
     */
    private List<String> midBusinessCodeList;
}
