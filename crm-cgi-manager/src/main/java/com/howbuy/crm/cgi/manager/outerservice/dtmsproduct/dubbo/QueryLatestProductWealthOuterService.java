/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsproduct.dubbo;

import com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.QueryProductWealthAppointmentConfigDTO;
import com.howbuy.crm.cgi.manager.utils.OutServiceResultUtils;
import com.howbuy.dtms.product.client.common.Response;
import com.howbuy.dtms.product.client.query.productwealthappointmentconfig.QueryLatestProductWealthAppointmentConfigFacade;
import com.howbuy.dtms.product.client.query.productwealthappointmentconfig.QueryLatestProductWealthAppointmentConfigRequest;
import com.howbuy.dtms.product.client.query.vo.QueryProductWealthAppointmentConfigVO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/24 18:52
 * @since JDK 1.8
 */
@Component
public class QueryLatestProductWealthOuterService {

    @DubboReference(registry = "dtms-product-remote", check = false)
    private QueryLatestProductWealthAppointmentConfigFacade queryLatestProductWealthAppointmentConfigFacade;

    /**
     * @description: 查询产品财富配置信息
     * @param investType
     * @return com.howbuy.crm.cgi.manager.domain.dtmsproduct.fund.QueryProductWealthAppointmentConfigDTO
     * @author: jinqing.rao
     * @date: 2025/4/25 10:14
     * @since JDK 1.8
     */
    public QueryProductWealthAppointmentConfigDTO queryLatestProductWealthAppointmentConfig(String investType) {
        QueryLatestProductWealthAppointmentConfigRequest request = new QueryLatestProductWealthAppointmentConfigRequest();
        request.setInvestType(investType);
        Response<QueryProductWealthAppointmentConfigVO> response = queryLatestProductWealthAppointmentConfigFacade.execute(request);
        OutServiceResultUtils.checkResultSuccessForProduct(response,null);
        QueryProductWealthAppointmentConfigVO responseData = response.getData();
        QueryProductWealthAppointmentConfigDTO configDTO = new QueryProductWealthAppointmentConfigDTO();
        if(null == responseData){
            return configDTO;
        }
        configDTO.setInvestType(responseData.getInvestType());
        configDTO.setYearMonth(responseData.getYearMonth());
        configDTO.setWealthAllocationReport(responseData.getWealthAllocationReport());
        configDTO.setWealthAllocationReportUrl(responseData.getWealthAllocationReportUrl());
        configDTO.setAppointmentCalendarSummary(responseData.getAppointmentCalendarSummary());
        configDTO.setAppointmentCalendarSummaryUrl(responseData.getAppointmentCalendarSummaryUrl());
        configDTO.setCreateTime(responseData.getCreateTime());
        configDTO.setUpdateTime(responseData.getUpdateTime());
        return configDTO;
    }
}
