/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.outerservice.dtmsorder.dubbo;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.cgi.manager.domain.dtmsorder.request.fund.HkFundTxAcctDTO;
import com.howbuy.dtms.order.client.domain.request.fundtxacct.QueryAllFundTxAcctBalanceInfoListRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.fundtxacct.QueryAllFundTxAcctBalanceInfoListResponse;
import com.howbuy.dtms.order.client.facade.query.fundtxacct.QueryAllFundTxAcctBalanceInfoFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description 查询全部基金交易账号余额外部服务
 * <AUTHOR>
 * @date 2025-07-09 17:30:00
 * @since JDK 1.8
 */
@Slf4j
@Component
public class QueryAllFundTxAcctBalanceOuterService {

     @DubboReference(registry = "dtms-order-remote", check = false)
     private QueryAllFundTxAcctBalanceInfoFacade queryAllFundTxAcctBalanceInfoFacade;

    /**
     * @description 查询所有基金交易账号余额信息
     * @param hkCustNo 香港客户号
     * @return Object 查询结果（待定义具体类型）
     * <AUTHOR>
     * @date 2025-07-09 17:30:00
     * @since JDK 1.8
     */
    public List<HkFundTxAcctDTO> queryAllFundTxAcctBalance(String hkCustNo) {
        log.info("查询所有基金交易账号余额信息开始，香港客户号：{}", hkCustNo);
        
        QueryAllFundTxAcctBalanceInfoListRequest request = new QueryAllFundTxAcctBalanceInfoListRequest();
        request.setHkCustNo(hkCustNo);
        Response<QueryAllFundTxAcctBalanceInfoListResponse> response = queryAllFundTxAcctBalanceInfoFacade.execute(request);
        if (null == response || !response.isSuccess()) {
            log.error("查询所有基金交易账号余额信息异常，请求参数:{},返回结果为null", JSON.toJSONString(request));
            return new ArrayList<>();
        }
        if (null == response.getData() || CollectionUtils.isEmpty(response.getData().getFundTxAcctVOList())) {
            log.error("查询所有基金交易账号余额信息为空，请求参数:{},返回结果:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            return new ArrayList<>();
        }

        QueryAllFundTxAcctBalanceInfoListResponse queryAllFundTxAcctBalanceInfoListResponse = response.getData();
        return queryAllFundTxAcctBalanceInfoListResponse.getFundTxAcctVOList().stream()
                .map(it -> {
                    HkFundTxAcctDTO fundTxAcctNoDTO = new HkFundTxAcctDTO();
                    fundTxAcctNoDTO.setHkCustNo(it.getHkCustNo());
                    fundTxAcctNoDTO.setFundTxAccType(it.getFundTxAccType());
                    fundTxAcctNoDTO.setFundCode(it.getFundCode());
                    fundTxAcctNoDTO.setFundTxAcctNo(it.getFundTxAcctNo());
                    fundTxAcctNoDTO.setFundTxAcctStat(it.getFundTxAcctStat());
                    fundTxAcctNoDTO.setCreateTimestamp(it.getCreateTimestamp());
                    fundTxAcctNoDTO.setHasBalance(it.getHasBalance());
                    return fundTxAcctNoDTO;
                }).collect(Collectors.toList());
    }
}