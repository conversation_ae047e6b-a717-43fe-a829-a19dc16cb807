/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.cgi.manager.domain.hkfin;

import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/7/8 13:42
 * @since JDK 1.8
 */
@Builder
@Getter
public class HkFinRefundInfoRequestDTO implements Serializable {

    private static final long serialVersionUID = -5161080366967811965L;

    /**
     * 香港客户号，不能为空
     */
    private String hkCustNo;

    /**
     * 基金交易账号, 不能为空
     */
    private List<String> fundTxAcctNoList;

    /**
     * 数据来源：不传，即查所有，【3-线下资金系统、4-线上】
     */
    private String dataSource;

    /**
     * 审核状态：不传，即查所有，【0-正常、1-删除、2-未审核，3-审核拒绝、4-无需审核】
     */
    private List<String> recStatList;

    /**
     * 付款状态：不传，即查所有，【0-成功、1-失败、2-未付款、3-付款中、4-删除】
     */
    private List<String> orderPayStatusList;

    /**
     * 客户删除标志：不传，即查所有，【0-否、1-是】
     */
    private String deleteFlag;

    /**
     * 开始申请日期 yyyyMMdd
     */
    private String startDataDt;

    /**
     * 结束申请日期 yyyyMMdd
     */
    private String endDataDt;
}
